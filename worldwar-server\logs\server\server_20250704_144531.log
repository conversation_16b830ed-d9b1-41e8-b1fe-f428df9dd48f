2025-07-04 14:45:31 - server_ServerMain - INFO - [enhanced_logger.py:174] - ============================================================
2025-07-04 14:45:31 - server_ServerMain - INFO - [enhanced_logger.py:174] - 🚀 世界大战游戏服务器启动 / World War Game Server Starting
2025-07-04 14:45:31 - server_ServerMain - INFO - [enhanced_logger.py:174] - ============================================================
2025-07-04 14:45:31 - server_ServerPrint - INFO - [enhanced_logger.py:265] - ============================================================
2025-07-04 14:45:31 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 🎮 世界大战游戏服务器 / World War Game Server
2025-07-04 14:45:31 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 📡 版本: v3.0
2025-07-04 14:45:31 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 🌐 地址: localhost:8888
2025-07-04 14:45:31 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 📝 日志: logs/server/ 目录
2025-07-04 14:45:31 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 🛑 按 Ctrl+C 停止服务器
2025-07-04 14:45:31 - server_ServerPrint - INFO - [enhanced_logger.py:265] - ============================================================
2025-07-04 14:45:31 - server_ServerMain - INFO - [enhanced_logger.py:174] - 初始化服务器 - 地址: localhost:8888
2025-07-04 14:45:31 - server_ConfigManager - INFO - [enhanced_logger.py:174] - 配置管理器初始化完成
2025-07-04 14:45:31 - server_ConnectionManager - INFO - [enhanced_logger.py:174] - 连接管理器初始化完成 / Connection manager initialized
2025-07-04 14:45:31 - server_GameManager - INFO - [enhanced_logger.py:174] - 游戏管理器初始化完成 / Game manager initialized
2025-07-04 14:45:31 - server_MessageProcessor - INFO - [enhanced_logger.py:174] - 消息处理器初始化完成 / Message processor initialized
2025-07-04 14:45:31 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器监控器初始化完成 / Server monitor initialized
2025-07-04 14:45:31 - server_SecureGameServer - INFO - [enhanced_logger.py:174] - 安全游戏服务器初始化完成 / Secure game server initialized
2025-07-04 14:45:31 - server_ServerMain - INFO - [enhanced_logger.py:174] - 服务器初始化完成，开始启动...
2025-07-04 14:45:31 - server_SecureGameServer - INFO - [enhanced_logger.py:174] - 正在启动游戏服务器...
2025-07-04 14:45:31 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 🚀 正在启动游戏服务器... / Starting game server...
2025-07-04 14:45:31 - server_ServerPrint - INFO - [enhanced_logger.py:265] - ✅ 成功绑定到 localhost:8888 / Successfully bound to localhost:8888
2025-07-04 14:45:31 - server_ServerPrint - INFO - [enhanced_logger.py:265] - ============================================================
2025-07-04 14:45:31 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 🎮 世界大战游戏服务器已启动 / World War Game Server Started
2025-07-04 14:45:31 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 📡 服务器地址 / Server Address: localhost:8888
2025-07-04 14:45:31 - server_ServerPrint - INFO - [enhanced_logger.py:265] - ⏳ 等待客户端连接... / Waiting for client connections...
2025-07-04 14:45:31 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 🛑 按 Ctrl+C 停止服务器 / Press Ctrl+C to stop server
2025-07-04 14:45:31 - server_ServerPrint - INFO - [enhanced_logger.py:265] - ============================================================
2025-07-04 14:45:31 - server_SecureGameServer - INFO - [enhanced_logger.py:174] - 服务器启动在 localhost:8888
2025-07-04 14:45:31 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 0秒, 活跃连接: 0, 总连接数: 0, 峰值连接: 0, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 14:45:31 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器监控已启动，监控间隔: 30秒
2025-07-04 14:45:31 - server_ServerMonitor - ERROR - [enhanced_logger.py:204] - 清理过期数据时发生错误: 'RoomManager' object has no attribute 'cleanup_expired_rooms'
2025-07-04 14:45:31 - server_SecureGameServer - INFO - [enhanced_logger.py:174] - 房间管理线程已启动 / Room management thread started
2025-07-04 14:46:01 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 30秒, 活跃连接: 0, 总连接数: 0, 峰值连接: 0, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 14:46:01 - server_ServerMonitor - ERROR - [enhanced_logger.py:204] - 清理过期数据时发生错误: 'RoomManager' object has no attribute 'cleanup_expired_rooms'
