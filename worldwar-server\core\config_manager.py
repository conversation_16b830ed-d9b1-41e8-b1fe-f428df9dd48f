#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器模块
Configuration Manager Module
"""

import json
import configparser
from pathlib import Path
from typing import Any, Dict, Optional
from shared.enhanced_logger import get_server_logger


class ConfigManager:
    """配置管理器 - 统一管理服务器配置"""
    
    def __init__(self):
        """初始化配置管理器"""
        self.logger = get_server_logger("ConfigManager")
        
        # 配置文件路径
        self.config_dir = Path("config")
        self.config_dir.mkdir(exist_ok=True)
        
        self.game_config_file = self.config_dir / "game_config.ini"
        self.security_config_file = self.config_dir / "security.json"
        self.server_config_file = self.config_dir / "server_config.ini"
        
        # 配置数据
        self.game_config = configparser.ConfigParser()
        self.security_config = {}
        self.server_config = configparser.ConfigParser()
        
        # 加载配置
        self._load_all_configs()
        
        self.logger.info("配置管理器初始化完成")
    
    def _load_all_configs(self):
        """加载所有配置文件"""
        self._load_game_config()
        self._load_security_config()
        self._load_server_config()
    
    def _load_game_config(self):
        """加载游戏配置"""
        try:
            if self.game_config_file.exists():
                self.game_config.read(self.game_config_file, encoding='utf-8')
            else:
                self._create_default_game_config()
        except Exception as e:
            self.logger.error(f"加载游戏配置失败: {e}")
            self._create_default_game_config()
    
    def _load_security_config(self):
        """加载安全配置"""
        try:
            if self.security_config_file.exists():
                with open(self.security_config_file, 'r', encoding='utf-8') as f:
                    self.security_config = json.load(f)
            else:
                self._create_default_security_config()
        except Exception as e:
            self.logger.error(f"加载安全配置失败: {e}")
            self._create_default_security_config()
    
    def _load_server_config(self):
        """加载服务器配置"""
        try:
            if self.server_config_file.exists():
                self.server_config.read(self.server_config_file, encoding='utf-8')
            else:
                self._create_default_server_config()
        except Exception as e:
            self.logger.error(f"加载服务器配置失败: {e}")
            self._create_default_server_config()
    
    def _create_default_game_config(self):
        """创建默认游戏配置"""
        self.game_config['GAME'] = {
            'min_players': '2',
            'max_players': '8',
            'world_size': 'medium',
            'difficulty': 'normal',
            'language': 'chinese',
            'data_mode': 'ai_generated'
        }
        
        self.game_config['DISPLAY'] = {
            'use_colors': 'true',
            'show_coordinates': 'false',
            'animation_speed': 'normal',
            'log_level': 'INFO'
        }
        
        self.game_config['MECHANICS'] = {
            'turn_time_limit': '0',
            'auto_save': 'true',
            'save_interval': '5',
            'random_events': 'true',
            'natural_disasters': 'true'
        }
        
        with open(self.game_config_file, 'w', encoding='utf-8') as f:
            self.game_config.write(f)
        
        self.logger.info("创建默认游戏配置文件")
    
    def _create_default_security_config(self):
        """创建默认安全配置"""
        self.security_config = {
            "server": {
                "host": "localhost",
                "port": 8888,
                "max_connections": 100,
                "heartbeat_interval": 30,
                "heartbeat_timeout": 60,
                "session_timeout": 300
            },
            "security": {
                "signature_algorithm": "sha256",
                "max_timestamp_diff": 300,
                "key_rotation_interval": 86400,
                "enable_encryption": False,
                "min_password_length": 6
            },
            "logging": {
                "level": "INFO",
                "max_file_size": 10485760,
                "backup_count": 5
            },
            "game": {
                "max_rooms": 50,
                "max_players_per_room": 4,
                "room_idle_timeout": 1800,
                "turn_timeout": 300
            }
        }
        
        with open(self.security_config_file, 'w', encoding='utf-8') as f:
            json.dump(self.security_config, f, indent=2, ensure_ascii=False)
        
        self.logger.info("创建默认安全配置文件")
    
    def _create_default_server_config(self):
        """创建默认服务器配置"""
        self.server_config['server'] = {
            'host': 'localhost',
            'port': '8888',
            'max_connections': '100',
            'heartbeat_interval': '30'
        }
        
        self.server_config['logging'] = {
            'level': 'INFO',
            'log_to_file': 'true',
            'log_to_console': 'false',
            'log_type': 'server'
        }
        
        self.server_config['security'] = {
            'encryption_enabled': 'true',
            'key_rotation_interval': '3600',
            'max_failed_attempts': '5'
        }
        
        with open(self.server_config_file, 'w', encoding='utf-8') as f:
            self.server_config.write(f)
        
        self.logger.info("创建默认服务器配置文件")
    
    def get_game_config(self, section: str, key: str, default: Any = None) -> Any:
        """获取游戏配置"""
        try:
            return self.game_config.get(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError):
            return default
    
    def get_security_config(self, key_path: str, default: Any = None) -> Any:
        """获取安全配置"""
        keys = key_path.split('.')
        value = self.security_config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_server_config(self, section: str, key: str, default: Any = None) -> Any:
        """获取服务器配置"""
        try:
            return self.server_config.get(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError):
            return default
    
    def set_game_config(self, section: str, key: str, value: str):
        """设置游戏配置"""
        if not self.game_config.has_section(section):
            self.game_config.add_section(section)
        
        self.game_config.set(section, key, value)
        
        with open(self.game_config_file, 'w', encoding='utf-8') as f:
            self.game_config.write(f)
        
        self.logger.info(f"游戏配置已更新: {section}.{key} = {value}")
    
    def set_security_config(self, key_path: str, value: Any):
        """设置安全配置"""
        keys = key_path.split('.')
        config = self.security_config
        
        # 导航到最后一级
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # 设置值
        config[keys[-1]] = value
        
        with open(self.security_config_file, 'w', encoding='utf-8') as f:
            json.dump(self.security_config, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"安全配置已更新: {key_path} = {value}")
    
    def get_all_configs(self) -> Dict[str, Any]:
        """获取所有配置的摘要"""
        return {
            "game": dict(self.game_config._sections) if self.game_config._sections else {},
            "security": self.security_config,
            "server": dict(self.server_config._sections) if self.server_config._sections else {}
        }
