#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地数据存储模块
Local Data Storage Module
"""

import os
import json
import gzip
import pickle
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

class LocalStorage:
    """本地数据存储管理器"""
    
    def __init__(self):
        """初始化本地存储"""
        self.data_dir = Path("data/offline_data")
        self.cache_dir = Path("data/cache")
        self.saves_dir = Path("saves")
        
        # 创建必要的目录
        for directory in [self.data_dir, self.cache_dir, self.saves_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        # 数据文件路径
        self.poverty_data_file = self.data_dir / "poverty_data.json"
        self.terrain_data_file = self.data_dir / "terrain_data.json"
        self.climate_data_file = self.data_dir / "climate_data.json"
        self.resource_data_file = self.data_dir / "resource_data.json"
        self.disaster_data_file = self.data_dir / "disaster_data.json"
        self.cities_data_file = self.data_dir / "cities_data.json"
        self.policies_data_file = self.data_dir / "policies.json"
        self.victory_conditions_file = self.data_dir / "victory_conditions.json"
        self.game_settings_file = self.data_dir / "game_settings.json"
        
        # 元数据文件
        self.metadata_file = self.data_dir / "metadata.json"
    
    def check_data_exists(self) -> bool:
        """检查本地数据是否存在"""
        required_files = [
            self.poverty_data_file,
            self.terrain_data_file,
            self.climate_data_file,
            self.resource_data_file,
            self.disaster_data_file,
            self.cities_data_file,
            self.policies_data_file,
            self.victory_conditions_file,
            self.game_settings_file
        ]
        
        return all(file.exists() for file in required_files)

    def initialize_base_server_data(self):
        """初始化基础服务器数据（不包含世界数据）"""
        print("正在初始化基础服务器数据... / Initializing base server data...")

        # 创建基础的元数据文件
        metadata = {
            'data_source': 'base_server',
            'created_at': datetime.now().isoformat(),
            'version': '1.0',
            'description': '基础服务器数据，世界数据将在房间创建时初始化 / Base server data, world data will be initialized when rooms are created'
        }
        self.save_json_data(self.metadata_file, metadata)

        # 创建空的数据文件以满足 check_data_exists 的要求
        empty_data = {}
        data_files = [
            self.poverty_data_file,
            self.terrain_data_file,
            self.climate_data_file,
            self.resource_data_file,
            self.disaster_data_file,
            self.cities_data_file,
            self.policies_data_file,
            self.victory_conditions_file,
            self.game_settings_file
        ]

        for file in data_files:
            self.save_json_data(file, empty_data)

        print("✅ 基础服务器数据初始化完成 / Base server data initialization complete")

    def initialize_real_world_data(self):
        """初始化混合数据（真实贫困城市 + AI生成环境）"""
        print("正在初始化混合数据模式...")
        print("Initializing hybrid data mode (real poverty cities + AI environment)...")

        # 导入数据获取器
        from data.data_fetcher import DataFetcher

        fetcher = DataFetcher()

        try:
            # 获取真实贫困城市数据
            print("正在获取真实贫困城市数据... / Fetching real poverty city data...")
            poverty_data = fetcher.fetch_poverty_cities()
            self.save_json_data(self.poverty_data_file, poverty_data)

            # 使用AI生成地形数据
            print("正在生成地形数据... / Generating terrain data...")
            terrain_data = fetcher.fetch_terrain_data()  # 现在内部使用AI生成
            self.save_json_data(self.terrain_data_file, terrain_data)

            # 使用AI生成气候数据
            print("正在生成气候数据... / Generating climate data...")
            climate_data = fetcher.fetch_climate_data()  # 现在内部使用AI生成
            self.save_json_data(self.climate_data_file, climate_data)

            # 生成资源数据
            print("正在生成资源数据... / Generating resource data...")
            resource_data = fetcher.fetch_resource_data()
            self.save_json_data(self.resource_data_file, resource_data)

            # 生成灾害数据
            print("正在生成灾害数据... / Generating disaster data...")
            disaster_data = fetcher.fetch_disaster_data()
            self.save_json_data(self.disaster_data_file, disaster_data)

            # 生成城市数据
            print("正在生成城市数据... / Generating cities data...")
            cities_data = fetcher.fetch_cities_data()
            self.save_json_data(self.cities_data_file, cities_data)

            # 初始化经济和政策系统
            print("正在初始化经济系统... / Initializing economic system...")
            self._initialize_economic_data()

            print("正在初始化政策系统... / Initializing policy system...")
            self._initialize_policy_data()

            # 保存元数据
            metadata = {
                'data_source': 'hybrid',
                'created_at': datetime.now().isoformat(),
                'version': '1.0',
                'description': '混合数据：真实贫困城市 + AI生成环境 / Hybrid data: real poverty cities + AI environment'
            }
            self.save_json_data(self.metadata_file, metadata)

            print("混合数据初始化完成！")
            print("Hybrid data initialization completed!")

        except Exception as e:
            print(f"混合数据初始化失败 / Failed to initialize hybrid data: {e}")
            print("正在切换到完全AI生成数据模式...")
            print("Switching to full AI generated data mode...")
            self.initialize_ai_generated_data()
    
    def initialize_ai_generated_data(self):
        """初始化AI生成的数据"""
        print("正在生成AI数据...")
        print("Generating AI data...")
        
        # 导入AI生成器
        from world.ai_generator import AIDataGenerator
        
        generator = AIDataGenerator()
        
        try:
            # 生成贫困城市数据
            print("正在生成贫困城市数据... / Generating poverty city data...")
            poverty_data = generator.generate_poverty_cities()
            self.save_json_data(self.poverty_data_file, poverty_data)
            
            # 生成地形数据
            print("正在生成地形数据... / Generating terrain data...")
            terrain_data = generator.generate_terrain_data()
            self.save_json_data(self.terrain_data_file, terrain_data)
            
            # 生成气候数据
            print("正在生成气候数据... / Generating climate data...")
            climate_data = generator.generate_climate_data()
            self.save_json_data(self.climate_data_file, climate_data)
            
            # 生成资源数据
            print("正在生成资源数据... / Generating resource data...")
            resource_data = generator.generate_resource_data()
            self.save_json_data(self.resource_data_file, resource_data)
            
            # 生成灾害数据
            print("正在生成灾害数据... / Generating disaster data...")
            disaster_data = generator.generate_disaster_data()
            self.save_json_data(self.disaster_data_file, disaster_data)
            
            # 生成城市数据
            print("正在生成城市数据... / Generating cities data...")
            cities_data = generator.generate_cities_data()
            self.save_json_data(self.cities_data_file, cities_data)
            
            # 初始化经济和政策系统
            print("正在初始化经济系统... / Initializing economic system...")
            self._initialize_economic_data()

            print("正在初始化政策系统... / Initializing policy system...")
            self._initialize_policy_data()

            # 保存元数据
            metadata = {
                'data_source': 'ai_generated',
                'created_at': datetime.now().isoformat(),
                'version': '1.0',
                'description': 'AI生成数据 / AI generated data',
                'seed': generator.get_seed()
            }
            self.save_json_data(self.metadata_file, metadata)

            print("AI数据生成完成！")
            print("AI data generation completed!")
            
        except Exception as e:
            print(f"AI数据生成失败 / AI data generation failed: {e}")
            # 创建最基础的备用数据
            self.create_fallback_data()
    
    def create_fallback_data(self):
        """创建最基础的备用数据"""
        print("正在创建备用数据... / Creating fallback data...")
        
        # 基础贫困城市数据
        poverty_data = {
            "cities": [
                {"name": "贫困城市A", "country": "国家A", "poverty_index": 0.8, "population": 500000},
                {"name": "贫困城市B", "country": "国家B", "poverty_index": 0.7, "population": 300000},
                {"name": "贫困城市C", "country": "国家C", "poverty_index": 0.9, "population": 200000}
            ]
        }
        
        # 基础地形数据
        terrain_data = {
            "terrain_types": ["mountain", "plain", "desert", "forest", "coastal"],
            "terrain_effects": {
                "mountain": {"defense_bonus": 0.3, "movement_penalty": 0.2},
                "plain": {"defense_bonus": 0.0, "movement_penalty": 0.0},
                "desert": {"defense_bonus": 0.1, "movement_penalty": 0.3},
                "forest": {"defense_bonus": 0.2, "movement_penalty": 0.1},
                "coastal": {"defense_bonus": 0.1, "movement_penalty": 0.0}
            }
        }
        
        # 保存备用数据
        self.save_json_data(self.poverty_data_file, poverty_data)
        self.save_json_data(self.terrain_data_file, terrain_data)
        
        # 创建其他空数据文件
        for file in [self.climate_data_file, self.resource_data_file, 
                    self.disaster_data_file, self.cities_data_file]:
            self.save_json_data(file, {})
        
        print("备用数据创建完成！")
        print("Fallback data creation completed!")
    
    def save_json_data(self, file_path: Path, data: Dict[str, Any]):
        """保存JSON数据到文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except IOError as e:
            print(f"保存数据失败 / Failed to save data to {file_path}: {e}")
    
    def load_json_data(self, file_path: Path) -> Dict[str, Any]:
        """从文件加载JSON数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (IOError, json.JSONDecodeError) as e:
            print(f"加载数据失败 / Failed to load data from {file_path}: {e}")
            return {}
    
    def get_poverty_cities(self) -> List[Dict[str, Any]]:
        """获取贫困城市数据"""
        data = self.load_json_data(self.poverty_data_file)
        return data.get('cities', [])
    
    def get_terrain_data(self) -> Dict[str, Any]:
        """获取地形数据"""
        return self.load_json_data(self.terrain_data_file)
    
    def get_climate_data(self) -> Dict[str, Any]:
        """获取气候数据"""
        return self.load_json_data(self.climate_data_file)
    
    def get_resource_data(self) -> Dict[str, Any]:
        """获取资源数据"""
        return self.load_json_data(self.resource_data_file)
    
    def get_disaster_data(self) -> Dict[str, Any]:
        """获取灾害数据"""
        return self.load_json_data(self.disaster_data_file)
    
    def get_cities_data(self) -> Dict[str, Any]:
        """获取城市数据"""
        return self.load_json_data(self.cities_data_file)
    
    def get_metadata(self) -> Dict[str, Any]:
        """获取元数据"""
        return self.load_json_data(self.metadata_file)

    def get_policies_data(self) -> Optional[Dict[str, Any]]:
        """获取政策数据"""
        return self.load_json_data(self.policies_data_file)

    def get_victory_conditions_data(self) -> Optional[Dict[str, Any]]:
        """获取胜利条件数据"""
        return self.load_json_data(self.victory_conditions_file)

    def get_game_settings_data(self) -> Optional[Dict[str, Any]]:
        """获取游戏设置数据"""
        return self.load_json_data(self.game_settings_file)

    def _initialize_economic_data(self):
        """初始化经济系统数据"""
        try:
            from game_logic.economic_system import EconomicSystem

            economic_system = EconomicSystem()

            # 从贫困城市数据创建经济领土
            poverty_data = self.load_json_data(self.poverty_data_file)
            if poverty_data and 'cities' in poverty_data:
                for city in poverty_data['cities']:
                    economic_system.add_territory(
                        territory_id=city.get('id', f"territory_{len(economic_system.territories)}"),
                        territory_name=city.get('name', 'Unknown City'),
                        population=city.get('population', 100000),
                        initial_poverty_index=city.get('poverty_index', 0.5)
                    )

            # 保存经济系统配置
            economic_config = {
                'initialized': True,
                'territory_count': len(economic_system.territories),
                'creation_time': datetime.now().isoformat()
            }

            economic_data_file = self.data_dir / "economic_data.json"
            self.save_json_data(economic_data_file, economic_config)

            print(f"经济系统初始化完成，创建了 {len(economic_system.territories)} 个经济领土")

        except Exception as e:
            print(f"经济系统初始化失败: {e}")

    def _initialize_policy_data(self):
        """初始化政策系统数据"""
        try:
            from game_logic.policy_system import PolicyManager

            policy_manager = PolicyManager()

            # 获取所有政策信息
            policy_data = {}
            for policy_id, policy in policy_manager.policies.items():
                policy_data[policy_id] = policy_manager.get_policy_info(policy_id)

            # 保存政策系统配置
            policy_config = {
                'initialized': True,
                'total_policies': len(policy_data),
                'policies': policy_data,
                'creation_time': datetime.now().isoformat()
            }

            policy_data_file = self.data_dir / "policy_data.json"
            self.save_json_data(policy_data_file, policy_config)

            print(f"政策系统初始化完成，加载了 {len(policy_data)} 个政策")

        except Exception as e:
            print(f"政策系统初始化失败: {e}")

    def get_economic_data(self) -> Dict[str, Any]:
        """获取经济数据"""
        economic_data_file = self.data_dir / "economic_data.json"
        return self.load_json_data(economic_data_file)

    def get_policy_data(self) -> Dict[str, Any]:
        """获取政策数据"""
        policy_data_file = self.data_dir / "policy_data.json"
        return self.load_json_data(policy_data_file)
