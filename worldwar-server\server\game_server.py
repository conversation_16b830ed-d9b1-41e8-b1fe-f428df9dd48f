#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏服务器主程序 - 重构版
Game Server Main Program - Refactored Version

采用模块化架构，将各功能拆分到独立模块中
Uses modular architecture with functionality split into independent modules
"""

import socket
import threading
import time
import sys
from pathlib import Path
from typing import Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 核心模块导入
from server.room_manager import RoomManager
from server.player_session import PlayerSession, SessionManager
from server.network_handler import NetworkHandler
from server.connection_manager import ConnectionManager
from server.message_processor import MessageProcessor
from server.game_manager import GameManager
from server.server_monitor import ServerMonitor

# 共享模块导入
from shared.secure_protocol import SecureProtocol
from shared.message_manager import ServerMessageManager
from shared.enhanced_logger import get_server_logger

# 系统模块导入
from core.config_manager import ConfigManager
from data.local_storage import LocalStorage

class SecureGameServer:
    """安全世界大战游戏服务器 - 重构版"""

    def __init__(self, host: str = "localhost", port: int = 8888):
        """初始化安全游戏服务器"""
        self.host = host
        self.port = port
        self.running = False

        # 网络相关
        self.server_socket = None
        self.client_threads = []

        # 系统组件
        self.logger = get_server_logger("SecureGameServer")
        self.config = ConfigManager()
        self.storage = LocalStorage()

        # 核心协议和管理器
        self.protocol = SecureProtocol(server_mode=True)
        self.session_manager = SessionManager()
        self.network_handler = NetworkHandler()
        self.room_manager = RoomManager(self.protocol)
        self.message_manager = ServerMessageManager(self.protocol)

        # 功能模块
        self.connection_manager = ConnectionManager(
            self.protocol, self.session_manager, self.network_handler
        )
        self.game_manager = GameManager(self.room_manager)
        self.message_processor = MessageProcessor(
            self.protocol, self.room_manager, self.session_manager, self.message_manager
        )
        self.server_monitor = ServerMonitor(self.room_manager)

        # 设置模块间的回调关系
        self._setup_module_callbacks()

        self.logger.info("安全游戏服务器初始化完成 / Secure game server initialized")

    def _setup_module_callbacks(self):
        """设置模块间的回调关系"""
        # 连接管理器回调
        self.connection_manager.set_message_processor(self.message_processor.process_message)
        self.connection_manager.set_cleanup_callback(self._handle_client_cleanup)

        # 消息处理器回调
        self.message_processor.set_game_manager(self.game_manager)

        # 服务器监控回调
        self.server_monitor.set_connection_stats_callback(self.connection_manager.get_connection_stats)
        self.server_monitor.set_game_stats_callback(self.game_manager.get_active_games_info)
    
    def start_server(self):
        """启动游戏服务器"""
        try:
            self.logger.info("正在启动游戏服务器...")
            print(f"🚀 正在启动游戏服务器... / Starting game server...")

            # 检查数据完整性
            if not self.storage.check_data_exists():
                self.logger.info("检测到首次运行，正在初始化数据...")
                print(f"📦 初始化游戏数据... / Initializing game data...")
                self._initialize_server_data()
                print(f"✅ 数据初始化完成 / Data initialization complete")

            # 创建并配置服务器套接字
            self._setup_server_socket()

            self.running = True

            print(f"=" * 60)
            print(f"🎮 世界大战游戏服务器已启动 / World War Game Server Started")
            print(f"📡 服务器地址 / Server Address: {self.host}:{self.port}")
            print(f"⏳ 等待客户端连接... / Waiting for client connections...")
            print(f"🛑 按 Ctrl+C 停止服务器 / Press Ctrl+C to stop server")
            print(f"=" * 60)

            self.logger.info(f"服务器启动在 {self.host}:{self.port}")

            # 启动服务器监控
            self.server_monitor.start_monitoring()

            # 启动房间管理线程
            self._start_room_management_thread()

            # 主循环：接受客户端连接
            self._accept_connections()

        except KeyboardInterrupt:
            print(f"\n🛑 收到中断信号，正在关闭服务器... / Received interrupt signal, shutting down server...")
            self.shutdown_server()
        except Exception as e:
            self.logger.error(f"服务器启动失败: {e}")
            print(f"❌ 服务器启动失败 / Server startup failed: {e}")
            import traceback
            traceback.print_exc()
            self.shutdown_server()

    def _setup_server_socket(self):
        """设置服务器套接字"""
        # 创建服务器套接字
        self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

        # 设置套接字选项
        self.network_handler.set_socket_options(self.server_socket)

        # 绑定地址和端口
        try:
            self.server_socket.bind((self.host, self.port))
            print(f"✅ 成功绑定到 {self.host}:{self.port} / Successfully bound to {self.host}:{self.port}")
        except OSError as e:
            if e.errno == 10048 or "Address already in use" in str(e):  # Port already in use
                print(f"❌ 端口 {self.port} 已被占用 / Port {self.port} is already in use")
                print(f"💡 请尝试其他端口或关闭占用该端口的程序 / Please try another port or close the program using this port")
            elif e.errno == 10049 or "Cannot assign requested address" in str(e):
                print(f"❌ 无法绑定到地址 {self.host} / Cannot bind to address {self.host}")
                print(f"💡 请检查网络配置 / Please check network configuration")
            else:
                print(f"❌ 绑定失败: {e} / Bind failed: {e}")
            raise

        self.server_socket.listen(10)  # 最多10个等待连接

    def _initialize_server_data(self):
        """初始化服务器数据"""
        print("正在初始化服务器数据... / Initializing server data...")

        # 初始化基础服务器数据（不包含世界数据）
        # 世界数据将在创建房间时根据用户选择的世界类型来初始化
        self.storage.initialize_base_server_data()
        print("✅ 基础服务器数据初始化完成 / Base server data initialization complete")

    def _start_room_management_thread(self):
        """启动房间管理线程"""
        room_thread = threading.Thread(target=self._room_management_loop, daemon=True)
        room_thread.start()
        self.logger.info("房间管理线程已启动 / Room management thread started")
    
    def _accept_connections(self):
        """接受客户端连接的主循环"""
        while self.running:
            try:
                # 设置超时以便能够响应shutdown信号
                self.server_socket.settimeout(1.0)

                try:
                    client_socket, client_address = self.server_socket.accept()
                except socket.timeout:
                    # 超时是正常的，继续循环
                    continue

                self.logger.info(f"新客户端连接: {client_address}")
                print(f"✅ 新客户端连接 / New client connected: {client_address}")

                # 设置客户端套接字选项
                try:
                    self.network_handler.set_socket_options(client_socket)
                except Exception as e:
                    self.logger.warning(f"设置客户端套接字选项失败: {e}")

                # 使用连接管理器处理客户端连接
                client_thread = threading.Thread(
                    target=self.connection_manager.handle_client_connection,
                    args=(client_socket, client_address),
                    daemon=True,
                    name=f"ClientHandler-{client_address[0]}:{client_address[1]}"
                )
                client_thread.start()
                self.client_threads.append(client_thread)

                # 清理已结束的线程
                self.client_threads = [t for t in self.client_threads if t.is_alive()]

            except socket.error as e:
                if self.running:  # 只有在服务器运行时才记录错误
                    self.logger.error(f"接受连接时发生错误: {e}")
                    print(f"❌ 接受连接错误 / Connection accept error: {e}")
                break
            except Exception as e:
                if self.running:
                    self.logger.error(f"接受连接时发生未知错误: {e}")
                    print(f"❌ 未知错误 / Unknown error: {e}")
                break
    def _handle_client_cleanup(self, session: Optional[PlayerSession], session_id: str):
        """处理客户端清理回调"""
        try:
            if session and session.current_room:
                # 从房间中移除玩家
                self.room_manager.leave_room(session.current_room, session)

            self.logger.info(f"客户端会话已清理: {session_id}")

        except Exception as e:
            self.logger.error(f"清理客户端回调时发生错误: {e}")


    def _room_management_loop(self):
        """房间管理循环"""
        while self.running:
            try:
                self.room_manager.update_rooms()
                # 清理已结束的游戏
                self.game_manager.cleanup_finished_games()
                time.sleep(1)  # 每秒更新一次
            except Exception as e:
                self.logger.error(f"房间管理循环错误: {e}")

    def shutdown_server(self):
        """关闭服务器"""
        self.logger.info("正在关闭服务器...")
        print("正在关闭服务器... / Shutting down server...")

        self.running = False

        # 停止服务器监控
        self.server_monitor.stop_monitoring()

        # 关闭所有活跃的游戏
        for room_id in list(self.game_manager.active_games.keys()):
            self.game_manager.end_game(room_id, "服务器关闭")

        # 关闭所有客户端连接
        for session_id in list(self.session_manager.sessions.keys()):
            session = self.session_manager.get_session(session_id)
            if session and session.socket:
                try:
                    session.socket.close()
                except:
                    pass

        # 关闭服务器套接字
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass

        self.logger.info("服务器已关闭 / Server shutdown complete")

# 向后兼容性适配器
class GameServer(SecureGameServer):
    """
    向后兼容的游戏服务器类
    保持与旧代码的兼容性
    """

    def __init__(self, host: str = "localhost", port: int = 8888):
        """初始化兼容性游戏服务器"""
        super().__init__(host, port)
        self.logger.info("使用兼容性游戏服务器 / Using compatibility game server")

def main():
    """服务器主程序入口"""
    server = SecureGameServer()

    try:
        server.start_server()
    except KeyboardInterrupt:
        print("\n收到中断信号，正在关闭服务器...")
        server.shutdown_server()
    except Exception as e:
        print(f"服务器发生错误: {e}")
        server.shutdown_server()

if __name__ == "__main__":
    main()
