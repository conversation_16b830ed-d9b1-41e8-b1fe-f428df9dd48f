2025-07-06 21:43:45 - server_WelcomeManager - INFO - [19592] - 欢迎语管理器初始化完成
2025-07-06 21:43:45 - server_DebugManager - INFO - [19592] - 调试管理器初始化完成
2025-07-06 21:43:45 - server_DebugManager - INFO - [19592] - 日志级别设置为: INFO
2025-07-06 21:47:32 - server_WelcomeManager - INFO - [2496] - 欢迎语管理器初始化完成
2025-07-06 21:47:32 - server_DebugManager - INFO - [2496] - 调试管理器初始化完成
2025-07-06 21:47:32 - server_DebugManager - INFO - [2496] - 日志级别设置为: INFO
2025-07-06 21:49:23 - server_WelcomeManager - INFO - [9956] - 欢迎语管理器初始化完成
2025-07-06 21:49:23 - server_DebugManager - INFO - [9956] - 调试管理器初始化完成
2025-07-06 21:49:23 - server_DebugManager - INFO - [9956] - 日志级别设置为: INFO
2025-07-06 22:03:31 - server_WelcomeManager - INFO - [13112] - 欢迎语管理器初始化完成
2025-07-06 22:03:31 - server_DebugManager - INFO - [13112] - 调试管理器初始化完成
2025-07-06 22:03:31 - server_DebugManager - INFO - [13112] - 日志级别设置为: INFO
2025-07-06 22:05:03 - server_WelcomeManager - INFO - [1096] - 欢迎语管理器初始化完成
2025-07-06 22:05:03 - server_DebugManager - INFO - [1096] - 调试管理器初始化完成
2025-07-06 22:05:03 - server_DebugManager - INFO - [1096] - 日志级别设置为: INFO
2025-07-06 22:06:08 - server_WelcomeManager - INFO - [25512] - 欢迎语管理器初始化完成
2025-07-06 22:06:08 - server_DebugManager - INFO - [25512] - 调试管理器初始化完成
2025-07-06 22:06:08 - server_DebugManager - INFO - [25512] - 日志级别设置为: INFO
2025-07-06 22:07:10 - server_WelcomeManager - INFO - [28536] - 欢迎语管理器初始化完成
2025-07-06 22:07:10 - server_DebugManager - INFO - [28536] - 调试管理器初始化完成
2025-07-06 22:07:10 - server_DebugManager - INFO - [28536] - 日志级别设置为: INFO
2025-07-06 22:08:08 - server_WelcomeManager - INFO - [14384] - 欢迎语管理器初始化完成
2025-07-06 22:08:08 - server_DebugManager - INFO - [14384] - 调试管理器初始化完成
2025-07-06 22:08:08 - server_DebugManager - INFO - [14384] - 日志级别设置为: INFO
2025-07-06 22:09:56 - server_WelcomeManager - INFO - [2496] - 欢迎语管理器初始化完成
2025-07-06 22:09:56 - server_DebugManager - INFO - [2496] - 调试管理器初始化完成
2025-07-06 22:09:56 - server_DebugManager - INFO - [2496] - 调试模式已启用，模块: ['user_actions', 'world_gen', 'performance', 'game_logic', 'database', 'security', 'ai', 'network']
2025-07-06 22:09:56 - server_DebugManager - INFO - [2496] - 详细模式已启用
2025-07-06 22:09:56 - server_DebugManager - INFO - [2496] - 日志级别设置为: INFO
2025-07-06 22:13:08 - server_WelcomeManager - INFO - [25512] - 欢迎语管理器初始化完成
2025-07-06 22:13:08 - server_DebugManager - INFO - [25512] - 调试管理器初始化完成
2025-07-06 22:13:08 - server_DebugManager - INFO - [25512] - 日志级别设置为: INFO
2025-07-06 22:32:36 - server_WelcomeManager - INFO - [6992] - 欢迎语管理器初始化完成
2025-07-06 22:32:36 - server_DebugManager - INFO - [6992] - 调试管理器初始化完成
2025-07-06 22:32:36 - server_DebugManager - INFO - [6992] - 调试模式已启用，模块: ['world_gen', 'security', 'network', 'performance', 'game_logic', 'ai', 'user_actions', 'database']
2025-07-06 22:32:36 - server_DebugManager - INFO - [6992] - 详细模式已启用
2025-07-06 22:32:36 - server_DebugManager - INFO - [6992] - 日志级别设置为: INFO
2025-07-06 22:33:34 - server_WelcomeManager - INFO - [26004] - 欢迎语管理器初始化完成
2025-07-06 22:33:34 - server_DebugManager - INFO - [26004] - 调试管理器初始化完成
2025-07-06 22:33:34 - server_DebugManager - INFO - [26004] - 调试模式已启用，模块: ['game_logic', 'ai', 'network', 'performance', 'user_actions', 'world_gen', 'security', 'database']
2025-07-06 22:33:34 - server_DebugManager - INFO - [26004] - 详细模式已启用
2025-07-06 22:33:34 - server_DebugManager - INFO - [26004] - 日志级别设置为: INFO
2025-07-06 22:34:28 - server_WelcomeManager - INFO - [28824] - 欢迎语管理器初始化完成
2025-07-06 22:34:28 - server_DebugManager - INFO - [28824] - 调试管理器初始化完成
2025-07-06 22:34:28 - server_DebugManager - INFO - [28824] - 调试模式已启用，模块: ['game_logic', 'network', 'security', 'database', 'world_gen', 'user_actions', 'ai', 'performance']
2025-07-06 22:34:28 - server_DebugManager - INFO - [28824] - 详细模式已启用
2025-07-06 22:34:28 - server_DebugManager - INFO - [28824] - 日志级别设置为: INFO
2025-07-06 22:38:46 - server_WelcomeManager - INFO - [9624] - 欢迎语管理器初始化完成
2025-07-06 22:38:46 - server_DebugManager - INFO - [9624] - 调试管理器初始化完成
2025-07-06 22:38:46 - server_DebugManager - INFO - [9624] - 调试模式已启用，模块: ['security', 'game_logic', 'world_gen', 'user_actions', 'performance', 'network', 'database', 'ai']
2025-07-06 22:38:46 - server_DebugManager - INFO - [9624] - 详细模式已启用
2025-07-06 22:38:46 - server_DebugManager - INFO - [9624] - 日志级别设置为: INFO
2025-07-06 22:41:23 - server_WelcomeManager - INFO - [13060] - 欢迎语管理器初始化完成
2025-07-06 22:41:23 - server_DebugManager - INFO - [13060] - 调试管理器初始化完成
2025-07-06 22:41:23 - server_DebugManager - INFO - [13060] - 调试模式已启用，模块: ['world_gen', 'performance', 'security', 'network', 'user_actions', 'game_logic', 'ai', 'database']
2025-07-06 22:41:23 - server_DebugManager - INFO - [13060] - 详细模式已启用
2025-07-06 22:41:23 - server_DebugManager - INFO - [13060] - 日志级别设置为: INFO
2025-07-06 22:42:22 - server_WelcomeManager - INFO - [17864] - 欢迎语管理器初始化完成
2025-07-06 22:42:22 - server_DebugManager - INFO - [17864] - 调试管理器初始化完成
2025-07-06 22:42:22 - server_DebugManager - INFO - [17864] - 调试模式已启用，模块: ['security', 'performance', 'database', 'ai', 'world_gen', 'user_actions', 'network', 'game_logic']
2025-07-06 22:42:22 - server_DebugManager - INFO - [17864] - 详细模式已启用
2025-07-06 22:42:22 - server_DebugManager - INFO - [17864] - 日志级别设置为: INFO
2025-07-07 14:51:10 - server_WelcomeManager - INFO - [31396] - 欢迎语管理器初始化完成
2025-07-07 14:51:10 - server_DebugManager - INFO - [31396] - 调试管理器初始化完成
2025-07-07 14:51:10 - server_DebugManager - INFO - [31396] - 日志级别设置为: INFO
2025-07-07 15:03:34 - server_WelcomeManager - INFO - [29912] - 欢迎语管理器初始化完成
2025-07-07 15:03:34 - server_DebugManager - INFO - [29912] - 调试管理器初始化完成
2025-07-07 15:03:34 - server_DebugManager - INFO - [29912] - 日志级别设置为: INFO
2025-07-07 15:09:53 - server_WelcomeManager - INFO - [38392] - 欢迎语管理器初始化完成
2025-07-07 15:09:53 - server_DebugManager - INFO - [38392] - 调试管理器初始化完成
2025-07-07 15:09:53 - server_DebugManager - INFO - [38392] - 日志级别设置为: INFO
2025-07-07 15:14:52 - server_WelcomeManager - INFO - [25060] - 欢迎语管理器初始化完成
2025-07-07 15:14:52 - server_DebugManager - INFO - [25060] - 调试管理器初始化完成
2025-07-07 15:14:52 - server_DebugManager - INFO - [25060] - 日志级别设置为: INFO
2025-07-07 15:20:07 - server_WelcomeManager - INFO - [40708] - 欢迎语管理器初始化完成
2025-07-07 15:20:07 - server_DebugManager - INFO - [40708] - 调试管理器初始化完成
2025-07-07 15:20:07 - server_DebugManager - INFO - [40708] - 日志级别设置为: INFO
2025-07-07 15:37:35 - server_WelcomeManager - INFO - [7404] - 欢迎语管理器初始化完成
2025-07-07 15:37:35 - server_DebugManager - INFO - [7404] - 调试管理器初始化完成
2025-07-07 15:37:35 - server_RoomIdGenerator - INFO - [7404] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 15:37:35 - server_SecurityManager - INFO - [7404] - 已加载现有服务器密钥
2025-07-07 15:37:35 - server_SecurityManager - INFO - [7404] - 安全管理器初始化完成 - 服务器模式
2025-07-07 15:37:35 - server_SecureProtocol - INFO - [7404] - 安全协议初始化完成 - 服务器模式
2025-07-07 15:37:35 - server_MessageManager - INFO - [7404] - 消息管理器初始化完成 - 服务器模式
2025-07-07 15:37:35 - server_RoomManager - INFO - [7404] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 15:38:21 - server_WelcomeManager - INFO - [7436] - 欢迎语管理器初始化完成
2025-07-07 15:38:21 - server_DebugManager - INFO - [7436] - 调试管理器初始化完成
2025-07-07 15:38:21 - server_RoomIdGenerator - INFO - [7436] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 15:38:21 - server_SecurityManager - INFO - [7436] - 已加载现有服务器密钥
2025-07-07 15:38:21 - server_SecurityManager - INFO - [7436] - 安全管理器初始化完成 - 服务器模式
2025-07-07 15:38:21 - server_SecureProtocol - INFO - [7436] - 安全协议初始化完成 - 服务器模式
2025-07-07 15:38:21 - server_MessageManager - INFO - [7436] - 消息管理器初始化完成 - 服务器模式
2025-07-07 15:38:21 - server_RoomManager - INFO - [7436] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 15:39:29 - server_WelcomeManager - INFO - [35688] - 欢迎语管理器初始化完成
2025-07-07 15:39:29 - server_DebugManager - INFO - [35688] - 调试管理器初始化完成
2025-07-07 15:39:29 - server_RoomIdGenerator - INFO - [35688] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 15:39:29 - server_SecurityManager - INFO - [35688] - 已加载现有服务器密钥
2025-07-07 15:39:29 - server_SecurityManager - INFO - [35688] - 安全管理器初始化完成 - 服务器模式
2025-07-07 15:39:29 - server_SecureProtocol - INFO - [35688] - 安全协议初始化完成 - 服务器模式
2025-07-07 15:39:29 - server_MessageManager - INFO - [35688] - 消息管理器初始化完成 - 服务器模式
2025-07-07 15:39:29 - server_RoomManager - INFO - [35688] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 15:39:29 - server_SecurityManager - INFO - [35688] - 已加载现有服务器密钥
2025-07-07 15:39:29 - server_SecurityManager - INFO - [35688] - 安全管理器初始化完成 - 服务器模式
2025-07-07 15:39:29 - server_SecureProtocol - INFO - [35688] - 安全协议初始化完成 - 服务器模式
2025-07-07 15:39:29 - server_SecurityManager - INFO - [35688] - 已加载现有服务器密钥
2025-07-07 15:39:29 - server_SecurityManager - INFO - [35688] - 安全管理器初始化完成 - 服务器模式
2025-07-07 15:39:29 - server_SecureProtocol - INFO - [35688] - 安全协议初始化完成 - 服务器模式
2025-07-07 15:39:29 - server_MessageManager - INFO - [35688] - 消息管理器初始化完成 - 服务器模式
2025-07-07 15:39:29 - server_MessageProcessor - INFO - [35688] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 15:39:29 - server_DebugManager - INFO - [35688] - 调试模式已启用，模块: ['user_actions', 'network', 'database', 'security', 'ai', 'world_gen', 'game_logic', 'performance']
2025-07-07 15:39:29 - server_DebugManager - INFO - [35688] - 详细模式已启用
2025-07-07 15:39:29 - server_DebugManager - INFO - [35688] - 日志级别设置为: INFO
2025-07-07 15:41:35 - server_WelcomeManager - INFO - [38776] - 欢迎语管理器初始化完成
2025-07-07 15:41:35 - server_DebugManager - INFO - [38776] - 调试管理器初始化完成
2025-07-07 15:41:35 - server_RoomIdGenerator - INFO - [38776] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 15:41:35 - server_SecurityManager - INFO - [38776] - 已加载现有服务器密钥
2025-07-07 15:41:35 - server_SecurityManager - INFO - [38776] - 安全管理器初始化完成 - 服务器模式
2025-07-07 15:41:35 - server_SecureProtocol - INFO - [38776] - 安全协议初始化完成 - 服务器模式
2025-07-07 15:41:35 - server_MessageManager - INFO - [38776] - 消息管理器初始化完成 - 服务器模式
2025-07-07 15:41:35 - server_RoomManager - INFO - [38776] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 15:41:35 - server_SecurityManager - INFO - [38776] - 已加载现有服务器密钥
2025-07-07 15:41:35 - server_SecurityManager - INFO - [38776] - 安全管理器初始化完成 - 服务器模式
2025-07-07 15:41:35 - server_SecureProtocol - INFO - [38776] - 安全协议初始化完成 - 服务器模式
2025-07-07 15:41:35 - server_SecurityManager - INFO - [38776] - 已加载现有服务器密钥
2025-07-07 15:41:35 - server_SecurityManager - INFO - [38776] - 安全管理器初始化完成 - 服务器模式
2025-07-07 15:41:35 - server_SecureProtocol - INFO - [38776] - 安全协议初始化完成 - 服务器模式
2025-07-07 15:41:35 - server_MessageManager - INFO - [38776] - 消息管理器初始化完成 - 服务器模式
2025-07-07 15:41:35 - server_MessageProcessor - INFO - [38776] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 15:41:35 - server_DebugManager - INFO - [38776] - 日志级别设置为: INFO
2025-07-07 15:47:26 - server_WelcomeManager - INFO - [34732] - 欢迎语管理器初始化完成
2025-07-07 15:47:26 - server_DebugManager - INFO - [34732] - 调试管理器初始化完成
2025-07-07 15:47:26 - server_RoomIdGenerator - INFO - [34732] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 15:47:26 - server_SecurityManager - INFO - [34732] - 已加载现有服务器密钥
2025-07-07 15:47:26 - server_SecurityManager - INFO - [34732] - 安全管理器初始化完成 - 服务器模式
2025-07-07 15:47:26 - server_SecureProtocol - INFO - [34732] - 安全协议初始化完成 - 服务器模式
2025-07-07 15:47:26 - server_MessageManager - INFO - [34732] - 消息管理器初始化完成 - 服务器模式
2025-07-07 15:47:26 - server_RoomManager - INFO - [34732] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 15:47:26 - server_SecurityManager - INFO - [34732] - 已加载现有服务器密钥
2025-07-07 15:47:26 - server_SecurityManager - INFO - [34732] - 安全管理器初始化完成 - 服务器模式
2025-07-07 15:47:26 - server_SecureProtocol - INFO - [34732] - 安全协议初始化完成 - 服务器模式
2025-07-07 15:47:26 - server_SecurityManager - INFO - [34732] - 已加载现有服务器密钥
2025-07-07 15:47:26 - server_SecurityManager - INFO - [34732] - 安全管理器初始化完成 - 服务器模式
2025-07-07 15:47:26 - server_SecureProtocol - INFO - [34732] - 安全协议初始化完成 - 服务器模式
2025-07-07 15:47:26 - server_MessageManager - INFO - [34732] - 消息管理器初始化完成 - 服务器模式
2025-07-07 15:47:26 - server_MessageProcessor - INFO - [34732] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 15:47:26 - server_DebugManager - INFO - [34732] - 日志级别设置为: INFO
2025-07-07 15:50:40 - server_WelcomeManager - INFO - [15468] - 欢迎语管理器初始化完成
2025-07-07 15:50:40 - server_DebugManager - INFO - [15468] - 调试管理器初始化完成
2025-07-07 15:50:40 - server_RoomIdGenerator - INFO - [15468] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 15:50:40 - server_SecurityManager - INFO - [15468] - 已加载现有服务器密钥
2025-07-07 15:50:40 - server_SecurityManager - INFO - [15468] - 安全管理器初始化完成 - 服务器模式
2025-07-07 15:50:40 - server_SecureProtocol - INFO - [15468] - 安全协议初始化完成 - 服务器模式
2025-07-07 15:50:40 - server_MessageManager - INFO - [15468] - 消息管理器初始化完成 - 服务器模式
2025-07-07 15:50:40 - server_RoomManager - INFO - [15468] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 15:50:40 - server_SecurityManager - INFO - [15468] - 已加载现有服务器密钥
2025-07-07 15:50:40 - server_SecurityManager - INFO - [15468] - 安全管理器初始化完成 - 服务器模式
2025-07-07 15:50:40 - server_SecureProtocol - INFO - [15468] - 安全协议初始化完成 - 服务器模式
2025-07-07 15:50:40 - server_SecurityManager - INFO - [15468] - 已加载现有服务器密钥
2025-07-07 15:50:40 - server_SecurityManager - INFO - [15468] - 安全管理器初始化完成 - 服务器模式
2025-07-07 15:50:40 - server_SecureProtocol - INFO - [15468] - 安全协议初始化完成 - 服务器模式
2025-07-07 15:50:40 - server_MessageManager - INFO - [15468] - 消息管理器初始化完成 - 服务器模式
2025-07-07 15:50:40 - server_MessageProcessor - INFO - [15468] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 15:50:40 - server_DebugManager - INFO - [15468] - 调试模式已启用，模块: ['user_actions', 'ai', 'world_gen', 'security', 'database', 'game_logic', 'network', 'performance']
2025-07-07 15:50:40 - server_DebugManager - INFO - [15468] - 详细模式已启用
2025-07-07 15:50:40 - server_DebugManager - INFO - [15468] - 日志级别设置为: INFO
2025-07-07 15:57:29 - server_WelcomeManager - INFO - [34464] - 欢迎语管理器初始化完成
2025-07-07 15:57:29 - server_DebugManager - INFO - [34464] - 调试管理器初始化完成
2025-07-07 15:57:29 - server_RoomIdGenerator - INFO - [34464] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 15:57:29 - server_SecurityManager - INFO - [34464] - 已加载现有服务器密钥
2025-07-07 15:57:29 - server_SecurityManager - INFO - [34464] - 安全管理器初始化完成 - 服务器模式
2025-07-07 15:57:29 - server_SecureProtocol - INFO - [34464] - 安全协议初始化完成 - 服务器模式
2025-07-07 15:57:29 - server_MessageManager - INFO - [34464] - 消息管理器初始化完成 - 服务器模式
2025-07-07 15:57:29 - server_RoomManager - INFO - [34464] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 15:57:29 - server_SecurityManager - INFO - [34464] - 已加载现有服务器密钥
2025-07-07 15:57:29 - server_SecurityManager - INFO - [34464] - 安全管理器初始化完成 - 服务器模式
2025-07-07 15:57:29 - server_SecureProtocol - INFO - [34464] - 安全协议初始化完成 - 服务器模式
2025-07-07 15:57:29 - server_SecurityManager - INFO - [34464] - 已加载现有服务器密钥
2025-07-07 15:57:29 - server_SecurityManager - INFO - [34464] - 安全管理器初始化完成 - 服务器模式
2025-07-07 15:57:29 - server_SecureProtocol - INFO - [34464] - 安全协议初始化完成 - 服务器模式
2025-07-07 15:57:29 - server_MessageManager - INFO - [34464] - 消息管理器初始化完成 - 服务器模式
2025-07-07 15:57:29 - server_MessageProcessor - INFO - [34464] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 15:57:29 - server_DebugManager - INFO - [34464] - 调试模式已启用，模块: ['database', 'performance', 'network', 'world_gen', 'user_actions', 'game_logic', 'ai', 'security']
2025-07-07 15:57:29 - server_DebugManager - INFO - [34464] - 详细模式已启用
2025-07-07 15:57:29 - server_DebugManager - INFO - [34464] - 日志级别设置为: INFO
2025-07-07 16:00:29 - server_WelcomeManager - INFO - [31108] - 欢迎语管理器初始化完成
2025-07-07 16:00:29 - server_DebugManager - INFO - [31108] - 调试管理器初始化完成
2025-07-07 16:00:29 - server_RoomIdGenerator - INFO - [31108] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 16:00:29 - server_SecurityManager - INFO - [31108] - 已加载现有服务器密钥
2025-07-07 16:00:29 - server_SecurityManager - INFO - [31108] - 安全管理器初始化完成 - 服务器模式
2025-07-07 16:00:29 - server_SecureProtocol - INFO - [31108] - 安全协议初始化完成 - 服务器模式
2025-07-07 16:00:29 - server_MessageManager - INFO - [31108] - 消息管理器初始化完成 - 服务器模式
2025-07-07 16:00:29 - server_RoomManager - INFO - [31108] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 16:00:29 - server_SecurityManager - INFO - [31108] - 已加载现有服务器密钥
2025-07-07 16:00:29 - server_SecurityManager - INFO - [31108] - 安全管理器初始化完成 - 服务器模式
2025-07-07 16:00:29 - server_SecureProtocol - INFO - [31108] - 安全协议初始化完成 - 服务器模式
2025-07-07 16:00:29 - server_SecurityManager - INFO - [31108] - 已加载现有服务器密钥
2025-07-07 16:00:29 - server_SecurityManager - INFO - [31108] - 安全管理器初始化完成 - 服务器模式
2025-07-07 16:00:29 - server_SecureProtocol - INFO - [31108] - 安全协议初始化完成 - 服务器模式
2025-07-07 16:00:29 - server_MessageManager - INFO - [31108] - 消息管理器初始化完成 - 服务器模式
2025-07-07 16:00:29 - server_MessageProcessor - INFO - [31108] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 16:00:29 - server_DebugManager - INFO - [31108] - 调试模式已启用，模块: ['performance', 'network', 'database', 'game_logic', 'world_gen', 'user_actions', 'security', 'ai']
2025-07-07 16:00:29 - server_DebugManager - INFO - [31108] - 详细模式已启用
2025-07-07 16:00:29 - server_DebugManager - INFO - [31108] - 日志级别设置为: INFO
2025-07-07 16:03:24 - server_WelcomeManager - INFO - [12460] - 欢迎语管理器初始化完成
2025-07-07 16:03:24 - server_DebugManager - INFO - [12460] - 调试管理器初始化完成
2025-07-07 16:03:24 - server_RoomIdGenerator - INFO - [12460] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 16:03:24 - server_SecurityManager - INFO - [12460] - 已加载现有服务器密钥
2025-07-07 16:03:24 - server_SecurityManager - INFO - [12460] - 安全管理器初始化完成 - 服务器模式
2025-07-07 16:03:24 - server_SecureProtocol - INFO - [12460] - 安全协议初始化完成 - 服务器模式
2025-07-07 16:03:24 - server_MessageManager - INFO - [12460] - 消息管理器初始化完成 - 服务器模式
2025-07-07 16:03:24 - server_RoomManager - INFO - [12460] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 16:03:24 - server_SecurityManager - INFO - [12460] - 已加载现有服务器密钥
2025-07-07 16:03:24 - server_SecurityManager - INFO - [12460] - 安全管理器初始化完成 - 服务器模式
2025-07-07 16:03:24 - server_SecureProtocol - INFO - [12460] - 安全协议初始化完成 - 服务器模式
2025-07-07 16:03:24 - server_SecurityManager - INFO - [12460] - 已加载现有服务器密钥
2025-07-07 16:03:24 - server_SecurityManager - INFO - [12460] - 安全管理器初始化完成 - 服务器模式
2025-07-07 16:03:24 - server_SecureProtocol - INFO - [12460] - 安全协议初始化完成 - 服务器模式
2025-07-07 16:03:24 - server_MessageManager - INFO - [12460] - 消息管理器初始化完成 - 服务器模式
2025-07-07 16:03:24 - server_MessageProcessor - INFO - [12460] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 16:03:24 - server_DebugManager - INFO - [12460] - 调试模式已启用，模块: ['security', 'world_gen', 'database', 'user_actions', 'network', 'ai', 'game_logic', 'performance']
2025-07-07 16:03:24 - server_DebugManager - INFO - [12460] - 详细模式已启用
2025-07-07 16:03:24 - server_DebugManager - INFO - [12460] - 日志级别设置为: INFO
2025-07-07 16:05:04 - server_DebugManager - INFO - [37672] - [USER_ACTIONS] 用户操作 - ('127.0.0.1', 49406): send_message, 详情: {'message_length': 13}
2025-07-07 16:09:09 - server_WelcomeManager - INFO - [33120] - 欢迎语管理器初始化完成
2025-07-07 16:09:09 - server_DebugManager - INFO - [33120] - 调试管理器初始化完成
2025-07-07 16:09:09 - server_RoomIdGenerator - INFO - [33120] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 16:09:09 - server_SecurityManager - INFO - [33120] - 已加载现有服务器密钥
2025-07-07 16:09:09 - server_SecurityManager - INFO - [33120] - 安全管理器初始化完成 - 服务器模式
2025-07-07 16:09:09 - server_SecureProtocol - INFO - [33120] - 安全协议初始化完成 - 服务器模式
2025-07-07 16:09:09 - server_MessageManager - INFO - [33120] - 消息管理器初始化完成 - 服务器模式
2025-07-07 16:09:09 - server_RoomManager - INFO - [33120] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 16:09:09 - server_SecurityManager - INFO - [33120] - 已加载现有服务器密钥
2025-07-07 16:09:09 - server_SecurityManager - INFO - [33120] - 安全管理器初始化完成 - 服务器模式
2025-07-07 16:09:09 - server_SecureProtocol - INFO - [33120] - 安全协议初始化完成 - 服务器模式
2025-07-07 16:09:09 - server_SecurityManager - INFO - [33120] - 已加载现有服务器密钥
2025-07-07 16:09:09 - server_SecurityManager - INFO - [33120] - 安全管理器初始化完成 - 服务器模式
2025-07-07 16:09:09 - server_SecureProtocol - INFO - [33120] - 安全协议初始化完成 - 服务器模式
2025-07-07 16:09:09 - server_MessageManager - INFO - [33120] - 消息管理器初始化完成 - 服务器模式
2025-07-07 16:09:09 - server_MessageProcessor - INFO - [33120] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 16:09:09 - server_DebugManager - INFO - [33120] - 调试模式已启用，模块: ['user_actions', 'database', 'network', 'ai', 'security', 'world_gen', 'game_logic', 'performance']
2025-07-07 16:09:09 - server_DebugManager - INFO - [33120] - 详细模式已启用
2025-07-07 16:09:09 - server_DebugManager - INFO - [33120] - 日志级别设置为: INFO
2025-07-07 16:11:02 - server_DebugManager - INFO - [32588] - [USER_ACTIONS] 用户操作 - ('127.0.0.1', 49785): send_message, 详情: {'message_length': 13}
2025-07-07 16:13:01 - server_DebugManager - INFO - [32588] - [USER_ACTIONS] 用户操作 - ('127.0.0.1', 49785): send_message, 详情: {'message_length': 124}
2025-07-07 16:13:01 - server_MessageProcessor - INFO - [32588] - 收到客户端消息: create_room
2025-07-07 16:13:01 - server_MessageProcessor - INFO - [32588] - 开始创建房间 - 玩家: sweatent, 房间名: TestRoom, 最大玩家: 8, 模式: ai_generated, 难度: normal
2025-07-07 16:13:01 - server_RoomManager - INFO - [32588] - 创建房间: TestRoom (ID: ROOM59815EV, 类型: alphanumeric)
2025-07-07 16:13:01 - server_MessageProcessor - INFO - [32588] - 房间创建成功 - ID: ROOM59815EV, 名称: TestRoom
2025-07-07 16:13:01 - server_MessageProcessor - INFO - [32588] - 房主 sweatent 尝试加入房间 ROOM59815EV
2025-07-07 16:13:01 - server_MessageProcessor - INFO - [32588] - 房主成功加入房间 - 玩家: sweatent, 房间: ROOM59815EV
2025-07-07 16:13:01 - server_MessageProcessor - INFO - [32588] - 发送房间创建响应 - 数据: {'success': True, 'room_info': {'room_id': 'ROOM59815EV', 'room_name': 'TestRoom', 'is_host': True, 'max_players': 8, 'game_mode': 'ai_generated', 'difficulty': 'normal', 'is_private': False, 'status': 'waiting', 'host_player': 'd09af172-8e39-40ad-ba08-5351a4fa2487'}}
2025-07-07 16:13:01 - server_SecureProtocol - ERROR - [32588] - 握手未完成，无法发送安全消息
2025-07-07 16:13:01 - server_MessageManager - ERROR - [32588] - 发送安全消息失败: room_created
2025-07-07 16:13:01 - server_MessageProcessor - ERROR - [32588] - ❌ 发送房间创建响应失败 - 房间ID: ROOM59815EV
2025-07-07 16:13:33 - server_DebugManager - INFO - [32588] - [USER_ACTIONS] 用户操作 - ('127.0.0.1', 49785): send_message, 详情: {'message_length': 13}
2025-07-07 16:14:41 - server_WelcomeManager - INFO - [40600] - 欢迎语管理器初始化完成
2025-07-07 16:14:41 - server_DebugManager - INFO - [40600] - 调试管理器初始化完成
2025-07-07 16:14:41 - server_RoomIdGenerator - INFO - [40600] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 16:14:41 - server_SecurityManager - INFO - [40600] - 已加载现有服务器密钥
2025-07-07 16:14:41 - server_SecurityManager - INFO - [40600] - 安全管理器初始化完成 - 服务器模式
2025-07-07 16:14:41 - server_SecureProtocol - INFO - [40600] - 安全协议初始化完成 - 服务器模式
2025-07-07 16:14:41 - server_MessageManager - INFO - [40600] - 消息管理器初始化完成 - 服务器模式
2025-07-07 16:14:41 - server_RoomManager - INFO - [40600] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 16:14:41 - server_SecurityManager - INFO - [40600] - 已加载现有服务器密钥
2025-07-07 16:14:41 - server_SecurityManager - INFO - [40600] - 安全管理器初始化完成 - 服务器模式
2025-07-07 16:14:41 - server_SecureProtocol - INFO - [40600] - 安全协议初始化完成 - 服务器模式
2025-07-07 16:14:41 - server_SecurityManager - INFO - [40600] - 已加载现有服务器密钥
2025-07-07 16:14:41 - server_SecurityManager - INFO - [40600] - 安全管理器初始化完成 - 服务器模式
2025-07-07 16:14:41 - server_SecureProtocol - INFO - [40600] - 安全协议初始化完成 - 服务器模式
2025-07-07 16:14:41 - server_MessageManager - INFO - [40600] - 消息管理器初始化完成 - 服务器模式
2025-07-07 16:14:41 - server_MessageProcessor - INFO - [40600] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 16:14:41 - server_DebugManager - INFO - [40600] - 调试模式已启用，模块: ['security', 'game_logic', 'database', 'world_gen', 'user_actions', 'network', 'performance', 'ai']
2025-07-07 16:14:41 - server_DebugManager - INFO - [40600] - 详细模式已启用
2025-07-07 16:14:41 - server_DebugManager - INFO - [40600] - 日志级别设置为: INFO
2025-07-07 16:16:25 - server_DebugManager - INFO - [29556] - [USER_ACTIONS] 用户操作 - ('127.0.0.1', 50852): send_message, 详情: {'message_length': 13}
2025-07-07 16:16:56 - server_DebugManager - INFO - [29556] - [USER_ACTIONS] 用户操作 - ('127.0.0.1', 50852): send_message, 详情: {'message_length': 124}
2025-07-07 16:16:56 - server_MessageProcessor - INFO - [29556] - 收到客户端消息: create_room
2025-07-07 16:16:56 - server_MessageProcessor - INFO - [29556] - 开始创建房间 - 玩家: sweatent, 房间名: TestRoom, 最大玩家: 8, 模式: ai_generated, 难度: normal
2025-07-07 16:16:56 - server_RoomManager - INFO - [29556] - 创建房间: TestRoom (ID: ROOM6216WYS, 类型: alphanumeric)
2025-07-07 16:16:56 - server_MessageProcessor - INFO - [29556] - 房间创建成功 - ID: ROOM6216WYS, 名称: TestRoom
2025-07-07 16:16:56 - server_MessageProcessor - INFO - [29556] - 房主 sweatent 尝试加入房间 ROOM6216WYS
2025-07-07 16:16:56 - server_MessageProcessor - INFO - [29556] - 房主成功加入房间 - 玩家: sweatent, 房间: ROOM6216WYS
2025-07-07 16:16:56 - server_MessageProcessor - INFO - [29556] - 发送房间创建响应 - 数据: {'success': True, 'room_info': {'room_id': 'ROOM6216WYS', 'room_name': 'TestRoom', 'is_host': True, 'max_players': 8, 'game_mode': 'ai_generated', 'difficulty': 'normal', 'is_private': False, 'status': 'waiting', 'host_player': '95a7d857-d792-4c0f-a33d-0e2cec18a14c'}}
2025-07-07 16:16:56 - server_SecureProtocol - ERROR - [29556] - 握手未完成，无法发送安全消息
2025-07-07 16:16:56 - server_MessageManager - ERROR - [29556] - 发送安全消息失败: room_created
2025-07-07 16:16:56 - server_MessageProcessor - ERROR - [29556] - ❌ 发送房间创建响应失败 - 房间ID: ROOM6216WYS
2025-07-07 16:17:17 - server_DebugManager - INFO - [29556] - [USER_ACTIONS] 用户操作 - ('127.0.0.1', 50852): send_message, 详情: {'message_length': 13}
2025-07-07 16:17:17 - server_MessageProcessor - INFO - [29556] - 收到客户端消息: get_room_list
2025-07-07 16:17:17 - server_SecureProtocol - ERROR - [29556] - 握手未完成，无法发送安全消息
2025-07-07 16:17:17 - server_MessageManager - ERROR - [29556] - 发送安全消息失败: room_list
2025-07-07 16:17:17 - server_MessageProcessor - ERROR - [29556] - 发送房间列表失败: 95a7d857-d792-4c0f-a33d-0e2cec18a14c
2025-07-07 16:20:01 - server_WelcomeManager - INFO - [12852] - 欢迎语管理器初始化完成
2025-07-07 16:20:01 - server_DebugManager - INFO - [12852] - 调试管理器初始化完成
2025-07-07 16:20:01 - server_RoomIdGenerator - INFO - [12852] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 16:20:01 - server_SecurityManager - INFO - [12852] - 已加载现有服务器密钥
2025-07-07 16:20:01 - server_SecurityManager - INFO - [12852] - 安全管理器初始化完成 - 服务器模式
2025-07-07 16:20:01 - server_SecureProtocol - INFO - [12852] - 安全协议初始化完成 - 服务器模式
2025-07-07 16:20:01 - server_MessageManager - INFO - [12852] - 消息管理器初始化完成 - 服务器模式
2025-07-07 16:20:01 - server_RoomManager - INFO - [12852] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 16:20:01 - server_SecurityManager - INFO - [12852] - 已加载现有服务器密钥
2025-07-07 16:20:01 - server_SecurityManager - INFO - [12852] - 安全管理器初始化完成 - 服务器模式
2025-07-07 16:20:01 - server_SecureProtocol - INFO - [12852] - 安全协议初始化完成 - 服务器模式
2025-07-07 16:20:01 - server_SecurityManager - INFO - [12852] - 已加载现有服务器密钥
2025-07-07 16:20:01 - server_SecurityManager - INFO - [12852] - 安全管理器初始化完成 - 服务器模式
2025-07-07 16:20:01 - server_SecureProtocol - INFO - [12852] - 安全协议初始化完成 - 服务器模式
2025-07-07 16:20:01 - server_MessageManager - INFO - [12852] - 消息管理器初始化完成 - 服务器模式
2025-07-07 16:20:01 - server_MessageProcessor - INFO - [12852] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 16:20:01 - server_DebugManager - INFO - [12852] - 调试模式已启用，模块: ['game_logic', 'database', 'world_gen', 'network', 'security', 'performance', 'user_actions', 'ai']
2025-07-07 16:20:01 - server_DebugManager - INFO - [12852] - 详细模式已启用
2025-07-07 16:20:01 - server_DebugManager - INFO - [12852] - 日志级别设置为: INFO
2025-07-07 16:22:33 - server_DebugManager - INFO - [21384] - [USER_ACTIONS] 用户操作 - ('127.0.0.1', 51480): send_message, 详情: {'message_length': 13}
2025-07-07 16:23:01 - server_DebugManager - INFO - [21384] - [USER_ACTIONS] 用户操作 - ('127.0.0.1', 51480): send_message, 详情: {'message_length': 124}
2025-07-07 16:23:01 - server_MessageProcessor - INFO - [21384] - 收到客户端消息: create_room
2025-07-07 16:23:01 - server_MessageProcessor - INFO - [21384] - 开始创建房间 - 玩家: sweatent, 房间名: TestRoom, 最大玩家: 8, 模式: ai_generated, 难度: normal
2025-07-07 16:23:01 - server_RoomManager - INFO - [21384] - 创建房间: TestRoom (ID: ROOM6581MTS, 类型: alphanumeric)
2025-07-07 16:23:01 - server_MessageProcessor - INFO - [21384] - 房间创建成功 - ID: ROOM6581MTS, 名称: TestRoom
2025-07-07 16:23:01 - server_MessageProcessor - INFO - [21384] - 房主 sweatent 尝试加入房间 ROOM6581MTS
2025-07-07 16:23:01 - server_MessageProcessor - INFO - [21384] - 房主成功加入房间 - 玩家: sweatent, 房间: ROOM6581MTS
2025-07-07 16:23:01 - server_MessageProcessor - INFO - [21384] - 发送房间创建响应 - 数据: {'success': True, 'room_info': {'room_id': 'ROOM6581MTS', 'room_name': 'TestRoom', 'is_host': True, 'max_players': 8, 'game_mode': 'ai_generated', 'difficulty': 'normal', 'is_private': False, 'status': 'waiting', 'host_player': 'eaf3a9e1-3513-403c-ad20-84f15834bf58'}}
2025-07-07 16:23:01 - server_SecureProtocol - ERROR - [21384] - 握手未完成，无法发送安全消息
2025-07-07 16:23:01 - server_MessageManager - ERROR - [21384] - 发送安全消息失败: room_created
2025-07-07 16:23:01 - server_MessageProcessor - ERROR - [21384] - ❌ 发送房间创建响应失败 - 房间ID: ROOM6581MTS
2025-07-07 16:26:09 - server_WelcomeManager - INFO - [38220] - 欢迎语管理器初始化完成
2025-07-07 16:26:09 - server_DebugManager - INFO - [38220] - 调试管理器初始化完成
2025-07-07 16:26:09 - server_RoomIdGenerator - INFO - [38220] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 16:26:09 - server_SecurityManager - INFO - [38220] - 已加载现有服务器密钥
2025-07-07 16:26:09 - server_SecurityManager - INFO - [38220] - 安全管理器初始化完成 - 服务器模式
2025-07-07 16:26:09 - server_SecureProtocol - INFO - [38220] - 安全协议初始化完成 - 服务器模式
2025-07-07 16:26:09 - server_MessageManager - INFO - [38220] - 消息管理器初始化完成 - 服务器模式
2025-07-07 16:26:09 - server_RoomManager - INFO - [38220] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 16:26:09 - server_SecurityManager - INFO - [38220] - 已加载现有服务器密钥
2025-07-07 16:26:09 - server_SecurityManager - INFO - [38220] - 安全管理器初始化完成 - 服务器模式
2025-07-07 16:26:09 - server_SecureProtocol - INFO - [38220] - 安全协议初始化完成 - 服务器模式
2025-07-07 16:26:09 - server_SecurityManager - INFO - [38220] - 已加载现有服务器密钥
2025-07-07 16:26:09 - server_SecurityManager - INFO - [38220] - 安全管理器初始化完成 - 服务器模式
2025-07-07 16:26:09 - server_SecureProtocol - INFO - [38220] - 安全协议初始化完成 - 服务器模式
2025-07-07 16:26:09 - server_MessageManager - INFO - [38220] - 消息管理器初始化完成 - 服务器模式
2025-07-07 16:26:09 - server_MessageProcessor - INFO - [38220] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 16:26:09 - server_DebugManager - INFO - [38220] - 调试模式已启用，模块: ['world_gen', 'game_logic', 'user_actions', 'database', 'performance', 'ai', 'network', 'security']
2025-07-07 16:26:09 - server_DebugManager - INFO - [38220] - 详细模式已启用
2025-07-07 16:26:09 - server_DebugManager - INFO - [38220] - 日志级别设置为: INFO
2025-07-07 16:37:09 - server_DebugManager - INFO - [15416] - [USER_ACTIONS] 用户操作 - ('127.0.0.1', 51800): send_message, 详情: {'message_length': 13}
2025-07-07 16:37:37 - server_DebugManager - INFO - [15416] - [USER_ACTIONS] 用户操作 - ('127.0.0.1', 51800): send_message, 详情: {'message_length': 124}
2025-07-07 16:37:37 - server_MessageProcessor - INFO - [15416] - 收到客户端消息: create_room
2025-07-07 16:37:37 - server_MessageProcessor - INFO - [15416] - 开始创建房间 - 玩家: sweatent, 房间名: TestRoom, 最大玩家: 8, 模式: ai_generated, 难度: normal
2025-07-07 16:37:37 - server_RoomManager - INFO - [15416] - 创建房间: TestRoom (ID: ROOM745713T, 类型: alphanumeric)
2025-07-07 16:37:37 - server_MessageProcessor - INFO - [15416] - 房间创建成功 - ID: ROOM745713T, 名称: TestRoom
2025-07-07 16:37:37 - server_MessageProcessor - INFO - [15416] - 房主 sweatent 尝试加入房间 ROOM745713T
2025-07-07 16:37:37 - server_MessageProcessor - INFO - [15416] - 房主成功加入房间 - 玩家: sweatent, 房间: ROOM745713T
2025-07-07 16:37:37 - server_MessageProcessor - INFO - [15416] - 发送房间创建响应 - 数据: {'success': True, 'room_info': {'room_id': 'ROOM745713T', 'room_name': 'TestRoom', 'is_host': True, 'max_players': 8, 'game_mode': 'ai_generated', 'difficulty': 'normal', 'is_private': False, 'status': 'waiting', 'host_player': '765a1b75-a11f-4408-92bb-4991b0a3476c'}}
2025-07-07 16:37:37 - server_MessageProcessor - INFO - [15416] - ✅ 房间创建完成 - 玩家: sweatent, 房间ID: ROOM745713T, 模式: ai_generated
2025-07-07 16:38:01 - server_DebugManager - INFO - [15416] - [USER_ACTIONS] 用户操作 - ('127.0.0.1', 51800): send_message, 详情: {'message_length': 13}
2025-07-07 16:43:09 - server_DebugManager - INFO - [19860] - [USER_ACTIONS] 用户操作 - ('127.0.0.1', 53125): send_message, 详情: {'message_length': 13}
2025-07-07 16:43:32 - server_DebugManager - INFO - [19860] - [USER_ACTIONS] 用户操作 - ('127.0.0.1', 53125): send_message, 详情: {'message_length': 125}
2025-07-07 16:43:32 - server_MessageProcessor - INFO - [19860] - 收到客户端消息: create_room
2025-07-07 16:43:32 - server_MessageProcessor - INFO - [19860] - 开始创建房间 - 玩家: sweatent, 房间名: TestRoom2, 最大玩家: 8, 模式: ai_generated, 难度: normal
2025-07-07 16:43:32 - server_RoomManager - INFO - [19860] - 创建房间: TestRoom2 (ID: ROOM7812IWV, 类型: alphanumeric)
2025-07-07 16:43:32 - server_MessageProcessor - INFO - [19860] - 房间创建成功 - ID: ROOM7812IWV, 名称: TestRoom2
2025-07-07 16:43:32 - server_MessageProcessor - INFO - [19860] - 房主 sweatent 尝试加入房间 ROOM7812IWV
2025-07-07 16:43:32 - server_MessageProcessor - INFO - [19860] - 房主成功加入房间 - 玩家: sweatent, 房间: ROOM7812IWV
2025-07-07 16:43:32 - server_MessageProcessor - INFO - [19860] - 发送房间创建响应 - 数据: {'success': True, 'room_info': {'room_id': 'ROOM7812IWV', 'room_name': 'TestRoom2', 'is_host': True, 'max_players': 8, 'game_mode': 'ai_generated', 'difficulty': 'normal', 'is_private': False, 'status': 'waiting', 'host_player': '46db9dd4-35db-4540-ba21-61c410627954'}}
2025-07-07 16:43:32 - server_MessageProcessor - INFO - [19860] - ✅ 房间创建完成 - 玩家: sweatent, 房间ID: ROOM7812IWV, 模式: ai_generated
2025-07-07 16:44:01 - server_DebugManager - INFO - [19860] - [USER_ACTIONS] 用户操作 - ('127.0.0.1', 53125): send_message, 详情: {'message_length': 13}
2025-07-07 16:46:12 - server_DebugManager - INFO - [20472] - [USER_ACTIONS] 用户操作 - ('127.0.0.1', 53530): send_message, 详情: {'message_length': 13}
2025-07-07 16:46:53 - server_DebugManager - INFO - [19860] - [USER_ACTIONS] 用户操作 - ('127.0.0.1', 53125): send_message, 详情: {'message_length': 5}
2025-07-07 16:47:01 - server_DebugManager - INFO - [20472] - [USER_ACTIONS] 用户操作 - ('127.0.0.1', 53530): send_message, 详情: {'message_length': 5}
2025-07-07 16:58:17 - server_WelcomeManager - INFO - [38760] - 欢迎语管理器初始化完成
2025-07-07 16:58:17 - server_DebugManager - INFO - [38760] - 调试管理器初始化完成
2025-07-07 16:58:17 - server_RoomIdGenerator - INFO - [38760] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 16:58:17 - server_SecurityManager - INFO - [38760] - 已加载现有服务器密钥
2025-07-07 16:58:17 - server_SecurityManager - INFO - [38760] - 安全管理器初始化完成 - 服务器模式
2025-07-07 16:58:17 - server_SecureProtocol - INFO - [38760] - 安全协议初始化完成 - 服务器模式
2025-07-07 16:58:17 - server_MessageManager - INFO - [38760] - 消息管理器初始化完成 - 服务器模式
2025-07-07 16:58:17 - server_RoomManager - INFO - [38760] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 16:58:17 - server_SecurityManager - INFO - [38760] - 已加载现有服务器密钥
2025-07-07 16:58:17 - server_SecurityManager - INFO - [38760] - 安全管理器初始化完成 - 服务器模式
2025-07-07 16:58:17 - server_SecureProtocol - INFO - [38760] - 安全协议初始化完成 - 服务器模式
2025-07-07 16:58:17 - server_SecurityManager - INFO - [38760] - 已加载现有服务器密钥
2025-07-07 16:58:17 - server_SecurityManager - INFO - [38760] - 安全管理器初始化完成 - 服务器模式
2025-07-07 16:58:17 - server_SecureProtocol - INFO - [38760] - 安全协议初始化完成 - 服务器模式
2025-07-07 16:58:17 - server_MessageManager - INFO - [38760] - 消息管理器初始化完成 - 服务器模式
2025-07-07 16:58:17 - server_MessageProcessor - INFO - [38760] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 16:58:17 - server_DebugManager - INFO - [38760] - 调试模式已启用，模块: ['security', 'database', 'world_gen', 'network', 'performance', 'ai', 'user_actions', 'game_logic']
2025-07-07 16:58:17 - server_DebugManager - INFO - [38760] - 详细模式已启用
2025-07-07 16:58:17 - server_DebugManager - INFO - [38760] - 日志级别设置为: INFO
2025-07-07 17:00:14 - server_WelcomeManager - INFO - [7072] - 欢迎语管理器初始化完成
2025-07-07 17:00:14 - server_DebugManager - INFO - [7072] - 调试管理器初始化完成
2025-07-07 17:00:14 - server_RoomIdGenerator - INFO - [7072] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 17:00:14 - server_SecurityManager - INFO - [7072] - 已加载现有服务器密钥
2025-07-07 17:00:14 - server_SecurityManager - INFO - [7072] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:00:14 - server_SecureProtocol - INFO - [7072] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:00:14 - server_MessageManager - INFO - [7072] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:00:14 - server_RoomManager - INFO - [7072] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 17:00:14 - server_SecurityManager - INFO - [7072] - 已加载现有服务器密钥
2025-07-07 17:00:14 - server_SecurityManager - INFO - [7072] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:00:14 - server_SecureProtocol - INFO - [7072] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:00:14 - server_SecurityManager - INFO - [7072] - 已加载现有服务器密钥
2025-07-07 17:00:14 - server_SecurityManager - INFO - [7072] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:00:14 - server_SecureProtocol - INFO - [7072] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:00:14 - server_MessageManager - INFO - [7072] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:00:14 - server_MessageProcessor - INFO - [7072] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 17:00:14 - server_DebugManager - INFO - [7072] - 调试模式已启用，模块: ['security', 'game_logic', 'world_gen', 'database', 'performance', 'user_actions', 'network', 'ai']
2025-07-07 17:00:14 - server_DebugManager - INFO - [7072] - 详细模式已启用
2025-07-07 17:00:14 - server_DebugManager - INFO - [7072] - 日志级别设置为: INFO
2025-07-07 17:00:44 - server_WelcomeManager - INFO - [37768] - 欢迎语管理器初始化完成
2025-07-07 17:00:44 - server_DebugManager - INFO - [37768] - 调试管理器初始化完成
2025-07-07 17:00:44 - server_RoomIdGenerator - INFO - [37768] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 17:00:44 - server_SecurityManager - INFO - [37768] - 已加载现有服务器密钥
2025-07-07 17:00:44 - server_SecurityManager - INFO - [37768] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:00:44 - server_SecureProtocol - INFO - [37768] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:00:44 - server_MessageManager - INFO - [37768] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:00:44 - server_RoomManager - INFO - [37768] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 17:00:44 - server_SecurityManager - INFO - [37768] - 已加载现有服务器密钥
2025-07-07 17:00:44 - server_SecurityManager - INFO - [37768] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:00:44 - server_SecureProtocol - INFO - [37768] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:00:44 - server_SecurityManager - INFO - [37768] - 已加载现有服务器密钥
2025-07-07 17:00:44 - server_SecurityManager - INFO - [37768] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:00:44 - server_SecureProtocol - INFO - [37768] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:00:44 - server_MessageManager - INFO - [37768] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:00:44 - server_MessageProcessor - INFO - [37768] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 17:00:44 - server_DebugManager - INFO - [37768] - 调试模式已启用，模块: ['game_logic', 'world_gen', 'ai', 'user_actions', 'performance', 'network', 'security', 'database']
2025-07-07 17:00:44 - server_DebugManager - INFO - [37768] - 详细模式已启用
2025-07-07 17:17:32 - server_RoomIdGenerator - INFO - [25324] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 17:17:32 - server_SecurityManager - INFO - [25324] - 已加载现有服务器密钥
2025-07-07 17:17:32 - server_SecurityManager - INFO - [25324] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:17:32 - server_SecureProtocol - INFO - [25324] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:17:32 - server_MessageManager - INFO - [25324] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:17:32 - server_RoomManager - INFO - [25324] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 17:17:59 - server_WelcomeManager - INFO - [39144] - 欢迎语管理器初始化完成
2025-07-07 17:17:59 - server_DebugManager - INFO - [39144] - 调试管理器初始化完成
2025-07-07 17:17:59 - server_RoomIdGenerator - INFO - [39144] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 17:17:59 - server_SecurityManager - INFO - [39144] - 已加载现有服务器密钥
2025-07-07 17:17:59 - server_SecurityManager - INFO - [39144] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:17:59 - server_SecureProtocol - INFO - [39144] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:17:59 - server_MessageManager - INFO - [39144] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:17:59 - server_RoomManager - INFO - [39144] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 17:17:59 - server_SecurityManager - INFO - [39144] - 已加载现有服务器密钥
2025-07-07 17:17:59 - server_SecurityManager - INFO - [39144] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:17:59 - server_SecureProtocol - INFO - [39144] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:17:59 - server_SecurityManager - INFO - [39144] - 已加载现有服务器密钥
2025-07-07 17:17:59 - server_SecurityManager - INFO - [39144] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:17:59 - server_SecureProtocol - INFO - [39144] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:17:59 - server_MessageManager - INFO - [39144] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:17:59 - server_MessageProcessor - INFO - [39144] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 17:18:10 - server_WelcomeManager - INFO - [23484] - 欢迎语管理器初始化完成
2025-07-07 17:18:10 - server_DebugManager - INFO - [23484] - 调试管理器初始化完成
2025-07-07 17:18:10 - server_RoomIdGenerator - INFO - [23484] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 17:18:10 - server_SecurityManager - INFO - [23484] - 已加载现有服务器密钥
2025-07-07 17:18:10 - server_SecurityManager - INFO - [23484] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:18:10 - server_SecureProtocol - INFO - [23484] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:18:10 - server_MessageManager - INFO - [23484] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:18:10 - server_RoomManager - INFO - [23484] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 17:18:10 - server_SecurityManager - INFO - [23484] - 已加载现有服务器密钥
2025-07-07 17:18:10 - server_SecurityManager - INFO - [23484] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:18:10 - server_SecureProtocol - INFO - [23484] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:18:10 - server_SecurityManager - INFO - [23484] - 已加载现有服务器密钥
2025-07-07 17:18:10 - server_SecurityManager - INFO - [23484] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:18:10 - server_SecureProtocol - INFO - [23484] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:18:10 - server_MessageManager - INFO - [23484] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:18:10 - server_MessageProcessor - INFO - [23484] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 17:18:10 - server_DebugManager - INFO - [23484] - 调试模式已启用，模块: ['performance', 'user_actions', 'world_gen', 'ai', 'database', 'game_logic', 'network', 'security']
2025-07-07 17:18:10 - server_DebugManager - INFO - [23484] - 详细模式已启用
2025-07-07 17:19:29 - server_WelcomeManager - INFO - [32716] - 欢迎语管理器初始化完成
2025-07-07 17:19:29 - server_DebugManager - INFO - [32716] - 调试管理器初始化完成
2025-07-07 17:19:29 - server_RoomIdGenerator - INFO - [32716] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 17:19:29 - server_SecurityManager - INFO - [32716] - 已加载现有服务器密钥
2025-07-07 17:19:29 - server_SecurityManager - INFO - [32716] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:19:29 - server_SecureProtocol - INFO - [32716] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:19:29 - server_MessageManager - INFO - [32716] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:19:29 - server_RoomManager - INFO - [32716] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 17:19:29 - server_SecurityManager - INFO - [32716] - 已加载现有服务器密钥
2025-07-07 17:19:29 - server_SecurityManager - INFO - [32716] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:19:29 - server_SecureProtocol - INFO - [32716] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:19:29 - server_SecurityManager - INFO - [32716] - 已加载现有服务器密钥
2025-07-07 17:19:29 - server_SecurityManager - INFO - [32716] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:19:29 - server_SecureProtocol - INFO - [32716] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:19:29 - server_MessageManager - INFO - [32716] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:19:29 - server_MessageProcessor - INFO - [32716] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 17:19:29 - server_DebugManager - INFO - [32716] - 调试模式已启用，模块: ['performance', 'security', 'database', 'ai', 'game_logic', 'network', 'user_actions', 'world_gen']
2025-07-07 17:19:29 - server_DebugManager - INFO - [32716] - 详细模式已启用
2025-07-07 17:19:29 - server_DebugManager - INFO - [32716] - 日志级别设置为: INFO
2025-07-07 17:19:53 - server_WelcomeManager - INFO - [21344] - 欢迎语管理器初始化完成
2025-07-07 17:19:53 - server_DebugManager - INFO - [21344] - 调试管理器初始化完成
2025-07-07 17:19:53 - server_RoomIdGenerator - INFO - [21344] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 17:19:53 - server_SecurityManager - INFO - [21344] - 已加载现有服务器密钥
2025-07-07 17:19:53 - server_SecurityManager - INFO - [21344] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:19:53 - server_SecureProtocol - INFO - [21344] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:19:53 - server_MessageManager - INFO - [21344] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:19:53 - server_RoomManager - INFO - [21344] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 17:19:53 - server_SecurityManager - INFO - [21344] - 已加载现有服务器密钥
2025-07-07 17:19:53 - server_SecurityManager - INFO - [21344] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:19:53 - server_SecureProtocol - INFO - [21344] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:19:53 - server_SecurityManager - INFO - [21344] - 已加载现有服务器密钥
2025-07-07 17:19:53 - server_SecurityManager - INFO - [21344] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:19:53 - server_SecureProtocol - INFO - [21344] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:19:53 - server_MessageManager - INFO - [21344] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:19:53 - server_MessageProcessor - INFO - [21344] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 17:19:53 - server_DebugManager - INFO - [21344] - 调试模式已启用，模块: ['ai', 'database', 'world_gen', 'security', 'user_actions', 'performance', 'game_logic', 'network']
2025-07-07 17:19:53 - server_DebugManager - INFO - [21344] - 详细模式已启用
2025-07-07 17:19:53 - server_DebugManager - INFO - [21344] - 日志级别设置为: INFO
2025-07-07 17:21:27 - server_WelcomeManager - INFO - [24204] - 欢迎语管理器初始化完成
2025-07-07 17:21:27 - server_DebugManager - INFO - [24204] - 调试管理器初始化完成
2025-07-07 17:21:27 - server_RoomIdGenerator - INFO - [24204] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 17:21:27 - server_SecurityManager - INFO - [24204] - 已加载现有服务器密钥
2025-07-07 17:21:27 - server_SecurityManager - INFO - [24204] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:21:27 - server_SecureProtocol - INFO - [24204] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:21:27 - server_MessageManager - INFO - [24204] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:21:27 - server_RoomManager - INFO - [24204] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 17:21:27 - server_SecurityManager - INFO - [24204] - 已加载现有服务器密钥
2025-07-07 17:21:27 - server_SecurityManager - INFO - [24204] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:21:27 - server_SecureProtocol - INFO - [24204] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:21:27 - server_SecurityManager - INFO - [24204] - 已加载现有服务器密钥
2025-07-07 17:21:27 - server_SecurityManager - INFO - [24204] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:21:27 - server_SecureProtocol - INFO - [24204] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:21:27 - server_MessageManager - INFO - [24204] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:21:27 - server_MessageProcessor - INFO - [24204] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 17:21:27 - server_DebugManager - INFO - [24204] - 调试模式已启用，模块: ['network', 'performance', 'game_logic', 'database', 'security', 'world_gen', 'ai', 'user_actions']
2025-07-07 17:21:27 - server_DebugManager - INFO - [24204] - 详细模式已启用
2025-07-07 17:21:51 - server_WelcomeManager - INFO - [21688] - 欢迎语管理器初始化完成
2025-07-07 17:21:51 - server_DebugManager - INFO - [21688] - 调试管理器初始化完成
2025-07-07 17:21:51 - server_RoomIdGenerator - INFO - [21688] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 17:21:51 - server_SecurityManager - INFO - [21688] - 已加载现有服务器密钥
2025-07-07 17:21:51 - server_SecurityManager - INFO - [21688] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:21:51 - server_SecureProtocol - INFO - [21688] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:21:51 - server_MessageManager - INFO - [21688] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:21:51 - server_RoomManager - INFO - [21688] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 17:21:51 - server_SecurityManager - INFO - [21688] - 已加载现有服务器密钥
2025-07-07 17:21:51 - server_SecurityManager - INFO - [21688] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:21:51 - server_SecureProtocol - INFO - [21688] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:21:51 - server_SecurityManager - INFO - [21688] - 已加载现有服务器密钥
2025-07-07 17:21:51 - server_SecurityManager - INFO - [21688] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:21:51 - server_SecureProtocol - INFO - [21688] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:21:51 - server_MessageManager - INFO - [21688] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:21:51 - server_MessageProcessor - INFO - [21688] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 17:21:51 - server_DebugManager - INFO - [21688] - 调试模式已启用，模块: ['network', 'security', 'database', 'performance', 'ai', 'world_gen', 'game_logic', 'user_actions']
2025-07-07 17:21:51 - server_DebugManager - INFO - [21688] - 详细模式已启用
2025-07-07 17:22:24 - server_WelcomeManager - INFO - [36848] - 欢迎语管理器初始化完成
2025-07-07 17:22:24 - server_DebugManager - INFO - [36848] - 调试管理器初始化完成
2025-07-07 17:22:24 - server_RoomIdGenerator - INFO - [36848] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 17:22:24 - server_SecurityManager - INFO - [36848] - 已加载现有服务器密钥
2025-07-07 17:22:24 - server_SecurityManager - INFO - [36848] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:22:24 - server_SecureProtocol - INFO - [36848] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:22:24 - server_MessageManager - INFO - [36848] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:22:24 - server_RoomManager - INFO - [36848] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 17:22:24 - server_SecurityManager - INFO - [36848] - 已加载现有服务器密钥
2025-07-07 17:22:24 - server_SecurityManager - INFO - [36848] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:22:24 - server_SecureProtocol - INFO - [36848] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:22:24 - server_SecurityManager - INFO - [36848] - 已加载现有服务器密钥
2025-07-07 17:22:24 - server_SecurityManager - INFO - [36848] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:22:24 - server_SecureProtocol - INFO - [36848] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:22:24 - server_MessageManager - INFO - [36848] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:22:24 - server_MessageProcessor - INFO - [36848] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 17:22:57 - server_WelcomeManager - INFO - [27440] - 欢迎语管理器初始化完成
2025-07-07 17:22:57 - server_DebugManager - INFO - [27440] - 调试管理器初始化完成
2025-07-07 17:22:57 - server_RoomIdGenerator - INFO - [27440] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 17:22:57 - server_SecurityManager - INFO - [27440] - 已加载现有服务器密钥
2025-07-07 17:22:57 - server_SecurityManager - INFO - [27440] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:22:57 - server_SecureProtocol - INFO - [27440] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:22:57 - server_MessageManager - INFO - [27440] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:22:57 - server_RoomManager - INFO - [27440] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 17:22:57 - server_SecurityManager - INFO - [27440] - 已加载现有服务器密钥
2025-07-07 17:22:57 - server_SecurityManager - INFO - [27440] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:22:57 - server_SecureProtocol - INFO - [27440] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:22:57 - server_SecurityManager - INFO - [27440] - 已加载现有服务器密钥
2025-07-07 17:22:57 - server_SecurityManager - INFO - [27440] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:22:57 - server_SecureProtocol - INFO - [27440] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:22:57 - server_MessageManager - INFO - [27440] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:22:57 - server_MessageProcessor - INFO - [27440] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 17:22:57 - server_DebugManager - INFO - [27440] - 调试模式已启用，模块: ['ai', 'security', 'network', 'user_actions', 'performance', 'database', 'game_logic', 'world_gen']
2025-07-07 17:22:57 - server_DebugManager - INFO - [27440] - 详细模式已启用
2025-07-07 17:22:57 - server_DebugManager - INFO - [27440] - 日志级别设置为: INFO
2025-07-07 17:24:10 - server_WelcomeManager - INFO - [28232] - 欢迎语管理器初始化完成
2025-07-07 17:24:10 - server_DebugManager - INFO - [28232] - 调试管理器初始化完成
2025-07-07 17:24:10 - server_RoomIdGenerator - INFO - [28232] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 17:24:10 - server_SecurityManager - INFO - [28232] - 已加载现有服务器密钥
2025-07-07 17:24:10 - server_SecurityManager - INFO - [28232] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:24:10 - server_SecureProtocol - INFO - [28232] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:24:10 - server_MessageManager - INFO - [28232] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:24:10 - server_RoomManager - INFO - [28232] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 17:24:10 - server_SecurityManager - INFO - [28232] - 已加载现有服务器密钥
2025-07-07 17:24:10 - server_SecurityManager - INFO - [28232] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:24:10 - server_SecureProtocol - INFO - [28232] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:24:10 - server_SecurityManager - INFO - [28232] - 已加载现有服务器密钥
2025-07-07 17:24:10 - server_SecurityManager - INFO - [28232] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:24:10 - server_SecureProtocol - INFO - [28232] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:24:10 - server_MessageManager - INFO - [28232] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:24:10 - server_MessageProcessor - INFO - [28232] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 17:24:10 - server_DebugManager - INFO - [28232] - 调试模式已启用，模块: ['user_actions', 'network', 'game_logic', 'performance', 'ai', 'security', 'world_gen', 'database']
2025-07-07 17:24:10 - server_DebugManager - INFO - [28232] - 详细模式已启用
2025-07-07 17:24:56 - server_WelcomeManager - INFO - [32108] - 欢迎语管理器初始化完成
2025-07-07 17:24:56 - server_DebugManager - INFO - [32108] - 调试管理器初始化完成
2025-07-07 17:24:56 - server_RoomIdGenerator - INFO - [32108] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 17:24:56 - server_SecurityManager - INFO - [32108] - 已加载现有服务器密钥
2025-07-07 17:24:56 - server_SecurityManager - INFO - [32108] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:24:56 - server_SecureProtocol - INFO - [32108] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:24:56 - server_MessageManager - INFO - [32108] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:24:56 - server_RoomManager - INFO - [32108] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 17:24:56 - server_SecurityManager - INFO - [32108] - 已加载现有服务器密钥
2025-07-07 17:24:56 - server_SecurityManager - INFO - [32108] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:24:56 - server_SecureProtocol - INFO - [32108] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:24:56 - server_SecurityManager - INFO - [32108] - 已加载现有服务器密钥
2025-07-07 17:24:56 - server_SecurityManager - INFO - [32108] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:24:56 - server_SecureProtocol - INFO - [32108] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:24:56 - server_MessageManager - INFO - [32108] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:24:56 - server_MessageProcessor - INFO - [32108] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 17:24:56 - server_DebugManager - INFO - [32108] - 调试模式已启用，模块: ['database', 'world_gen', 'ai', 'performance', 'network', 'user_actions', 'security', 'game_logic']
2025-07-07 17:24:56 - server_DebugManager - INFO - [32108] - 详细模式已启用
2025-07-07 17:25:41 - server_WelcomeManager - INFO - [31764] - 欢迎语管理器初始化完成
2025-07-07 17:25:41 - server_DebugManager - INFO - [31764] - 调试管理器初始化完成
2025-07-07 17:25:41 - server_RoomIdGenerator - INFO - [31764] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 17:25:41 - server_SecurityManager - INFO - [31764] - 已加载现有服务器密钥
2025-07-07 17:25:41 - server_SecurityManager - INFO - [31764] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:25:41 - server_SecureProtocol - INFO - [31764] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:25:41 - server_MessageManager - INFO - [31764] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:25:41 - server_RoomManager - INFO - [31764] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 17:25:41 - server_SecurityManager - INFO - [31764] - 已加载现有服务器密钥
2025-07-07 17:25:41 - server_SecurityManager - INFO - [31764] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:25:41 - server_SecureProtocol - INFO - [31764] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:25:41 - server_SecurityManager - INFO - [31764] - 已加载现有服务器密钥
2025-07-07 17:25:41 - server_SecurityManager - INFO - [31764] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:25:41 - server_SecureProtocol - INFO - [31764] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:25:41 - server_MessageManager - INFO - [31764] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:25:41 - server_MessageProcessor - INFO - [31764] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 17:25:41 - server_DebugManager - INFO - [31764] - 调试模式已启用，模块: ['world_gen', 'game_logic', 'database', 'user_actions', 'security', 'ai', 'performance', 'network']
2025-07-07 17:25:41 - server_DebugManager - INFO - [31764] - 详细模式已启用
2025-07-07 17:26:05 - server_WelcomeManager - INFO - [10744] - 欢迎语管理器初始化完成
2025-07-07 17:26:05 - server_DebugManager - INFO - [10744] - 调试管理器初始化完成
2025-07-07 17:26:05 - server_RoomIdGenerator - INFO - [10744] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 17:26:05 - server_SecurityManager - INFO - [10744] - 已加载现有服务器密钥
2025-07-07 17:26:05 - server_SecurityManager - INFO - [10744] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:26:05 - server_SecureProtocol - INFO - [10744] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:26:05 - server_MessageManager - INFO - [10744] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:26:05 - server_RoomManager - INFO - [10744] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 17:26:05 - server_SecurityManager - INFO - [10744] - 已加载现有服务器密钥
2025-07-07 17:26:05 - server_SecurityManager - INFO - [10744] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:26:05 - server_SecureProtocol - INFO - [10744] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:26:05 - server_SecurityManager - INFO - [10744] - 已加载现有服务器密钥
2025-07-07 17:26:05 - server_SecurityManager - INFO - [10744] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:26:05 - server_SecureProtocol - INFO - [10744] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:26:05 - server_MessageManager - INFO - [10744] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:26:05 - server_MessageProcessor - INFO - [10744] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 17:26:05 - server_DebugManager - INFO - [10744] - 调试模式已启用，模块: ['world_gen', 'database', 'security', 'network', 'user_actions', 'ai', 'game_logic', 'performance']
2025-07-07 17:26:05 - server_DebugManager - INFO - [10744] - 详细模式已启用
2025-07-07 17:33:18 - server_WelcomeManager - INFO - [38204] - 欢迎语管理器初始化完成
2025-07-07 17:33:18 - server_DebugManager - INFO - [38204] - 调试管理器初始化完成
2025-07-07 17:33:18 - server_RoomIdGenerator - INFO - [38204] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-07 17:33:18 - server_SecurityManager - INFO - [38204] - 已加载现有服务器密钥
2025-07-07 17:33:18 - server_SecurityManager - INFO - [38204] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:33:18 - server_SecureProtocol - INFO - [38204] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:33:18 - server_MessageManager - INFO - [38204] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:33:18 - server_RoomManager - INFO - [38204] - 房间管理器初始化完成 / Room manager initialized
2025-07-07 17:33:18 - server_SecurityManager - INFO - [38204] - 已加载现有服务器密钥
2025-07-07 17:33:18 - server_SecurityManager - INFO - [38204] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:33:18 - server_SecureProtocol - INFO - [38204] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:33:18 - server_SecurityManager - INFO - [38204] - 已加载现有服务器密钥
2025-07-07 17:33:18 - server_SecurityManager - INFO - [38204] - 安全管理器初始化完成 - 服务器模式
2025-07-07 17:33:18 - server_SecureProtocol - INFO - [38204] - 安全协议初始化完成 - 服务器模式
2025-07-07 17:33:18 - server_MessageManager - INFO - [38204] - 消息管理器初始化完成 - 服务器模式
2025-07-07 17:33:18 - server_MessageProcessor - INFO - [38204] - 消息处理器初始化完成 / Message processor initialized
2025-07-07 17:33:18 - server_DebugManager - INFO - [38204] - 调试模式已启用，模块: ['database', 'network', 'user_actions', 'ai', 'game_logic', 'security', 'performance', 'world_gen']
2025-07-07 17:33:18 - server_DebugManager - INFO - [38204] - 详细模式已启用
2025-07-07 17:33:18 - server_DebugManager - INFO - [38204] - 日志级别设置为: INFO
2025-07-11 08:45:26 - server_UsernameManager - INFO - [12992] - 用户名管理器初始化完成
2025-07-11 08:45:26 - server_SessionManager - INFO - [12992] - 线程安全会话管理器初始化完成 / Thread-safe session manager initialized
2025-07-11 08:45:26 - server_WelcomeManager - INFO - [12992] - 欢迎语管理器初始化完成
2025-07-11 08:45:26 - server_DebugManager - INFO - [12992] - 调试管理器初始化完成
2025-07-11 08:45:26 - server_RoomIdGenerator - INFO - [12992] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-11 08:45:26 - server_SecurityManager - INFO - [12992] - 已加载现有服务器密钥
2025-07-11 08:45:26 - server_SecurityManager - INFO - [12992] - 安全管理器初始化完成 - 服务器模式
2025-07-11 08:45:26 - server_SecureProtocol - INFO - [12992] - 安全协议初始化完成 - 服务器模式
2025-07-11 08:45:26 - server_MessageManager - INFO - [12992] - 消息管理器初始化完成 - 服务器模式
2025-07-11 08:45:26 - server_RoomManager - INFO - [12992] - 房间管理器初始化完成 / Room manager initialized
2025-07-11 08:45:26 - server_SecurityManager - INFO - [12992] - 已加载现有服务器密钥
2025-07-11 08:45:26 - server_SecurityManager - INFO - [12992] - 安全管理器初始化完成 - 服务器模式
2025-07-11 08:45:26 - server_SecureProtocol - INFO - [12992] - 安全协议初始化完成 - 服务器模式
2025-07-11 08:45:26 - server_SecurityManager - INFO - [12992] - 已加载现有服务器密钥
2025-07-11 08:45:26 - server_SecurityManager - INFO - [12992] - 安全管理器初始化完成 - 服务器模式
2025-07-11 08:45:26 - server_SecureProtocol - INFO - [12992] - 安全协议初始化完成 - 服务器模式
2025-07-11 08:45:26 - server_MessageManager - INFO - [12992] - 消息管理器初始化完成 - 服务器模式
2025-07-11 08:45:26 - server_MessageProcessor - INFO - [12992] - 消息处理器初始化完成 / Message processor initialized
2025-07-11 08:45:26 - server_DebugManager - INFO - [12992] - 日志级别设置为: INFO
2025-07-11 08:45:34 - server_SessionManager - INFO - [26016] - 创建会话: 2ee254ca-d5ab-4fe0-acfc-e7fefa52cbed 来自 ('127.0.0.1', 17259)
2025-07-11 08:45:50 - server_UsernameManager - INFO - [26016] - 预留用户名: sweatent
2025-07-11 08:45:50 - server_SessionManager - INFO - [26016] - 会话 2ee254ca-d5ab-4fe0-acfc-e7fefa52cbed 成功认证为玩家 sweatent
2025-07-11 08:46:03 - server_MessageProcessor - INFO - [26016] - 收到客户端消息: create_room
2025-07-11 08:46:03 - server_MessageProcessor - INFO - [26016] - 开始创建房间 - 玩家: sweatent, 房间名: test1, 最大玩家: 8, 模式: ai_generated, 难度: normal
2025-07-11 08:46:03 - server_RoomManager - INFO - [26016] - 创建房间: test1 (ID: ROOM4763MV0, 类型: alphanumeric)
2025-07-11 08:46:03 - server_MessageProcessor - INFO - [26016] - 房间创建成功 - ID: ROOM4763MV0, 名称: test1
2025-07-11 08:46:03 - server_MessageProcessor - INFO - [26016] - 房主 sweatent 尝试加入房间 ROOM4763MV0
2025-07-11 08:46:03 - server_MessageProcessor - INFO - [26016] - 房主成功加入房间 - 玩家: sweatent, 房间: ROOM4763MV0
2025-07-11 08:46:03 - server_MessageProcessor - INFO - [26016] - 发送房间创建响应 - 数据: {'success': True, 'room_info': {'room_id': 'ROOM4763MV0', 'room_name': 'test1', 'is_host': True, 'max_players': 8, 'game_mode': 'ai_generated', 'difficulty': 'normal', 'is_private': False, 'status': 'waiting', 'host_player': '2ee254ca-d5ab-4fe0-acfc-e7fefa52cbed'}}
2025-07-11 08:46:03 - server_SecureProtocol - ERROR - [26016] - 握手未完成，无法发送安全消息
2025-07-11 08:46:03 - server_MessageManager - ERROR - [26016] - 发送安全消息失败: room_created
2025-07-11 08:46:03 - server_MessageProcessor - INFO - [26016] - ✅ 房间创建完成 - 玩家: sweatent, 房间ID: ROOM4763MV0, 模式: ai_generated
2025-07-11 12:15:34 - server_UsernameManager - INFO - [26016] - 释放用户名: sweatent
2025-07-11 12:15:34 - server_SessionManager - INFO - [26016] - 移除会话: 2ee254ca-d5ab-4fe0-acfc-e7fefa52cbed
