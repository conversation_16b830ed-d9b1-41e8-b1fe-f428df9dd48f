#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资源管理器
Resource Manager

提供内存、连接和系统资源的统一管理
Provides unified management of memory, connections and system resources
"""

import psutil
import threading
import time
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict, deque
from dataclasses import dataclass

from shared.structured_logger import get_structured_logger, LogCategory

@dataclass
class MemoryBlock:
    """内存块信息"""
    block_id: str
    name: str
    size_bytes: int
    allocated_at: float
    last_accessed: float
    access_count: int

@dataclass
class ConnectionInfo:
    """连接信息"""
    connection_id: str
    peer_address: str
    port: int
    created_at: float
    last_activity: float
    bytes_sent: int
    bytes_received: int
    status: str

class ResourceManager:
    """资源管理器"""
    
    def __init__(self):
        """初始化资源管理器"""
        self.logger = get_structured_logger("resource_manager")
        
        # 内存池管理
        self.memory_pool: Dict[str, MemoryBlock] = {}
        self.memory_lock = threading.Lock()
        
        # 连接池管理
        self.connection_pool: Dict[str, ConnectionInfo] = {}
        self.connection_lock = threading.Lock()
        
        # 资源限制
        self.resource_limits = {
            'max_memory_mb': 1024,  # 最大内存使用 1GB
            'max_connections': 100,  # 最大连接数
            'max_cpu_percent': 80,   # 最大CPU使用率
            'max_disk_usage_percent': 90  # 最大磁盘使用率
        }
        
        # 使用统计
        self.usage_stats = {
            'memory_allocations': 0,
            'memory_deallocations': 0,
            'connections_created': 0,
            'connections_closed': 0,
            'resource_limit_violations': 0
        }
        
        # 监控相关
        self.monitoring_active = False
        self.monitoring_thread = None
        self.monitoring_interval = 5.0
        self.monitoring_history = deque(maxlen=1000)
        
        # 性能指标
        self.performance_metrics = defaultdict(list)
        self.metrics_lock = threading.Lock()
        
        self.logger.info(LogCategory.SYSTEM, "资源管理器初始化完成")
    
    def allocate_memory_block(self, name: str, size_bytes: int) -> Optional[str]:
        """
        分配内存块
        
        Args:
            name: 内存块名称
            size_bytes: 大小（字节）
            
        Returns:
            内存块ID，失败返回None
        """
        size_mb = size_bytes / (1024 * 1024)
        
        # 检查内存限制
        current_usage = self.get_memory_usage()
        if current_usage['total_allocated_mb'] + size_mb > self.resource_limits['max_memory_mb']:
            self.logger.warning(
                LogCategory.SYSTEM,
                f"内存分配失败：超出限制 ({current_usage['total_allocated_mb'] + size_mb:.2f}MB > {self.resource_limits['max_memory_mb']}MB)",
                data={'requested_size_mb': size_mb, 'current_usage_mb': current_usage['total_allocated_mb']}
            )
            self.usage_stats['resource_limit_violations'] += 1
            return None
        
        block_id = str(uuid.uuid4())
        current_time = time.time()
        
        memory_block = MemoryBlock(
            block_id=block_id,
            name=name,
            size_bytes=size_bytes,
            allocated_at=current_time,
            last_accessed=current_time,
            access_count=1
        )
        
        with self.memory_lock:
            self.memory_pool[block_id] = memor
        """清空对象池"""
        with self.lock:
            while self.pool:
                obj = self.pool.popleft()
                if self.cleanup_func:
                    try:
                        self.cleanup_func(obj)
                    except Exception as e:
                        self.logger.error(f"清理对象时发生错误: {e}")
                self.stats.total_destroyed += 1
    
    def get_stats(self) -> ResourceStats:
        """获取统计信息"""
        with self.lock:
            self.stats.memory_usage_mb = self._estimate_memory_usage()
            return self.stats
    
    def _estimate_memory_usage(self) -> float:
        """估算内存使用量"""
        try:
            # 简单估算：每个对象平均1KB
            return len(self.pool) * 1.0 / 1024  # MB
        except:
            return 0.0


class ConnectionPool:
    """连接池管理器"""
    
    def __init__(self, config: ConnectionPoolConfig):
        """初始化连接池"""
        self.config = config
        self.available_connections: queue.Queue = queue.Queue()
        self.active_connections: Set[socket.socket] = set()
        self.connection_times: Dict[socket.socket, float] = {}
        self.lock = threading.RLock()
        self.stats = ResourceStats()
        self.logger = get_server_logger("ConnectionPool")
        
        # 启动清理线程
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleanup_thread.start()
        
        # 预创建最小连接数
        self._ensure_min_connections()
    
    def _ensure_min_connections(self):
        """确保最小连接数"""
        with self.lock:
            current_count = self.available_connections.qsize() + len(self.active_connections)
            needed = self.config.min_connections - current_count
            
            for _ in range(needed):
                try:
                    conn = self._create_connection()
                    if conn:
                        self.available_connections.put(conn)
                        self.stats.total_created += 1
                except Exception as e:
                    self.logger.error(f"创建连接时发生错误: {e}")
                    break
    
    def _create_connection(self) -> Optional[socket.socket]:
        """创建新连接"""
        try:
            # 这里创建一个占位符连接对象
            # 实际实现中应该根据具体需求创建真实连接
            conn = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            conn.settimeout(self.config.connection_timeout)
            return conn
        except Exception as e:
            self.logger.error(f"创建连接失败: {e}")
            return None
    
    def acquire_connection(self) -> Optional[socket.socket]:
        """获取连接"""
        try:
            # 尝试从池中获取可用连接
            conn = self.available_connections.get_nowait()
            
            with self.lock:
                self.active_connections.add(conn)
                self.connection_times[conn] = time.time()
                self.stats.currently_active += 1
                self.stats.pool_hits += 1
                
                # 更新峰值使用量
                if self.stats.currently_active > self.stats.peak_usage:
                    self.stats.peak_usage = self.stats.currently_active
            
            return conn
            
        except queue.Empty:
            # 池中没有可用连接，检查是否可以创建新连接
            with self.lock:
                total_connections = len(self.active_connections) + self.available_connections.qsize()
                
                if total_connections < self.config.max_connections:
                    conn = self._create_connection()
                    if conn:
                        self.active_connections.add(conn)
                        self.connection_times[conn] = time.time()
                        self.stats.total_created += 1
                        self.stats.currently_active += 1
                        self.stats.pool_misses += 1
                        
                        if self.stats.currently_active > self.stats.peak_usage:
                            self.stats.peak_usage = self.stats.currently_active
                        
                        return conn
                
                # 无法创建新连接
                self.stats.pool_misses += 1
                return None
    
    def release_connection(self, conn: socket.socket):
        """释放连接回池"""
        with self.lock:
            if conn in self.active_connections:
                self.active_connections.remove(conn)
                self.connection_times.pop(conn, None)
                self.stats.currently_active -= 1
                
                # 检查连接是否仍然有效
                if self._is_connection_valid(conn):
                    try:
                        self.available_connections.put_nowait(conn)
                    except queue.Full:
                        # 池已满，关闭连接
                        self._close_connection(conn)
                        self.stats.total_destroyed += 1
                else:
                    # 连接无效，关闭它
                    self._close_connection(conn)
                    self.stats.total_destroyed += 1
    
    def _is_connection_valid(self, conn: socket.socket) -> bool:
        """检查连接是否有效"""
        try:
            # 简单的连接有效性检查
            return conn.fileno() != -1
        except:
            return False
    
    def _close_connection(self, conn: socket.socket):
        """关闭连接"""
        try:
            conn.close()
        except:
            pass
    
    def _cleanup_loop(self):
        """清理循环"""
        while True:
            try:
                time.sleep(self.config.cleanup_interval)
                self._cleanup_idle_connections()
                self._ensure_min_connections()
            except Exception as e:
                self.logger.error(f"连接池清理时发生错误: {e}")
    
    def _cleanup_idle_connections(self):
        """清理空闲连接"""
        current_time = time.time()
        idle_connections = []
        
        # 检查活跃连接中的空闲连接
        with self.lock:
            for conn, start_time in list(self.connection_times.items()):
                if current_time - start_time > self.config.idle_timeout:
                    idle_connections.append(conn)
            
            # 移除空闲连接
            for conn in idle_connections:
                if conn in self.active_connections:
                    self.active_connections.remove(conn)
                    self.connection_times.pop(conn, None)
                    self.stats.currently_active -= 1
                    self._close_connection(conn)
                    self.stats.total_destroyed += 1
        
        if idle_connections:
            self.logger.info(f"清理了 {len(idle_connections)} 个空闲连接")
    
    def get_stats(self) -> ResourceStats:
        """获取统计信息"""
        with self.lock:
            stats = self.stats
            stats.memory_usage_mb = self._estimate_memory_usage()
            stats.last_cleanup_time = time.time()
            return stats
    
    def _estimate_memory_usage(self) -> float:
        """估算内存使用量"""
        try:
            # 估算每个连接占用约10KB内存
            total_connections = len(self.active_connections) + self.available_connections.qsize()
            return total_connections * 10.0 / 1024  # MB
        except:
            return 0.0
    
    def shutdown(self):
        """关闭连接池"""
        with self.lock:
            # 关闭所有活跃连接
            for conn in list(self.active_connections):
                self._close_connection(conn)
            self.active_connections.clear()
            
            # 关闭所有可用连接
            while not self.available_connections.empty():
                try:
                    conn = self.available_connections.get_nowait()
                    self._close_connection(conn)
                except queue.Empty:
                    break
        
        self.logger.info("连接池已关闭")


class ResourceLeakDetector:
    """资源泄漏检测器"""
    
    def __init__(self, check_interval: float = 60.0):
        """初始化资源泄漏检测器"""
        self.check_interval = check_interval
        self.tracked_objects: Dict[str, Set[weakref.ref]] = {}
        self.object_counts: Dict[str, int] = {}
        self.lock = threading.RLock()
        self.logger = get_server_logger("ResourceLeakDetector")
        
        # 启动检测线程
        self.detector_thread = threading.Thread(target=self._detection_loop, daemon=True)
        self.detector_thread.start()
    
    def track_object(self, obj: Any, category: str = "default"):
        """跟踪对象"""
        with self.lock:
            if category not in self.tracked_objects:
                self.tracked_objects[category] = set()
                self.object_counts[category] = 0
            
            # 创建弱引用
            weak_ref = weakref.ref(obj, lambda ref: self._on_object_deleted(ref, category))
            self.tracked_objects[category].add(weak_ref)
            self.object_counts[category] += 1
    
    def _on_object_deleted(self, weak_ref: weakref.ref, category: str):
        """对象被删除时的回调"""
        with self.lock:
            if category in self.tracked_objects:
                self.tracked_objects[category].discard(weak_ref)
                self.object_counts[category] -= 1
    
    def _detection_loop(self):
        """检测循环"""
        while True:
            try:
                time.sleep(self.check_interval)
                self._check_for_leaks()
            except Exception as e:
                self.logger.error(f"资源泄漏检测时发生错误: {e}")
    
    def _check_for_leaks(self):
        """检查资源泄漏"""
        with self.lock:
            for category, refs in self.tracked_objects.items():
                # 清理已删除对象的弱引用
                alive_refs = set()
                for ref in refs:
                    if ref() is not None:
                        alive_refs.add(ref)
                
                self.tracked_objects[category] = alive_refs
                current_count = len(alive_refs)
                
                # 检查是否有潜在泄漏
                if current_count > 100:  # 阈值可配置
                    self.logger.warning(f"检测到潜在资源泄漏: {category} 类别有 {current_count} 个活跃对象")
                
                # 更新计数
                self.object_counts[category] = current_count
    
    def get_leak_report(self) -> Dict[str, Any]:
        """获取泄漏报告"""
        with self.lock:
            report = {}
            for category, count in self.object_counts.items():
                report[category] = {
                    "active_objects": count,
                    "potential_leak": count > 100
                }
            return report
    
    def force_cleanup(self):
        """强制清理"""
        gc.collect()  # 强制垃圾回收
        self._check_for_leaks()


class ConcurrentTaskManager:
    """并发任务管理器"""
    
    def __init__(self, max_workers: int = 10, queue_size: int = 1000):
        """初始化并发任务管理器"""
        self.max_workers = max_workers
        self.queue_size = queue_size
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.task_queue: queue.Queue = queue.Queue(maxsize=queue_size)
        self.active_tasks: Dict[str, Future] = {}
        self.completed_tasks: int = 0
        self.failed_tasks: int = 0
        self.lock = threading.RLock()
        self.logger = get_server_logger("ConcurrentTaskManager")
        
        # 启动任务处理线程
        self.processor_thread = threading.Thread(target=self._process_tasks, daemon=True)
        self.processor_thread.start()
    
    def submit_task(self, task_id: str, func: Callable, *args, **kwargs) -> bool:
        """提交任务"""
        try:
            task_info = {
                "task_id": task_id,
                "func": func,
                "args": args,
                "kwargs": kwargs,
                "submit_time": time.time()
            }
            
            self.task_queue.put_nowait(task_info)
            return True
            
        except queue.Full:
            self.logger.warning(f"任务队列已满，无法提交任务: {task_id}")
            return False
    
    def _process_tasks(self):
        """处理任务"""
        while True:
            try:
                task_info = self.task_queue.get(timeout=1.0)
                
                task_id = task_info["task_id"]
                func = task_info["func"]
                args = task_info["args"]
                kwargs = task_info["kwargs"]
                
                # 提交到线程池执行
                future = self.executor.submit(func, *args, **kwargs)
                
                with self.lock:
                    self.active_tasks[task_id] = future
                
                # 设置完成回调
                future.add_done_callback(lambda f: self._on_task_completed(task_id, f))
                
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"处理任务时发生错误: {e}")
    
    def _on_task_completed(self, task_id: str, future: Future):
        """任务完成回调"""
        with self.lock:
            self.active_tasks.pop(task_id, None)
            
            if future.exception():
                self.failed_tasks += 1
                self.logger.error(f"任务 {task_id} 执行失败: {future.exception()}")
            else:
                self.completed_tasks += 1
    
    def get_task_stats(self) -> Dict[str, Any]:
        """获取任务统计"""
        with self.lock:
            return {
                "active_tasks": len(self.active_tasks),
                "queued_tasks": self.task_queue.qsize(),
                "completed_tasks": self.completed_tasks,
                "failed_tasks": self.failed_tasks,
                "max_workers": self.max_workers,
                "queue_size": self.queue_size
            }
    
    def shutdown(self):
        """关闭任务管理器"""
        self.executor.shutdown(wait=True)
        self.logger.info("并发任务管理器已关闭")


class ResourceManager:
    """资源管理器主类"""
    
    def __init__(self):
        """初始化资源管理器"""
        self.logger = get_server_logger("ResourceManager")
        
        # 组件初始化
        self.connection_pool = ConnectionPool(ConnectionPoolConfig())
        self.leak_detector = ResourceLeakDetector()
        self.task_manager = ConcurrentTaskManager()
        
        # 对象池
        self.object_pools: Dict[str, ObjectPool] = {}
        
        # 资源监控
        self.monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.monitor_interval = 30.0
        
        # 统计信息
        self.start_time = time.time()
        
        self.logger.info("资源管理器初始化完成")
    
    def create_object_pool(self, name: str, factory: Callable[[], T], 
                          max_size: int = 100, 
                          cleanup_func: Optional[Callable[[T], None]] = None) -> ObjectPool[T]:
        """创建对象池"""
        pool = ObjectPool(factory, max_size, cleanup_func)
        self.object_pools[name] = pool
        self.logger.info(f"创建对象池: {name}, 最大大小: {max_size}")
        return pool
    
    def get_object_pool(self, name: str) -> Optional[ObjectPool]:
        """获取对象池"""
        return self.object_pools.get(name)
    
    def start_monitoring(self, interval: float = 30.0):
        """开始资源监控"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_interval = interval
        
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        self.logger.info(f"资源监控已启动，监控间隔: {interval}秒")
    
    def stop_monitoring(self):
        """停止资源监控"""
        self.monitoring = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5.0)
        self.logger.info("资源监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                self._check_resource_usage()
                time.sleep(self.monitor_interval)
            except Exception as e:
                self.logger.error(f"资源监控时发生错误: {e}")
    
    def _check_resource_usage(self):
        """检查资源使用情况"""
        try:
            # 系统资源检查
            process = psutil.Process()
            memory_info = process.memory_info()
            cpu_percent = process.cpu_percent()
            
            # 连接池统计
            conn_stats = self.connection_pool.get_stats()
            
            # 任务管理器统计
            task_stats = self.task_manager.get_task_stats()
            
            # 对象池统计
            pool_stats = {}
            for name, pool in self.object_pools.items():
                pool_stats[name] = pool.get_stats()
            
            # 泄漏检测报告
            leak_report = self.leak_detector.get_leak_report()
            
            # 记录资源使用情况
            self.logger.info(
                f"资源使用情况 - "
                f"内存: {memory_info.rss / 1024 / 1024:.1f}MB, "
                f"CPU: {cpu_percent:.1f}%, "
                f"活跃连接: {conn_stats.currently_active}, "
                f"活跃任务: {task_stats['active_tasks']}"
            )
            
            # 检查是否需要警告
            if memory_info.rss / 1024 / 1024 > 500:  # 500MB
                self.logger.warning(f"内存使用量较高: {memory_info.rss / 1024 / 1024:.1f}MB")
            
            if conn_stats.currently_active > 40:
                self.logger.warning(f"活跃连接数较多: {conn_stats.currently_active}")
            
            # 检查资源泄漏
            for category, info in leak_report.items():
                if info["potential_leak"]:
                    self.logger.warning(f"检测到潜在资源泄漏: {category}")
            
        except Exception as e:
            self.logger.error(f"检查资源使用情况时发生错误: {e}")
    
    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """获取综合统计信息"""
        try:
            # 系统信息
            process = psutil.Process()
            memory_info = process.memory_info()
            
            # 运行时间
            uptime = time.time() - self.start_time
            
            stats = {
                "uptime_seconds": uptime,
                "system": {
                    "memory_mb": memory_info.rss / 1024 / 1024,
                    "cpu_percent": process.cpu_percent(),
                    "threads": process.num_threads(),
                    "open_files": len(process.open_files()) if hasattr(process, 'open_files') else 0
                },
                "connection_pool": self.connection_pool.get_stats().__dict__,
                "task_manager": self.task_manager.get_task_stats(),
                "object_pools": {
                    name: pool.get_stats().__dict__ 
                    for name, pool in self.object_pools.items()
                },
                "leak_detection": self.leak_detector.get_leak_report()
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取统计信息时发生错误: {e}")
            return {}
    
    def optimize_resources(self):
        """优化资源使用"""
        try:
            self.logger.info("开始资源优化...")
            
            # 强制垃圾回收
            collected = gc.collect()
            self.logger.info(f"垃圾回收清理了 {collected} 个对象")
            
            # 清理对象池
            for name, pool in self.object_pools.items():
                old_size = len(pool.pool)
                # 这里可以实现更智能的清理策略
                if old_size > pool.max_size // 2:
                    # 清理一半对象
                    for _ in range(old_size // 2):
                        if pool.pool:
                            obj = pool.pool.popleft()
                            if pool.cleanup_func:
                                pool.cleanup_func(obj)
                            pool.stats.total_destroyed += 1
                    
                    new_size = len(pool.pool)
                    self.logger.info(f"对象池 {name} 清理: {old_size} -> {new_size}")
            
            # 强制泄漏检测清理
            self.leak_detector.force_cleanup()
            
            self.logger.info("资源优化完成")
            
        except Exception as e:
            self.logger.error(f"资源优化时发生错误: {e}")
    
    def shutdown(self):
        """关闭资源管理器"""
        self.logger.info("正在关闭资源管理器...")
        
        # 停止监控
        self.stop_monitoring()
        
        # 关闭各个组件
        self.connection_pool.shutdown()
        self.task_manager.shutdown()
        
        # 清理对象池
        for name, pool in self.object_pools.items():
            pool.clear()
            self.logger.info(f"清理对象池: {name}")
        
        self.logger.info("资源管理器已关闭")


# 全局资源管理器实例
_resource_manager: Optional[ResourceManager] = None
_resource_manager_lock = threading.Lock()


def get_resource_manager() -> ResourceManager:
    """获取全局资源管理器实例"""
    global _resource_manager
    
    if _resource_manager is None:
        with _resource_manager_lock:
            if _resource_manager is None:
                _resource_manager = ResourceManager()
    
    return _resource_manager


def shutdown_resource_manager():
    """关闭全局资源管理器"""
    global _resource_manager
    
    if _resource_manager is not None:
        with _resource_manager_lock:
            if _resource_manager is not None:
                _resource_manager.shutdown()
                _resource_manager = None