#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志分析和问题诊断工具
Log Analysis and Problem Diagnosis Tool

提供日志分析、问题诊断和性能监控功能
Provides log analysis, problem diagnosis and performance monitoring functionality
"""

import json
import re
import sys
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict, Counter
from dataclasses import dataclass
import argparse

@dataclass
class LogAnalysisResult:
    """日志分析结果"""
    total_entries: int
    time_range: Tuple[str, str]
    level_distribution: Dict[str, int]
    category_distribution: Dict[str, int]
    error_summary: List[Dict[str, Any]]
    performance_summary: Dict[str, Any]
    network_summary: Dict[str, Any]
    top_errors: List[Dict[str, Any]]
    recommendations: List[str]

class LogAnalyzer:
    """日志分析器"""
    
    def __init__(self, log_dir: Path = None):
        """
        初始化日志分析器
        
        Args:
            log_dir: 日志目录路径
        """
        self.log_dir = log_dir or Path("logs")
        self.patterns = {
            'error_patterns': [
                r'连接.*失败',
                r'超时',
                r'异常',
                r'错误',
                r'失败',
                r'Exception',
                r'Error',
                r'Failed',
                r'Timeout'
            ],
            'performance_patterns': [
                r'duration=(\d+\.?\d*)ms',
                r'响应时间.*?(\d+\.?\d*)ms',
                r'处理时间.*?(\d+\.?\d*)ms'
            ],
            'network_patterns': [
                r'网络(连接|断开|发送|接收)',
                r'客户端.*?(连接|断开)',
                r'消息.*?(发送|接收)'
            ]
        }
    
    def analyze_logs(self, log_type: str = "server", 
                    start_time: datetime = None,
                    end_time: datetime = None,
                    include_structured: bool = True) -> LogAnalysisResult:
        """
        分析日志文件
        
        Args:
            log_type: 日志类型
            start_time: 开始时间
            end_time: 结束时间
            include_structured: 是否包含结构化日志
            
        Returns:
            分析结果
        """
        log_dir = self.log_dir / log_type
        if not log_dir.exists():
            raise FileNotFoundError(f"日志目录不存在: {log_dir}")
        
        # 收集日志条目
        entries = []
        structured_entries = []
        
        # 分析普通日志文件
        for log_file in log_dir.glob("*.log"):
            if log_file.name.startswith("structured_"):
                continue
            entries.extend(self._parse_log_file(log_file, start_time, end_time))
        
        # 分析结构化日志文件
        if include_structured:
            for jsonl_file in log_dir.glob("structured_*.jsonl"):
                structured_entries.extend(self._parse_structured_log_file(jsonl_file, start_time, end_time))
        
        # 执行分析
        return self._perform_analysis(entries, structured_entries)
    
    def _parse_log_file(self, log_file: Path, start_time: datetime = None,
                       end_time: datetime = None) -> List[Dict[str, Any]]:
        """解析普通日志文件"""
        entries = []
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                    
                    entry = self._parse_log_line(line, line_num, log_file.name)
                    if entry and self._is_in_time_range(entry.get('timestamp'), start_time, end_time):
                        entries.append(entry)
        
        except Exception as e:
            print(f"解析日志文件失败 {log_file}: {e}")
        
        return entries
    
    def _parse_structured_log_file(self, jsonl_file: Path, start_time: datetime = None,
                                  end_time: datetime = None) -> List[Dict[str, Any]]:
        """解析结构化日志文件"""
        entries = []
        
        try:
            with open(jsonl_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        entry = json.loads(line)
                        if self._is_in_time_range(entry.get('timestamp'), start_time, end_time):
                            entries.append(entry)
                    except json.JSONDecodeError as e:
                        print(f"解析JSON行失败 {jsonl_file}:{line_num}: {e}")
        
        except Exception as e:
            print(f"解析结构化日志文件失败 {jsonl_file}: {e}")
        
        return entries
    
    def _parse_log_line(self, line: str, line_num: int, filename: str) -> Optional[Dict[str, Any]]:
        """解析日志行"""
        # 标准日志格式: 2025-01-15 10:30:00 - component - LEVEL - [thread] - message
        pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - ([^-]+) - ([^-]+) - (?:\[(\d+)\] - )?(.+)'
        match = re.match(pattern, line)
        
        if match:
            timestamp_str, component, level, thread_id, message = match.groups()
            
            try:
                timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                return {
                    'timestamp': timestamp.isoformat(),
                    'component': component.strip(),
                    'level': level.strip(),
                    'thread_id': thread_id,
                    'message': message.strip(),
                    'source_file': filename,
                    'line_number': line_num,
                    'raw_line': line
                }
            except ValueError:
                pass
        
        # 如果标准格式解析失败，尝试简单格式
        return {
            'timestamp': None,
            'component': 'unknown',
            'level': 'INFO',
            'thread_id': None,
            'message': line,
            'source_file': filename,
            'line_number': line_num,
            'raw_line': line
        }
    
    def _is_in_time_range(self, timestamp_str: str, start_time: datetime = None,
                         end_time: datetime = None) -> bool:
        """检查时间戳是否在指定范围内"""
        if not timestamp_str:
            return True
        
        try:
            if 'T' in timestamp_str:
                timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            else:
                timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
            
            if start_time and timestamp < start_time:
                return False
            if end_time and timestamp > end_time:
                return False
            
            return True
        except (ValueError, TypeError):
            return True
    
    def _perform_analysis(self, entries: List[Dict[str, Any]], 
                         structured_entries: List[Dict[str, Any]]) -> LogAnalysisResult:
        """执行日志分析"""
        all_entries = entries + structured_entries
        
        if not all_entries:
            return LogAnalysisResult(
                total_entries=0,
                time_range=("", ""),
                level_distribution={},
                category_distribution={},
                error_summary=[],
                performance_summary={},
                network_summary={},
                top_errors=[],
                recommendations=[]
            )
        
        # 基本统计
        total_entries = len(all_entries)
        
        # 时间范围
        timestamps = [e.get('timestamp') for e in all_entries if e.get('timestamp')]
        time_range = (min(timestamps), max(timestamps)) if timestamps else ("", "")
        
        # 级别分布
        level_distribution = Counter(e.get('level', 'UNKNOWN') for e in all_entries)
        
        # 分类分布（仅结构化日志）
        category_distribution = Counter(e.get('category', 'UNKNOWN') for e in structured_entries)
        
        # 错误分析
        error_summary = self._analyze_errors(all_entries)
        top_errors = self._get_top_errors(all_entries)
        
        # 性能分析
        performance_summary = self._analyze_performance(structured_entries)
        
        # 网络分析
        network_summary = self._analyze_network(all_entries, structured_entries)
        
        # 生成建议
        recommendations = self._generate_recommendations(
            level_distribution, error_summary, performance_summary, network_summary
        )
        
        return LogAnalysisResult(
            total_entries=total_entries,
            time_range=time_range,
            level_distribution=dict(level_distribution),
            category_distribution=dict(category_distribution),
            error_summary=error_summary,
            performance_summary=performance_summary,
            network_summary=network_summary,
            top_errors=top_errors,
            recommendations=recommendations
        )
    
    def _analyze_errors(self, entries: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """分析错误"""
        error_entries = [e for e in entries if e.get('level') in ['ERROR', 'CRITICAL']]
        
        error_types = defaultdict(list)
        
        for entry in error_entries:
            message = entry.get('message', '')
            
            # 分类错误类型
            error_type = 'unknown'
            for pattern in self.patterns['error_patterns']:
                if re.search(pattern, message, re.IGNORECASE):
                    error_type = pattern
                    break
            
            error_types[error_type].append(entry)
        
        summary = []
        for error_type, error_list in error_types.items():
            summary.append({
                'type': error_type,
                'count': len(error_list),
                'first_occurrence': error_list[0].get('timestamp'),
                'last_occurrence': error_list[-1].get('timestamp'),
                'sample_messages': [e.get('message', '') for e in error_list[:3]]
            })
        
        return sorted(summary, key=lambda x: x['count'], reverse=True)
    
    def _get_top_errors(self, entries: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """获取最常见的错误"""
        error_entries = [e for e in entries if e.get('level') in ['ERROR', 'CRITICAL']]
        
        error_messages = Counter(e.get('message', '') for e in error_entries)
        
        top_errors = []
        for message, count in error_messages.most_common(10):
            top_errors.append({
                'message': message,
                'count': count,
                'percentage': (count / len(error_entries) * 100) if error_entries else 0
            })
        
        return top_errors
    
    def _analyze_performance(self, structured_entries: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析性能"""
        performance_entries = [
            e for e in structured_entries 
            if e.get('category') == 'performance' or e.get('duration_ms') is not None
        ]
        
        if not performance_entries:
            return {}
        
        durations = []
        for entry in performance_entries:
            duration = entry.get('duration_ms')
            if duration is not None:
                durations.append(duration)
        
        if not durations:
            return {}
        
        durations.sort()
        n = len(durations)
        
        return {
            'total_requests': n,
            'avg_duration_ms': sum(durations) / n,
            'min_duration_ms': min(durations),
            'max_duration_ms': max(durations),
            'p50_duration_ms': durations[n // 2],
            'p95_duration_ms': durations[int(n * 0.95)] if n > 20 else durations[-1],
            'p99_duration_ms': durations[int(n * 0.99)] if n > 100 else durations[-1],
            'slow_requests': len([d for d in durations if d > 1000])  # >1秒的请求
        }
    
    def _analyze_network(self, entries: List[Dict[str, Any]], 
                        structured_entries: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析网络通信"""
        # 从结构化日志中获取网络事件
        network_entries = [e for e in structured_entries if e.get('category') == 'network']
        
        # 从普通日志中查找网络相关消息
        network_messages = []
        for entry in entries:
            message = entry.get('message', '')
            for pattern in self.patterns['network_patterns']:
                if re.search(pattern, message, re.IGNORECASE):
                    network_messages.append(entry)
                    break
        
        total_network_events = len(network_entries) + len(network_messages)
        
        if total_network_events == 0:
            return {}
        
        # 分析网络事件类型
        event_types = Counter()
        connection_events = 0
        error_events = 0
        
        for entry in network_entries:
            data = entry.get('data', {})
            event_type = data.get('event_type', 'unknown')
            event_types[event_type] += 1
            
            if event_type in ['connect', 'disconnect']:
                connection_events += 1
            
            if not data.get('success', True):
                error_events += 1
        
        return {
            'total_events': total_network_events,
            'structured_events': len(network_entries),
            'connection_events': connection_events,
            'error_events': error_events,
            'error_rate': (error_events / total_network_events * 100) if total_network_events > 0 else 0,
            'event_types': dict(event_types)
        }
    
    def _generate_recommendations(self, level_distribution: Dict[str, int],
                                error_summary: List[Dict[str, Any]],
                                performance_summary: Dict[str, Any],
                                network_summary: Dict[str, Any]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 错误率检查
        total_logs = sum(level_distribution.values())
        error_count = level_distribution.get('ERROR', 0) + level_distribution.get('CRITICAL', 0)
        
        if total_logs > 0:
            error_rate = error_count / total_logs * 100
            if error_rate > 10:
                recommendations.append(f"错误率过高 ({error_rate:.1f}%)，建议检查系统稳定性")
            elif error_rate > 5:
                recommendations.append(f"错误率较高 ({error_rate:.1f}%)，建议关注错误处理")
        
        # 性能检查
        if performance_summary:
            avg_duration = performance_summary.get('avg_duration_ms', 0)
            p95_duration = performance_summary.get('p95_duration_ms', 0)
            slow_requests = performance_summary.get('slow_requests', 0)
            
            if avg_duration > 500:
                recommendations.append(f"平均响应时间过长 ({avg_duration:.1f}ms)，建议优化性能")
            
            if p95_duration > 2000:
                recommendations.append(f"95%响应时间过长 ({p95_duration:.1f}ms)，存在性能瓶颈")
            
            if slow_requests > 0:
                recommendations.append(f"发现 {slow_requests} 个慢请求 (>1秒)，建议优化")
        
        # 网络检查
        if network_summary:
            error_rate = network_summary.get('error_rate', 0)
            if error_rate > 5:
                recommendations.append(f"网络错误率过高 ({error_rate:.1f}%)，建议检查网络连接")
        
        # 错误模式检查
        for error in error_summary[:3]:  # 检查前3个最常见错误
            if error['count'] > 10:
                recommendations.append(f"频繁出现错误: {error['type']} ({error['count']}次)")
        
        if not recommendations:
            recommendations.append("系统运行状态良好，未发现明显问题")
        
        return recommendations
    
    def generate_report(self, result: LogAnalysisResult, output_file: Path = None) -> str:
        """生成分析报告"""
        report_lines = [
            "=" * 60,
            "日志分析报告",
            "=" * 60,
            "",
            f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"日志条目总数: {result.total_entries:,}",
            f"时间范围: {result.time_range[0]} 到 {result.time_range[1]}",
            "",
            "日志级别分布:",
            "-" * 20
        ]
        
        for level, count in sorted(result.level_distribution.items()):
            percentage = count / result.total_entries * 100 if result.total_entries > 0 else 0
            report_lines.append(f"  {level}: {count:,} ({percentage:.1f}%)")
        
        if result.category_distribution:
            report_lines.extend([
                "",
                "日志分类分布:",
                "-" * 20
            ])
            for category, count in sorted(result.category_distribution.items()):
                percentage = count / len([e for e in [result] if hasattr(e, 'category_distribution')]) * 100
                report_lines.append(f"  {category}: {count:,}")
        
        if result.error_summary:
            report_lines.extend([
                "",
                "错误分析:",
                "-" * 20
            ])
            for error in result.error_summary[:5]:
                report_lines.append(f"  类型: {error['type']}")
                report_lines.append(f"    数量: {error['count']}")
                report_lines.append(f"    首次出现: {error['first_occurrence']}")
                report_lines.append(f"    最后出现: {error['last_occurrence']}")
                report_lines.append("")
        
        if result.performance_summary:
            report_lines.extend([
                "",
                "性能分析:",
                "-" * 20
            ])
            perf = result.performance_summary
            report_lines.extend([
                f"  总请求数: {perf.get('total_requests', 0):,}",
                f"  平均响应时间: {perf.get('avg_duration_ms', 0):.2f}ms",
                f"  最小响应时间: {perf.get('min_duration_ms', 0):.2f}ms",
                f"  最大响应时间: {perf.get('max_duration_ms', 0):.2f}ms",
                f"  P50响应时间: {perf.get('p50_duration_ms', 0):.2f}ms",
                f"  P95响应时间: {perf.get('p95_duration_ms', 0):.2f}ms",
                f"  P99响应时间: {perf.get('p99_duration_ms', 0):.2f}ms",
                f"  慢请求数量: {perf.get('slow_requests', 0)}"
            ])
        
        if result.network_summary:
            report_lines.extend([
                "",
                "网络通信分析:",
                "-" * 20
            ])
            net = result.network_summary
            report_lines.extend([
                f"  总网络事件: {net.get('total_events', 0):,}",
                f"  结构化事件: {net.get('structured_events', 0):,}",
                f"  连接事件: {net.get('connection_events', 0):,}",
                f"  错误事件: {net.get('error_events', 0):,}",
                f"  错误率: {net.get('error_rate', 0):.2f}%"
            ])
            
            if net.get('event_types'):
                report_lines.append("  事件类型分布:")
                for event_type, count in net['event_types'].items():
                    report_lines.append(f"    {event_type}: {count}")
        
        if result.top_errors:
            report_lines.extend([
                "",
                "最常见错误 (前5个):",
                "-" * 20
            ])
            for i, error in enumerate(result.top_errors[:5], 1):
                report_lines.append(f"  {i}. {error['message'][:100]}...")
                report_lines.append(f"     出现次数: {error['count']} ({error['percentage']:.1f}%)")
                report_lines.append("")
        
        if result.recommendations:
            report_lines.extend([
                "",
                "优化建议:",
                "-" * 20
            ])
            for i, rec in enumerate(result.recommendations, 1):
                report_lines.append(f"  {i}. {rec}")
        
        report_lines.extend([
            "",
            "=" * 60,
            "报告结束",
            "=" * 60
        ])
        
        report_text = "\n".join(report_lines)
        
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(report_text)
                print(f"报告已保存到: {output_file}")
            except Exception as e:
                print(f"保存报告失败: {e}")
        
        return report_text

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='日志分析工具')
    parser.add_argument('--log-type', default='server', choices=['server', 'client'],
                       help='日志类型')
    parser.add_argument('--log-dir', type=Path, help='日志目录路径')
    parser.add_argument('--start-time', help='开始时间 (YYYY-MM-DD HH:MM:SS)')
    parser.add_argument('--end-time', help='结束时间 (YYYY-MM-DD HH:MM:SS)')
    parser.add_argument('--output', type=Path, help='输出报告文件路径')
    parser.add_argument('--no-structured', action='store_true', help='不包含结构化日志')
    
    args = parser.parse_args()
    
    # 解析时间参数
    start_time = None
    end_time = None
    
    if args.start_time:
        try:
            start_time = datetime.strptime(args.start_time, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            print(f"无效的开始时间格式: {args.start_time}")
            return 1
    
    if args.end_time:
        try:
            end_time = datetime.strptime(args.end_time, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            print(f"无效的结束时间格式: {args.end_time}")
            return 1
    
    # 创建分析器
    analyzer = LogAnalyzer(args.log_dir)
    
    try:
        # 执行分析
        print("正在分析日志...")
        result = analyzer.analyze_logs(
            log_type=args.log_type,
            start_time=start_time,
            end_time=end_time,
            include_structured=not args.no_structured
        )
        
        # 生成报告
        report = analyzer.generate_report(result, args.output)
        
        if not args.output:
            print(report)
        
        return 0
        
    except Exception as e:
        print(f"分析失败: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())