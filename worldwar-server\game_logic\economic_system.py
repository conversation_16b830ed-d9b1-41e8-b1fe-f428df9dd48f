#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
经济系统模块
Economic System Module
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
import random
import math

@dataclass
class EconomicIndicators:
    """经济指标"""
    gdp: float = 0.0                    # 国内生产总值
    gdp_growth_rate: float = 0.0        # GDP增长率
    per_capita_income: float = 0.0      # 人均收入
    unemployment_rate: float = 0.1      # 失业率
    inflation_rate: float = 0.02        # 通胀率
    poverty_index: float = 0.5          # 贫困指数
    education_level: float = 0.3        # 教育水平
    health_level: float = 0.4           # 健康水平
    infrastructure_level: float = 0.2   # 基础设施水平
    
    def calculate_development_index(self) -> float:
        """计算综合发展指数"""
        # 综合考虑各项指标
        development_score = (
            (1 - self.poverty_index) * 0.3 +
            self.education_level * 0.25 +
            self.health_level * 0.2 +
            self.infrastructure_level * 0.15 +
            (1 - self.unemployment_rate) * 0.1
        )
        return max(0.0, min(1.0, development_score))

@dataclass
class IndustryData:
    """产业数据"""
    agriculture: float = 0.3            # 农业占比
    manufacturing: float = 0.2          # 制造业占比
    services: float = 0.3               # 服务业占比
    technology: float = 0.1             # 高科技产业占比
    tourism: float = 0.1                # 旅游业占比
    
    def get_total_ratio(self) -> float:
        """获取总占比"""
        return self.agriculture + self.manufacturing + self.services + self.technology + self.tourism
    
    def normalize(self):
        """标准化占比"""
        total = self.get_total_ratio()
        if total > 0:
            self.agriculture /= total
            self.manufacturing /= total
            self.services /= total
            self.technology /= total
            self.tourism /= total

@dataclass
class TerritoryEconomy:
    """领土经济"""
    territory_id: str
    territory_name: str
    owner: Optional[str] = None
    population: int = 100000
    
    # 经济指标
    indicators: EconomicIndicators = field(default_factory=EconomicIndicators)
    
    # 产业结构
    industries: IndustryData = field(default_factory=IndustryData)
    
    # 资源和收入
    tax_income: float = 0.0             # 税收收入
    trade_income: float = 0.0           # 贸易收入
    resource_income: float = 0.0        # 资源收入
    total_income: float = 0.0           # 总收入
    
    # 支出
    government_spending: float = 0.0    # 政府支出
    infrastructure_spending: float = 0.0 # 基础设施支出
    education_spending: float = 0.0     # 教育支出
    healthcare_spending: float = 0.0    # 医疗支出
    
    # 建筑和设施
    buildings: List[str] = field(default_factory=list)
    universities: int = 0               # 大学数量
    hospitals: int = 0                  # 医院数量
    factories: int = 0                  # 工厂数量
    tech_parks: int = 0                 # 科技园区数量
    
    # 事件效果
    event_modifiers: List[Dict[str, Any]] = field(default_factory=list)

    def calculate_gdp(self) -> float:
        """计算GDP"""
        base_gdp = self.population * self.indicators.per_capita_income
        
        industry_multiplier = (
            self.industries.agriculture * 1.0 +
            self.industries.manufacturing * 1.5 +
            self.industries.services * 1.2 +
            self.industries.technology * 2.0 +
            self.industries.tourism * 1.3
        )
        
        infrastructure_bonus = 1 + (self.indicators.infrastructure_level * 0.5)
        education_bonus = 1 + (self.indicators.education_level * 0.3)
        
        # 应用事件修正
        gdp_event_modifier = 1.0
        for mod in self.event_modifiers:
            gdp_event_modifier += mod.get("modifiers", {}).get("gdp_modifier", 0)

        self.indicators.gdp = base_gdp * industry_multiplier * infrastructure_bonus * education_bonus * gdp_event_modifier
        return self.indicators.gdp
    
    def calculate_income(self) -> float:
        """计算总收入"""
        tax_rate = 0.15 + (self.indicators.infrastructure_level * 0.1)
        self.tax_income = self.indicators.gdp * tax_rate
        
        # 应用事件修正
        income_event_modifier = 1.0
        for mod in self.event_modifiers:
            income_event_modifier += mod.get("modifiers", {}).get("income_modifier", 0)
        self.tax_income *= income_event_modifier
        
        # 贸易收入基于连通性和产业结构
        trade_multiplier = (self.industries.manufacturing + self.industries.services) * 0.5
        self.trade_income = self.indicators.gdp * trade_multiplier * 0.1
        
        # 资源收入（将来从资源系统获取）
        self.resource_income = 0  # 暂时设为0
        
        self.total_income = self.tax_income + self.trade_income + self.resource_income
        return self.total_income
    
    def update_economic_indicators(self, policy_effects: List[Any] = None):
        """更新经济指标"""
        # 更新并应用事件效果
        self._update_event_modifiers()

        # 应用政策效果
        if policy_effects:
            self._apply_policy_effects(policy_effects)
        
        # 计算GDP和收入
        self.calculate_gdp()
        self.calculate_income()
        
        # 更新人均收入
        if self.population > 0:
            self.indicators.per_capita_income = self.indicators.gdp / self.population
        
        # 更新其他指标
        self._update_unemployment()
        self._update_poverty_index()
        self._update_growth_rate()
    
    def _apply_policy_effects(self, policy_effects: List[Any]):
        """应用政策效果"""
        for effect in policy_effects:
            if effect.target == "territory":
                self._apply_single_effect(effect)
    
    def _apply_single_effect(self, effect):
        """应用单个政策效果"""
        effect_type = effect.effect_type
        value = effect.value
        
        if effect_type == "gdp_growth":
            self.indicators.gdp_growth_rate += value
        elif effect_type == "education_level":
            self.indicators.education_level = min(1.0, self.indicators.education_level + value)
        elif effect_type == "health_level":
            self.indicators.health_level = min(1.0, self.indicators.health_level + value)
        elif effect_type == "infrastructure_level":
            self.indicators.infrastructure_level = min(1.0, self.indicators.infrastructure_level + value)
        elif effect_type == "unemployment":
            self.indicators.unemployment_rate = max(0.0, self.indicators.unemployment_rate + value)
        elif effect_type == "poverty_index":
            self.indicators.poverty_index = max(0.0, self.indicators.poverty_index + value)
        elif effect_type == "tech_level":
            # 科技等级提升影响高科技产业
            self.industries.technology = min(0.5, self.industries.technology + value * 0.1)
            self.industries.normalize()
    
    def _update_event_modifiers(self):
        """更新事件修正的持续时间并移除过期项"""
        # 应用发展指数修正
        development_modifier = 0.0
        for mod in self.event_modifiers:
            development_modifier += mod.get("modifiers", {}).get("development_modifier", 0)
        
        if development_modifier != 0:
            # 确保发展指数在合理范围内
            current_dev_index = self.indicators.calculate_development_index()
            new_dev_index = max(0, min(1, current_dev_index + development_modifier))
            # 注意：这里只是一个简化的应用方式，实际可能需要更复杂的逻辑来调整基础指标
            self.indicators.poverty_index = max(0, min(1, self.indicators.poverty_index - development_modifier))


        # 更新持续时间并移除过期效果
        self.event_modifiers = [
            mod for mod in self.event_modifiers if mod.get("duration", 0) - 1 > 0
        ]
        for mod in self.event_modifiers:
            mod["duration"] -= 1
    
    def _update_unemployment(self):
        """更新失业率"""
        # 基础失业率受教育水平和产业结构影响
        base_unemployment = 0.15
        
        # 教育水平降低失业率
        education_effect = self.indicators.education_level * 0.1
        
        # 制造业和服务业提供就业
        industry_effect = (self.industries.manufacturing + self.industries.services) * 0.05
        
        self.indicators.unemployment_rate = max(0.02, base_unemployment - education_effect - industry_effect)
    
    def _update_poverty_index(self):
        """更新贫困指数"""
        # 贫困指数受人均收入、失业率、教育水平影响
        income_effect = min(0.3, self.indicators.per_capita_income / 50000)  # 假设50000为富裕线
        unemployment_effect = self.indicators.unemployment_rate * 0.5
        education_effect = self.indicators.education_level * 0.2
        
        self.indicators.poverty_index = max(0.0, 
            0.8 - income_effect + unemployment_effect - education_effect)
    
    def _update_growth_rate(self):
        """更新GDP增长率"""
        # 基础增长率
        base_growth = 0.02
        
        # 基础设施和教育促进增长
        infrastructure_growth = self.indicators.infrastructure_level * 0.03
        education_growth = self.indicators.education_level * 0.02
        
        # 高科技产业促进增长
        tech_growth = self.industries.technology * 0.05
        
        # 随机波动
        random_factor = random.uniform(-0.01, 0.01)
        
        self.indicators.gdp_growth_rate = (base_growth + infrastructure_growth + 
                                         education_growth + tech_growth + random_factor)
    
    def add_building(self, building_type: str):
        """添加建筑"""
        self.buildings.append(building_type)
        
        if building_type == "university":
            self.universities += 1
            self.indicators.education_level = min(1.0, self.indicators.education_level + 0.1)
        elif building_type == "hospital":
            self.hospitals += 1
            self.indicators.health_level = min(1.0, self.indicators.health_level + 0.1)
        elif building_type == "factory":
            self.factories += 1
            self.industries.manufacturing = min(0.6, self.industries.manufacturing + 0.05)
            self.industries.normalize()
        elif building_type == "tech_park":
            self.tech_parks += 1
            self.industries.technology = min(0.4, self.industries.technology + 0.1)
            self.industries.normalize()
    
    def get_economic_summary(self) -> Dict[str, Any]:
        """获取经济摘要"""
        return {
            "territory_id": self.territory_id,
            "territory_name": self.territory_name,
            "population": self.population,
            "gdp": round(self.indicators.gdp, 2),
            "gdp_growth_rate": round(self.indicators.gdp_growth_rate * 100, 2),
            "per_capita_income": round(self.indicators.per_capita_income, 2),
            "unemployment_rate": round(self.indicators.unemployment_rate * 100, 2),
            "poverty_index": round(self.indicators.poverty_index * 100, 2),
            "development_index": round(self.indicators.calculate_development_index() * 100, 2),
            "total_income": round(self.total_income, 2),
            "industries": {
                "agriculture": round(self.industries.agriculture * 100, 1),
                "manufacturing": round(self.industries.manufacturing * 100, 1),
                "services": round(self.industries.services * 100, 1),
                "technology": round(self.industries.technology * 100, 1),
                "tourism": round(self.industries.tourism * 100, 1)
            },
            "buildings": {
                "universities": self.universities,
                "hospitals": self.hospitals,
                "factories": self.factories,
                "tech_parks": self.tech_parks
            }
        }

class EconomicSystem:
    """经济系统管理器"""
    
    def __init__(self, player_manager, game_settings: Dict[str, Any]):
        """初始化经济系统"""
        self.territories: Dict[str, TerritoryEconomy] = {}
        self.player_manager = player_manager
        self.unit_definitions = game_settings.get("units", {})
        
        # 导入日志
        from shared.enhanced_logger import get_server_logger
        self.logger = get_server_logger("EconomicSystem")
        
        self.logger.info("经济系统初始化完成 / Economic system initialized")
    
    def add_territory(self, territory_id: str, territory_name: str,
                     population: int, owner: str, initial_poverty_index: float = 0.5):
        """添加领土"""
        territory = TerritoryEconomy(
            territory_id=territory_id,
            territory_name=territory_name,
            owner=owner,
            population=population
        )
        
        # 设置初始经济指标
        territory.indicators.poverty_index = initial_poverty_index
        territory.indicators.per_capita_income = max(5000, 30000 * (1 - initial_poverty_index))
        territory.indicators.education_level = max(0.1, 0.8 * (1 - initial_poverty_index))
        territory.indicators.health_level = max(0.2, 0.7 * (1 - initial_poverty_index))
        territory.indicators.infrastructure_level = max(0.1, 0.6 * (1 - initial_poverty_index))
        
        # 初始化产业结构
        if initial_poverty_index > 0.7:  # 贫困地区以农业为主
            territory.industries.agriculture = 0.6
            territory.industries.manufacturing = 0.1
            territory.industries.services = 0.2
            territory.industries.technology = 0.05
            territory.industries.tourism = 0.05
        else:  # 相对发达地区
            territory.industries.agriculture = 0.2
            territory.industries.manufacturing = 0.3
            territory.industries.services = 0.4
            territory.industries.technology = 0.08
            territory.industries.tourism = 0.02
        
        territory.industries.normalize()
        
        # 初始计算
        territory.update_economic_indicators()
        
        self.territories[territory_id] = territory
        self.logger.info(f"添加领土: {territory_name} (贫困指数: {initial_poverty_index:.2f})")
    
    def update_all_territories(self, policy_effects_by_territory: Dict[str, List[Any]] = None):
        """更新所有领土的经济状况（通常由政策系统调用）"""
        for territory_id, territory in self.territories.items():
            policy_effects = policy_effects_by_territory.get(territory_id, []) if policy_effects_by_territory else []
            territory.update_economic_indicators(policy_effects)

    def apply_event_modifier_to_territory(self, territory_id: str, modifiers: Dict[str, Any], duration: int):
        """向指定领土应用事件修正"""
        territory = self.get_territory_economy(territory_id)
        if territory:
            event_effect = {
                "modifiers": modifiers,
                "duration": duration
            }
            territory.event_modifiers.append(event_effect)
            self.logger.info(f"向领土 {territory_id} 应用事件效果: {modifiers}，持续 {duration} 回合。")

    def update_all_event_modifiers(self):
        """更新所有领土的事件修正（由GameLoop在回合开始时调用）"""
        for territory in self.territories.values():
            territory._update_event_modifiers()
    
    def get_territory_economy(self, territory_id: str) -> Optional[TerritoryEconomy]:
        """获取领土经济数据"""
        return self.territories.get(territory_id)

    def get_player_territories(self, player_id: str) -> List[str]:
        """获取玩家的所有领土ID"""
        return [
            tid for tid, t in self.territories.items() if t.owner == player_id
        ]
    
    def get_player_economic_summary(self, player_territories: List[str]) -> Dict[str, Any]:
        """获取玩家的经济摘要"""
        if not player_territories:
            return {}
        
        total_gdp = 0
        total_population = 0
        total_income = 0
        avg_poverty = 0
        avg_development = 0
        
        territory_summaries = []
        
        for territory_id in player_territories:
            if territory_id in self.territories:
                territory = self.territories[territory_id]
                summary = territory.get_economic_summary()
                territory_summaries.append(summary)
                
                total_gdp += territory.indicators.gdp
                total_population += territory.population
                total_income += territory.total_income
                avg_poverty += territory.indicators.poverty_index
                avg_development += territory.indicators.calculate_development_index()
        
        territory_count = len(territory_summaries)
        if territory_count == 0:
            return {}
        
        return {
            "total_gdp": round(total_gdp, 2),
            "total_population": total_population,
            "total_income": round(total_income, 2),
            "average_poverty_index": round((avg_poverty / territory_count) * 100, 2),
            "average_development_index": round((avg_development / territory_count) * 100, 2),
            "per_capita_gdp": round(total_gdp / total_population if total_population > 0 else 0, 2),
            "territory_count": territory_count,
            "territories": territory_summaries
        }
    
    def calculate_policy_points(self, player_territories: List[str]) -> int:
        """计算玩家的政策点数"""
        if not player_territories:
            return 0
        
        total_points = 0
        
        for territory_id in player_territories:
            if territory_id in self.territories:
                territory = self.territories[territory_id]
                
                # 基础政策点数
                base_points = 1
                
                # 发展水平加成
                development_bonus = territory.indicators.calculate_development_index() * 2
                
                # 教育水平加成
                education_bonus = territory.indicators.education_level * 1
                
                # 大学加成
                university_bonus = territory.universities * 0.5
                
                territory_points = base_points + development_bonus + education_bonus + university_bonus
                total_points += territory_points
        
        return int(total_points)


    def get_total_territory_count(self) -> int:
        """获取世界上的总领土数量"""
        return len(self.territories)

    def get_player_average_development_index(self, player_id: str) -> float:
        """计算并返回玩家的平均发展指数"""
        player_territories = self.get_player_territories(player_id)
        if not player_territories:
            return 0.0
        
        total_development_index = 0
        for territory_id in player_territories:
            territory = self.territories.get(territory_id)
            if territory:
                total_development_index += territory.indicators.calculate_development_index()
        
        return total_development_index / len(player_territories)

    def produce_unit(self, player_id: str, unit_type: str, quantity: int) -> bool:
        """生产军事单位"""
        player = self.player_manager.get_player(player_id)
        if not player:
            self.logger.warning(f"生产单位失败：找不到玩家 {player_id}")
            return False

        unit_def = self.unit_definitions.get(unit_type)
        if not unit_def:
            self.logger.warning(f"生产单位失败：找不到单位类型 {unit_type}")
            return False

        # 检查资源
        total_cost = {}
        for resource, amount in unit_def.get("cost", {}).items():
            total_cost[resource] = amount * quantity
        
        can_afford = True
        for resource, amount in total_cost.items():
            if player.get_resource(resource) < amount:
                can_afford = False
                self.logger.info(f"玩家 {player_id} 资源不足，无法生产 {quantity} 个 {unit_type} (需要 {amount} {resource})")
                break
        
        if not can_afford:
            return False

        # 扣除资源并添加单位
        for resource, amount in total_cost.items():
            player.consume_resource(resource, amount)
        
        player.military_units[unit_type] = player.military_units.get(unit_type, 0) + quantity
        
        self.logger.info(f"玩家 {player_id} 成功生产了 {quantity} 个 {unit_type}")
        return True
