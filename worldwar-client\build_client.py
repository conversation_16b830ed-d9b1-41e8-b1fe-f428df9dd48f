#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客户端打包脚本
Client Packaging Script
"""

import os
import sys
import shutil
from pathlib import Path

def create_directories():
    """创建必要的目录"""
    print("创建目录结构...")
    directories = [
        "dist/WorldWarClient/config",
        "dist/WorldWarClient/logs",
        "dist/WorldWarClient/logs/client",
        "dist/WorldWarClient/client/languages"
    ]

    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"  OK {directory}")

def copy_resources():
    """复制资源文件"""
    print("\n复制资源文件...")
    resources = [
        ("client/languages/chinese.json", "dist/WorldWarClient/client/languages/"),
        ("client/languages/english.json", "dist/WorldWarClient/client/languages/"),
        ("config/client_config.ini", "dist/WorldWarClient/config/"),
        ("README.md", "dist/WorldWarClient/")
    ]

    for src, dst in resources:
        if os.path.exists(src):
            os.makedirs(os.path.dirname(dst), exist_ok=True)
            shutil.copy2(src, dst)
            print(f"  OK {src} -> {dst}")
        else:
            print(f"  WARNING 文件不存在: {src}")

def create_readme():
    """创建使用说明"""
    print("\n创建使用说明...")
    readme_content = '''# 世界大战策略游戏客户端 / World War Strategy Game Client

## 🚀 使用方法 / Usage

### 启动客户端 / Start Client
1. 确保服务器已启动 (WorldWarServer.exe)
2. 双击 WorldWarClient.exe 启动客户端
3. 首次运行会显示语言选择界面：
   - 1. 中文
   - 2. English
   - 3. Bilingual Mode / 双语模式 (推荐)
4. 选择语言后按任意键继续
5. 在主菜单选择 "加入游戏"
6. 输入服务器地址 (默认: localhost:8888)
7. 输入玩家名称开始游戏

### 退出客户端 / Exit Client
- 按 Ctrl+C 随时退出
- 或在菜单中选择退出选项

## 🎮 游戏功能 / Game Features
- **加入游戏**: 连接到服务器
- **创建房间**: 创建新的游戏房间
- **加入房间**: 加入现有房间
- **聊天系统**: 与其他玩家聊天
- **设置**: 更改语言和其他选项

## ⚙️ 配置文件 / Configuration Files
- `config/client_config.ini` - 客户端主配置
- `config/client_language.json` - 语言设置 (自动生成)

## 📝 日志文件 / Log Files
- `logs/latest.log` - 最新日志
- `logs/client/` - 客户端专用日志
- `logs/game_YYYYMMDD_HHMMSS.log` - 历史日志

## 🌐 连接设置 / Connection Settings
- 默认服务器: localhost:8888
- 支持命令行参数: WorldWarClient.exe --server *************:9999
- 支持调试模式: WorldWarClient.exe --debug

## ⚠️ 注意事项 / Important Notes
- 需要先启动服务器才能连接
- 确保网络连接正常
- 首次运行需要选择语言偏好
- 玩家名称不能重复

## 🔧 故障排除 / Troubleshooting
- 连接失败: 检查服务器是否启动
- 网络错误: 检查防火墙设置
- 查看 logs/latest.log 获取详细错误信息
- 删除 config/ 目录可重置所有设置

## 🎯 快速开始 / Quick Start
1. 启动服务器: 双击 WorldWarServer.exe
2. 启动客户端: 双击 WorldWarClient.exe  
3. 选择语言: 推荐选择双语模式
4. 连接服务器: 使用默认地址 localhost:8888
5. 输入玩家名称开始游戏

## 📞 技术支持 / Technical Support
- 查看项目文档获取更多信息
- 检查日志文件诊断问题
'''
    
    with open("dist/WorldWarClient/README.txt", "w", encoding="utf-8") as f:
        f.write(readme_content)
    print("  ✅ README.txt")

def create_batch_file():
    """创建启动批处理文件"""
    print("\n🔧 创建启动脚本...")
    batch_content = '''@echo off
chcp 65001 >nul 2>&1
title World War Game Client

echo.
echo ============================================================
echo 🎮 World War Strategy Game Client / 世界大战策略游戏客户端
echo ============================================================
echo.

WorldWarClient.exe

pause
'''
    
    with open("dist/WorldWarClient/StartClient.bat", "w", encoding="utf-8") as f:
        f.write(batch_content)
    print("  ✅ StartClient.bat")

def create_config_template():
    """创建配置文件模板"""
    print("\n⚙️ 创建配置模板...")
    
    # 客户端配置模板
    client_config = '''[client]
default_server = localhost:8888
auto_connect = false
player_name = 

[display]
language = bilingual
show_tips = true
animations = true

[logging]
level = INFO
max_log_files = 5
max_log_size_mb = 5
'''
    
    config_path = "dist/WorldWarClient/config/client_config.ini"
    if not os.path.exists(config_path):
        with open(config_path, "w", encoding="utf-8") as f:
            f.write(client_config)
        print("  ✅ client_config.ini")

def main():
    """主函数"""
    print("世界大战游戏客户端打包准备")
    print("=" * 50)
    
    try:
        create_directories()
        copy_resources()
        create_readme()
        create_batch_file()
        create_config_template()
        
        print("\n" + "=" * 50)
        print("✅ 客户端打包准备完成！")
        print("\n📋 下一步:")
        print("1. 运行: pyinstaller client.spec")
        print("2. 检查: dist/WorldWarClient/ 目录")
        print("3. 测试: 运行生成的 WorldWarClient.exe")
        
    except Exception as e:
        print(f"\n❌ 打包准备失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
