#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连接管理器模块
Connection Manager Module

负责处理客户端连接、安全握手验证、连接状态监控等功能
Handles client connections, secure handshake verification, connection monitoring, etc.
"""

import socket
import threading
import time
import sys
from pathlib import Path
from typing import Dict, Any, Optional, Callable

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from server.player_session import PlayerSession, SessionManager
from server.network_handler import NetworkHandler
from shared.secure_protocol import SecureProtocol
from shared.message_types import MessageType
from shared.enhanced_logger import get_server_logger


class ConnectionManager:
    """连接管理器 - 负责客户端连接的生命周期管理"""

    def __init__(self, protocol: SecureProtocol, session_manager: SessionManager, 
                 network_handler: NetworkHandler):
        """初始化连接管理器"""
        self.protocol = protocol
        self.session_manager = session_manager
        self.network_handler = network_handler
        self.logger = get_server_logger("ConnectionManager")
        
        # 连接统计
        self.total_connections = 0
        self.active_connections = 0
        
        # 回调函数
        self.message_processor: Optional[Callable] = None
        self.cleanup_callback: Optional[Callable] = None
        
        self.logger.info("连接管理器初始化完成 / Connection manager initialized")

    def set_message_processor(self, processor: Callable):
        """设置消息处理器回调"""
        self.message_processor = processor

    def set_cleanup_callback(self, callback: Callable):
        """设置清理回调"""
        self.cleanup_callback = callback

    def handle_client_connection(self, client_socket: socket.socket, client_address: tuple) -> None:
        """处理单个客户端连接（支持安全握手）"""
        session_id = f"session_{self.total_connections}_{int(time.time())}"
        player_session = None

        try:
            self.total_connections += 1
            self.active_connections += 1

            # 设置客户端套接字超时
            client_socket.settimeout(30.0)  # 30秒超时

            # 创建玩家会话
            player_session = self.session_manager.create_session(session_id, client_socket, client_address)

            # INFO级别：基本连接信息
            self.logger.info(f"新客户端连接")
            print(f"📝 创建玩家会话 / Created player session: {session_id}")

            # 执行安全握手
            self.logger.info(f"开始安全握手")
            if not self._perform_secure_handshake(client_socket, player_session):
                # ERROR级别：详细握手失败信息
                self.logger.error(f"安全握手失败 - 会话ID: {session_id}, 客户端地址: {client_address}")
                return

            # INFO级别：握手成功
            self.logger.info(f"安全握手完成")
            print(f"🔐 安全握手完成 / Secure handshake completed: {session_id}")

            # 发送欢迎消息
            if not self._send_welcome_message(client_socket, session_id):
                self.logger.error(f"发送欢迎消息失败: {session_id}")
                return

            # 开始消息处理循环
            self._message_processing_loop(player_session)

        except Exception as e:
            self.logger.error(f"处理客户端连接时发生错误: {e}")
            print(f"❌ 客户端连接错误 / Client connection error: {e}")

        finally:
            # 清理客户端连接
            self._cleanup_client_connection(player_session, session_id)

    def _perform_secure_handshake(self, client_socket: socket.socket, session: PlayerSession) -> bool:
        """执行安全握手"""
        try:
            # 发起握手
            if not self.protocol.initiate_handshake(client_socket):
                return False

            # 等待握手响应（原始消息，无签名验证）
            response_msg = self.protocol.receive_secure_message(client_socket, timeout=10.0)
            if not response_msg or response_msg.get("type") != "handshake_response":
                self.logger.error("未收到有效的握手响应")
                return False

            # 完成握手验证
            if self.protocol.complete_handshake(client_socket, response_msg):
                # 标记会话握手完成
                session_token = getattr(self.protocol, 'session_tokens', {}).get(client_socket, "")
                session.complete_handshake(session_token)
                return True
            else:
                return False

        except Exception as e:
            self.logger.error(f"安全握手过程发生错误: {e}")
            return False

    def _send_welcome_message(self, client_socket: socket.socket, session_id: str) -> bool:
        """发送欢迎消息"""
        welcome_msg = {
            "type": MessageType.WELCOME,
            "data": {
                "server_name": "安全世界大战游戏服务器 / Secure World War Game Server",
                "version": "3.0",
                "session_id": session_id,
                "heartbeat_interval": 30  # 心跳间隔30秒
            }
        }

        return self.protocol.send_secure_message(client_socket, welcome_msg)

    def _message_processing_loop(self, player_session: PlayerSession) -> None:
        """消息处理循环"""
        last_activity = time.time()
        heartbeat_timeout = 60  # 60秒心跳超时

        # 处理客户端消息
        while player_session.is_connected:
            try:
                message = self.protocol.receive_secure_message(player_session.socket, timeout=5.0)
                if message:
                    last_activity = time.time()  # 更新活动时间
                    player_session.update_activity()
                    
                    # 调用消息处理器
                    if self.message_processor:
                        self.message_processor(player_session, message)
                else:
                    # 检查心跳超时
                    current_time = time.time()
                    if current_time - last_activity > heartbeat_timeout:
                        self.logger.warning(f"客户端心跳超时: {player_session.session_id}")
                        print(f"⚠️ 客户端心跳超时 / Client heartbeat timeout: {player_session.session_id}")
                        break
                    continue  # 继续等待

            except socket.timeout:
                # 检查心跳超时
                current_time = time.time()
                if current_time - last_activity > heartbeat_timeout:
                    self.logger.warning(f"客户端心跳超时: {player_session.session_id}")
                    print(f"⚠️ 客户端心跳超时 / Client heartbeat timeout: {player_session.session_id}")
                    break
                continue  # 超时继续等待

            except ConnectionResetError:
                self.logger.info(f"客户端重置连接: {player_session.session_id}")
                print(f"🔌 客户端重置连接 / Client reset connection: {player_session.session_id}")
                break

            except Exception as e:
                self.logger.error(f"处理客户端消息时发生错误: {e}")
                print(f"❌ 客户端处理错误 / Client handling error: {e}")
                break

    def _cleanup_client_connection(self, session: Optional[PlayerSession], session_id: str) -> None:
        """清理客户端连接"""
        try:
            if session:
                # 清理安全协议会话
                if session.socket:
                    self.protocol.cleanup_session(session.socket)

                self.logger.info(f"客户端 {session.client_address} 断开连接")

            # 使用会话管理器移除会话
            self.session_manager.remove_session(session_id)

            self.active_connections -= 1

            # 调用清理回调
            if self.cleanup_callback:
                self.cleanup_callback(session, session_id)

        except Exception as e:
            self.logger.error(f"清理客户端时发生错误: {e}")

    def get_connection_stats(self) -> Dict[str, int]:
        """获取连接统计信息"""
        return {
            "total_connections": self.total_connections,
            "active_connections": self.active_connections
        }
