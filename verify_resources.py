#!/usr/bin/env python3
import sys
sys.path.append('worldwar-server')

from world.terrain.perlin_generator import <PERSON><PERSON><PERSON><PERSON>rainGenerator, TerrainConfig
from world.terrain.biome_generator import BiomeGenerator
from world.resources.resource_generator import ResourceGenerator, ResourceType

# Create terrain generator
terrain_config = TerrainConfig(size=(64, 64))
terrain_gen = PerlinTerrainGenerator(seed=12345, terrain_config=terrain_config)

print("Generating base maps...")
heightmap = terrain_gen.generate_heightmap()
temperature_map = terrain_gen.generate_temperature_map(heightmap)
precipitation_map = terrain_gen.generate_precipitation_map(heightmap)

# Create biome generator
biome_gen = BiomeGenerator(seed=12345)

print("Generating biome and terrain maps...")
biome_map = biome_gen.generate_biome_map(heightmap, temperature_map, precipitation_map)
terrain_map = biome_gen.generate_terrain_from_biomes(
    biome_map, heightmap, temperature_map, precipitation_map
)

# Create resource generator
resource_gen = ResourceGenerator(seed=12345)

print("Generating resource distribution...")
resource_maps = resource_gen.generate_resource_distribution(
    heightmap, temperature_map, precipitation_map, biome_map, terrain_map
)

print(f"Generated {len(resource_maps)} resource types:")
for resource_key, resource_map in resource_maps.items():
    print(f"  {resource_key}: {resource_map.min():.3f} - {resource_map.max():.3f} (mean: {resource_map.mean():.3f})")

print("\nGetting resource statistics...")
statistics = resource_gen.get_resource_statistics(resource_maps)

print("\nTop 5 most abundant resources:")
abundance_ranking = sorted(statistics.items(), key=lambda x: x[1]['mean'], reverse=True)
for i, (resource_key, stats) in enumerate(abundance_ranking[:5]):
    print(f"  {i+1}. {resource_key}: {stats['mean']:.3f} (coverage: {stats['coverage']:.3f})")

print("\nCalculating balance scores...")
balance_scores = resource_gen.calculate_resource_balance_score(resource_maps)

print("\nMost balanced resources:")
balance_ranking = sorted(balance_scores.items(), key=lambda x: x[1]['balance_score'], reverse=True)
for i, (resource_key, scores) in enumerate(balance_ranking[:5]):
    print(f"  {i+1}. {resource_key}: {scores['balance_score']:.3f}")

print("\nStrategic resources analysis:")
strategic_resources = ['oil', 'rare_metals', 'rare_minerals', 'gold']
for resource_key in strategic_resources:
    if resource_key in statistics:
        stats = statistics[resource_key]
        print(f"  {resource_key}: mean={stats['mean']:.3f}, coverage={stats['coverage']:.3f}, hotspots={stats['hotspots']}")

print("✓ Resource generator working correctly!")