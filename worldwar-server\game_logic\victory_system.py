#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
胜利条件系统
Victory Condition System
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import math
from shared.enhanced_logger import get_server_logger
from data.local_storage import LocalStorage

class VictoryType(Enum):
    """胜利类型枚举"""
    ECONOMIC_DOMINANCE = "economic_dominance"       # 经济统治
    TERRITORIAL_CONTROL = "territorial_control"     # 领土控制
    TECHNOLOGICAL_SUPREMACY = "technological_supremacy"  # 科技霸权
    DEVELOPMENT_ACHIEVEMENT = "development_achievement"   # 发展成就
    ELIMINATION = "elimination"                     # 消灭对手
    TIME_LIMIT = "time_limit"                      # 时间限制

@dataclass
class VictoryCondition:
    """胜利条件"""
    condition_type: VictoryType
    name: str
    description: str
    target_value: float
    current_progress: Dict[str, float]
    is_enabled: bool = True
    required_lead: int = 0 # 特定条件可能需要的领先值，例如科技
    
    def check_condition(self, player_id: str) -> bool:
        """检查玩家是否满足此胜利条件"""
        if not self.is_enabled:
            return False
        
        current_value = self.current_progress.get(player_id, 0.0)
        return current_value >= self.target_value
    
    def get_progress_percentage(self, player_id: str) -> float:
        """获取玩家在此条件下的进度百分比"""
        current_value = self.current_progress.get(player_id, 0.0)
        if self.target_value <= 0:
            return 0.0
        return min(100.0, (current_value / self.target_value) * 100.0)

class VictorySystem:
    """胜利条件系统管理器"""
    
    def __init__(self, player_manager, economic_system, game_settings: Dict[str, Any]):
        """初始化胜利条件系统"""
        self.player_manager = player_manager
        self.economic_system = economic_system
        self.victory_conditions: Dict[VictoryType, VictoryCondition] = {}
        self.game_settings = game_settings
        
        # 初始化胜利条件
        self._initialize_victory_conditions()
        
        self.logger = get_server_logger("VictorySystem")
        
        self.logger.info("胜利条件系统初始化完成 / Victory system initialized")
    
    def _initialize_victory_conditions(self):
        """从文件初始化胜利条件"""
        storage = LocalStorage()
        conditions_data = storage.get_victory_conditions_data()
        
        if not conditions_data or "victory_conditions" not in conditions_data:
            self.logger.warning("无法加载胜利条件数据，将使用空设置。")
            return

        for condition_data in conditions_data["victory_conditions"]:
            try:
                condition_type = VictoryType(condition_data["condition_type"])
                condition = VictoryCondition(
                    condition_type=condition_type,
                    name=condition_data["name"],
                    description=condition_data["description"],
                    target_value=condition_data["target_value"],
                    is_enabled=condition_data.get("is_enabled", True),
                    required_lead=condition_data.get("required_lead", 0),
                    current_progress={} # 进度在游戏开始时为空
                )
                self.victory_conditions[condition_type] = condition
            except (ValueError, KeyError) as e:
                self.logger.error(f"解析胜利条件时出错: {condition_data}. 错误: {e}")

        self.logger.info(f"从文件加载了 {len(self.victory_conditions)} 个胜利条件。")
    
    def update_victory_progress(self):
        """更新胜利条件进度"""
        active_players = self.player_manager.get_active_players()
        all_players = list(self.player_manager.players.values())

        if not active_players:
            return

        # 更新经济统治进度
        self._update_economic_dominance_progress(active_players)

        # 更新领土控制进度
        self._update_territorial_control_progress(active_players)

        # 更新科技霸权进度
        self._update_technological_supremacy_progress(active_players)

        # 更新发展成就进度
        self._update_development_achievement_progress(active_players)

        # 更新消灭对手进度
        self._update_elimination_progress(all_players, active_players)
    
    def _update_economic_dominance_progress(self, active_players: List['Player']):
        """更新经济统治进度"""
        condition = self.victory_conditions.get(VictoryType.ECONOMIC_DOMINANCE)
        if not condition or not condition.is_enabled:
            return
        
        total_gdp = sum(player.gdp for player in active_players)
        
        for player in active_players:
            if total_gdp > 0:
                gdp_ratio = player.gdp / total_gdp
                condition.current_progress[player.player_id] = gdp_ratio
            else:
                condition.current_progress[player.player_id] = 0.0
    
    def _update_territorial_control_progress(self, active_players: List['Player']):
        """更新领土控制进度"""
        condition = self.victory_conditions.get(VictoryType.TERRITORIAL_CONTROL)
        if not condition or not condition.is_enabled:
            return
        
        # 假设 economic_system 有一个方法可以获取世界总领土数
        total_world_territories = self.economic_system.get_total_territory_count()
        if total_world_territories == 0:
             # 避免除以零，并记录警告
            self.logger.warning("世界总领土数为0，无法计算领土控制胜利条件。")
            for player in active_players:
                condition.current_progress[player.player_id] = 0.0
            return

        for player in active_players:
            territory_ratio = player.territory_count / total_world_territories
            condition.current_progress[player.player_id] = territory_ratio
    
    def _update_technological_supremacy_progress(self, active_players: List['Player']):
        """更新科技霸权进度 (重构版)"""
        condition = self.victory_conditions.get(VictoryType.TECHNOLOGICAL_SUPREMACY)
        if not condition or not condition.is_enabled:
            return
            
        if not active_players:
            return

        for player in active_players:
            player_tech = player.tech_level
            
            other_players_max_tech = 0
            if len(active_players) > 1:
                other_players_max_tech = max(p.tech_level for p in active_players if p.player_id != player.player_id)

            tech_lead = player_tech - other_players_max_tech
            
            # 胜利条件：达到目标等级，并且领先至少 'required_lead' 级
            if player_tech >= condition.target_value and tech_lead >= condition.required_lead:
                # 直接满足条件
                condition.current_progress[player.player_id] = condition.target_value
            else:
                # 如果未满足，进度为当前科技水平
                condition.current_progress[player.player_id] = player_tech
    
    def _update_development_achievement_progress(self, active_players: List['Player']):
        """更新发展成就进度"""
        condition = self.victory_conditions.get(VictoryType.DEVELOPMENT_ACHIEVEMENT)
        if not condition or not condition.is_enabled:
            return
        
        for player in active_players:
            # 假设 economic_system 可以直接计算一个玩家的平均发展指数
            development_index = self.economic_system.get_player_average_development_index(player.player_id)
            condition.current_progress[player.player_id] = development_index
    
    def _update_elimination_progress(self, all_players: List['Player'], active_players: List['Player']):
        """更新消滅對手進度"""
        condition = self.victory_conditions.get(VictoryType.ELIMINATION)
        if not condition or not condition.is_enabled:
            return
        
        total_players = len(all_players)
        eliminated_count = total_players - len(active_players)

        for player in all_players:
            if not player.is_alive:
                condition.current_progress[player.player_id] = 0.0
            elif len(active_players) == 1 and player.is_alive:
                condition.current_progress[player.player_id] = 1.0  # 唯一幸存者
            else:
                # 进度基于消灭的对手数量
                total_opponents = total_players - 1
                if total_opponents > 0:
                    # 对于活跃玩家，他们的进度是基于被消灭的玩家数
                    condition.current_progress[player.player_id] = eliminated_count / total_opponents
                else:
                    condition.current_progress[player.player_id] = 0.0
    
    def check_victory(self, current_turn: int) -> Optional[Tuple[str, VictoryType]]:
        """
        检查是否有玩家满足任何胜利条件。
        返回一个元组 (winner_id, victory_type)，如果没有赢家则返回 None。
        """
        self.update_victory_progress()
        
        active_players = self.player_manager.get_active_players()

        # 1. 检查消灭胜利
        if self.victory_conditions[VictoryType.ELIMINATION].is_enabled:
            if len(active_players) == 1:
                winner = active_players[0]
                self.logger.info(f"玩家 {winner.name} 通过消灭所有对手获胜！")
                winner.achieved_victory_conditions.append(VictoryType.ELIMINATION.value)
                return winner.player_id, VictoryType.ELIMINATION

        # 2. 检查其他胜利条件
        for player in active_players:
            for condition_type, condition in self.victory_conditions.items():
                if condition.check_condition(player.player_id):
                    self.logger.info(f"玩家 {player.name} 满足 '{condition.name}' 胜利条件！")
                    player.achieved_victory_conditions.append(condition.condition_type.value)
                    return player.player_id, condition.condition_type
        
        # 3. 检查时间限制
        if self.game_settings.get("max_turns", 0) > 0 and current_turn >= self.game_settings["max_turns"]:
            self.logger.info(f"达到最大回合数 {current_turn}。根据分数决定胜者。")
            winner = self._get_winner_by_score(active_players)
            if winner:
                winner.achieved_victory_conditions.append(VictoryType.TIME_LIMIT.value)
                return winner.player_id, VictoryType.TIME_LIMIT
            
        return None
    
    # _validate_victory_condition 和其子方法已被移除，
    # 因为它们的逻辑已被整合到各个 _update_*_progress 方法中。

    def _get_winner_by_score(self, active_players: List['Player']) -> Optional['Player']:
        """根据综合实力分数选出获胜者"""
        if not active_players:
            return None
        
        return max(active_players, key=lambda p: p.calculate_power_score())
    
    def get_victory_progress_summary(self, players: List[str]) -> Dict[str, Any]:
        """获取胜利条件进度摘要"""
        summary = {
            "conditions": {},
            "leaders": {},
            "closest_to_victory": None
        }
        
        # 收集每个条件的进度
        for condition_type, condition in self.victory_conditions.items():
            if not condition.is_enabled:
                continue
            
            condition_summary = {
                "name": condition.name,
                "description": condition.description,
                "target": condition.target_value,
                "players": {}
            }
            
            leader = None
            max_progress = 0
            
            for player in players:
                progress = condition.current_progress.get(player, 0.0)
                percentage = condition.get_progress_percentage(player)
                
                condition_summary["players"][player] = {
                    "progress": progress,
                    "percentage": percentage,
                    "achieved": condition.check_condition(player)
                }
                
                if progress > max_progress:
                    max_progress = progress
                    leader = player
            
            condition_summary["leader"] = leader
            summary["conditions"][condition_type.value] = condition_summary
            summary["leaders"][condition_type.value] = leader
        
        # 找出最接近胜利的玩家
        closest_player = None
        max_overall_progress = 0
        
        for player in players:
            total_progress = 0
            condition_count = 0
            
            for condition in self.victory_conditions.values():
                if condition.is_enabled:
                    progress = condition.get_progress_percentage(player)
                    total_progress += progress
                    condition_count += 1
            
            if condition_count > 0:
                average_progress = total_progress / condition_count
                if average_progress > max_overall_progress:
                    max_overall_progress = average_progress
                    closest_player = player
        
        summary["closest_to_victory"] = closest_player
        
        return summary
    
    def configure_victory_conditions(self, settings: Dict[str, Any]):
        """配置胜利条件"""
        if "economic_dominance_threshold" in settings:
            self.victory_conditions[VictoryType.ECONOMIC_DOMINANCE].target_value = settings["economic_dominance_threshold"]
        
        if "territorial_control_threshold" in settings:
            self.victory_conditions[VictoryType.TERRITORIAL_CONTROL].target_value = settings["territorial_control_threshold"]
        
        if "tech_supremacy_level" in settings:
            self.victory_conditions[VictoryType.TECHNOLOGICAL_SUPREMACY].target_value = settings["tech_supremacy_level"]
        
        if "development_threshold" in settings:
            self.victory_conditions[VictoryType.DEVELOPMENT_ACHIEVEMENT].target_value = settings["development_threshold"]
        
        if "elimination_enabled" in settings:
            self.victory_conditions[VictoryType.ELIMINATION].is_enabled = settings["elimination_enabled"]
        
        self.logger.info("胜利条件配置已更新")
    
    def get_victory_conditions_info(self) -> Dict[str, Any]:
        """获取胜利条件信息"""
        info = {}
        
        for condition_type, condition in self.victory_conditions.items():
            info[condition_type.value] = {
                "name": condition.name,
                "description": condition.description,
                "target_value": condition.target_value,
                "enabled": condition.is_enabled
            }
        
        return info
