# 📦 打包指南 / Packaging Guide

## 📋 概述 / Overview

本指南提供了将世界大战策略游戏打包为Windows可执行文件(.exe)的完整方案，包括客户端和服务端的独立打包。

This guide provides a complete solution for packaging the World War Strategy Game into Windows executable files (.exe), including independent packaging for both client and server.

## 🛠️ 准备工作 / Prerequisites

### 1. 安装打包工具 / Install Packaging Tools

```bash
# 安装PyInstaller (推荐)
pip install pyinstaller

# 或者安装cx_Freeze (备选)
pip install cx_freeze

# 安装其他依赖
pip install auto-py-to-exe  # 图形界面工具(可选)
```

### 2. 检查依赖 / Check Dependencies

```bash
# 检查项目依赖
cd worldwar-server
pip freeze > requirements.txt

cd ../worldwar-client  
pip freeze > requirements.txt
```

## 🎯 打包策略 / Packaging Strategy

### 打包目标 / Packaging Goals
- ✅ **独立可执行文件**: 不需要Python环境
- ✅ **包含所有依赖**: 自包含运行环境
- ✅ **自动创建目录**: 日志、配置、数据目录
- ✅ **语言包内置**: 包含完整语言支持
- ✅ **配置文件外置**: 用户可修改配置

### 文件结构规划 / File Structure Planning
```
WorldWarGame/
├── 📁 Server/                    # 服务端发布包
│   ├── 🚀 WorldWarServer.exe     # 服务端可执行文件
│   ├── 📁 config/                # 配置文件目录
│   ├── 📁 logs/                  # 日志目录(自动创建)
│   ├── 📁 data/                  # 数据目录(自动创建)
│   └── 📄 README.txt             # 使用说明
├── 📁 Client/                    # 客户端发布包
│   ├── 🚀 WorldWarClient.exe     # 客户端可执行文件
│   ├── 📁 config/                # 配置文件目录
│   ├── 📁 logs/                  # 日志目录(自动创建)
│   └── 📄 README.txt             # 使用说明
└── 📄 INSTALL.txt                # 安装说明
```

## 🖥️ 服务端打包 / Server Packaging

### 1. 创建打包脚本 / Create Packaging Script

创建 `worldwar-server/build_server.py`:

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务端打包脚本
Server Packaging Script
"""

import os
import sys
import shutil
from pathlib import Path

def create_directories():
    """创建必要的目录"""
    directories = [
        "dist/WorldWarServer/config",
        "dist/WorldWarServer/logs",
        "dist/WorldWarServer/logs/server", 
        "dist/WorldWarServer/data",
        "dist/WorldWarServer/data/cache",
        "dist/WorldWarServer/saves",
        "dist/WorldWarServer/languages"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ 创建目录: {directory}")

def copy_resources():
    """复制资源文件"""
    resources = [
        ("languages/chinese.json", "dist/WorldWarServer/languages/"),
        ("languages/english.json", "dist/WorldWarServer/languages/"),
        ("config/server_config.ini", "dist/WorldWarServer/config/"),
        ("README.md", "dist/WorldWarServer/")
    ]
    
    for src, dst in resources:
        if os.path.exists(src):
            shutil.copy2(src, dst)
            print(f"✅ 复制文件: {src} -> {dst}")

def create_readme():
    """创建使用说明"""
    readme_content = '''# 世界大战策略游戏服务端 / World War Strategy Game Server

## 使用方法 / Usage
1. 双击 WorldWarServer.exe 启动服务器
2. 首次运行会显示语言选择界面
3. 选择语言后按任意键继续
4. 服务器将在 localhost:8888 启动
5. 按 Ctrl+C 停止服务器

## 配置文件 / Configuration
- config/server_config.ini - 服务器配置
- config/server_language.json - 语言设置

## 日志文件 / Log Files
- logs/server/ - 服务器日志
- logs/latest.log - 最新日志

## 注意事项 / Notes
- 确保端口8888未被占用
- 防火墙可能需要允许程序通过
- 首次运行需要选择语言
'''
    
    with open("dist/WorldWarServer/README.txt", "w", encoding="utf-8") as f:
        f.write(readme_content)
    print("✅ 创建使用说明")

if __name__ == "__main__":
    print("🚀 开始服务端打包...")
    create_directories()
    copy_resources() 
    create_readme()
    print("✅ 服务端打包准备完成")
```

### 2. 创建PyInstaller配置 / Create PyInstaller Config

创建 `worldwar-server/server.spec`:

```python
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['server_main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('languages/*.json', 'languages'),
        ('config/*.ini', 'config'),
        ('shared', 'shared'),
        ('server', 'server'),
        ('utils', 'utils'),
        ('core', 'core'),
        ('game_logic', 'game_logic'),
        ('world', 'world'),
        ('data', 'data')
    ],
    hiddenimports=[
        'shared.enhanced_logger',
        'shared.language_manager',
        'shared.security_manager',
        'server.game_server',
        'utils.process_lock',
        'utils.signal_handler'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='WorldWarServer',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    cofile=None,
    icon=None,
)
```

### 3. 打包命令 / Packaging Commands

```bash
cd worldwar-server

# 方法1: 使用spec文件 (推荐)
pyinstaller server.spec

# 方法2: 直接命令行
pyinstaller --onefile --console --name WorldWarServer ^
    --add-data "languages;languages" ^
    --add-data "config;config" ^
    --add-data "shared;shared" ^
    --add-data "server;server" ^
    --add-data "utils;utils" ^
    --hidden-import shared.enhanced_logger ^
    --hidden-import shared.language_manager ^
    server_main.py

# 运行打包准备脚本
python build_server.py
```

## 💻 客户端打包 / Client Packaging

### 1. 创建打包脚本 / Create Packaging Script

创建 `worldwar-client/build_client.py`:

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客户端打包脚本
Client Packaging Script
"""

import os
import sys
import shutil
from pathlib import Path

def create_directories():
    """创建必要的目录"""
    directories = [
        "dist/WorldWarClient/config",
        "dist/WorldWarClient/logs",
        "dist/WorldWarClient/logs/client",
        "dist/WorldWarClient/client/languages"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ 创建目录: {directory}")

def copy_resources():
    """复制资源文件"""
    resources = [
        ("client/languages/chinese.json", "dist/WorldWarClient/client/languages/"),
        ("client/languages/english.json", "dist/WorldWarClient/client/languages/"),
        ("config/client_config.ini", "dist/WorldWarClient/config/"),
        ("README.md", "dist/WorldWarClient/")
    ]
    
    for src, dst in resources:
        if os.path.exists(src):
            shutil.copy2(src, dst)
            print(f"✅ 复制文件: {src} -> {dst}")

def create_readme():
    """创建使用说明"""
    readme_content = '''# 世界大战策略游戏客户端 / World War Strategy Game Client

## 使用方法 / Usage
1. 双击 WorldWarClient.exe 启动客户端
2. 首次运行会显示语言选择界面
3. 选择语言后按任意键继续
4. 在主菜单选择连接服务器
5. 输入服务器地址 (默认: localhost:8888)
6. 按 Ctrl+C 可随时退出

## 配置文件 / Configuration
- config/client_config.ini - 客户端配置
- config/client_language.json - 语言设置

## 日志文件 / Log Files
- logs/client/ - 客户端日志
- logs/latest.log - 最新日志

## 注意事项 / Notes
- 需要先启动服务器
- 确保网络连接正常
- 首次运行需要选择语言
'''
    
    with open("dist/WorldWarClient/README.txt", "w", encoding="utf-8") as f:
        f.write(readme_content)
    print("✅ 创建使用说明")

if __name__ == "__main__":
    print("🚀 开始客户端打包...")
    create_directories()
    copy_resources()
    create_readme()
    print("✅ 客户端打包准备完成")
```

### 2. 创建PyInstaller配置 / Create PyInstaller Config

创建 `worldwar-client/client.spec`:

```python
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['client_main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('client/languages/*.json', 'client/languages'),
        ('config/*.ini', 'config'),
        ('shared', 'shared'),
        ('client', 'client'),
        ('utils', 'utils')
    ],
    hiddenimports=[
        'shared.enhanced_logger',
        'shared.language_manager',
        'shared.security_manager',
        'client.core.new_client_core',
        'utils.process_lock',
        'utils.signal_handler'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='WorldWarClient',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    cofile=None,
    icon=None,
)
```

### 3. 打包命令 / Packaging Commands

```bash
cd worldwar-client

# 使用spec文件打包
pyinstaller client.spec

# 运行打包准备脚本
python build_client.py
```

## 🔧 日志目录自动创建 / Auto Log Directory Creation

### 修改日志系统确保目录创建

需要修改 `shared/enhanced_logger.py` 确保日志目录自动创建：

```python
def ensure_log_directory(log_dir):
    """确保日志目录存在"""
    try:
        os.makedirs(log_dir, exist_ok=True)
        return True
    except Exception as e:
        print(f"Warning: Could not create log directory {log_dir}: {e}")
        return False

def get_server_logger(name, level="INFO", log_to_console=False):
    """获取服务器日志器"""
    # 确保日志目录存在
    log_dir = "logs/server"
    ensure_log_directory(log_dir)
    # ... 其余代码

def get_client_logger(name, level="INFO", log_to_console=False):
    """获取客户端日志器"""
    # 确保日志目录存在
    log_dir = "logs/client"
    ensure_log_directory(log_dir)
    # ... 其余代码
```

## 📋 完整打包流程 / Complete Packaging Process

### 🚀 一键打包 (推荐) / One-Click Packaging (Recommended)

```bash
# 运行完整打包脚本
python build_all.py
```

这个脚本会自动完成：
- ✅ 检查并安装PyInstaller
- ✅ 打包服务端和客户端
- ✅ 创建完整的发布包
- ✅ 生成使用说明文档

### 🔧 手动打包 / Manual Packaging

如果需要手动控制打包过程：

#### 1. 准备环境 / Prepare Environment
```bash
# 安装PyInstaller
pip install pyinstaller
```

#### 2. 打包服务端 / Package Server
```bash
cd worldwar-server
python build_server.py
pyinstaller server.spec
```

#### 3. 打包客户端 / Package Client
```bash
cd worldwar-client
python build_client.py
pyinstaller client.spec
```

#### 4. 手动整理发布包 / Manual Release Organization
```bash
# 运行完整打包脚本的发布包创建部分
python build_all.py
```

## ⚠️ 注意事项 / Important Notes

### 1. 依赖问题 / Dependency Issues
- 确保所有Python依赖都已安装
- 检查隐藏导入是否完整
- 测试打包后的exe是否能正常运行

### 2. 文件路径 / File Paths
- 使用相对路径而不是绝对路径
- 确保资源文件正确包含在打包中
- 测试配置文件和语言文件是否可访问

### 3. 性能优化 / Performance Optimization
- 使用 `--onefile` 创建单文件exe
- 使用 UPX 压缩减小文件大小
- 排除不必要的模块减小体积

### 4. 测试建议 / Testing Recommendations
- 在干净的Windows环境中测试
- 测试首次运行的语言选择功能
- 验证日志文件是否正确创建
- 测试客户端-服务端连接功能

## 🎯 最终发布结构 / Final Release Structure

```
WorldWarGame_Release/
├── 📁 Server/
│   ├── 🚀 WorldWarServer.exe     # 服务端 (~50MB)
│   ├── 🔧 StartServer.bat        # 启动脚本
│   ├── 📁 config/                # 配置文件
│   ├── 📁 languages/             # 语言包
│   ├── 📁 logs/                  # 日志目录(自动创建)
│   └── 📄 README.txt             # 使用说明
├── 📁 Client/
│   ├── 🚀 WorldWarClient.exe     # 客户端 (~45MB)
│   ├── 🔧 StartClient.bat        # 启动脚本
│   ├── 📁 config/                # 配置文件
│   ├── 📁 client/languages/      # 语言包
│   ├── 📁 logs/                  # 日志目录(自动创建)
│   └── 📄 README.txt             # 使用说明
└── 📄 README.txt                 # 主安装说明
```

## 🚀 快速打包命令 / Quick Packaging Commands

### 一键打包 / One-Click Packaging
```bash
# 运行完整打包脚本
python build_all.py
```

### 分步打包 / Step-by-Step Packaging
```bash
# 1. 安装PyInstaller
pip install pyinstaller

# 2. 打包服务端
cd worldwar-server
python build_server.py
pyinstaller server.spec

# 3. 打包客户端
cd ../worldwar-client
python build_client.py
pyinstaller client.spec

# 4. 创建发布包
cd ..
python build_all.py  # 只运行发布包创建部分
```

## ✅ 打包完成后的测试 / Testing After Packaging

### 1. 测试服务端 / Test Server
```bash
cd WorldWarGame_Release/Server
WorldWarServer.exe
# 或双击 StartServer.bat
```

### 2. 测试客户端 / Test Client
```bash
cd WorldWarGame_Release/Client
WorldWarClient.exe
# 或双击 StartClient.bat
```

### 3. 测试连接 / Test Connection
1. 启动服务端，选择语言，等待"服务器准备就绪"
2. 启动客户端，选择语言，连接到localhost:8888
3. 输入玩家名称，测试基本功能

## 📦 分发建议 / Distribution Recommendations

### 压缩发布包 / Compress Release Package
```bash
# 创建ZIP文件
7z a WorldWarGame_v3.0.zip WorldWarGame_Release/

# 或创建RAR文件
rar a WorldWarGame_v3.0.rar WorldWarGame_Release/
```

### 发布清单 / Release Checklist
- [ ] 服务端exe正常启动
- [ ] 客户端exe正常启动
- [ ] 语言选择功能正常
- [ ] 客户端-服务端连接正常
- [ ] 日志文件正常创建
- [ ] 配置文件正常保存
- [ ] 所有必要文件已包含

这样打包后，用户只需要双击exe文件就能运行游戏，所有必要的目录都会自动创建！🎉
