#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多开防护管理器
Multi-Instance Guard Manager

防止同一程序的多个实例同时运行
Prevents multiple instances of the same program from running simultaneously
"""

import os
import sys
import time
import atexit
from pathlib import Path
from typing import Optional, Dict, Any

# 尝试导入fcntl，在Windows上可能不可用
try:
    import fcntl
    HAS_FCNTL = True
except ImportError:
    HAS_FCNTL = False

class MultiInstanceGuard:
    """多开防护管理器"""
    
    def __init__(self, app_name: str, lock_dir: str = None, allow_multi: bool = False):
        """
        初始化多开防护管理器
        
        Args:
            app_name: 应用程序名称
            lock_dir: 锁文件目录
            allow_multi: 是否允许多开
        """
        self.app_name = app_name
        self.allow_multi = allow_multi
        self.lock_file = None
        self.lock_fd = None
        
        # 确定锁文件目录
        if lock_dir:
            self.lock_dir = Path(lock_dir)
        else:
            # 使用临时目录
            if os.name == 'nt':  # Windows
                self.lock_dir = Path(os.environ.get('TEMP', 'C:/temp'))
            else:  # Unix/Linux
                self.lock_dir = Path('/tmp')
        
        self.lock_dir.mkdir(parents=True, exist_ok=True)
        self.lock_file = self.lock_dir / f"{app_name}.lock"
        
        # 注册退出时清理
        atexit.register(self.release)
    
    def acquire(self) -> bool:
        """
        获取锁，防止多开
        
        Returns:
            是否成功获取锁
        """
        if self.allow_multi:
            return True
        
        try:
            # 打开锁文件
            self.lock_fd = open(self.lock_file, 'w')
            
            if os.name == 'nt':
                # Windows系统使用文件锁
                try:
                    import msvcrt
                    msvcrt.locking(self.lock_fd.fileno(), msvcrt.LK_NBLCK, 1)
                except (ImportError, IOError):
                    self.lock_fd.close()
                    return False
            else:
                # Unix/Linux系统使用fcntl
                if HAS_FCNTL:
                    try:
                        fcntl.flock(self.lock_fd.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
                    except IOError:
                        self.lock_fd.close()
                        return False
                else:
                    # 如果没有fcntl，使用简单的文件存在检查
                    if self.lock_file.exists():
                        self.lock_fd.close()
                        return False
            
            # 写入进程信息
            self.lock_fd.write(f"{os.getpid()}\n")
            self.lock_fd.write(f"{time.time()}\n")
            self.lock_fd.flush()
            
            return True
            
        except Exception as e:
            print(f"获取进程锁失败: {e}")
            if self.lock_fd:
                self.lock_fd.close()
                self.lock_fd = None
            return False
    
    def release(self):
        """释放锁"""
        if self.lock_fd:
            try:
                if os.name == 'nt':
                    try:
                        import msvcrt
                        msvcrt.locking(self.lock_fd.fileno(), msvcrt.LK_UNLCK, 1)
                    except ImportError:
                        pass
                else:
                    if HAS_FCNTL:
                        fcntl.flock(self.lock_fd.fileno(), fcntl.LOCK_UN)

                self.lock_fd.close()
                self.lock_fd = None

                # 删除锁文件
                if self.lock_file.exists():
                    try:
                        self.lock_file.unlink()
                    except PermissionError:
                        # Windows可能在文件关闭后还需要一些时间才能完全释放文件
                        print(f"注意: 无法立即删除锁文件 {self.lock_file}，将在下次启动时自动清理")
                    except Exception as e:
                        print(f"删除锁文件失败: {e}")

            except Exception as e:
                print(f"释放进程锁失败: {e}")
    
    def is_running(self) -> bool:
        """
        检查是否有其他实例正在运行
        
        Returns:
            是否有其他实例运行
        """
        if self.allow_multi:
            return False
        
        if not self.lock_file.exists():
            return False
        
        try:
            with open(self.lock_file, 'r') as f:
                lines = f.readlines()
                if len(lines) >= 1:
                    pid = int(lines[0].strip())
                    
                    # 检查进程是否还在运行
                    if self._is_process_running(pid):
                        return True
                    else:
                        # 进程已死，删除锁文件
                        self.lock_file.unlink()
                        return False
        except Exception:
            # 锁文件损坏，删除它
            try:
                self.lock_file.unlink()
            except:
                pass
            return False
        
        return False
    
    def _is_process_running(self, pid: int) -> bool:
        """
        检查指定PID的进程是否还在运行
        
        Args:
            pid: 进程ID
            
        Returns:
            进程是否在运行
        """
        try:
            if os.name == 'nt':
                # Windows
                import subprocess
                result = subprocess.run(
                    ['tasklist', '/FI', f'PID eq {pid}'],
                    capture_output=True,
                    text=True
                )
                return str(pid) in result.stdout
            else:
                # Unix/Linux
                os.kill(pid, 0)
                return True
        except (OSError, subprocess.SubprocessError):
            return False
    
    def get_running_instance_info(self) -> Optional[Dict[str, Any]]:
        """
        获取正在运行的实例信息
        
        Returns:
            实例信息字典，如果没有运行实例则返回None
        """
        if not self.is_running():
            return None
        
        try:
            with open(self.lock_file, 'r') as f:
                lines = f.readlines()
                if len(lines) >= 2:
                    pid = int(lines[0].strip())
                    start_time = float(lines[1].strip())
                    
                    return {
                        'pid': pid,
                        'start_time': start_time,
                        'running_duration': time.time() - start_time,
                        'app_name': self.app_name
                    }
        except Exception:
            pass
        
        return None
    
    def force_kill_running_instance(self) -> bool:
        """
        强制终止正在运行的实例
        
        Returns:
            是否成功终止
        """
        info = self.get_running_instance_info()
        if not info:
            return True
        
        try:
            pid = info['pid']
            
            if os.name == 'nt':
                # Windows
                import subprocess
                subprocess.run(['taskkill', '/F', '/PID', str(pid)], 
                             capture_output=True)
            else:
                # Unix/Linux
                os.kill(pid, 9)  # SIGKILL
            
            # 等待一下，然后检查
            time.sleep(1)
            
            if not self._is_process_running(pid):
                # 清理锁文件
                if self.lock_file.exists():
                    self.lock_file.unlink()
                return True
            
        except Exception as e:
            print(f"强制终止进程失败: {e}")
        
        return False

def create_instance_guard(app_name: str, allow_multi: bool = False) -> MultiInstanceGuard:
    """
    创建多开防护实例
    
    Args:
        app_name: 应用程序名称
        allow_multi: 是否允许多开
        
    Returns:
        多开防护实例
    """
    return MultiInstanceGuard(app_name, allow_multi=allow_multi)

def check_and_prevent_multi_instance(app_name: str, 
                                    allow_multi: bool = False,
                                    show_message: bool = True) -> bool:
    """
    检查并防止多开
    
    Args:
        app_name: 应用程序名称
        allow_multi: 是否允许多开
        show_message: 是否显示消息
        
    Returns:
        是否可以继续运行（True表示可以运行，False表示应该退出）
    """
    # 修复Windows控制台Unicode输出问题
    if sys.stdout.encoding != 'utf-8':
        try:
            sys.stdout.reconfigure(encoding='utf-8')
        except TypeError:
            # 在某些环境下 reconfigure 可能不可用，尝试其他方法
            import codecs
            sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')

    guard = create_instance_guard(app_name, allow_multi)
    
    if guard.is_running():
        if show_message:
            info = guard.get_running_instance_info()
            if info:
                print(f"❌ {app_name} 已经在运行中!")
                print(f"   进程ID: {info['pid']}")
                print(f"   运行时长: {info['running_duration']:.1f} 秒")
                print(f"   如需强制启动，请先关闭现有实例或使用 --allow-multi 参数")
            else:
                print(f"❌ {app_name} 已经在运行中!")
        return False
    
    if not guard.acquire():
        if show_message:
            print(f"❌ 无法获取 {app_name} 的运行锁，可能有其他实例正在启动")
        return False
    
    if show_message:
        print(f"✅ {app_name} 启动锁获取成功")
    
    return True