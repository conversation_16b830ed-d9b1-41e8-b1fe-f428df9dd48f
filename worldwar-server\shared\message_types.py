#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消息类型定义和标准化消息格式
Message Types Definition and Standardized Message Format
"""

import time
import uuid
from enum import Enum
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, asdict
import json


class MessageType(Enum):
    """网络消息类型常量"""
    
    # 连接相关 / Connection Related
    WELCOME = "welcome"                    # 服务器欢迎消息
    PING = "ping"                         # 心跳包
    PONG = "pong"                         # 心跳响应
    DISCONNECT = "disconnect"             # 断开连接
    
    # 认证相关 / Authentication Related
    JOIN_GAME = "join_game"               # 加入游戏请求
    JOIN_SUCCESS = "join_success"         # 加入游戏成功
    JOIN_FAILED = "join_failed"           # 加入游戏失败
    
    # 房间管理 / Room Management
    CREATE_ROOM = "create_room"           # 创建房间
    ROOM_CREATED = "room_created"         # 房间创建成功
    JOIN_ROOM = "join_room"               # 加入房间
    ROOM_JOINED = "room_joined"           # 加入房间成功
    LEAVE_ROOM = "leave_room"             # 离开房间
    ROOM_LEFT = "room_left"               # 离开房间成功
    GET_ROOM_LIST = "get_room_list"       # 获取房间列表请求
    ROOM_LIST = "room_list"               # 房间列表
    ROOM_UPDATE = "room_update"           # 房间状态更新
    ROOM_CLOSED = "room_closed"           # 房间关闭
    
    # 游戏状态 / Game State
    GAME_START = "game_start"             # 游戏开始
    GAME_END = "game_end"                 # 游戏结束
    GAME_PAUSE = "game_pause"             # 游戏暂停
    GAME_RESUME = "game_resume"           # 游戏恢复
    TURN_START = "turn_start"             # 回合开始
    TURN_END = "turn_end"                 # 回合结束
    PLAYER_TURN = "player_turn"           # 玩家回合
    
    # 游戏动作 / Game Actions
    GAME_ACTION = "game_action"           # 游戏动作
    ACTION_RESULT = "action_result"       # 动作结果
    MOVE_UNITS = "move_units"             # 移动单位
    ATTACK_TERRITORY = "attack_territory" # 攻击领土
    BUILD_STRUCTURE = "build_structure"   # 建造建筑
    RESEARCH_TECH = "research_tech"       # 研究科技
    TRADE_RESOURCES = "trade_resources"   # 交易资源
    
    # 世界事件 / World Events
    NATURAL_DISASTER = "natural_disaster" # 自然灾害
    RANDOM_EVENT = "random_event"         # 随机事件
    RESOURCE_DISCOVERY = "resource_discovery" # 资源发现
    CLIMATE_CHANGE = "climate_change"     # 气候变化
    
    # 玩家交互 / Player Interaction
    CHAT_MESSAGE = "chat_message"         # 聊天消息
    PRIVATE_MESSAGE = "private_message"   # 私人消息
    ALLIANCE_REQUEST = "alliance_request" # 联盟请求
    ALLIANCE_RESPONSE = "alliance_response" # 联盟响应
    TRADE_OFFER = "trade_offer"           # 贸易提议
    TRADE_RESPONSE = "trade_response"     # 贸易响应
    
    # 信息查询 / Information Query
    PLAYER_INFO = "player_info"           # 玩家信息
    TERRITORY_INFO = "territory_info"     # 领土信息
    WORLD_MAP = "world_map"               # 世界地图
    GAME_STATISTICS = "game_statistics"   # 游戏统计
    LEADERBOARD = "leaderboard"           # 排行榜
    
    # 错误和状态 / Error and Status
    ERROR = "error"                       # 错误消息
    SUCCESS = "success"                   # 成功消息
    WARNING = "warning"                   # 警告消息
    INFO = "info"                         # 信息消息
    
    # 管理员命令 / Admin Commands
    ADMIN_COMMAND = "admin_command"       # 管理员命令
    SERVER_STATUS = "server_status"       # 服务器状态
    KICK_PLAYER = "kick_player"           # 踢出玩家
    BAN_PLAYER = "ban_player"             # 封禁玩家
    SERVER_SHUTDOWN = "server_shutdown"   # 服务器关闭

class ActionType:
    """游戏动作类型"""
    
    # 军事动作 / Military Actions
    RECRUIT_ARMY = "recruit_army"         # 招募军队
    MOVE_ARMY = "move_army"               # 移动军队
    ATTACK = "attack"                     # 攻击
    DEFEND = "defend"                     # 防御
    RETREAT = "retreat"                   # 撤退
    
    # 经济动作 / Economic Actions
    COLLECT_RESOURCES = "collect_resources" # 收集资源
    BUILD_INFRASTRUCTURE = "build_infrastructure" # 建设基础设施
    UPGRADE_BUILDING = "upgrade_building" # 升级建筑
    ESTABLISH_TRADE_ROUTE = "establish_trade_route" # 建立贸易路线
    
    # 科技动作 / Technology Actions
    RESEARCH_TECHNOLOGY = "research_technology" # 研究科技
    APPLY_TECHNOLOGY = "apply_technology" # 应用科技
    
    # 外交动作 / Diplomatic Actions
    PROPOSE_ALLIANCE = "propose_alliance" # 提议联盟
    BREAK_ALLIANCE = "break_alliance"     # 破坏联盟
    DECLARE_WAR = "declare_war"           # 宣战
    NEGOTIATE_PEACE = "negotiate_peace"   # 和平谈判
    
    # 特殊动作 / Special Actions
    USE_SPECIAL_ABILITY = "use_special_ability" # 使用特殊能力
    SURRENDER = "surrender"               # 投降
    PASS_TURN = "pass_turn"               # 跳过回合

class GamePhase:
    """游戏阶段"""
    
    LOBBY = "lobby"                       # 大厅阶段
    SETUP = "setup"                       # 设置阶段
    PLAYING = "playing"                   # 游戏进行中
    PAUSED = "paused"                     # 暂停
    ENDED = "ended"                       # 游戏结束

class TurnPhase:
    """回合阶段"""
    
    DEVELOPMENT = "development"           # 发展阶段
    EXPANSION = "expansion"               # 扩张阶段
    COMBAT = "combat"                     # 战斗阶段
    EVENTS = "events"                     # 事件阶段

class PlayerStatus:
    """玩家状态"""
    
    CONNECTED = "connected"               # 已连接
    DISCONNECTED = "disconnected"         # 已断开
    IN_GAME = "in_game"                   # 游戏中
    SPECTATING = "spectating"             # 观战中
    ELIMINATED = "eliminated"             # 被淘汰
    SURRENDERED = "surrendered"           # 已投降

class RoomStatus:
    """房间状态"""
    
    WAITING = "waiting"                   # 等待玩家
    STARTING = "starting"                 # 正在开始
    IN_PROGRESS = "in_progress"           # 游戏进行中
    PAUSED = "paused"                     # 暂停
    FINISHED = "finished"                 # 已结束
    CLOSED = "closed"                     # 已关闭

class ErrorCode:
    """错误代码"""
    
    # 连接错误 / Connection Errors
    CONNECTION_FAILED = 1001
    CONNECTION_TIMEOUT = 1002
    CONNECTION_LOST = 1003
    
    # 认证错误 / Authentication Errors
    INVALID_CREDENTIALS = 2001
    PLAYER_NAME_TAKEN = 2002
    PLAYER_NOT_FOUND = 2003
    
    # 房间错误 / Room Errors
    ROOM_NOT_FOUND = 3001
    ROOM_FULL = 3002
    ROOM_CLOSED = 3003
    ALREADY_IN_ROOM = 3004
    NOT_IN_ROOM = 3005
    
    # 游戏错误 / Game Errors
    GAME_NOT_STARTED = 4001
    INVALID_ACTION = 4002
    NOT_PLAYER_TURN = 4003
    INSUFFICIENT_RESOURCES = 4004
    INVALID_TARGET = 4005
    
    # 服务器错误 / Server Errors
    SERVER_FULL = 5001
    SERVER_MAINTENANCE = 5002
    INTERNAL_ERROR = 5003
    
    # 权限错误 / Permission Errors
    INSUFFICIENT_PERMISSIONS = 6001
    ADMIN_REQUIRED = 6002


@dataclass
class MessageMetadata:
    """消息元数据"""
    sender: Optional[str] = None
    session: Optional[str] = None
    room_id: Optional[str] = None
    target: Optional[str] = None


@dataclass
class Message:
    """标准化消息基类"""
    version: str = "2.0"
    type: str = ""
    id: str = ""
    timestamp: str = ""
    data: Dict[str, Any] = None
    metadata: MessageMetadata = None
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.timestamp:
            self.timestamp = time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime())
        if self.data is None:
            self.data = {}
        if self.metadata is None:
            self.metadata = MessageMetadata()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = asdict(self)
        # 清理空值
        if not result['metadata']['sender'] and not result['metadata']['session'] and \
           not result['metadata']['room_id'] and not result['metadata']['target']:
            result['metadata'] = {}
        return result
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        """从字典创建消息"""
        metadata_data = data.get('metadata', {})
        metadata = MessageMetadata(
            sender=metadata_data.get('sender'),
            session=metadata_data.get('session'),
            room_id=metadata_data.get('room_id'),
            target=metadata_data.get('target')
        )
        
        return cls(
            version=data.get('version', '2.0'),
            type=data.get('type', ''),
            id=data.get('id', ''),
            timestamp=data.get('timestamp', ''),
            data=data.get('data', {}),
            metadata=metadata
        )
    
    @classmethod
    def from_json(cls, json_str: str) -> 'Message':
        """从JSON字符串创建消息"""
        data = json.loads(json_str)
        return cls.from_dict(data)


class MessageValidator:
    """消息验证器"""
    
    PROTOCOL_VERSION = "2.0"
    MAX_MESSAGE_SIZE = 1024 * 1024  # 1MB
    REQUIRED_FIELDS = ['version', 'type', 'id', 'timestamp', 'data']
    
    @staticmethod
    def validate_message(message: Union[Message, Dict[str, Any]]) -> tuple[bool, str]:
        """
        验证消息格式
        
        Args:
            message: 消息对象或字典
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            # 转换为字典格式
            if isinstance(message, Message):
                msg_dict = message.to_dict()
            elif isinstance(message, dict):
                msg_dict = message
            else:
                return False, "消息必须是Message对象或字典"
            
            # 检查必需字段
            for field in MessageValidator.REQUIRED_FIELDS:
                if field not in msg_dict:
                    return False, f"缺少必需字段: {field}"
            
            # 验证协议版本
            if msg_dict.get('version') != MessageValidator.PROTOCOL_VERSION:
                return False, f"协议版本不匹配: {msg_dict.get('version')} != {MessageValidator.PROTOCOL_VERSION}"
            
            # 验证消息类型
            if not isinstance(msg_dict.get('type'), str) or not msg_dict.get('type'):
                return False, "消息类型必须是非空字符串"
            
            # 验证消息ID
            if not isinstance(msg_dict.get('id'), str) or not msg_dict.get('id'):
                return False, "消息ID必须是非空字符串"
            
            # 验证时间戳
            timestamp = msg_dict.get('timestamp')
            if not isinstance(timestamp, str) or not timestamp:
                return False, "时间戳必须是非空字符串"
            
            # 验证数据字段
            if not isinstance(msg_dict.get('data'), dict):
                return False, "数据字段必须是字典"
            
            # 验证消息大小
            message_size = len(json.dumps(msg_dict, ensure_ascii=False).encode('utf-8'))
            if message_size > MessageValidator.MAX_MESSAGE_SIZE:
                return False, f"消息过大: {message_size} > {MessageValidator.MAX_MESSAGE_SIZE}"
            
            # 验证元数据（如果存在）
            metadata = msg_dict.get('metadata')
            if metadata and not isinstance(metadata, dict):
                return False, "元数据必须是字典"
            
            return True, ""
            
        except Exception as e:
            return False, f"验证过程中发生错误: {str(e)}"
    
    @staticmethod
    def validate_message_type(message_type: str) -> bool:
        """验证消息类型是否有效"""
        try:
            # 检查是否是有效的MessageType枚举值
            valid_types = [mt.value for mt in MessageType]
            return message_type in valid_types
        except Exception:
            return False
    
    @staticmethod
    def sanitize_message_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """清理和验证消息数据"""
        if not isinstance(data, dict):
            return {}
        
        # 移除None值和空字符串
        sanitized = {}
        for key, value in data.items():
            if value is not None and value != "":
                if isinstance(value, dict):
                    sanitized[key] = MessageValidator.sanitize_message_data(value)
                elif isinstance(value, list):
                    sanitized[key] = [item for item in value if item is not None and item != ""]
                else:
                    sanitized[key] = value
        
        return sanitized


class MessageFactory:
    """消息工厂类"""
    
    @staticmethod
    def create_message(message_type: Union[MessageType, str], 
                      data: Dict[str, Any] = None,
                      sender: str = None,
                      session: str = None,
                      room_id: str = None,
                      target: str = None) -> Message:
        """
        创建标准化消息
        
        Args:
            message_type: 消息类型
            data: 消息数据
            sender: 发送者
            session: 会话ID
            room_id: 房间ID
            target: 目标
            
        Returns:
            Message对象
        """
        # 处理消息类型
        if isinstance(message_type, MessageType):
            msg_type = message_type.value
        else:
            msg_type = str(message_type)
        
        # 创建元数据
        metadata = MessageMetadata(
            sender=sender,
            session=session,
            room_id=room_id,
            target=target
        )
        
        # 清理数据
        clean_data = MessageValidator.sanitize_message_data(data or {})
        
        return Message(
            type=msg_type,
            data=clean_data,
            metadata=metadata
        )
    
    @staticmethod
    def create_error_message(error_code: int, error_message: str, 
                           sender: str = None, session: str = None) -> Message:
        """创建错误消息"""
        return MessageFactory.create_message(
            MessageType.ERROR,
            {
                "error_code": error_code,
                "error_message": error_message
            },
            sender=sender,
            session=session
        )
    
    @staticmethod
    def create_success_message(data: Dict[str, Any] = None,
                             sender: str = None, session: str = None) -> Message:
        """创建成功消息"""
        return MessageFactory.create_message(
            MessageType.SUCCESS,
            data or {"status": "success"},
            sender=sender,
            session=session
        )
    
    @staticmethod
    def create_heartbeat_message(sender: str = None, session: str = None) -> Message:
        """创建心跳消息"""
        return MessageFactory.create_message(
            MessageType.PING,
            {"heartbeat": True},
            sender=sender,
            session=session
        )
    
    @staticmethod
    def create_room_message(message_type: MessageType, room_data: Dict[str, Any],
                          sender: str = None, session: str = None, room_id: str = None) -> Message:
        """创建房间相关消息"""
        return MessageFactory.create_message(
            message_type,
            room_data,
            sender=sender,
            session=session,
            room_id=room_id
        )
