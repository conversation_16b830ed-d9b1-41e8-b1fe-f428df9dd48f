#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客户端用户名助手
"""

import random
import re
from shared.language_manager import SimpleLanguageManager

class ClientUsernameHelper:
    """处理客户端用户名的验证和生成"""

    def __init__(self, language_manager: SimpleLanguageManager):
        """
        初始化用户名助手

        Args:
            language_manager: 语言管理器实例
        """
        self.language_manager = language_manager
        self.validation_rules = {
            "min_length": 3,
            "max_length": 20,
            "allowed_chars": r"^[a-zA-Z0-9_]+$",
        }

    def validate_username(self, username: str) -> dict:
        """
        验证用户名是否符合规则

        Args:
            username: 要验证的用户名

        Returns:
            一个包含验证结果的字典
        """
        min_len = self.validation_rules["min_length"]
        if len(username) < min_len:
            return {
                "success": False,
                "message": self.language_manager.get_text("username_too_short", min_length=min_len),
            }

        max_len = self.validation_rules["max_length"]
        if len(username) > max_len:
            return {
                "success": False,
                "message": self.language_manager.get_text("username_too_long", max_length=max_len),
            }

        allowed_chars = self.validation_rules["allowed_chars"]
        if not re.match(allowed_chars, username):
            return {
                "success": False,
                "message": self.language_manager.get_text("username_invalid_chars"),
            }

        return {"success": True, "message": "Username is valid."}

    def generate_random_username(self) -> str:
        """
        生成一个随机的访客用户名

        Returns:
            一个随机的用户名
        """
        prefix = self.language_manager.get_text("guest_prefix")
        random_number = random.randint(1000, 9999)
        return f"{prefix}_{random_number}"
