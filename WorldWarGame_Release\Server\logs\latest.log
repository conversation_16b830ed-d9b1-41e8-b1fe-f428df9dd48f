2025-07-04 19:32:15 - server_ServerMain - INFO - [enhanced_logger.py:179] - 服务器启动
2025-07-04 19:32:15 - server_ServerMain - INFO - [enhanced_logger.py:179] - 🚀 世界大战游戏服务器启动
2025-07-04 19:32:15 - server_ServerPrint - INFO - [enhanced_logger.py:270] - ============================================================
2025-07-04 19:32:15 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 🎮 世界大战游戏服务器 / World War Game Server
2025-07-04 19:32:15 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 📡 版本: v3.0
2025-07-04 19:32:15 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 🌐 地址: localhost:8888
2025-07-04 19:32:15 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 📝 日志: logs/server/ 目录
2025-07-04 19:32:15 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 🛑 按 Ctrl+C 停止服务器
2025-07-04 19:32:15 - server_ServerPrint - INFO - [enhanced_logger.py:270] - ============================================================
2025-07-04 19:32:15 - server_ServerPrint - INFO - [enhanced_logger.py:270] - ============================================================
2025-07-04 19:32:15 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 🖥️ World War Strategy Game Server / 世界大战策略游戏服务器
2025-07-04 19:32:15 - server_ServerPrint - INFO - [enhanced_logger.py:270] - ============================================================
2025-07-04 19:32:15 - server_ServerPrint - INFO - [enhanced_logger.py:270] - ============================================================
2025-07-04 19:32:15 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 🌍 Language Selection / 语言选择
2025-07-04 19:32:15 - server_ServerPrint - INFO - [enhanced_logger.py:270] - ============================================================
2025-07-04 19:32:15 - server_ServerPrint - INFO - [enhanced_logger.py:270] - Available languages / 可用语言:
2025-07-04 19:32:15 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 1. 中文
2025-07-04 19:32:15 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 2. English
2025-07-04 19:32:15 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 3. Bilingual Mode / 双语模式 (中文 + English)
2025-07-04 19:32:15 - server_ServerPrint - INFO - [enhanced_logger.py:270] - Please select / 请选择 (1-3):
2025-07-04 19:33:04 - server_ServerPrint - INFO - [enhanced_logger.py:270] - Language set to / 语言设置为: 中文
2025-07-04 19:33:04 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 🖥️ 欢迎使用世界大战策略游戏服务器
2025-07-04 19:33:04 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 按任意键继续...
2025-07-04 19:33:05 - server_SignalHandler - INFO - [enhanced_logger.py:179] - 信号处理器已注册 - 世界大战游戏服务器
2025-07-04 19:33:05 - server_SignalHandler - INFO - [enhanced_logger.py:179] - 添加清理回调: cleanup
2025-07-04 19:33:05 - server_ServerMain - INFO - [enhanced_logger.py:179] - 初始化服务器 - 地址: localhost:8888
2025-07-04 19:33:05 - server_ConfigManager - INFO - [enhanced_logger.py:179] - 创建默认游戏配置文件
2025-07-04 19:33:05 - server_ConfigManager - INFO - [enhanced_logger.py:179] - 创建默认安全配置文件
2025-07-04 19:33:05 - server_ConfigManager - INFO - [enhanced_logger.py:179] - 配置管理器初始化完成
2025-07-04 19:33:05 - server_SecurityManager - INFO - [enhanced_logger.py:179] - 已生成新的服务器密钥
2025-07-04 19:33:05 - server_SecurityManager - INFO - [enhanced_logger.py:179] - 安全管理器初始化完成 - 服务器模式
2025-07-04 19:33:05 - server_SecureProtocol - INFO - [enhanced_logger.py:179] - 安全协议初始化完成 - 服务器模式
2025-07-04 19:33:05 - server_SessionManager - INFO - [enhanced_logger.py:179] - 线程安全会话管理器初始化完成 / Thread-safe session manager initialized
2025-07-04 19:33:05 - server_RoomIdGenerator - INFO - [enhanced_logger.py:179] - 房间ID生成器初始化完成 / Room ID generator initialized
2025-07-04 19:33:05 - server_MessageManager - INFO - [enhanced_logger.py:179] - 消息管理器初始化完成 - 服务器模式
2025-07-04 19:33:05 - server_RoomManager - INFO - [enhanced_logger.py:179] - 房间管理器初始化完成 / Room manager initialized
2025-07-04 19:33:05 - server_MessageManager - INFO - [enhanced_logger.py:179] - 消息管理器初始化完成 - 服务器模式
2025-07-04 19:33:05 - server_ConnectionManager - INFO - [enhanced_logger.py:179] - 连接管理器初始化完成 / Connection manager initialized
2025-07-04 19:33:05 - server_GameManager - INFO - [enhanced_logger.py:179] - 游戏管理器初始化完成 / Game manager initialized
2025-07-04 19:33:05 - server_MessageProcessor - INFO - [enhanced_logger.py:179] - 消息处理器初始化完成 / Message processor initialized
2025-07-04 19:33:05 - server_ServerMonitor - INFO - [enhanced_logger.py:179] - 服务器监控器初始化完成 / Server monitor initialized
2025-07-04 19:33:05 - server_SecureGameServer - INFO - [enhanced_logger.py:179] - 安全游戏服务器初始化完成 / Secure game server initialized
2025-07-04 19:33:05 - server_ServerMain - INFO - [enhanced_logger.py:179] - 服务器初始化完成，开始启动...
2025-07-04 19:33:05 - server_SecureGameServer - INFO - [enhanced_logger.py:179] - 正在启动游戏服务器...
2025-07-04 19:33:05 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 🚀 正在启动游戏服务器... / Starting game server...
2025-07-04 19:33:05 - server_SecureGameServer - INFO - [enhanced_logger.py:179] - 检测到首次运行，正在初始化数据...
2025-07-04 19:33:05 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 📦 初始化游戏数据... / Initializing game data...
2025-07-04 19:33:05 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 正在初始化服务器数据... / Initializing server data...
2025-07-04 19:33:05 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 正在初始化基础服务器数据... / Initializing base server data...
2025-07-04 19:33:05 - server_ServerPrint - INFO - [enhanced_logger.py:270] - ✅ 基础服务器数据初始化完成 / Base server data initialization complete
2025-07-04 19:33:05 - server_ServerPrint - INFO - [enhanced_logger.py:270] - ✅ 基础服务器数据初始化完成 / Base server data initialization complete
2025-07-04 19:33:05 - server_ServerPrint - INFO - [enhanced_logger.py:270] - ✅ 数据初始化完成 / Data initialization complete
2025-07-04 19:33:05 - server_ServerPrint - INFO - [enhanced_logger.py:270] - ✅ 成功绑定到 localhost:8888 / Successfully bound to localhost:8888
2025-07-04 19:33:05 - server_ServerPrint - INFO - [enhanced_logger.py:270] - ============================================================
2025-07-04 19:33:05 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 🎮 世界大战游戏服务器已启动 / World War Game Server Started
2025-07-04 19:33:05 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 📡 服务器地址 / Server Address: localhost:8888
2025-07-04 19:33:05 - server_ServerPrint - INFO - [enhanced_logger.py:270] - ⏳ 等待客户端连接... / Waiting for client connections...
2025-07-04 19:33:05 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 🛑 按 Ctrl+C 停止服务器 / Press Ctrl+C to stop server
2025-07-04 19:33:05 - server_ServerPrint - INFO - [enhanced_logger.py:270] - ============================================================
2025-07-04 19:33:05 - server_SecureGameServer - INFO - [enhanced_logger.py:179] - 服务器启动在 localhost:8888
2025-07-04 19:33:05 - server_ServerMonitor - INFO - [enhanced_logger.py:179] - 服务器状态 - 运行时间: 0秒, 活跃连接: 0, 总连接数: 0, 峰值连接: 0, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 19:33:05 - server_ServerMonitor - INFO - [enhanced_logger.py:179] - 服务器监控已启动，监控间隔: 30秒
2025-07-04 19:33:05 - server_SecureGameServer - INFO - [enhanced_logger.py:179] - 房间管理线程已启动 / Room management thread started
2025-07-04 19:33:18 - server_SecureGameServer - INFO - [enhanced_logger.py:179] - 新客户端连接: ('127.0.0.1', 40033)
2025-07-04 19:33:18 - server_ServerPrint - INFO - [enhanced_logger.py:270] - ✅ 新客户端连接 / New client connected: ('127.0.0.1', 40033)
2025-07-04 19:33:18 - server_SessionManager - INFO - [enhanced_logger.py:179] - 创建会话: session_0_1751628798 来自 ('127.0.0.1', 40033)
2025-07-04 19:33:18 - server_ConnectionManager - INFO - [enhanced_logger.py:179] - 新客户端连接
2025-07-04 19:33:18 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 📝 创建玩家会话 / Created player session: session_0_1751628798
2025-07-04 19:33:18 - server_ConnectionManager - INFO - [enhanced_logger.py:179] - 开始安全握手
2025-07-04 19:33:18 - server_SecureProtocol - INFO - [enhanced_logger.py:179] - 握手挑战已发送
2025-07-04 19:33:18 - server_SecureProtocol - INFO - [enhanced_logger.py:179] - 握手验证成功
2025-07-04 19:33:18 - server_ConnectionManager - INFO - [enhanced_logger.py:179] - 安全握手完成
2025-07-04 19:33:18 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 🔐 安全握手完成 / Secure handshake completed: session_0_1751628798
2025-07-04 19:33:21 - server_MessageProcessor - INFO - [enhanced_logger.py:179] - 收到客户端消息: join_game
2025-07-04 19:33:21 - server_SessionManager - INFO - [enhanced_logger.py:179] - 会话 session_0_1751628798 成功认证为玩家 sweatent
2025-07-04 19:33:21 - server_MessageProcessor - INFO - [enhanced_logger.py:179] - 玩家 sweatent 成功加入游戏
2025-07-04 19:33:21 - server_ServerPrint - INFO - [enhanced_logger.py:270] - ✅ 玩家加入游戏 / Player joined: sweatent
2025-07-04 19:33:32 - server_MessageProcessor - INFO - [enhanced_logger.py:179] - 收到客户端消息: create_room
2025-07-04 19:33:32 - server_MessageProcessor - INFO - [enhanced_logger.py:179] - 开始创建房间 - 玩家: sweatent, 房间名: 我的房间, 最大玩家: 4, 模式: ai_generated, 难度: normal
2025-07-04 19:33:32 - server_RoomManager - INFO - [enhanced_logger.py:179] - 创建房间: 我的房间 (ID: ROOM8812QIV, 类型: alphanumeric)
2025-07-04 19:33:32 - server_MessageProcessor - INFO - [enhanced_logger.py:179] - 房间创建成功 - ID: ROOM8812QIV, 名称: 我的房间
2025-07-04 19:33:32 - server_MessageProcessor - INFO - [enhanced_logger.py:179] - 房主 sweatent 尝试加入房间 ROOM8812QIV
2025-07-04 19:33:32 - server_MessageProcessor - INFO - [enhanced_logger.py:179] - 房主成功加入房间 - 玩家: sweatent, 房间: ROOM8812QIV
2025-07-04 19:33:32 - server_MessageProcessor - INFO - [enhanced_logger.py:179] - 发送房间创建响应 - 数据: {'success': True, 'room_info': {'room_id': 'ROOM8812QIV', 'room_name': '我的房间', 'is_host': True, 'max_players': 4, 'game_mode': 'ai_generated', 'difficulty': 'normal', 'is_private': False, 'status': 'waiting', 'host_player': 'session_0_1751628798'}}
2025-07-04 19:33:32 - server_MessageProcessor - INFO - [enhanced_logger.py:179] - ✅ 房间创建完成 - 玩家: sweatent, 房间ID: ROOM8812QIV, 模式: ai_generated
2025-07-04 19:33:35 - server_ServerMonitor - INFO - [enhanced_logger.py:179] - 服务器状态 - 运行时间: 30秒, 活跃连接: 1, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 19:34:03 - server_MessageProcessor - INFO - [enhanced_logger.py:179] - 收到客户端消息: ping
2025-07-04 19:34:05 - server_ServerMonitor - INFO - [enhanced_logger.py:179] - 服务器状态 - 运行时间: 60秒, 活跃连接: 1, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 19:34:35 - server_ServerMonitor - INFO - [enhanced_logger.py:179] - 服务器状态 - 运行时间: 90秒, 活跃连接: 1, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 19:34:48 - server_MessageProcessor - INFO - [enhanced_logger.py:179] - 收到客户端消息: ping
2025-07-04 19:35:05 - server_ServerMonitor - INFO - [enhanced_logger.py:179] - 服务器状态 - 运行时间: 120秒, 活跃连接: 1, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 19:35:33 - server_MessageProcessor - INFO - [enhanced_logger.py:179] - 收到客户端消息: ping
2025-07-04 19:35:33 - server_MessageProcessor - INFO - [enhanced_logger.py:179] - 收到客户端消息: leave_room
2025-07-04 19:35:33 - server_RoomManager - INFO - [enhanced_logger.py:179] - 玩家 sweatent 离开房间 ROOM8812QIV
2025-07-04 19:35:33 - server_RoomManager - INFO - [enhanced_logger.py:179] - 房间 ROOM8812QIV 已关闭并释放ID
2025-07-04 19:35:35 - server_ServerMonitor - INFO - [enhanced_logger.py:179] - 服务器状态 - 运行时间: 150秒, 活跃连接: 1, 总连接数: 1, 峰值连接: 1, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 19:35:43 - server_SecureGameServer - INFO - [enhanced_logger.py:179] - 新客户端连接: ('127.0.0.1', 40197)
2025-07-04 19:35:43 - server_ServerPrint - INFO - [enhanced_logger.py:270] - ✅ 新客户端连接 / New client connected: ('127.0.0.1', 40197)
2025-07-04 19:35:43 - server_SessionManager - INFO - [enhanced_logger.py:179] - 创建会话: session_1_1751628943 来自 ('127.0.0.1', 40197)
2025-07-04 19:35:43 - server_ConnectionManager - INFO - [enhanced_logger.py:179] - 新客户端连接
2025-07-04 19:35:43 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 📝 创建玩家会话 / Created player session: session_1_1751628943
2025-07-04 19:35:43 - server_ConnectionManager - INFO - [enhanced_logger.py:179] - 开始安全握手
2025-07-04 19:35:43 - server_SecureProtocol - INFO - [enhanced_logger.py:179] - 握手挑战已发送
2025-07-04 19:35:43 - server_SecurityManager - WARNING - [enhanced_logger.py:199] - 握手响应哈希验证失败
2025-07-04 19:35:43 - server_SecureProtocol - WARNING - [enhanced_logger.py:199] - 握手验证失败
2025-07-04 19:35:43 - server_ConnectionManager - ERROR - [enhanced_logger.py:209] - 安全握手失败 - 会话ID: session_1_1751628943, 客户端地址: ('127.0.0.1', 40197)
2025-07-04 19:35:43 - server_ConnectionManager - INFO - [enhanced_logger.py:179] - 客户端 ('127.0.0.1', 40197) 断开连接
2025-07-04 19:35:43 - server_SessionManager - INFO - [enhanced_logger.py:179] - 移除会话: session_1_1751628943
2025-07-04 19:35:43 - server_SecureGameServer - INFO - [enhanced_logger.py:179] - 客户端会话已清理: session_1_1751628943
2025-07-04 19:35:55 - server_SecureGameServer - INFO - [enhanced_logger.py:179] - 新客户端连接: ('127.0.0.1', 40214)
2025-07-04 19:35:55 - server_ServerPrint - INFO - [enhanced_logger.py:270] - ✅ 新客户端连接 / New client connected: ('127.0.0.1', 40214)
2025-07-04 19:35:55 - server_SessionManager - INFO - [enhanced_logger.py:179] - 创建会话: session_2_1751628955 来自 ('127.0.0.1', 40214)
2025-07-04 19:35:55 - server_ConnectionManager - INFO - [enhanced_logger.py:179] - 新客户端连接
2025-07-04 19:35:55 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 📝 创建玩家会话 / Created player session: session_2_1751628955
2025-07-04 19:35:55 - server_ConnectionManager - INFO - [enhanced_logger.py:179] - 开始安全握手
2025-07-04 19:35:55 - server_SecureProtocol - INFO - [enhanced_logger.py:179] - 握手挑战已发送
2025-07-04 19:35:55 - server_SecurityManager - WARNING - [enhanced_logger.py:199] - 握手响应哈希验证失败
2025-07-04 19:35:55 - server_SecureProtocol - WARNING - [enhanced_logger.py:199] - 握手验证失败
2025-07-04 19:35:55 - server_ConnectionManager - ERROR - [enhanced_logger.py:209] - 安全握手失败 - 会话ID: session_2_1751628955, 客户端地址: ('127.0.0.1', 40214)
2025-07-04 19:35:55 - server_ConnectionManager - INFO - [enhanced_logger.py:179] - 客户端 ('127.0.0.1', 40214) 断开连接
2025-07-04 19:35:55 - server_SessionManager - INFO - [enhanced_logger.py:179] - 移除会话: session_2_1751628955
2025-07-04 19:35:55 - server_SecureGameServer - INFO - [enhanced_logger.py:179] - 客户端会话已清理: session_2_1751628955
2025-07-04 19:36:05 - server_ServerMonitor - INFO - [enhanced_logger.py:179] - 服务器状态 - 运行时间: 180秒, 活跃连接: 1, 总连接数: 3, 峰值连接: 1, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 19:36:12 - server_SecureGameServer - INFO - [enhanced_logger.py:179] - 新客户端连接: ('127.0.0.1', 40232)
2025-07-04 19:36:12 - server_ServerPrint - INFO - [enhanced_logger.py:270] - ✅ 新客户端连接 / New client connected: ('127.0.0.1', 40232)
2025-07-04 19:36:12 - server_SessionManager - INFO - [enhanced_logger.py:179] - 创建会话: session_3_1751628972 来自 ('127.0.0.1', 40232)
2025-07-04 19:36:12 - server_ConnectionManager - INFO - [enhanced_logger.py:179] - 新客户端连接
2025-07-04 19:36:12 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 📝 创建玩家会话 / Created player session: session_3_1751628972
2025-07-04 19:36:12 - server_ConnectionManager - INFO - [enhanced_logger.py:179] - 开始安全握手
2025-07-04 19:36:12 - server_SecureProtocol - INFO - [enhanced_logger.py:179] - 握手挑战已发送
2025-07-04 19:36:12 - server_SecurityManager - WARNING - [enhanced_logger.py:199] - 握手响应哈希验证失败
2025-07-04 19:36:12 - server_SecureProtocol - WARNING - [enhanced_logger.py:199] - 握手验证失败
2025-07-04 19:36:12 - server_ConnectionManager - ERROR - [enhanced_logger.py:209] - 安全握手失败 - 会话ID: session_3_1751628972, 客户端地址: ('127.0.0.1', 40232)
2025-07-04 19:36:12 - server_ConnectionManager - INFO - [enhanced_logger.py:179] - 客户端 ('127.0.0.1', 40232) 断开连接
2025-07-04 19:36:12 - server_SessionManager - INFO - [enhanced_logger.py:179] - 移除会话: session_3_1751628972
2025-07-04 19:36:12 - server_SecureGameServer - INFO - [enhanced_logger.py:179] - 客户端会话已清理: session_3_1751628972
2025-07-04 19:36:33 - server_ConnectionManager - WARNING - [enhanced_logger.py:199] - 客户端心跳超时: session_0_1751628798
2025-07-04 19:36:33 - server_ServerPrint - INFO - [enhanced_logger.py:270] - ⚠️ 客户端心跳超时 / Client heartbeat timeout: session_0_1751628798
2025-07-04 19:36:33 - server_ConnectionManager - INFO - [enhanced_logger.py:179] - 客户端 ('127.0.0.1', 40033) 断开连接
2025-07-04 19:36:33 - server_SessionManager - INFO - [enhanced_logger.py:179] - 移除会话: session_0_1751628798
2025-07-04 19:36:33 - server_SecureGameServer - INFO - [enhanced_logger.py:179] - 客户端会话已清理: session_0_1751628798
2025-07-04 19:36:35 - server_ServerMonitor - INFO - [enhanced_logger.py:179] - 服务器状态 - 运行时间: 210秒, 活跃连接: 0, 总连接数: 4, 峰值连接: 1, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 19:37:05 - server_ServerMonitor - INFO - [enhanced_logger.py:179] - 服务器状态 - 运行时间: 240秒, 活跃连接: 0, 总连接数: 4, 峰值连接: 1, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 19:37:35 - server_ServerMonitor - INFO - [enhanced_logger.py:179] - 服务器状态 - 运行时间: 270秒, 活跃连接: 0, 总连接数: 4, 峰值连接: 1, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 19:38:05 - server_ServerMonitor - INFO - [enhanced_logger.py:179] - 服务器状态 - 运行时间: 300秒, 活跃连接: 0, 总连接数: 4, 峰值连接: 1, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 20:19:38 - server_ServerMain - INFO - [enhanced_logger.py:179] - 服务器启动
2025-07-04 20:19:38 - server_ServerMain - INFO - [enhanced_logger.py:179] - 🚀 世界大战游戏服务器启动
2025-07-04 20:19:38 - server_ServerPrint - INFO - [enhanced_logger.py:270] - ============================================================
2025-07-04 20:19:38 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 🎮 世界大战游戏服务器 / World War Game Server
2025-07-04 20:19:38 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 📡 版本: v3.0
2025-07-04 20:19:38 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 🌐 地址: localhost:8888
2025-07-04 20:19:38 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 📝 日志: logs/server/ 目录
2025-07-04 20:19:38 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 🛑 按 Ctrl+C 停止服务器
2025-07-04 20:19:38 - server_ServerPrint - INFO - [enhanced_logger.py:270] - ============================================================
2025-07-04 20:19:38 - server_ServerPrint - INFO - [enhanced_logger.py:270] - ============================================================
2025-07-04 20:19:38 - server_ServerPrint - INFO - [enhanced_logger.py:270] - World War Strategy Game Server / 世界大战策略游戏服务器
2025-07-04 20:19:38 - server_ServerPrint - INFO - [enhanced_logger.py:270] - ============================================================
2025-07-04 20:19:38 - server_ServerPrint - INFO - [enhanced_logger.py:270] - ============================================================
2025-07-04 20:19:38 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 🌍 Language Selection / 语言选择
2025-07-04 20:19:38 - server_ServerPrint - INFO - [enhanced_logger.py:270] - ============================================================
2025-07-04 20:19:38 - server_ServerPrint - INFO - [enhanced_logger.py:270] - Available languages / 可用语言:
2025-07-04 20:19:38 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 1. 中文
2025-07-04 20:19:38 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 2. English
2025-07-04 20:19:38 - server_ServerPrint - INFO - [enhanced_logger.py:270] - 3. Bilingual Mode / 双语模式 (中文 + English)
2025-07-04 20:19:38 - server_ServerPrint - INFO - [enhanced_logger.py:270] - Please select / 请选择 (1-3):
2025-07-04 20:39:46 - server_ServerMain - INFO - [enhanced_logger.py:211] - 服务器启动
2025-07-04 20:39:46 - server_ServerMain - INFO - [enhanced_logger.py:211] - 🚀 世界大战游戏服务器启动
2025-07-04 20:41:27 - server_ServerMain - INFO - [enhanced_logger.py:211] - 服务器启动
2025-07-04 20:41:27 - server_ServerMain - INFO - [enhanced_logger.py:211] - 🚀 世界大战游戏服务器启动
2025-07-04 20:41:52 - server_ServerPrint - INFO - [enhanced_logger.py:306] - ============================================================
2025-07-04 20:41:52 - server_ServerPrint - INFO - [enhanced_logger.py:306] - 🎮 世界大战游戏服务器 / World War Game Server
2025-07-04 20:41:52 - server_ServerPrint - INFO - [enhanced_logger.py:306] - 📡 版本: v3.0
2025-07-04 20:41:52 - server_ServerPrint - INFO - [enhanced_logger.py:306] - 🌐 地址: localhost:8888
2025-07-04 20:41:52 - server_ServerPrint - INFO - [enhanced_logger.py:306] - 📝 日志: logs/server/ 目录
2025-07-04 20:41:52 - server_ServerPrint - INFO - [enhanced_logger.py:306] - 🛑 按 Ctrl+C 停止服务器
2025-07-04 20:41:52 - server_ServerPrint - INFO - [enhanced_logger.py:306] - ============================================================
2025-07-04 20:41:52 - server_ServerPrint - INFO - [enhanced_logger.py:306] - 欢迎使用世界大战策略游戏服务器
2025-07-04 20:41:52 - server_ServerPrint - INFO - [enhanced_logger.py:306] - 按任意键继续...
2025-07-04 20:42:50 - server_ServerMain - INFO - [enhanced_logger.py:211] - 服务器启动
2025-07-04 20:42:50 - server_ServerMain - INFO - [enhanced_logger.py:211] - 🚀 世界大战游戏服务器启动
2025-07-04 20:42:53 - server_ServerPrint - INFO - [enhanced_logger.py:306] - ============================================================
2025-07-04 20:42:53 - server_ServerPrint - INFO - [enhanced_logger.py:306] - 🎮 世界大战游戏服务器 / World War Game Server
2025-07-04 20:42:53 - server_ServerPrint - INFO - [enhanced_logger.py:306] - 📡 版本: v3.0
2025-07-04 20:42:53 - server_ServerPrint - INFO - [enhanced_logger.py:306] - 🌐 地址: localhost:8888
2025-07-04 20:42:53 - server_ServerPrint - INFO - [enhanced_logger.py:306] - 📝 日志: logs/server/ 目录
2025-07-04 20:42:53 - server_ServerPrint - INFO - [enhanced_logger.py:306] - 🛑 按 Ctrl+C 停止服务器
2025-07-04 20:42:53 - server_ServerPrint - INFO - [enhanced_logger.py:306] - ============================================================
2025-07-04 20:42:53 - server_ServerPrint - INFO - [enhanced_logger.py:306] - 欢迎使用世界大战策略游戏服务器
2025-07-04 20:42:53 - server_ServerPrint - INFO - [enhanced_logger.py:306] - 按任意键继续...
