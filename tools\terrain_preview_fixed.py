#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地形预览工具 - 修复中文字体版本
Terrain Preview Tool - Fixed Chinese Font Version
"""

import json
import argparse
import sys
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import ListedColormap, BoundaryNorm
import matplotlib.font_manager as fm
import platform
import logging
from dataclasses import dataclass

# 强制设置中文字体
def force_chinese_font():
    """强制设置中文字体"""
    system = platform.system()
    
    if system == "Windows":
        font_name = 'Microsoft YaHei'
    elif system == "Darwin":
        font_name = 'PingFang SC'  
    else:
        font_name = 'WenQuanYi Micro Hei'
    
    # 强制设置所有字体参数
    plt.rcParams.update({
        'font.sans-serif': [font_name, 'DejaVu Sans', 'Arial'],
        'font.family': 'sans-serif',
        'font.serif': [font_name, 'DejaVu Serif'],
        'font.monospace': [font_name, 'DejaVu Sans Mono'],
        'axes.unicode_minus': False,
        'figure.titlesize': 'large',
        'axes.titlesize': 'medium',
        'axes.labelsize': 'medium'
    })
    
    print(f"✅ 强制设置中文字体: {font_name}")
    return True

# 初始化字体
force_chinese_font()

# 简化的数据结构
@dataclass
class TerrainCell:
    x: int
    y: int
    elevation: float
    biome: str
    temperature: float
    precipitation: float
    resources: Dict[str, float]

@dataclass  
class City:
    name: str
    population: int
    coordinates: Tuple[float, float]
    gdp_per_capita: float
    poverty_rate: float
    resources: Dict[str, float]
    infrastructure_level: float

@dataclass
class Region:
    name: str
    country: str
    center: Tuple[float, float]
    terrain: List[List[TerrainCell]]
    cities: List[City]
    economic_data: Dict[str, Any]
    demographic_data: Dict[str, Any]

class TerrainVisualizer:
    """地形可视化器 - 修复版"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 生物群系颜色映射
        self.biome_colors = {
            'ocean': '#1f77b4',      # 蓝色
            'plains': '#90EE90',     # 浅绿色
            'forest': '#228B22',     # 深绿色
            'desert': '#F4A460',     # 沙棕色
            'mountain': '#8B4513',   # 棕色
            'tundra': '#E0E0E0',     # 浅灰色
            'swamp': '#556B2F',      # 橄榄绿
            'grassland': '#9ACD32',  # 黄绿色
            'hills': '#CD853F',      # 秘鲁色
            'coastal': '#20B2AA'     # 浅海绿色
        }
    
    def load_region_data(self, data_file: Path) -> Region:
        """加载地区数据"""
        with open(data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 解析地形数据
        terrain = []
        for row in data.get('terrain', []):
            terrain_row = []
            for cell_data in row:
                cell = TerrainCell(
                    x=cell_data['x'],
                    y=cell_data['y'],
                    elevation=cell_data['elevation'],
                    biome=cell_data['biome'],
                    temperature=cell_data['temperature'],
                    precipitation=cell_data['precipitation'],
                    resources=cell_data.get('resources', {})
                )
                terrain_row.append(cell)
            terrain.append(terrain_row)
        
        # 解析城市数据
        cities = []
        for city_data in data.get('cities', []):
            city = City(
                name=city_data['name'],
                population=city_data['population'],
                coordinates=tuple(city_data['coordinates']),
                gdp_per_capita=city_data['gdp_per_capita'],
                poverty_rate=city_data['poverty_rate'],
                resources=city_data.get('resources', {}),
                infrastructure_level=city_data.get('infrastructure_level', 0.5)
            )
            cities.append(city)
        
        return Region(
            name=data['name'],
            country=data['country'],
            center=tuple(data['center']),
            terrain=terrain,
            cities=cities,
            economic_data=data.get('economic_data', {}),
            demographic_data=data.get('demographic_data', {})
        )
    
    def generate_elevation_map(self, region: Region, output_file: Path = None) -> plt.Figure:
        """生成高度图"""
        if not region.terrain:
            raise ValueError("地形数据为空")
        
        height = len(region.terrain)
        width = len(region.terrain[0])
        elevation_data = np.zeros((height, width))
        
        for y in range(height):
            for x in range(width):
                elevation_data[y, x] = region.terrain[y][x].elevation
        
        # 创建图像
        fig, ax = plt.subplots(figsize=(12, 10))
        
        # 绘制高度图
        im = ax.imshow(elevation_data, cmap='terrain', aspect='equal', origin='lower')
        
        # 添加等高线
        contours = ax.contour(elevation_data, levels=10, colors='black', alpha=0.3, linewidths=0.5)
        ax.clabel(contours, inline=True, fontsize=8, fmt='%d m')
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('海拔高度 (米)', fontsize=12)
        
        # 设置标题和标签 - 使用强制字体
        ax.set_title(f'{region.name} - 地形高度图', fontsize=16, fontweight='bold', 
                    fontfamily='sans-serif')
        ax.set_xlabel('X 坐标', fontsize=12, fontfamily='sans-serif')
        ax.set_ylabel('Y 坐标', fontsize=12, fontfamily='sans-serif')
        
        # 添加网格
        ax.grid(True, alpha=0.3)
        
        # 添加统计信息
        min_elev = np.min(elevation_data)
        max_elev = np.max(elevation_data)
        mean_elev = np.mean(elevation_data)
        
        stats_text = f'最低: {min_elev:.0f}m\n最高: {max_elev:.0f}m\n平均: {mean_elev:.0f}m'
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                verticalalignment='top', fontfamily='sans-serif',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        
        if output_file:
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            self.logger.info(f"高度图已保存到: {output_file}")
        
        return fig


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="地形预览工具 - 修复版")
    parser.add_argument("data_file", type=Path, help="地形数据文件 (JSON格式)")
    parser.add_argument("--output-dir", type=Path, default=Path("terrain_previews"), 
                       help="输出目录")
    parser.add_argument("--show", action="store_true", help="显示图像而不保存")
    
    args = parser.parse_args()
    
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(message)s')
    
    if not args.data_file.exists():
        print(f"❌ 数据文件不存在: {args.data_file}")
        return 1
    
    try:
        # 创建可视化器
        visualizer = TerrainVisualizer()
        
        # 加载数据
        print(f"📊 加载地形数据: {args.data_file}")
        region = visualizer.load_region_data(args.data_file)
        print(f"✅ 成功加载地区: {region.name}")
        
        # 生成地图
        if not args.show:
            args.output_dir.mkdir(parents=True, exist_ok=True)
            output_file = args.output_dir / f"{region.name}_elevation_fixed.png"
        else:
            output_file = None
        
        fig = visualizer.generate_elevation_map(region, output_file)
        
        if args.show:
            plt.show()
        
        print("✅ 地形预览生成完成!")
        return 0
        
    except Exception as e:
        print(f"❌ 生成地形预览失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())