#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WorldWar游戏启动器
WorldWar Game Launcher
"""

import os
import sys
import subprocess
import time
from pathlib import Path

# 添加worldwar-server到路径以导入语言管理器
sys.path.insert(0, str(Path(__file__).parent / "worldwar-server"))
from shared.enhanced_language_manager import get_enhanced_language_manager

lang_manager = get_enhanced_language_manager("server")
class GameLauncher:
    """游戏启动器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.server_dir = self.project_root / "worldwar-server"
        self.client_dir = self.project_root / "worldwar-client"
    
    def show_menu(self):
        """显示启动菜单"""
        print("=" * 60)
        print(lang_manager.get_text("launcher.title"))
        print(lang_manager.get_text("launcher.subtitle"))
        print("=" * 60)
        print()
        print(lang_manager.get_text("launcher.select_mode"))
        print()
        print(lang_manager.get_text("launcher.start_server"))
        print(lang_manager.get_text("launcher.start_client"))
        print(lang_manager.get_text("launcher.run_tests"))
        print(lang_manager.get_text("launcher.show_help"))
        print(lang_manager.get_text("launcher.quick_start"))
        print(lang_manager.get_text("launcher.exit"))
        print()
    
    def start_server(self, in_new_window=True):
        """启动服务器"""
        print(lang_manager.get_text("launcher.starting_server"))
        server_script = self.server_dir / "server_main.py"
        if not server_script.exists():
            print(lang_manager.get_text("launcher.server_script_not_found", script_path=server_script))
            return None

        if in_new_window:
            if sys.platform == "win32":
                return subprocess.Popen([sys.executable, str(server_script)], creationflags=subprocess.CREATE_NEW_CONSOLE)
            else:
                # For macOS and Linux
                # For macOS and Linux, start_new_session is a more modern way
                return subprocess.Popen([sys.executable, str(server_script)], start_new_session=True)
        else:
            return subprocess.Popen([sys.executable, str(server_script)])
    
    def start_client(self):
        """启动客户端"""
        print(lang_manager.get_text("launcher.starting_client"))
        client_script = self.client_dir / "client_main.py"
        if not client_script.exists():
            print(lang_manager.get_text("launcher.client_script_not_found", script_path=client_script))
            return None
        
        try:
            # 在当前窗口运行客户端
            process = subprocess.Popen([sys.executable, str(client_script)])
            process.wait()
        except KeyboardInterrupt:
            print(lang_manager.get_text("launcher.client_exited"))
        except Exception as e:
            print(lang_manager.get_text("launcher.client_start_failed", error=e))
    
    def run_tests(self):
        """运行测试"""
        print(lang_manager.get_text("launcher.running_tests"))
        print("=" * 40)

        # 检查服务器和客户端是否可以正常启动
        print(lang_manager.get_text("launcher.test_server_script"))
        server_script = self.project_root / "worldwar-server" / "server_main.py"
        if not server_script.exists():
            print(lang_manager.get_text("launcher.server_script_missing"))
            return False

        print(lang_manager.get_text("launcher.test_client_script"))
        client_script = self.project_root / "worldwar-client" / "client_main.py"
        if not client_script.exists():
            print(lang_manager.get_text("launcher.client_script_missing"))
            return False

        print(lang_manager.get_text("launcher.all_scripts_ok"))
        print(lang_manager.get_text("launcher.server_tip"))
        print(lang_manager.get_text("launcher.client_tip"))
        return True
    
    def show_help(self):
        """显示帮助"""
        print(f"\n{lang_manager.get_text('launcher.help_title')}")
        print("=" * 40)
        print()
        print(lang_manager.get_text("launcher.game_intro"))
        print(f"   {lang_manager.get_text('launcher.game_description')}")
        print(f"   {lang_manager.get_text('launcher.game_interaction')}")
        print()
        print(lang_manager.get_text("launcher.quick_start_title"))
        print(f"   {lang_manager.get_text('launcher.quick_start_step1')}")
        print(f"   {lang_manager.get_text('launcher.quick_start_step2')}")
        print(f"   {lang_manager.get_text('launcher.quick_start_step3')}")
        print(f"   {lang_manager.get_text('launcher.quick_start_step4')}")
        print()
        print(lang_manager.get_text("launcher.advanced_usage"))
        print(f"   {lang_manager.get_text('launcher.server_custom')}")
        print(f"   {lang_manager.get_text('launcher.client_custom')}")
        print(f"   {lang_manager.get_text('launcher.multi_client')}")
        print()
        print(lang_manager.get_text("launcher.language_support"))
        print(f"   {lang_manager.get_text('launcher.chinese_interface')}")
        print(f"   {lang_manager.get_text('launcher.english_interface')}")
        print(f"   {lang_manager.get_text('launcher.bilingual_mode')}")
        print()
        print(lang_manager.get_text("launcher.tech_support"))
        print(f"   {lang_manager.get_text('launcher.check_summary')}")
        print(f"   {lang_manager.get_text('launcher.run_tests_check')}")
        print()
    
    def quick_start(self):
        """快速开始"""
        print(f"\n{lang_manager.get_text('launcher.quick_starting')}")
        server_process = self.start_server()
        if not server_process:
            return

        print(f"\n{lang_manager.get_text('launcher.waiting_server')}")
        time.sleep(3)

        self.start_client()

        # 清理服务器进程
        print(f"\n{lang_manager.get_text('launcher.closing_server')}")
        server_process.terminate()
        try:
            server_process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            server_process.kill()
    
    def run(self):
        """运行启动器"""
        while True:
            try:
                self.show_menu()
                choice = input("请选择 / Please select (0-5): ").strip()
                
                if choice == "1":
                    self.start_server()
                elif choice == "2":
                    self.start_client()
                elif choice == "3":
                    self.run_tests()
                elif choice == "4":
                    self.show_help()
                elif choice == "5":
                    self.quick_start()
                elif choice == "0":
                    print("\n👋 再见! / Goodbye!")
                    break
                else:
                    print("\n❌ 无效选择，请重试 / Invalid choice, please try again")
                
                if choice != "0":
                    input("\n按回车键返回主菜单 / Press Enter to return to main menu...")
                
            except KeyboardInterrupt:
                print("\n\n👋 再见! / Goodbye!")
                break
            except Exception as e:
                print(f"\n❌ 错误: {e}")
                input("按回车键继续 / Press Enter to continue...")

def main():
    """主函数"""
    launcher = GameLauncher()
    launcher.run()

if __name__ == "__main__":
    main()
