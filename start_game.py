#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WorldWar游戏启动器
WorldWar Game Launcher
"""

import sys
import subprocess
import time
from pathlib import Path

# 简化的双语文本
TEXTS = {
    "title": "🎮 WorldWar 世界大战策略游戏启动器",
    "subtitle": "🎯 World War Strategy Game Launcher",
    "select_mode": "请选择启动模式 / Please select launch mode:",
    "start_server": "1. 🖥️  启动服务器 / Start Server",
    "start_client": "2. 🎮 启动客户端 / Start Client",
    "run_tests": "3. 🧪 运行测试 / Run Tests",
    "show_help": "4. 📚 查看帮助 / Show Help",
    "quick_start": "5. 🚀 快速开始 / Quick Start",
    "exit": "0. ❌ 退出 / Exit",
    "starting_server": "🖥️ 启动服务器...",
    "starting_client": "🎮 启动客户端...",
    "server_start_failed": "❌ 启动服务器失败: {error}",
    "client_start_failed": "❌ 启动客户端失败: {error}",
    "waiting_server": "⏳ 等待服务器启动...",
    "goodbye": "👋 再见! / Goodbye!",
    "invalid_choice": "❌ 无效选择，请重试 / Invalid choice, please try again",
    "press_enter": "按回车键返回主菜单 / Press Enter to return to main menu...",
    "press_enter_continue": "按回车键继续 / Press Enter to continue..."
}
class GameLauncher:
    """游戏启动器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.server_dir = self.project_root / "worldwar-server"
        self.client_dir = self.project_root / "worldwar-client"
    
    def show_menu(self):
        """显示启动菜单"""
        print("=" * 60)
        print(TEXTS["title"])
        print(TEXTS["subtitle"])
        print("=" * 60)
        print()
        print(TEXTS["select_mode"])
        print()
        print(TEXTS["start_server"])
        print(TEXTS["start_client"])
        print(TEXTS["run_tests"])
        print(TEXTS["show_help"])
        print(TEXTS["quick_start"])
        print(TEXTS["exit"])
        print()
    
    def start_server(self, in_new_window=True):
        """启动服务器"""
        print(TEXTS["starting_server"])
        server_script = self.server_dir / "server_main.py"
        if not server_script.exists():
            print(f"❌ 服务器脚本未找到: {server_script}")
            return None

        try:
            if in_new_window:
                if sys.platform == "win32":
                    return subprocess.Popen([sys.executable, str(server_script)], creationflags=subprocess.CREATE_NEW_CONSOLE)
                else:
                    # For macOS and Linux
                    # For macOS and Linux, start_new_session is a more modern way
                    return subprocess.Popen([sys.executable, str(server_script)], start_new_session=True)
            else:
                return subprocess.Popen([sys.executable, str(server_script)])
        except Exception as e:
            print(TEXTS["server_start_failed"].format(error=str(e)))
            return None
    
    def start_client(self):
        """启动客户端"""
        print(TEXTS["starting_client"])
        client_script = self.client_dir / "client_main.py"
        if not client_script.exists():
            print(f"❌ 客户端脚本未找到: {client_script}")
            return None

        try:
            # 在当前窗口运行客户端
            process = subprocess.Popen([sys.executable, str(client_script)])
            process.wait()
        except KeyboardInterrupt:
            print("🛑 客户端已退出")
        except Exception as e:
            print(TEXTS["client_start_failed"].format(error=str(e)))
    
    def run_tests(self):
        """运行测试"""
        print(lang_manager.get_text("launcher.running_tests"))
        print("=" * 40)

        # 检查服务器和客户端是否可以正常启动
        print(lang_manager.get_text("launcher.test_server_script"))
        server_script = self.project_root / "worldwar-server" / "server_main.py"
        if not server_script.exists():
            print(lang_manager.get_text("launcher.server_script_missing"))
            return False

        print(lang_manager.get_text("launcher.test_client_script"))
        client_script = self.project_root / "worldwar-client" / "client_main.py"
        if not client_script.exists():
            print(lang_manager.get_text("launcher.client_script_missing"))
            return False

        print(lang_manager.get_text("launcher.all_scripts_ok"))
        print(lang_manager.get_text("launcher.server_tip"))
        print(lang_manager.get_text("launcher.client_tip"))
        return True
    
    def show_help(self):
        """显示帮助"""
        print(f"\n{lang_manager.get_text('launcher.help_title')}")
        print("=" * 40)
        print()
        print(lang_manager.get_text("launcher.game_intro"))
        print(f"   {lang_manager.get_text('launcher.game_description')}")
        print(f"   {lang_manager.get_text('launcher.game_interaction')}")
        print()
        print(lang_manager.get_text("launcher.quick_start_title"))
        print(f"   {lang_manager.get_text('launcher.quick_start_step1')}")
        print(f"   {lang_manager.get_text('launcher.quick_start_step2')}")
        print(f"   {lang_manager.get_text('launcher.quick_start_step3')}")
        print(f"   {lang_manager.get_text('launcher.quick_start_step4')}")
        print()
        print(lang_manager.get_text("launcher.advanced_usage"))
        print(f"   {lang_manager.get_text('launcher.server_custom')}")
        print(f"   {lang_manager.get_text('launcher.client_custom')}")
        print(f"   {lang_manager.get_text('launcher.multi_client')}")
        print()
        print(lang_manager.get_text("launcher.language_support"))
        print(f"   {lang_manager.get_text('launcher.chinese_interface')}")
        print(f"   {lang_manager.get_text('launcher.english_interface')}")
        print(f"   {lang_manager.get_text('launcher.bilingual_mode')}")
        print()
        print(lang_manager.get_text("launcher.tech_support"))
        print(f"   {lang_manager.get_text('launcher.check_summary')}")
        print(f"   {lang_manager.get_text('launcher.run_tests_check')}")
        print()
    
    def quick_start(self):
        """快速开始"""
        print(f"\n{lang_manager.get_text('launcher.quick_starting')}")
        server_process = self.start_server()
        if not server_process:
            return

        print(f"\n{lang_manager.get_text('launcher.waiting_server')}")
        time.sleep(3)

        self.start_client()

        # 清理服务器进程
        print(f"\n{lang_manager.get_text('launcher.closing_server')}")
        server_process.terminate()
        try:
            server_process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            server_process.kill()
    
    def run(self):
        """运行启动器"""
        while True:
            try:
                self.show_menu()
                choice = input("请选择 / Please select (0-5): ").strip()
                
                if choice == "1":
                    self.start_server()
                elif choice == "2":
                    self.start_client()
                elif choice == "3":
                    self.run_tests()
                elif choice == "4":
                    self.show_help()
                elif choice == "5":
                    self.quick_start()
                elif choice == "0":
                    print(f"\n{TEXTS['goodbye']}")
                    break
                else:
                    print(f"\n{TEXTS['invalid_choice']}")

                if choice != "0":
                    input(f"\n{TEXTS['press_enter']}")

            except KeyboardInterrupt:
                print(f"\n\n{TEXTS['goodbye']}")
                break
            except Exception as e:
                print(f"\n❌ 错误: {e}")
                input(TEXTS['press_enter_continue'])

def main():
    """主函数"""
    launcher = GameLauncher()
    launcher.run()

if __name__ == "__main__":
    main()
