#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI数据生成器模块
AI Data Generator Module
"""

import random
import math
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass

@dataclass
class GenerationConfig:
    """生成配置"""
    world_size: int = 100  # 世界大小（领土数量）
    city_count: int = 50   # 城市数量
    seed: int = None       # 随机种子
    
class AIDataGenerator:
    """AI数据生成器"""
    
    def __init__(self, config: GenerationConfig = None):
        """初始化AI数据生成器"""
        self.config = config or GenerationConfig()
        
        # 设置随机种子
        if self.config.seed is None:
            self.config.seed = random.randint(1, 1000000)
        
        random.seed(self.config.seed)
        
        # 预定义数据
        self.terrain_types = ["mountain", "plain", "desert", "forest", "coastal", "swamp", "tundra"]
        self.climate_types = ["tropical", "temperate", "arid", "polar", "mediterranean", "continental"]
        self.resource_types = ["iron", "coal", "oil", "gold", "copper", "uranium", "diamonds", "food", "water"]
        self.disaster_types = ["earthquake", "flood", "drought", "hurricane", "wildfire", "volcanic_eruption"]
        
        # 国家和城市名称模板
        self.country_prefixes = ["北", "南", "东", "西", "中", "新", "古", "大", "小"]
        self.country_suffixes = ["国", "邦", "州", "省", "岛", "联邦", "共和国", "王国"]
        self.city_prefixes = ["新", "老", "大", "小", "东", "西", "南", "北", "中"]
        self.city_suffixes = ["城", "市", "镇", "港", "堡", "村", "都", "府"]
    
    def get_seed(self) -> int:
        """获取当前使用的随机种子"""
        return self.config.seed
    
    def generate_poverty_cities(self) -> Dict[str, Any]:
        """生成贫困城市数据"""
        cities = []

        for i in range(self.config.city_count):
            # 生成城市名称
            city_name = self._generate_city_name()
            country_name = self._generate_country_name()

            # 生成贫困指数（0.5-1.0，越高越贫困）
            poverty_index = random.uniform(0.5, 1.0)

            # 生成人口（10万-200万）
            population = random.randint(100000, 2000000)

            # 根据贫困指数调整人口
            if poverty_index > 0.8:
                population = int(population * 0.7)  # 极度贫困地区人口较少

            # 生成经济数据
            economic_data = self._generate_economic_data(poverty_index, population)

            city = {
                "id": f"city_{i+1}",
                "name": city_name,
                "country": country_name,
                "poverty_index": round(poverty_index, 2),
                "population": population,
                "coordinates": self._generate_coordinates(),
                "infrastructure_level": self._calculate_infrastructure_level(poverty_index),
                "education_level": self._calculate_education_level(poverty_index),
                "health_level": self._calculate_health_level(poverty_index),
                "economic_data": economic_data,
                "development_potential": self._calculate_development_potential(poverty_index),
                "main_industries": self._generate_main_industries(poverty_index),
                "challenges": self._generate_challenges(poverty_index),
                "opportunities": self._generate_opportunities(poverty_index)
            }

            cities.append(city)

        return {
            "cities": cities,
            "generation_info": {
                "seed": self.config.seed,
                "total_cities": len(cities),
                "average_poverty": round(sum(c["poverty_index"] for c in cities) / len(cities), 2),
                "poverty_distribution": self._analyze_poverty_distribution(cities)
            }
        }
    
    def generate_terrain_data(self) -> Dict[str, Any]:
        """生成地形数据"""
        # 地形效果定义
        terrain_effects = {
            "mountain": {
                "defense_bonus": 0.4,
                "movement_penalty": 0.3,
                "resource_bonus": {"iron": 1.5, "coal": 1.3},
                "disaster_risk": {"earthquake": 0.3, "volcanic_eruption": 0.1}
            },
            "plain": {
                "defense_bonus": 0.0,
                "movement_penalty": 0.0,
                "resource_bonus": {"food": 1.2},
                "disaster_risk": {"flood": 0.2}
            },
            "desert": {
                "defense_bonus": 0.1,
                "movement_penalty": 0.4,
                "resource_bonus": {"oil": 1.4, "uranium": 1.2},
                "disaster_risk": {"drought": 0.4}
            },
            "forest": {
                "defense_bonus": 0.3,
                "movement_penalty": 0.2,
                "resource_bonus": {"food": 1.1, "water": 1.2},
                "disaster_risk": {"wildfire": 0.3}
            },
            "coastal": {
                "defense_bonus": 0.1,
                "movement_penalty": 0.0,
                "resource_bonus": {"food": 1.3, "water": 1.5},
                "disaster_risk": {"hurricane": 0.3, "flood": 0.2}
            },
            "swamp": {
                "defense_bonus": 0.2,
                "movement_penalty": 0.5,
                "resource_bonus": {"water": 1.8},
                "disaster_risk": {"flood": 0.4}
            },
            "tundra": {
                "defense_bonus": 0.1,
                "movement_penalty": 0.3,
                "resource_bonus": {"uranium": 1.1},
                "disaster_risk": {}
            }
        }
        
        # 生成地形分布
        terrain_distribution = self._generate_terrain_distribution()
        
        return {
            "terrain_types": self.terrain_types,
            "terrain_effects": terrain_effects,
            "terrain_distribution": terrain_distribution,
            "generation_info": {
                "seed": self.config.seed,
                "world_size": self.config.world_size
            }
        }
    
    def generate_climate_data(self) -> Dict[str, Any]:
        """生成气候数据"""
        climate_effects = {
            "tropical": {
                "temperature_range": (20, 35),
                "rainfall": "high",
                "growing_season": 12,
                "disaster_multiplier": {"hurricane": 1.5, "flood": 1.3}
            },
            "temperate": {
                "temperature_range": (5, 25),
                "rainfall": "medium",
                "growing_season": 8,
                "disaster_multiplier": {}
            },
            "arid": {
                "temperature_range": (10, 40),
                "rainfall": "low",
                "growing_season": 4,
                "disaster_multiplier": {"drought": 2.0}
            },
            "polar": {
                "temperature_range": (-20, 5),
                "rainfall": "low",
                "growing_season": 2,
                "disaster_multiplier": {}
            },
            "mediterranean": {
                "temperature_range": (10, 30),
                "rainfall": "medium",
                "growing_season": 10,
                "disaster_multiplier": {"wildfire": 1.2}
            },
            "continental": {
                "temperature_range": (-10, 30),
                "rainfall": "medium",
                "growing_season": 6,
                "disaster_multiplier": {"drought": 1.2}
            }
        }
        
        # 生成季节变化模式
        seasonal_patterns = self._generate_seasonal_patterns()
        
        return {
            "climate_types": self.climate_types,
            "climate_effects": climate_effects,
            "seasonal_patterns": seasonal_patterns,
            "generation_info": {
                "seed": self.config.seed
            }
        }
    
    def generate_resource_data(self) -> Dict[str, Any]:
        """生成资源数据"""
        # 资源基础属性
        resource_properties = {
            "iron": {"rarity": 0.3, "value": 10, "depletion_rate": 0.05},
            "coal": {"rarity": 0.4, "value": 8, "depletion_rate": 0.03},
            "oil": {"rarity": 0.2, "value": 15, "depletion_rate": 0.08},
            "gold": {"rarity": 0.1, "value": 50, "depletion_rate": 0.02},
            "copper": {"rarity": 0.35, "value": 12, "depletion_rate": 0.04},
            "uranium": {"rarity": 0.05, "value": 100, "depletion_rate": 0.01},
            "diamonds": {"rarity": 0.03, "value": 200, "depletion_rate": 0.01},
            "food": {"rarity": 0.8, "value": 5, "depletion_rate": 0.0},  # 可再生
            "water": {"rarity": 0.6, "value": 3, "depletion_rate": 0.0}   # 可再生
        }
        
        # 生成全球资源分布
        global_distribution = self._generate_global_resource_distribution()
        
        return {
            "resource_types": self.resource_types,
            "resource_properties": resource_properties,
            "global_distribution": global_distribution,
            "generation_info": {
                "seed": self.config.seed
            }
        }
    
    def generate_disaster_data(self) -> Dict[str, Any]:
        """生成灾害数据"""
        disaster_properties = {
            "earthquake": {
                "severity_range": (1, 10),
                "duration": (1, 3),  # 天数
                "affected_radius": (1, 3),  # 影响范围
                "damage_types": ["infrastructure", "population", "resources"]
            },
            "flood": {
                "severity_range": (1, 8),
                "duration": (3, 14),
                "affected_radius": (2, 5),
                "damage_types": ["agriculture", "infrastructure", "population"]
            },
            "drought": {
                "severity_range": (1, 7),
                "duration": (30, 365),
                "affected_radius": (3, 8),
                "damage_types": ["agriculture", "water_supply", "population"]
            },
            "hurricane": {
                "severity_range": (1, 5),
                "duration": (1, 7),
                "affected_radius": (2, 6),
                "damage_types": ["infrastructure", "agriculture", "population"]
            },
            "wildfire": {
                "severity_range": (1, 6),
                "duration": (1, 30),
                "affected_radius": (1, 4),
                "damage_types": ["forest", "infrastructure", "population"]
            },
            "volcanic_eruption": {
                "severity_range": (1, 8),
                "duration": (1, 180),
                "affected_radius": (1, 10),
                "damage_types": ["infrastructure", "agriculture", "population", "climate"]
            }
        }
        
        # 生成灾害频率模式
        frequency_patterns = self._generate_disaster_frequency_patterns()
        
        return {
            "disaster_types": self.disaster_types,
            "disaster_properties": disaster_properties,
            "frequency_patterns": frequency_patterns,
            "generation_info": {
                "seed": self.config.seed
            }
        }
    
    def generate_cities_data(self) -> Dict[str, Any]:
        """生成城市数据（扩展版）"""
        cities = []
        
        for i in range(self.config.world_size):
            city = {
                "id": f"territory_{i+1}",
                "name": self._generate_city_name(),
                "type": random.choice(["city", "town", "village", "outpost"]),
                "coordinates": self._generate_coordinates(),
                "terrain": random.choice(self.terrain_types),
                "climate": random.choice(self.climate_types),
                "population": random.randint(1000, 500000),
                "development_level": random.uniform(0.1, 1.0),
                "resources": self._generate_territory_resources(),
                "strategic_value": random.randint(1, 10)
            }
            
            cities.append(city)
        
        return {
            "territories": cities,
            "generation_info": {
                "seed": self.config.seed,
                "total_territories": len(cities)
            }
        }
    
    def _generate_city_name(self) -> str:
        """生成城市名称"""
        prefix = random.choice(self.city_prefixes)
        suffix = random.choice(self.city_suffixes)
        return f"{prefix}{suffix}"
    
    def _generate_country_name(self) -> str:
        """生成国家名称"""
        prefix = random.choice(self.country_prefixes)
        suffix = random.choice(self.country_suffixes)
        return f"{prefix}{suffix}"
    
    def _generate_coordinates(self) -> Tuple[float, float]:
        """生成地理坐标"""
        latitude = random.uniform(-90, 90)
        longitude = random.uniform(-180, 180)
        return (round(latitude, 4), round(longitude, 4))
    
    def _calculate_infrastructure_level(self, poverty_index: float) -> float:
        """根据贫困指数计算基础设施水平"""
        # 贫困指数越高，基础设施越差
        infrastructure = 1.0 - poverty_index + random.uniform(-0.1, 0.1)
        return max(0.0, min(1.0, infrastructure))
    
    def _calculate_education_level(self, poverty_index: float) -> float:
        """根据贫困指数计算教育水平"""
        education = 1.0 - poverty_index * 0.8 + random.uniform(-0.15, 0.15)
        return max(0.0, min(1.0, education))
    
    def _calculate_health_level(self, poverty_index: float) -> float:
        """根据贫困指数计算健康水平"""
        health = 1.0 - poverty_index * 0.9 + random.uniform(-0.1, 0.1)
        return max(0.0, min(1.0, health))
    
    def _generate_terrain_distribution(self) -> Dict[str, float]:
        """生成地形分布"""
        distribution = {}
        remaining = 1.0
        
        for i, terrain in enumerate(self.terrain_types[:-1]):
            if remaining > 0:
                percentage = random.uniform(0.05, remaining * 0.3)
                distribution[terrain] = round(percentage, 3)
                remaining -= percentage
        
        # 最后一个地形类型获得剩余的百分比
        distribution[self.terrain_types[-1]] = round(remaining, 3)
        
        return distribution
    
    def _generate_seasonal_patterns(self) -> Dict[str, Any]:
        """生成季节变化模式"""
        return {
            "spring": {"temperature_modifier": 0.0, "rainfall_modifier": 1.2},
            "summer": {"temperature_modifier": 0.3, "rainfall_modifier": 0.8},
            "autumn": {"temperature_modifier": 0.1, "rainfall_modifier": 1.1},
            "winter": {"temperature_modifier": -0.2, "rainfall_modifier": 0.9}
        }
    
    def _generate_global_resource_distribution(self) -> Dict[str, List[Dict]]:
        """生成全球资源分布"""
        distribution = {}
        
        for resource in self.resource_types:
            resource_deposits = []
            deposit_count = random.randint(5, 20)
            
            for _ in range(deposit_count):
                deposit = {
                    "coordinates": self._generate_coordinates(),
                    "richness": random.uniform(0.1, 1.0),
                    "accessibility": random.uniform(0.3, 1.0),
                    "estimated_reserves": random.randint(1000, 100000)
                }
                resource_deposits.append(deposit)
            
            distribution[resource] = resource_deposits
        
        return distribution
    
    def _generate_disaster_frequency_patterns(self) -> Dict[str, Dict]:
        """生成灾害频率模式"""
        patterns = {}
        
        for disaster in self.disaster_types:
            patterns[disaster] = {
                "base_frequency": random.uniform(0.01, 0.1),  # 每回合发生概率
                "seasonal_modifiers": {
                    "spring": random.uniform(0.8, 1.2),
                    "summer": random.uniform(0.8, 1.2),
                    "autumn": random.uniform(0.8, 1.2),
                    "winter": random.uniform(0.8, 1.2)
                },
                "geographic_modifiers": {
                    terrain: random.uniform(0.5, 2.0) 
                    for terrain in self.terrain_types
                }
            }
        
        return patterns
    
    def _generate_territory_resources(self) -> Dict[str, int]:
        """生成领土资源"""
        resources = {}

        for resource in self.resource_types:
            # 30%的概率该领土有这种资源
            if random.random() < 0.3:
                amount = random.randint(10, 1000)
                resources[resource] = amount

        return resources

    def _generate_economic_data(self, poverty_index: float, population: int) -> Dict[str, Any]:
        """生成经济数据"""
        # 基于贫困指数计算经济指标
        gdp_per_capita = max(1000, 50000 * (1 - poverty_index) + random.uniform(-5000, 5000))
        gdp = gdp_per_capita * population

        unemployment_rate = max(0.02, poverty_index * 0.4 + random.uniform(-0.05, 0.05))
        inflation_rate = max(0.01, poverty_index * 0.1 + random.uniform(-0.02, 0.02))

        return {
            "gdp": round(gdp, 2),
            "gdp_per_capita": round(gdp_per_capita, 2),
            "unemployment_rate": round(unemployment_rate, 3),
            "inflation_rate": round(inflation_rate, 3),
            "main_currency": self._generate_currency_name()
        }

    def _calculate_development_potential(self, poverty_index: float) -> Dict[str, float]:
        """计算发展潜力"""
        # 贫困地区往往有更大的发展潜力
        base_potential = 1 - poverty_index + 0.3

        return {
            "education_potential": min(1.0, base_potential + random.uniform(-0.2, 0.3)),
            "industrial_potential": min(1.0, base_potential + random.uniform(-0.3, 0.2)),
            "tourism_potential": min(1.0, random.uniform(0.1, 0.8)),
            "tech_potential": min(1.0, base_potential * 0.7 + random.uniform(-0.1, 0.4))
        }

    def _generate_main_industries(self, poverty_index: float) -> List[str]:
        """生成主要产业"""
        all_industries = [
            "农业", "渔业", "畜牧业", "林业",
            "采矿业", "制造业", "纺织业", "食品加工",
            "建筑业", "交通运输", "商贸", "旅游业",
            "金融服务", "教育", "医疗", "信息技术"
        ]

        # 贫困地区更多传统产业
        if poverty_index > 0.7:
            primary_industries = ["农业", "渔业", "畜牧业", "林业", "采矿业"]
            secondary_industries = ["制造业", "纺织业", "食品加工", "建筑业"]
            industry_pool = primary_industries + secondary_industries[:2]
        else:
            industry_pool = all_industries

        # 随机选择2-4个主要产业
        num_industries = random.randint(2, 4)
        return random.sample(industry_pool, min(num_industries, len(industry_pool)))

    def _generate_challenges(self, poverty_index: float) -> List[str]:
        """生成面临的挑战"""
        all_challenges = [
            "基础设施落后", "教育资源不足", "医疗条件差", "就业机会少",
            "资金短缺", "技术落后", "人才流失", "环境污染",
            "交通不便", "信息闭塞", "产业单一", "自然灾害频发"
        ]

        # 贫困指数越高，面临的挑战越多
        num_challenges = int(poverty_index * 6) + random.randint(1, 3)
        return random.sample(all_challenges, min(num_challenges, len(all_challenges)))

    def _generate_opportunities(self, poverty_index: float) -> List[str]:
        """生成发展机遇"""
        all_opportunities = [
            "政府扶贫政策", "外资投资", "基础设施建设", "教育援助",
            "技术转移", "旅游开发", "资源开发", "产业升级",
            "人才引进", "国际合作", "绿色发展", "数字化转型"
        ]

        # 贫困地区也有发展机遇
        num_opportunities = random.randint(2, 5)
        return random.sample(all_opportunities, min(num_opportunities, len(all_opportunities)))

    def _generate_currency_name(self) -> str:
        """生成货币名称"""
        currency_names = [
            "元", "币", "克朗", "第纳尔", "比索", "卢比", "先令", "法郎"
        ]
        prefix = random.choice(self.country_prefixes)
        suffix = random.choice(currency_names)
        return f"{prefix}{suffix}"

    def _analyze_poverty_distribution(self, cities: List[Dict]) -> Dict[str, Any]:
        """分析贫困分布"""
        poverty_levels = [city["poverty_index"] for city in cities]

        extreme_poverty = len([p for p in poverty_levels if p > 0.8])
        high_poverty = len([p for p in poverty_levels if 0.6 < p <= 0.8])
        moderate_poverty = len([p for p in poverty_levels if 0.5 <= p <= 0.6])

        return {
            "extreme_poverty_cities": extreme_poverty,
            "high_poverty_cities": high_poverty,
            "moderate_poverty_cities": moderate_poverty,
            "total_cities": len(cities)
        }
