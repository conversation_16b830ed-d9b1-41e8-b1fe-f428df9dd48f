#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
玩家管理模块
Player Manager Module
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from enum import Enum
from shared.enhanced_logger import get_server_logger

class PlayerStatus(Enum):
    """玩家状态枚举"""
    ACTIVE = "active"           # 活跃状态
    ELIMINATED = "eliminated"   # 被淘汰
    SURRENDERED = "surrendered" # 投降

class PlayerActionState(Enum):
    """玩家行动状态枚举"""
    WAITING = "waiting"         # 等待行动
    CONFIRMED = "confirmed"     # 已确认行动

@dataclass
class Player:
    """玩家类"""
    player_id: str
    name: str
    color: str = "white"
    status: PlayerStatus = PlayerStatus.ACTIVE
    
    # 起始位置信息
    starting_city: str = ""
    starting_country: str = ""
    
    # 游戏资源
    resource_inventory: Dict[str, int] = field(default_factory=dict)
    controlled_territories: List[str] = field(default_factory=list)
    
    # 军事和科技
    military_power: int = 100 # 旧的抽象军事力，可以保留或逐步淘汰
    military_units: Dict[str, int] = field(default_factory=dict)
    tech_level: int = 1
    
    # 外交关系
    diplomatic_relations: Dict[str, str] = field(default_factory=dict)
    
    # 游戏统计
    turns_survived: int = 0
    territories_conquered: int = 0
    battles_won: int = 0
    battles_lost: int = 0
    resources_collected: int = 0

    # 经济和政策
    policy_points: int = 0
    implemented_policies: List[str] = field(default_factory=list)
    gdp: float = 0.0
    total_income: float = 0.0
    # 胜利条件
    victory_points: int = 0
    achieved_victory_conditions: List[str] = field(default_factory=list)
    action_state: PlayerActionState = PlayerActionState.WAITING
    
    
    def __post_init__(self):
        """初始化后处理"""
        # 初始资源现在由 PlayerManager 根据游戏设置进行注入
        # 这里保留一个空的字典结构以确保类型正确
        if not self.resource_inventory:
            self.resource_inventory = {}
    
    @property
    def is_alive(self) -> bool:
        """检查玩家是否还活着"""
        return self.status == PlayerStatus.ACTIVE
    
    @property
    def territory_count(self) -> int:
        """获取控制的领土数量"""
        return len(self.controlled_territories)
    
    @property
    def total_population(self) -> int:
        """获取总人口"""
        return self.resource_inventory.get('population', 0)
    
    def add_resource(self, resource_type: str, amount: int):
        """添加资源"""
        if resource_type in self.resource_inventory:
            self.resource_inventory[resource_type] += amount
        else:
            self.resource_inventory[resource_type] = amount
        
        self.resources_collected += amount
    
    def consume_resource(self, resource_type: str, amount: int) -> bool:
        """消耗资源"""
        if resource_type not in self.resource_inventory:
            return False
        
        if self.resource_inventory[resource_type] >= amount:
            self.resource_inventory[resource_type] -= amount
            return True
        
        return False
    
    def get_resource(self, resource_type: str) -> int:
        """获取指定资源的数量"""
        return self.resource_inventory.get(resource_type, 0)
    
    def add_territory(self, territory_id: str):
        """添加控制的领土"""
        if territory_id not in self.controlled_territories:
            self.controlled_territories.append(territory_id)
            self.territories_conquered += 1
    
    def remove_territory(self, territory_id: str):
        """移除控制的领土"""
        if territory_id in self.controlled_territories:
            self.controlled_territories.remove(territory_id)
    
    def set_diplomatic_relation(self, other_player_id: str, relation: str):
        """设置与其他玩家的外交关系"""
        # 关系类型: 'ally', 'neutral', 'enemy', 'war'
        self.diplomatic_relations[other_player_id] = relation
    
    def get_diplomatic_relation(self, other_player_id: str) -> str:
        """获取与其他玩家的外交关系"""
        return self.diplomatic_relations.get(other_player_id, 'neutral')
    
    def eliminate(self):
        """淘汰玩家"""
        if self.status != PlayerStatus.ACTIVE:
            return
        self.status = PlayerStatus.ELIMINATED
        self.controlled_territories.clear()
    
    def surrender(self):
        """玩家投降"""
        if self.status != PlayerStatus.ACTIVE:
            return
        self.status = PlayerStatus.SURRENDERED
        self.controlled_territories.clear()
    
    def advance_turn(self):
        """推进回合（更新回合相关统计）"""
        if self.is_alive:
            self.turns_survived += 1
            # 每回合获得基础政策点数
            self.policy_points += 1

    def add_policy_points(self, points: int):
        """添加政策点数"""
        self.policy_points += points

    def consume_policy_points(self, points: int) -> bool:
        """消耗政策点数"""
        if self.policy_points >= points:
            self.policy_points -= points
            return True
        return False

    def implement_policy(self, policy_id: str):
        """实施政策"""
        if policy_id not in self.implemented_policies:
            self.implemented_policies.append(policy_id)

    def has_policy(self, policy_id: str) -> bool:
        """检查是否已实施某政策"""
        return policy_id in self.implemented_policies

    def update_economic_data(self, gdp: float, income: float):
        """更新经济数据"""
        self.gdp = gdp
        self.total_income = income
    
    def calculate_power_score(self) -> int:
        """计算玩家的综合实力分数"""
        territory_score = self.territory_count * 10
        population_score = self.total_population // 100
        military_score = self.military_power
        tech_score = self.tech_level * 50
        resource_score = sum(self.resource_inventory.values()) // 10
        
        return territory_score + population_score + military_score + tech_score + resource_score
    
    def get_status_summary(self) -> Dict[str, Any]:
        """获取玩家状态摘要"""
        return {
            'name': self.name,
            'status': self.status.value,
            'territories': self.territory_count,
            'population': self.total_population,
            'military_power': self.military_power,
            'tech_level': self.tech_level,
            'power_score': self.calculate_power_score(),
            'turns_survived': self.turns_survived,
            'policy_points': self.policy_points,
            'gdp': self.gdp,
            'total_income': self.total_income,
            'implemented_policies': len(self.implemented_policies),
            'action_state': self.action_state.value
        }

class PlayerManager:
    """玩家管理器"""
    
    def __init__(self, victory_system: 'VictorySystem', game_settings: Dict[str, Any]):
        """初始化玩家管理器"""
        self.players: Dict[str, Player] = {}
        self.turn_order: List[str] = []
        self.current_player_index: int = 0
        self.victory_system = victory_system
        self.game_settings = game_settings.get("player_defaults", {})
        self.logger = get_server_logger("PlayerManager")
        self.logger.info("玩家管理器初始化完成 / Player manager initialized")
    
    def add_player(self, player: Player):
        """添加玩家并应用默认设置"""
        if player.player_id in self.players:
            self.logger.warning(f"试图添加已存在的玩家: {player.player_id}")
            return

        # 应用默认资源
        initial_resources = self.game_settings.get("initial_resources", {})
        if initial_resources:
            player.resource_inventory = initial_resources.copy()

        # 应用默认军事力量和科技水平
        player.military_power = self.game_settings.get("initial_military_power", 100)
        player.tech_level = self.game_settings.get("initial_tech_level", 1)

        self.players[player.player_id] = player
        self.turn_order.append(player.player_id)
        self.logger.info(f"玩家已添加: {player.name} ({player.player_id})，并已应用默认设置。")
    
    def remove_player(self, player_id: str):
        """移除玩家"""
        if player_id in self.players:
            player_name = self.players[player_id].name
            del self.players[player_id]
            self.logger.info(f"玩家已移除: {player_name} ({player_id})")
        
        if player_id in self.turn_order:
            self.turn_order.remove(player_id)
        else:
            self.logger.warning(f"试图移除不在回合顺序中的玩家: {player_id}")
    
    def get_player(self, player_id: str) -> Optional[Player]:
        """获取玩家"""
        return self.players.get(player_id)
    
    def get_player_by_name(self, name: str) -> Optional[Player]:
        """根据名称获取玩家"""
        for player in self.players.values():
            if player.name == name:
                return player
        return None
    
    def get_active_players(self) -> List[Player]:
        """获取所有活跃玩家"""
        return [player for player in self.players.values() if player.is_alive]
    
    def get_eliminated_players(self) -> List[Player]:
        """获取所有被淘汰的玩家"""
        return [player for player in self.players.values() if not player.is_alive]
    
    def get_current_player(self) -> Optional[Player]:
        """获取当前回合的玩家"""
        if 0 <= self.current_player_index < len(self.turn_order):
            player_id = self.turn_order[self.current_player_index]
            return self.get_player(player_id)
        return None
    
    def next_player(self) -> Optional[Player]:
        """切换到下一个玩家"""
        if not self.turn_order:
            self.logger.warning("回合顺序为空，无法切换到下一个玩家")
            return None

        num_players = len(self.turn_order)
        for i in range(num_players):
            next_index = (self.current_player_index + 1 + i) % num_players
            player_id = self.turn_order[next_index]
            player = self.get_player(player_id)
            
            if player and player.is_alive:
                self.current_player_index = next_index
                self.logger.info(f"当前玩家切换为: {player.name} ({player.player_id})")
                return player
        
        self.logger.warning("没有找到活跃玩家")
        return None
    
    def advance_all_players_turn(self):
        """推进所有玩家的回合"""
        for player in self.players.values():
            player.advance_turn()
    
    def get_leaderboard(self) -> List[Player]:
        """获取排行榜（按实力分数排序）"""
        return sorted(self.players.values(), 
                     key=lambda p: p.calculate_power_score(), 
                     reverse=True)
    
    def get_diplomatic_matrix(self) -> Dict[str, Dict[str, str]]:
        """获取外交关系矩阵"""
        matrix = {}
        for player_id, player in self.players.items():
            matrix[player_id] = player.diplomatic_relations.copy()
        return matrix
    
    def check_victory_conditions(self, current_turn: int) -> Optional[Player]:
        """
        检查是否有玩家获胜 (重构版)。
        此方法现在委托 VictorySystem 进行检查。
        """
        if not self.victory_system:
            self.logger.warning("VictorySystem 未初始化，无法检查胜利条件。")
            return None

        winner_info = self.victory_system.check_victory(current_turn)
        
        if winner_info:
            winner_id, victory_type = winner_info
            winner = self.get_player(winner_id)
            if winner:
                self.logger.info(f"🏆 胜利者出现！玩家 {winner.name} ({winner.player_id}) "
                                 f"通过 '{victory_type.value}' 条件获胜！")
                return winner
        
        return None
    
    def get_game_statistics(self) -> Dict[str, Any]:
        """获取游戏统计信息"""
        active_count = len(self.get_active_players())
        eliminated_count = len(self.get_eliminated_players())
        
        return {
            'total_players': len(self.players),
            'active_players': active_count,
            'eliminated_players': eliminated_count,
            'leaderboard': [p.get_status_summary() for p in self.get_leaderboard()[:5]]
        }

    def set_player_action_state(self, player_id: str, state: PlayerActionState):
        """设置玩家的行动状态"""
        player = self.get_player(player_id)
        if player:
            player.action_state = state
            self.logger.info(f"玩家 {player_id} 的行动状态已更新为: {state.value}")
        else:
            self.logger.warning(f"试图更新不存在的玩家 {player_id} 的行动状态")

    def reset_all_players_action_state(self, state: PlayerActionState = PlayerActionState.WAITING):
        """重置所有活跃玩家的行动状态"""
        for player in self.get_active_players():
            player.action_state = state
        self.logger.info(f"所有活跃玩家的行动状态已重置为: {state.value}")
