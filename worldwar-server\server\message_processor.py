#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消息处理器模块
Message Processor Module

负责处理各种类型的客户端消息
Handles various types of client messages
"""

import time
import json
import threading
from typing import Dict, Any, Optional

from server.player_session import PlayerSession
from server.room_manager import RoomManager
from shared.secure_protocol import SecureProtocol
from shared.message_types import MessageType
from shared.message_manager import ServerMessageManager
from shared.enhanced_logger import get_server_logger


class MessageProcessor:
    """消息处理器 - 负责处理各种客户端消息"""

    def __init__(self, protocol: SecureProtocol, room_manager: RoomManager, 
                 session_manager, message_manager: ServerMessageManager):
        """初始化消息处理器"""
        self.protocol = protocol
        self.room_manager = room_manager
        self.session_manager = session_manager
        self.message_manager = message_manager
        self.logger = get_server_logger("MessageProcessor")
        
        # 游戏管理器引用（稍后设置）
        self.game_manager: Optional[Any] = None
        
        self.logger.info("消息处理器初始化完成 / Message processor initialized")

    def set_game_manager(self, game_manager):
        """设置游戏管理器引用"""
        self.game_manager = game_manager

    def process_message(self, session: PlayerSession, message: Dict[str, Any]) -> None:
        """处理客户端消息"""
        try:
            msg_type = message.get("type")
            msg_data = message.get("data", {})
            
            # INFO级别：记录基本消息接收信息（不显示具体内容）
            self.logger.info(f"收到客户端消息: {msg_type}")

            # 消息路由
            if msg_type == MessageType.JOIN_GAME:
                self._handle_join_game(session, msg_data)
            elif msg_type == MessageType.CREATE_ROOM:
                self._handle_create_room(session, msg_data)
            elif msg_type == MessageType.JOIN_ROOM:
                self._handle_join_room(session, msg_data)
            elif msg_type == MessageType.LEAVE_ROOM:
                self._handle_leave_room(session, msg_data)
            elif msg_type == MessageType.GET_ROOM_LIST:
                self._handle_get_room_list(session, msg_data)
            elif msg_type == MessageType.GAME_ACTION:
                self._handle_game_action(session, msg_data)
            elif msg_type == MessageType.CHAT_MESSAGE:
                self._handle_chat_message(session, msg_data)
            elif msg_type == MessageType.PING:
                self._handle_ping(session, msg_data)
            elif msg_type == "start_game":
                self._handle_start_game(session, msg_data)
            elif msg_type == "get_game_status":
                self._handle_get_game_status(session, msg_data)
            elif msg_type == "toggle_ready":
                self._handle_toggle_ready(session, msg_data)
            elif msg_type == "select_world_type":
                self._handle_select_world_type(session, msg_data)
            elif msg_type == "change_username":
                self._handle_change_username(session, msg_data)
            elif msg_type == "generate_username":
                self._handle_generate_username(session, msg_data)
            elif msg_type == "confirm_action":
                self._handle_confirm_action(session, msg_data)
            else:
                # WARNING级别：未知消息类型
                self.logger.warning(f"收到未知消息类型: {msg_type}")

        except Exception as e:
            # ERROR级别：详细错误信息，包含具体参数
            self.logger.error(f"处理消息时发生错误 - 消息类型: {message.get('type', 'Unknown')}, "
                            f"会话ID: {session.session_id}, 玩家: {session.player_name}, "
                            f"错误详情: {str(e)}, 消息数据: {message}")

    def _handle_join_game(self, session: PlayerSession, data: Dict[str, Any]) -> None:
        """处理加入游戏请求（使用安全会话管理）"""
        player_name = data.get("player_name", "").strip()
        request_id = data.get("request_id", "")

        # 使用会话管理器进行认证
        auth_result = self.session_manager.authenticate_session(session.session_id, player_name)

        if auth_result["success"]:
            # 认证成功，发送成功响应
            response = {
                "type": "join_game_response",
                "data": {
                    "success": True,
                    "player_name": player_name,
                    "session_id": session.session_id,
                    "request_id": request_id,
                    "available_rooms": self.room_manager.get_room_list()
                }
            }

            if self.protocol.send_secure_message(session.socket, response):
                self.logger.info(f"玩家 {player_name} 成功加入游戏")
                print(f"✅ 玩家加入游戏 / Player joined: {player_name}")
            else:
                self.logger.error(f"发送加入游戏响应失败: {session.session_id}")
        else:
            # 认证失败，发送错误响应
            response = {
                "type": "join_game_response",
                "data": {
                    "success": False,
                    "error_code": auth_result.get("error", "unknown"),
                    "message": auth_result.get("message", "认证失败"),
                    "request_id": request_id
                }
            }

            if self.protocol.send_secure_message(session.socket, response):
                self.logger.warning(f"玩家加入游戏失败: {player_name} - {auth_result['message']}")
            else:
                self.logger.error(f"发送加入游戏错误响应失败: {session.session_id}")

    def _handle_create_room(self, session: PlayerSession, data: Dict[str, Any]) -> None:
        """处理创建房间请求 - 集成地形生成选项"""
        if not session.is_authenticated:
            self._send_error(session, "请先加入游戏 / Please join game first")
            return

        room_name = data.get("room_name", "").strip()
        max_players = data.get("max_players", 4)
        game_mode = data.get("game_mode", "ai_generated")  # 默认AI生成模式
        difficulty = data.get("difficulty", "normal")  # 难度设置
        is_private = data.get("is_private", False)  # 是否私人房间
        password = data.get("password", "")  # 房间密码
        
        # 地形生成参数
        terrain_config = data.get("terrain_config", {})
        terrain_type = terrain_config.get("type", "perlin")  # "perlin" 或 "realworld"
        terrain_seed = terrain_config.get("seed")  # 柏林噪声种子
        region_name = terrain_config.get("region")  # 真实世界地区名
        terrain_size = terrain_config.get("size", "medium")  # 地形尺寸
        terrain_params = terrain_config.get("params", {})  # 额外参数

        if not room_name:
            self._send_error(session, "房间名称不能为空 / Room name cannot be empty")
            return

        # 验证参数
        if max_players < 2 or max_players > 8:
            max_players = 4

        if game_mode not in ["real_world", "ai_generated"]:
            game_mode = "ai_generated"

        if difficulty not in ["easy", "normal", "hard"]:
            difficulty = "normal"
            
        # 验证地形参数
        if terrain_type not in ["perlin", "realworld"]:
            terrain_type = "perlin"
            
        if terrain_size not in ["small", "medium", "large"]:
            terrain_size = "medium"

        # 验证真实世界地区参数
        if terrain_type == "realworld":
            if not region_name:
                self._send_error(session, "真实世界模式需要指定地区名称 / Real world mode requires region name")
                return
            
            # 检查支持的地区
            try:
                from world.data.realworld_generator import RealWorldGenerator
                generator = RealWorldGenerator()
                if region_name not in generator.get_supported_regions():
                    supported_regions = ", ".join(generator.get_supported_regions())
                    self._send_error(session, f"不支持的地区: {region_name}。支持的地区: {supported_regions}")
                    return
            except ImportError as e:
                self.logger.error(f"无法导入真实世界生成器: {e}")
                self._send_error(session, "真实世界地形生成功能暂时不可用")
                return

        # 创建房间
        self.logger.info(f"开始创建房间 - 玩家: {session.player_name}, 房间名: {room_name}, 最大玩家: {max_players}, 模式: {game_mode}, 难度: {difficulty}, 地形类型: {terrain_type}")

        room = self.room_manager.create_room_safe(
            room_name, session.session_id, max_players, game_mode, difficulty
        )

        if room:
            self.logger.info(f"房间创建成功 - ID: {room.room_id}, 名称: {room_name}")

            # 设置房间属性
            if is_private and password:
                room.is_private = True
                room.password = password
                self.logger.info(f"房间设置为私人房间 - ID: {room.room_id}")

            # 设置地形生成配置
            terrain_config_full = {
                "type": terrain_type,
                "size": terrain_size,
                "seed": terrain_seed,
                "region": region_name,
                "params": terrain_params,
                "status": "pending"  # 地形生成状态: pending, generating, completed, failed
            }
            room.terrain_config = terrain_config_full
            self.logger.info(f"房间地形配置已设置 - 类型: {terrain_type}, 尺寸: {terrain_size}")
            
            # 启动地形生成任务
            try:
                from world.terrain_generation_service import get_terrain_service
                terrain_service = get_terrain_service()
                
                # 创建进度回调函数
                def terrain_progress_callback(task_id: str, progress: float, message: str):
                    self.logger.info(f"房间 {room.room_id} 地形生成进度: {progress:.1%} - {message}")
                    # 这里可以向房间内的玩家广播进度更新
                    self._broadcast_terrain_progress(room.room_id, progress, message)
                
                # 启动地形生成
                task_id = terrain_service.start_terrain_generation(
                    room.room_id, terrain_config_full, terrain_progress_callback
                )
                
                room.terrain_task_id = task_id
                room.terrain_config["status"] = "generating"
                self.logger.info(f"地形生成任务已启动 - 任务ID: {task_id}")
                
            except Exception as e:
                self.logger.error(f"启动地形生成失败: {e}")
                room.terrain_config["status"] = "failed"

            # 房主自动加入房间
            self.logger.info(f"房主 {session.player_name} 尝试加入房间 {room.room_id}")
            join_result = room.add_player(session)

            if join_result["success"]:
                session.current_room = room.room_id
                self.logger.info(f"房主成功加入房间 - 玩家: {session.player_name}, 房间: {room.room_id}")

                # 准备响应数据
                response_data = {
                    "success": True,
                    "room_info": {
                        "room_id": room.room_id,
                        "room_name": room_name,
                        "is_host": True,
                        "max_players": max_players,
                        "game_mode": game_mode,
                        "difficulty": difficulty,
                        "is_private": is_private,
                        "status": "waiting",
                        "host_player": session.session_id,
                        "terrain_config": {
                            "type": terrain_type,
                            "size": terrain_size,
                            "region": region_name if terrain_type == "realworld" else None,
                            "status": "pending"
                        }
                    }
                }

                self.logger.info(f"发送房间创建响应 - 数据: {response_data}")

                # 发送响应 - 使用普通socket而不是安全协议
                self.message_manager.send_secure_message(
                    session.socket, "room_created", response_data
                )
                self.logger.info(f"✅ 房间创建完成 - 玩家: {session.player_name}, 房间ID: {room.room_id}, 模式: {game_mode}, 地形: {terrain_type}")
            else:
                self.logger.error(f"❌ 房主加入房间失败 - 原因: {join_result['message']}")
                self._send_error(session, f"加入房间失败: {join_result['message']}")
        else:
            self.logger.error(f"❌ 房间创建失败 - 玩家: {session.player_name}, 房间名: {room_name}")
            self._send_error(session, "创建房间失败 / Failed to create room")

    def _handle_join_room(self, session: PlayerSession, data: Dict[str, Any]) -> None:
        """处理加入房间请求"""
        if not session.is_authenticated:
            self._send_error(session, "请先加入游戏 / Please join game first")
            return
        
        room_id = data.get("room_id")
        password = data.get("password", "")

        join_result = self.room_manager.join_room_safe(room_id, session, password)
        if join_result["success"]:
            session.current_room = room_id
            response = {
                "type": MessageType.ROOM_JOINED,
                "data": {
                    "room_id": room_id,
                    "is_host": False,
                    "message": join_result["message"],
                    "room_info": join_result["room_info"]
                }
            }
            self.protocol.send_secure_message(session.socket, response)
            self.logger.info(f"玩家 {session.player_name} 加入房间 {room_id}")
        else:
            self._send_error(session, f"加入房间失败: {join_result['message']}")

    def _handle_leave_room(self, session: PlayerSession, data: Dict[str, Any]) -> None:
        """处理离开房间请求"""
        _ = data  # 忽略未使用的参数
        if session.current_room:
            leave_result = self.room_manager.leave_room_safe(session.current_room, session)
            if leave_result:
                session.current_room = None
                response = {
                    "type": MessageType.ROOM_LEFT,
                    "data": {"success": True, "message": "成功离开房间"}
                }
                self.protocol.send_secure_message(session.socket, response)
            else:
                self._send_error(session, "离开房间失败")

    def _handle_get_room_list(self, session: PlayerSession, data: Dict[str, Any]) -> None:
        """处理获取房间列表请求"""
        _ = data  # 忽略未使用的参数

        if not session.is_authenticated:
            self._send_error(session, "请先加入游戏 / Please join game first")
            return

        # 获取房间列表
        room_list = self.room_manager.get_room_list()

        # 使用普通socket发送房间列表
        response_message = {
            "type": "room_list",
            "data": {"rooms": room_list}
        }
        self.message_manager.send_secure_message(
            session.socket, "room_list", {"rooms": room_list}
        )
        self.logger.info(f"发送房间列表给玩家 {session.player_name}, 共 {len(room_list)} 个房间")

    def _handle_game_action(self, session: PlayerSession, data: Dict[str, Any]) -> None:
        """处理游戏动作"""
        if not session.current_room:
            self._send_error(session, "你不在任何房间中。")
            return

        action_type = data.get("action")
        if action_type == "produce_unit":
            self._handle_produce_unit(session, data)
        elif action_type == "attack":
            self._handle_attack(session, data)
        else:
            # 保留对旧的或其他的游戏动作的处理
            self.room_manager.handle_game_action(session.current_room, session, data)

    def _handle_chat_message(self, session: PlayerSession, data: Dict[str, Any]) -> None:
        """处理聊天消息"""
        if session.current_room:
            self.room_manager.broadcast_chat_message(session.current_room, session, data)

    def _handle_ping(self, session: PlayerSession, data: Dict[str, Any]) -> None:
        """处理心跳包"""
        _ = data  # 忽略未使用的参数
        response = {
            "type": MessageType.PONG,
            "data": {"timestamp": time.time()}
        }
        # 使用安全协议发送响应
        if not self.protocol.send_secure_message(session.socket, response):
            self.logger.warning(f"发送心跳响应失败: {session.session_id}")

    def _handle_toggle_ready(self, session: PlayerSession, data: Dict[str, Any]) -> None:
        """处理切换准备状态请求"""
        _ = data  # 忽略未使用的参数

        if not session.is_authenticated:
            self._send_error(session, "请先加入游戏")
            return

        if not session.current_room:
            self._send_error(session, "请先加入房间")
            return

        # 切换准备状态
        room = self.room_manager.get_room(session.current_room)
        if room:
            # 获取当前准备状态并切换
            current_ready = room.player_ready_status.get(session.session_id, False)
            new_ready = not current_ready

            # 设置新的准备状态
            self.room_manager.set_player_ready_safe(session.current_room, session.session_id, new_ready)

            response = {
                "type": "ready_toggled",
                "data": {
                    "ready": new_ready,
                    "player_name": session.player_name,
                    "room_id": session.current_room
                }
            }

            self.message_manager.send_secure_message(session.socket, "ready_toggled", response["data"])
            self.logger.info(f"玩家 {session.player_name} 切换准备状态: {new_ready}")

            # 检查是否所有玩家都准备好了
            can_start_result = room.can_start_game()
            if can_start_result["can_start"]:
                self.logger.info(f"房间 {session.current_room} 所有玩家准备完毕，开始游戏倒计时")
                self._start_game_countdown(room)
        else:
            self._send_error(session, "房间不存在")

    def _start_game_countdown(self, room):
        """开始游戏倒计时"""
        def countdown_thread():
            try:
                countdown_seconds = 5  # 5秒倒计时

                # 广播倒计时开始
                countdown_message = {
                    "type": "game_starting",
                    "data": {
                        "countdown": countdown_seconds,
                        "message": f"所有玩家已准备，游戏将在{countdown_seconds}秒后开始！"
                    }
                }
                self._broadcast_to_room(room, countdown_message)

                # 倒计时
                for i in range(countdown_seconds, 0, -1):
                    time.sleep(1)
                    countdown_message = {
                        "type": "game_countdown",
                        "data": {
                            "countdown": i,
                            "message": f"游戏开始倒计时: {i}"
                        }
                    }
                    self._broadcast_to_room(room, countdown_message)

                # 开始游戏
                start_result = self.room_manager.start_game(room.room_id)
                if start_result["success"]:
                    start_message = {
                        "type": "game_started",
                        "data": {
                            "message": "游戏开始！",
                            "room_id": room.room_id
                        }
                    }
                    self._broadcast_to_room(room, start_message)
                    self.logger.info(f"房间 {room.room_id} 游戏开始")
                else:
                    error_message = {
                        "type": "game_start_failed",
                        "data": {
                            "message": f"游戏开始失败: {start_result['message']}"
                        }
                    }
                    self._broadcast_to_room(room, error_message)

            except Exception as e:
                self.logger.error(f"游戏倒计时线程错误: {e}")

        # 启动倒计时线程
        threading.Thread(target=countdown_thread, daemon=True).start()

    def _broadcast_to_room(self, room, message):
        """向房间内所有玩家广播消息"""
        for session in room.players.values():
            self.message_manager.send_secure_message(
                session.socket, message["type"], message["data"]
            )

    def _handle_start_game(self, session: PlayerSession, data: Dict[str, Any]) -> None:
        """处理开始游戏请求"""
        _ = data  # 忽略未使用的参数

        if not session.is_authenticated:
            self._send_error(session, "请先加入游戏")
            return

        if not session.current_room:
            self._send_error(session, "请先加入房间")
            return

        room = self.room_manager.get_room(session.current_room)
        if not room:
            self._send_error(session, "房间不存在 / Room does not exist")
            return

        # 检查是否是房主
        if room.host_player != session.session_id:
            self._send_error(session, "只有房主可以开始游戏 / Only host can start the game")
            return

        # 使用游戏管理器开始游戏
        if self.game_manager:
            start_result = self.game_manager.start_game(room.room_id, room)
            if start_result["success"]:
                self.logger.info(f"房间 {room.room_id} 的游戏已开始")
            else:
                self._send_error(session, f"游戏启动失败: {start_result['message']}")
        else:
            self._send_error(session, "游戏管理器未初始化")

    def _handle_get_game_status(self, session: PlayerSession, data: Dict[str, Any]) -> None:
        """处理获取游戏状态请求"""
        _ = data  # 忽略未使用的参数

        if not session.is_authenticated:
            self._send_error(session, "请先加入游戏")
            return

        if not session.current_room:
            self._send_error(session, "您不在任何房间中 / You are not in any room")
            return

        room_id = session.current_room

        # 使用游戏管理器获取游戏状态
        if self.game_manager:
            game_status = self.game_manager.get_game_status(room_id)
            if game_status:
                # 使用消息管理器发送安全消息
                self.message_manager.send_secure_message(
                    session.socket,
                    "game_status",
                    game_status
                )
            else:
                # 游戏尚未开始
                room = self.room_manager.get_room(room_id)
                if room:
                    # 使用消息管理器发送安全消息
                    game_status_data = {
                        "game_status": {
                            "room_id": room_id,
                            "game_state": "waiting",
                            "players": list(room.players.keys()),
                            "host": room.host_player
                        }
                    }
                    self.message_manager.send_secure_message(
                        session.socket,
                        "game_status",
                        game_status_data
                    )
                else:
                    self._send_error(session, "房间不存在 / Room does not exist")
        else:
            self._send_error(session, "游戏管理器未初始化")

    def _send_error(self, session: PlayerSession, message: str) -> None:
        """发送错误消息"""
        # 使用统一的消息管理器发送错误消息
        self.message_manager.send_error(session.socket, message)

    def _handle_change_username(self, session: PlayerSession, data: Dict[str, Any]) -> None:
        """处理用户名更改请求"""
        if not session.is_authenticated:
            self._send_error(session, "请先加入游戏 / Please join game first")
            return

        new_username = data.get("new_username", "").strip()
        if not new_username:
            response = {
                "type": "username_changed",
                "data": {
                    "success": False,
                    "error": "empty_username",
                    "message": "新用户名不能为空"
                }
            }
            self.protocol.send_secure_message(session.socket, response)
            return

        # 使用会话管理器更改用户名
        change_result = self.session_manager.change_player_name(session.session_id, new_username)

        response = {
            "type": "username_changed",
            "data": change_result
        }

        if not self.protocol.send_secure_message(session.socket, response):
            self.logger.error(f"发送用户名更改响应失败: {session.session_id}")
        else:
            if change_result.get("success"):
                self.logger.info(f"用户名更改成功: {change_result.get('old_name')} -> {change_result.get('new_name')}")
            else:
                self.logger.warning(f"用户名更改失败: {change_result.get('message')}")

    def _handle_generate_username(self, session: PlayerSession, data: Dict[str, Any]) -> None:
        """处理生成随机用户名请求"""
        language = data.get("language", "mixed")

        try:
            # 使用会话管理器生成随机用户名
            random_username = self.session_manager.generate_random_username(language)

            response = {
                "type": "username_generated",
                "data": {
                    "success": True,
                    "username": random_username,
                    "message": "随机用户名生成成功"
                }
            }

            if not self.protocol.send_secure_message(session.socket, response):
                self.logger.error(f"发送随机用户名响应失败: {session.session_id}")
            else:
                self.logger.info(f"为会话 {session.session_id} 生成随机用户名: {random_username}")

        except Exception as e:
            self.logger.error(f"生成随机用户名时发生错误: {e}")
            response = {
                "type": "username_generated",
                "data": {
                    "success": False,
                    "error": "generation_failed",
                    "message": f"生成随机用户名失败: {str(e)}"
                }
            }
            self.protocol.send_secure_message(session.socket, response)

    def _handle_confirm_action(self, session: PlayerSession, data: Dict[str, Any]) -> None:
        """处理玩家确认行动的请求"""
        if not session.is_authenticated or not session.current_room:
            self._send_error(session, "无效的操作，请先加入一个正在游戏中的房间。")
            return

        room = self.room_manager.get_room(session.current_room)
        if not room or not room.game_loop:
            self._send_error(session, "游戏尚未开始或已结束。")
            return

        # 检查当前是否是该玩家的回合
        current_player = room.game_loop.current_turn.current_player
        if current_player != session.session_id:
            self._send_error(session, "现在不是您的回合。")
            return
            
        # 调用GameLoop来确认行动
        room.game_loop.confirm_player_action(session.session_id)

        # 发送确认响应
        response = {
            "type": "action_confirmed",
            "data": {
                "success": True,
                "message": "行动已确认，等待其他玩家或阶段结束。"
            }
        }
        self.message_manager.send_secure_message(session.socket, "action_confirmed", response["data"])

    def _handle_produce_unit(self, session: PlayerSession, data: Dict[str, Any]):
        """处理生产单位的动作"""
        room = self.room_manager.get_room(session.current_room)
        if not room or not room.game_loop:
            self._send_error(session, "游戏尚未开始或已结束。")
            return

        unit_type = data.get("unit_type")
        quantity = data.get("quantity")

        if not unit_type or not isinstance(quantity, int) or quantity <= 0:
            self._send_error(session, "无效的单位生产参数。")
            return

        success = room.game_loop.economic_system.produce_unit(session.session_id, unit_type, quantity)
        
        response_data = {
            "success": success,
            "unit_type": unit_type,
            "quantity": quantity,
            "message": f"成功生产 {quantity} 个 {unit_type}。" if success else f"生产 {unit_type} 失败。"
        }
        self.message_manager.send_secure_message(session.socket, "produce_unit_response", response_data)

    def _handle_attack(self, session: PlayerSession, data: Dict[str, Any]):
        """处理攻击动作"""
        room = self.room_manager.get_room(session.current_room)
        if not room or not room.game_loop:
            self._send_error(session, "游戏尚未开始或已结束。")
            return

        defender_id = data.get("defender_id")
        target_territory = data.get("target_territory")
        units = data.get("units")

        if not defender_id or not target_territory or not isinstance(units, dict):
            self._send_error(session, "无效的攻击参数。")
            return
        
        # 验证攻击者单位是否足够
        attacker = room.game_loop.player_manager.get_player(session.session_id)
        for unit_type, quantity in units.items():
            if attacker.military_units.get(unit_type, 0) < quantity:
                self._send_error(session, f"你没有足够的 {unit_type} 来发动攻击。")
                return

        room.game_loop.combat_system.add_combat_action(
            attacker_id=session.session_id,
            defender_id=defender_id,
            target_territory=target_territory,
            attacker_units=units
        )

        response_data = {
            "success": True,
            "message": f"已对 {target_territory} 的 {defender_id} 发动攻击！"
        }
        self.message_manager.send_secure_message(session.socket, "attack_response", response_data)

    def _broadcast_terrain_progress(self, room_id: str, progress: float, message: str):
        """向房间内所有玩家广播地形生成进度"""
        room = self.room_manager.get_room(room_id)
        if not room:
            return
        
        progress_message = {
            "type": "terrain_progress",
            "data": {
                "room_id": room_id,
                "progress": progress,
                "message": message,
                "timestamp": time.time()
            }
        }
        
        # 向房间内所有玩家发送进度更新
        for session in room.players.values():
            try:
                self.message_manager.send_secure_message(
                    session.socket, "terrain_progress", progress_message["data"]
                )
            except Exception as e:
                self.logger.error(f"向玩家 {session.player_name} 发送地形进度失败: {e}")

    def _handle_select_world_type(self, session: PlayerSession, data: Dict[str, Any]) -> None:
        """处理选择世界类型请求"""
        if not session.is_authenticated:
            self._send_error(session, "请先加入游戏")
            return

        if not session.current_room:
            self._send_error(session, "请先加入房间")
            return

        room = self.room_manager.get_room(session.current_room)
        if not room:
            self._send_error(session, "房间不存在")
            return

        # 检查是否是房主
        if room.host_player != session.session_id:
            self._send_error(session, "只有房主可以修改世界设置")
            return

        world_type = data.get("world_type", "perlin")
        region_name = data.get("region_name")
        terrain_size = data.get("terrain_size", "medium")
        terrain_seed = data.get("terrain_seed")

        # 验证参数
        if world_type not in ["perlin", "realworld"]:
            world_type = "perlin"

        if terrain_size not in ["small", "medium", "large"]:
            terrain_size = "medium"

        # 如果是真实世界模式，验证地区
        if world_type == "realworld":
            if not region_name:
                self._send_error(session, "真实世界模式需要指定地区名称")
                return

            try:
                from world.data.realworld_generator import RealWorldGenerator
                generator = RealWorldGenerator()
                if region_name not in generator.get_supported_regions():
                    supported_regions = ", ".join(generator.get_supported_regions())
                    self._send_error(session, f"不支持的地区: {region_name}。支持的地区: {supported_regions}")
                    return
            except ImportError as e:
                self.logger.error(f"无法导入真实世界生成器: {e}")
                self._send_error(session, "真实世界地形生成功能暂时不可用")
                return

        # 更新房间的地形配置
        if hasattr(room, 'terrain_config'):
            room.terrain_config.update({
                "type": world_type,
                "size": terrain_size,
                "seed": terrain_seed,
                "region": region_name,
                "status": "pending"
            })
        else:
            room.terrain_config = {
                "type": world_type,
                "size": terrain_size,
                "seed": terrain_seed,
                "region": region_name,
                "status": "pending"
            }

        # 发送确认响应
        response_data = {
            "success": True,
            "world_type": world_type,
            "region_name": region_name,
            "terrain_size": terrain_size,
            "message": f"世界类型已设置为: {world_type}"
        }

        self.message_manager.send_secure_message(
            session.socket, "world_type_selected", response_data
        )

        # 向房间内其他玩家广播设置变更
        for other_session in room.players.values():
            if other_session.session_id != session.session_id:
                self.message_manager.send_secure_message(
                    other_session.socket, "world_type_changed", response_data
                )

        self.logger.info(f"房间 {room.room_id} 世界类型已更新: {world_type}")
            return

        world_type = data.get("world_type", "perlin")
        region_name = data.get("region_name")
        terrain_size = data.get("terrain_size", "medium")
        terrain_seed = data.get("terrain_seed")

        # 验证参数
        if world_type not in ["perlin", "realworld"]:
            world_type = "perlin"

        if terrain_size not in ["small", "medium", "large"]:
            terrain_size = "medium"

        # 如果是真实世界模式，验证地区
        if world_type == "realworld":
            if not region_name:
                self._send_error(session, "真实世界模式需要指定地区名称")
                return

            try:
                from world.data.realworld_generator import RealWorldGenerator
                generator = RealWorldGenerator()
                if region_name not in generator.get_supported_regions():
                    supported_regions = ", ".join(generator.get_supported_regions())
                    self._send_error(session, f"不支持的地区: {region_name}。支持的地区: {supported_regions}")
                    return
            except ImportError as e:
                self.logger.error(f"无法导入真实世界生成器: {e}")
                self._send_error(session, "真实世界地形生成功能暂时不可用")
                return

        # 更新房间的地形配置
        if hasattr(room, 'terrain_config'):
            room.terrain_config.update({
                "type": world_type,
                "size": terrain_size,
                "seed": terrain_seed,
                "region": region_name,
                "status": "pending"
            })
        else:
            room.terrain_config = {
                "type": world_type,
                "size": terrain_size,
                "seed": terrain_seed,
                "region": region_name,
                "status": "pending"
            }

        # 发送确认响应
        response_data = {
            "success": True,
            "world_type": world_type,
            "region_name": region_name,
            "terrain_size": terrain_size,
            "message": f"世界类型已设置为: {world_type}"
        }

        self.message_manager.send_secure_message(
            session.socket, "world_type_selected", response_data
        )

        # 向房间内其他玩家广播设置变更
        for other_session in room.players.values():
            if other_session.session_id != session.session_id:
                self.message_manager.send_secure_message(
                    other_session.socket, "world_type_changed", response_data
                )

        self.logger.info(f"房间 {room_id} 世界类型已更新: {world_type}")
