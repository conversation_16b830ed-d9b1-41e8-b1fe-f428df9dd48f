2025-07-04 15:27:09 - server_ServerMain - INFO - [enhanced_logger.py:174] - 服务器启动
2025-07-04 15:27:09 - server_ServerMain - INFO - [enhanced_logger.py:174] - 🚀 世界大战游戏服务器启动
2025-07-04 15:27:09 - server_ServerPrint - INFO - [enhanced_logger.py:265] - ============================================================
2025-07-04 15:27:09 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 🎮 世界大战游戏服务器 / World War Game Server
2025-07-04 15:27:09 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 📡 版本: v3.0
2025-07-04 15:27:09 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 🌐 地址: localhost:8888
2025-07-04 15:27:09 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 📝 日志: logs/server/ 目录
2025-07-04 15:27:09 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 🛑 按 Ctrl+C 停止服务器
2025-07-04 15:27:09 - server_ServerPrint - INFO - [enhanced_logger.py:265] - ============================================================
2025-07-04 15:27:09 - server_ServerMain - INFO - [enhanced_logger.py:174] - 初始化服务器 - 地址: localhost:8888
2025-07-04 15:27:09 - server_ConfigManager - INFO - [enhanced_logger.py:174] - 配置管理器初始化完成
2025-07-04 15:27:09 - server_ConnectionManager - INFO - [enhanced_logger.py:174] - 连接管理器初始化完成 / Connection manager initialized
2025-07-04 15:27:09 - server_GameManager - INFO - [enhanced_logger.py:174] - 游戏管理器初始化完成 / Game manager initialized
2025-07-04 15:27:09 - server_MessageProcessor - INFO - [enhanced_logger.py:174] - 消息处理器初始化完成 / Message processor initialized
2025-07-04 15:27:09 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器监控器初始化完成 / Server monitor initialized
2025-07-04 15:27:09 - server_SecureGameServer - INFO - [enhanced_logger.py:174] - 安全游戏服务器初始化完成 / Secure game server initialized
2025-07-04 15:27:09 - server_ServerMain - INFO - [enhanced_logger.py:174] - 服务器初始化完成，开始启动...
2025-07-04 15:27:09 - server_SecureGameServer - INFO - [enhanced_logger.py:174] - 正在启动游戏服务器...
2025-07-04 15:27:09 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 🚀 正在启动游戏服务器... / Starting game server...
2025-07-04 15:27:09 - server_ServerPrint - INFO - [enhanced_logger.py:265] - ✅ 成功绑定到 localhost:8888 / Successfully bound to localhost:8888
2025-07-04 15:27:09 - server_ServerPrint - INFO - [enhanced_logger.py:265] - ============================================================
2025-07-04 15:27:09 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 🎮 世界大战游戏服务器已启动 / World War Game Server Started
2025-07-04 15:27:09 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 📡 服务器地址 / Server Address: localhost:8888
2025-07-04 15:27:09 - server_ServerPrint - INFO - [enhanced_logger.py:265] - ⏳ 等待客户端连接... / Waiting for client connections...
2025-07-04 15:27:09 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 🛑 按 Ctrl+C 停止服务器 / Press Ctrl+C to stop server
2025-07-04 15:27:09 - server_ServerPrint - INFO - [enhanced_logger.py:265] - ============================================================
2025-07-04 15:27:09 - server_SecureGameServer - INFO - [enhanced_logger.py:174] - 服务器启动在 localhost:8888
2025-07-04 15:27:09 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 0秒, 活跃连接: 0, 总连接数: 0, 峰值连接: 0, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 15:27:09 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器监控已启动，监控间隔: 30秒
2025-07-04 15:27:09 - server_SecureGameServer - INFO - [enhanced_logger.py:174] - 房间管理线程已启动 / Room management thread started
2025-07-04 15:27:39 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 30秒, 活跃连接: 0, 总连接数: 0, 峰值连接: 0, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 15:28:09 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 60秒, 活跃连接: 0, 总连接数: 0, 峰值连接: 0, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 15:28:39 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 90秒, 活跃连接: 0, 总连接数: 0, 峰值连接: 0, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 15:29:09 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 120秒, 活跃连接: 0, 总连接数: 0, 峰值连接: 0, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 15:29:39 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 150秒, 活跃连接: 0, 总连接数: 0, 峰值连接: 0, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 15:30:09 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 180秒, 活跃连接: 0, 总连接数: 0, 峰值连接: 0, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 15:30:39 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 210秒, 活跃连接: 0, 总连接数: 0, 峰值连接: 0, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 15:31:09 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 240秒, 活跃连接: 0, 总连接数: 0, 峰值连接: 0, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 15:31:39 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 270秒, 活跃连接: 0, 总连接数: 0, 峰值连接: 0, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 15:32:09 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 300秒, 活跃连接: 0, 总连接数: 0, 峰值连接: 0, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 15:32:39 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 330秒, 活跃连接: 0, 总连接数: 0, 峰值连接: 0, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 15:33:09 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 360秒, 活跃连接: 0, 总连接数: 0, 峰值连接: 0, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 15:33:39 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 390秒, 活跃连接: 0, 总连接数: 0, 峰值连接: 0, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 15:34:09 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 420秒, 活跃连接: 0, 总连接数: 0, 峰值连接: 0, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 15:34:39 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 450秒, 活跃连接: 0, 总连接数: 0, 峰值连接: 0, 活跃房间: 0, 消息数: 0, 错误数: 0
