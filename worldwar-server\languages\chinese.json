{"language_name": "中文", "server": {"starting": "正在启动服务器...", "started": "服务器已启动", "stopping": "正在停止服务器...", "stopped": "服务器已停止", "listening": "服务器正在监听", "address": "地址", "port": "端口", "max_connections": "最大连接数", "current_connections": "当前连接数", "uptime": "运行时间", "status": "状态", "online": "在线", "offline": "离线", "error": "错误", "warning": "警告", "info": "信息", "debug": "调试", "language_selection": "语言选择", "select_language": "请选择服务器语言", "language_changed": "语言已更改为中文", "welcome": "欢迎使用世界大战策略游戏服务器", "app_name": "世界大战游戏服务器", "cannot_acquire_lock": "无法获取进程锁，程序退出", "initializing": "初始化服务器", "init_complete": "服务器初始化完成，开始启动...", "keyboard_interrupt": "收到键盘中断信号", "startup_failed": "服务器启动失败", "error_details": "错误详情", "press_any_key": "按任意键继续...", "shutdown_signal": "收到关闭信号", "graceful_shutdown": "正在优雅关闭服务器...", "force_shutdown": "强制关闭服务器", "debug_enabled": "🔧 调试模式已启用 / Debug mode enabled", "debug_modules": "   调试模块 / Debug modules: {modules}", "debug_log_level": "   日志级别 / Log level: {level}", "server_started": "🎮 服务器已启动", "server_address": "📡 地址: {host}:{port}", "waiting_connections": "⏳ 等待客户端连接...", "stop_instruction": "🛑 按 Ctrl+C 停止服务器", "new_client": "📱 新客户端连接: {address}", "socket_error": "❌ 套接字错误", "keyboard_interrupt_received": "🛑 收到键盘中断", "startup_error": "❌ 服务器启动失败: {error}", "client_message": "📨 收到来自 {address} 的消息: {message}", "client_error": "❌ 处理客户端 {address} 时出错: {error}", "client_disconnected": "📱 客户端 {address} 已断开连接", "room_created_success": "✅ 房间创建成功: {room_name} (ID: {room_id})", "room_list_sent": "📋 发送房间列表，共 {count} 个房间", "shutting_down": "🛑 正在关闭服务器...", "server_stopped": "✅ 服务器已停止", "shutdown_signal_received": "🛑 收到停止信号 ({signal})，正在安全关闭服务器...", "shutdown_warning": "⚠️ 服务器关闭时出现警告: {error}", "shutdown_complete": "👋 服务器已安全退出", "multi_instance_warning": "⚠️ 多开防护检查失败，继续启动: {error}", "interrupt_signal": "🛑 收到中断信号", "runtime_error": "❌ 服务器运行错误: {error}"}, "game": {"room_created": "房间已创建", "room_deleted": "房间已删除", "player_joined": "玩家加入", "player_left": "玩家离开", "game_started": "游戏开始", "game_ended": "游戏结束", "turn_changed": "回合切换", "data_mode": "数据模式", "real_world_data": "真实世界数据", "ai_generated_data": "AI生成数据", "select_data_mode": "请选择数据模式", "loading_data": "正在加载数据...", "data_loaded": "数据加载完成", "data_error": "数据加载错误", "_创建玩家会话": "📝 创建玩家会话", "message_游戏循环模块不可用": "message\": \"游戏循环模块不可用", "_初始化游戏数据": "📦 初始化游戏数据...", "房间不存在": "房间不存在", "您不在任何房间中": "您不在任何房间中", "只有房主可以开始游戏": "只有房主可以开始游戏", "请先加入游戏": "请先加入游戏", "房间名称不能为空": "房间名称不能为空", "_玩家加入游戏": "✅ 玩家加入游戏", "message_f玩家未全部准备_ready_count": "message\": f\"玩家未全部准备 ({ready_count}", "玩家未全部准备_ready_count": "玩家未全部准备 ({ready_count}"}, "network": {"client_connected": "客户端已连接", "client_disconnected": "客户端已断开连接", "message_received": "收到消息", "message_sent": "发送消息", "invalid_message": "无效消息", "authentication_failed": "认证失败", "authentication_success": "认证成功", "handshake_completed": "握手完成", "connection_timeout": "连接超时", "connection_error": "连接错误", "_世界大战游戏服务器": "🎮 世界大战游戏服务器", "世界大战游戏服务器": "世界大战游戏服务器", "正在启动服务器": "正在启动服务器...", "1_直接启动服务器": "1. 直接启动服务器", "description_基础服务器数据世界数据将在房间创建时": "description': '基础服务器数据，世界数据将在房间创建时初始化", "正在初始化基础服务器数据": "正在初始化基础服务器数据...", "_客户端重置连接": "🔌 客户端重置连接", "server_name_安全世界大战游戏服务器": "server_name\": \"安全世界大战游戏服务器", "使用兼容性游戏服务器": "使用兼容性游戏服务器", "正在关闭服务器": "正在关闭服务器...", "_新客户端连接": "✅ 新客户端连接", "正在初始化服务器数据": "正在初始化服务器数据...", "_请检查网络配置": "💡 请检查网络配置", "n_收到中断信号正在关闭服务器": "\\n🛑 收到中断信号，正在关闭服务器...", "_按_ctrlc_停止服务器": "🛑 按 Ctrl+C 停止服务器", "_等待客户端连接": "⏳ 等待客户端连接...", "_服务器地址": "📡 服务器地址", "_正在启动游戏服务器": "🚀 正在启动游戏服务器...", "warningsappendf服务器运行时间较长_uptim": "warnings\"].append(f\"服务器运行时间较长: {uptime", "连接数必须在11000之间": "连接数必须在1-1000之间", "输入新的最大连接数": "输入新的最大连接数", "n当前最大连接数": "\\n当前最大连接数", "输入新的服务器端口": "输入新的服务器端口", "n当前服务器端口": "\\n当前服务器端口", "输入新的服务器地址": "输入新的服务器地址", "n当前服务器地址": "\\n当前服务器地址", "8_最大连接数": "8. 最大连接数", "7_服务器端口": "7. 服务器端口", "6_服务器地址": "6. 服务器地址", "服务器设置": "服务器设置", "total_sessions_lenselfsessions": "total_sessions = len(self.sessions)\n        active_sessions = len(self.get_active_sessions())\n        authenticated_sessions = len(self.get_authenticated_sessions())\n        \n        # 计算平均连接时间\n        if active_sessions > 0:\n            total_duration = sum(\n                session.get_connection_duration() \n                for session in self.get_active_sessions().values()\n            )\n            avg_connection_time = total_duration", "_服务器状态报告": "=== 服务器状态报告", "服务器运行时间较长_uptime": "服务器运行时间较长: {uptime"}, "errors": {"port_in_use": "端口已被占用", "invalid_port": "无效端口", "bind_failed": "绑定失败", "socket_error": "套接字错误", "config_error": "配置错误", "file_not_found": "文件未找到", "permission_denied": "权限被拒绝", "unknown_error": "未知错误"}, "config": {"loading_config": "正在加载配置...", "config_loaded": "配置加载完成", "saving_config": "正在保存配置...", "config_saved": "配置保存完成", "default_config": "使用默认配置", "invalid_config": "无效配置"}, "messages": {"enter_port": "请输入端口号 (默认 8888): ", "enter_address": "请输入服务器地址 (默认 localhost): ", "confirm_shutdown": "确定要关闭服务器吗？", "server_ready": "服务器准备就绪，等待客户端连接...", "press_ctrl_c": "按 Ctrl+C 停止服务器"}, "launcher": {"title": "🎮 WorldWar 世界大战策略游戏启动器", "subtitle": "🎯 World War Strategy Game Launcher", "select_mode": "请选择启动模式 / Please select launch mode:", "start_server": "1. 🖥️  启动服务器 / Start Server", "start_client": "2. 🎮 启动客户端 / Start Client", "run_tests": "3. 🧪 运行测试 / Run Tests", "show_help": "4. 📚 查看帮助 / Show Help", "quick_start": "5. 🚀 快速开始 / Quick Start", "exit": "0. ❌ 退出 / Exit", "starting_server": "🖥️ 启动服务器...", "starting_client": "🎮 启动客户端...", "server_script_not_found": "❌ 服务器脚本未找到: {script_path}", "client_script_not_found": "❌ 客户端脚本未找到: {script_path}", "client_exited": "🛑 客户端已退出", "client_start_failed": "❌ 启动客户端失败: {error}", "server_start_failed": "❌ 启动服务器失败: {error}", "waiting_server": "⏳ 等待服务器启动...", "running_tests": "🧪 运行功能测试...", "test_server_script": "✅ 测试服务器启动脚本...", "test_client_script": "✅ 测试客户端启动脚本...", "server_script_missing": "❌ 服务器启动脚本不存在!", "client_script_missing": "❌ 客户端启动脚本不存在!", "all_scripts_ok": "✅ 所有启动脚本检查通过!", "server_tip": "💡 提示: 使用 'python worldwar-server/server_main.py' 启动服务器", "client_tip": "💡 提示: 使用 'python worldwar-client/client_main.py' 启动客户端", "help_title": "📚 WorldWar游戏帮助", "game_intro": "🎯 游戏简介:", "game_description": "WorldWar是一个多人在线策略游戏，支持中英文双语界面。", "game_interaction": "玩家可以连接到服务器，与其他玩家进行实时互动。", "quick_start_title": "🚀 快速开始:", "quick_start_step1": "1. 选择 '1' 启动服务器", "quick_start_step2": "2. 在另一个终端选择 '2' 启动客户端", "quick_start_step3": "3. 在客户端选择 '1. 连接服务器'", "quick_start_step4": "4. 开始游戏!", "advanced_usage": "🔧 高级用法:", "server_custom": "• 服务器支持自定义地址和端口", "client_custom": "• 客户端支持指定默认服务器", "multi_client": "• 支持多个客户端同时连接", "language_support": "🌐 语言支持:", "chinese_interface": "• 中文界面", "english_interface": "• English interface", "bilingual_mode": "• 双语模式 (中文 / English)", "tech_support": "📞 技术支持:", "check_summary": "• 查看 FINAL_PROJECT_SUMMARY.md 了解详细信息", "run_tests_check": "• 运行测试检查功能状态", "quick_starting": "🚀 快速开始...", "closing_server": "🛑 关闭服务器..."}, "client": {"initialized": "🎮 世界大战游戏客户端已初始化", "server_info": "📡 服务器: {host}:{port}", "username_info": "👤 用户名: {username}", "starting": "🚀 正在启动客户端...", "connection_failed": "❌ 连接服务器失败，程序退出", "connected_success": "✅ 成功连接到服务器", "getting_rooms": "📋 正在获取房间列表...", "connection_attempt": "🔄 连接尝试 {attempt}/{max_attempts}...", "retry_wait": "⏳ 等待3秒后重试...", "title": "🎮 世界大战游戏客户端", "help_command": "📝 输入 'help' 查看可用命令", "quit_command": "🛑 输入 'quit' 退出游戏", "unknown_command": "❓ 未知命令: {command}", "help_tip": "💡 输入 'help' 查看可用命令", "interrupt_signal": "🛑 收到中断信号", "input_ended": "🛑 输入结束", "input_error": "❌ 处理输入时发生错误: {error}", "exiting": "👋 正在退出游戏...", "leaving_room": "📤 正在离开房间...", "safely_exited": "✅ 已安全退出", "disconnecting": "正在断开连接...", "help_info": "📖 帮助信息", "general_commands": "🔧 通用命令:", "help_desc": "help, h          - 显示此帮助信息", "clear_desc": "clear, cls       - 清屏", "quit_desc": "quit, exit, q    - 退出游戏", "status_desc": "status           - 显示连接状态", "rooms_desc": "rooms            - 刷新房间列表", "room_list_commands": "🏠 房间列表命令:", "join_desc": "join <序号>      - 加入指定房间", "create_desc": "create <房间名>  - 创建新房间", "refresh_desc": "refresh          - 刷新房间列表", "waiting_room_commands": "⏳ 等待房间命令:", "ready_desc": "ready            - 切换准备状态", "start_desc": "start            - 开始游戏 (仅房主)", "leave_desc": "leave            - 离开房间", "program_interrupted": "🛑 程序被用户中断", "program_error": "❌ 程序发生错误: {error}", "program_exited": "👋 程序已退出", "username_generated": "未提供用户名，已为您生成随机用户名: {username}", "connection_failed_error": "连接失败: {error}", "send_message_failed": "发送消息失败: {error}", "json_parse_error": "JSON解析错误: {error}", "ui_update_callback_error": "UI更新回调错误: {error}", "ui_update_thread_error": "UI更新工作线程错误: {error}"}, "common": {"2_检查_dist": "2. 检查: dist", "dist": "dist", "readmemd_dist": "README.md\", \"dist", "config": "config\"", "languages": "languages", "_日志_logs": "📝 日志: logs", "world_war_strategy_game_server": "World War Strategy Game Server", "按回车键继续": "按回车键继续", "退出程序": "退出程序", "3_退出": "3. 退出", "2_进入设置": "2. 进入设置", "selfworld_bank_api": "{self.world_bank_api}", "正在获取城市数据": "正在获取城市数据...", "正在获取灾害数据": "正在获取灾害数据...", "正在获取资源数据": "正在获取资源数据...", "正在生成气候数据": "正在生成气候数据...", "正在生成地形数据": "正在生成地形数据...", "正在获取世界银行贫困数据": "正在获取世界银行贫困数据...", "data": "data", "https": "https:", "useragent_worldwargame": "User-Agent': 'WorldWarGame", "正在创建备用数据": "正在创建备用数据...", "description_ai生成数据": "description': 'AI生成数据", "正在初始化政策系统": "正在初始化政策系统...", "正在初始化经济系统": "正在初始化经济系统...", "正在生成城市数据": "正在生成城市数据...", "正在生成灾害数据": "正在生成灾害数据...", "正在生成资源数据": "正在生成资源数据...", "正在生成贫困城市数据": "正在生成贫困城市数据...", "description_混合数据真实贫困城市_ai生成环境": "description': '混合数据：真实贫困城市 + AI生成环境", "正在获取真实贫困城市数据": "正在获取真实贫困城市数据...", "_客户端心跳超时": "⚠️ 客户端心跳超时", "_无法绑定到地址_selfhost": "❌ 无法绑定到地址 {self.host}", "_请尝试其他端口或关闭占用该端口的程序": "💡 请尝试其他端口或关闭占用该端口的程序", "广播消息到_success_count": "广播消息到 {success_count}", "id冲突重新生成_尝试_attempt_1": "ID冲突，重新生成 (尝试 {attempt + 1}", "不是你的回合": "不是你的回合", "取消重置": "取消重置", "n确定要重置所有设置吗": "\\n确定要重置所有设置吗？", "启用": "启用", "禁用": "禁用", "无效数字": "无效数字", "无效端口号": "无效端口号", "端口必须在102465535之间": "端口必须在1024-65535之间", "n日志级别": "\\n日志级别", "值必须在10010000之间": "值必须在100-10000之间", "输入新的最大日志行数": "输入新的最大日志行数", "n当前最大日志行数": "\\n当前最大日志行数", "3_双语模式": "3. 双语模式", "0_返回": "0. 返回", "10_重置所有设置": "10. 重置所有设置", "9_调试模式": "9. 调试模式", "5_自动归档日志": "5. 自动归档日志", "4_日志级别": "4. 日志级别", "3_清空终端": "3. 清空终端", "2_最大日志行数": "2. 最大日志行数", "1_语言设置": "1. 语言设置", "当前设置": "当前设置", "client": "client\"", "ninvalid_input": "\\nInvalid input", "invalid_choice": "Invalid choice", "nbilingual_mode_enabled": "\\nBilingual mode enabled", "nlanguage_set_to": "\\nLanguage set to", "please_select": "Please select", "lenavailable_languages_1_bilin": "{len(available_languages) + 1}. Bilingual Mode", "available_languages": "Available languages", "no_languages_available": "No languages available", "_language_selection": "🌍 Language Selection", "chinese_text": "{chinese_text}", "语言管理器": "\"\"语言管理器", "消息类型_messagegettype_n": "消息类型: {message.get('type', 'N", "消息时间戳_messagegettimestamp_n": "消息时间戳:   {message.get('timestamp', 'N", "unix": "\"\"Unix", "tasklist_": "tasklist', '", "_执行清理操作_i1": "📋 执行清理操作 {i+1}", "示例用法": "示例用法", "selfsession_requestssession_se": "self.session = requests.Session()\n        self.session.headers.update({\n            'User-Agent': 'WorldWarGame", "_min10_itemvalue": ": min(1.0, item['value']", "_windows特定的keepalive设置_sockioc": ":\n                # Windows特定的keepalive设置\n                sock.ioctl(socket.SIO_KEEPALIVE_VALS, (1, 30000, 5000))  # 30秒间隔，5秒超时\n            else:\n                # Linux", "_selfmessage_count": ": self.message_count", "_selferror_count": ": self.error_count", "_selftotal_connections": ": self.total_connections", "_selfactive_connections": ": (self.active_connections", "_total_players": ": total_players", "通用语言管理器": "通用语言管理器", "selflock_name_lock_name_selflo": "self.lock_name = lock_name\n        self.lock_dir = Path(lock_dir)\n        self.lock_file = self.lock_dir", "try_with_openselflock_file_r_e": "try:\n            with open(self.lock_file, 'r', encoding='utf-8') as f:\n                content = f.read()\n\n            # 提取PID\n            for line in content.split('\\n'):\n                if line.startswith('PID:'):\n                    pid = int(line.split(':')[1].strip())\n\n                    # 检查进程是否存在\n                    try:\n                        # Windows下检查进程是否存在\n                        import subprocess\n                        result = subprocess.run(['tasklist', '", "_windows下直接关闭文件_oscloseselfloc": ":\n                    # Windows下直接关闭文件\n                    os.close(self.lock_fd)\n                else:\n                    # Unix"}, "success": {"_世界大战游戏服务器已关闭": "🛑 世界大战游戏服务器已关闭", "_服务器已在运行程序退出": "❌ 服务器已在运行，程序退出", "气候数据生成完成": "气候数据生成完成", "地形数据生成完成": "地形数据生成完成", "_基础服务器数据初始化完成": "✅ 基础服务器数据初始化完成", "战斗系统初始化完成": "战斗系统初始化完成", "经济系统初始化完成": "经济系统初始化完成", "政策管理器初始化完成": "政策管理器初始化完成", "胜利条件系统初始化完成": "胜利条件系统初始化完成", "_安全握手完成": "🔐 安全握手完成", "连接管理器初始化完成": "连接管理器初始化完成", "游戏管理器初始化完成": "游戏管理器初始化完成", "服务器已关闭": "服务器已关闭", "房间管理线程已启动": "房间管理线程已启动", "_端口_selfport_已被占用": "❌ 端口 {self.port} 已被占用", "_成功绑定到_selfhostselfport": "✅ 成功绑定到 {self.host}:{self.port}", "_世界大战游戏服务器已启动": "🎮 世界大战游戏服务器已启动", "_数据初始化完成": "✅ 数据初始化完成", "安全游戏服务器初始化完成": "安全游戏服务器初始化完成", "消息处理器初始化完成": "消息处理器初始化完成", "连接意外断开已接收_lendata": "连接意外断开，已接收 {len(data)}", "线程安全会话管理器初始化完成": "线程安全会话管理器初始化完成", "房间id生成器初始化完成": "房间ID生成器初始化完成", "房间管理器初始化完成": "房间管理器初始化完成", "message_f成功加入房间_lenselfplayers": "message\": f\"成功加入房间 ({len(self.players)}", "message_f房间已满_lenselfplayers": "message\": f\"房间已满 ({len(self.players)}", "服务器监控器初始化完成": "服务器监控器初始化完成", "所有设置已重置为默认值": "所有设置已重置为默认值", "最大连接数已设置为": "最大连接数已设置为", "服务器端口已设置为": "服务器端口已设置为", "服务器地址已设置为": "服务器地址已设置为", "日志级别已设置为": "日志级别已设置为", "最大日志行数已设置为": "最大日志行数已设置为", "语言已设置为双语模式": "语言已设置为双语模式", "房间已满_lenselfplayers": "房间已满 ({len(self.players)}", "成功加入房间_lenselfplayers": "成功加入房间 ({len(self.players)}"}, "ui": {"无效选择": "无效选择", "输入选择": "输入选择", "请选择": "请选择", "服务器启动选项": "服务器启动选项", "n语言选择": "\\n语言选择", "输入新的最大日志行数": "输入新的最大日志行数", "输入新的服务器地址": "输入新的服务器地址", "输入新的服务器端口": "输入新的服务器端口", "输入新的最大连接数": "输入新的最大连接数"}, "error": {"获取城市数据失败_e": "获取城市数据失败: {e}", "获取灾害数据失败_e": "获取灾害数据失败: {e}", "获取资源数据失败_e": "获取资源数据失败: {e}", "生成气候数据失败_e": "生成气候数据失败: {e}", "生成地形数据失败_e": "生成地形数据失败: {e}", "获取贫困数据失败_e": "获取贫困数据失败: {e}", "api数据获取失败使用预设数据": "API数据获取失败，使用预设数据...", "加载数据失败": "加载数据失败", "保存数据失败": "保存数据失败", "ai数据生成失败": "AI数据生成失败", "混合数据初始化失败": "混合数据初始化失败", "_客户端处理错误": "❌ 客户端处理错误", "_客户端连接错误": "❌ 客户端连接错误", "message_游戏循环启动失败": "message\": \"游戏循环启动失败", "_未知错误": "❌ 未知错误", "_接受连接错误": "❌ 接受连接错误", "_绑定失败_e": "❌ 绑定失败: {e}", "_服务器启动失败": "❌ 服务器启动失败", "创建房间失败": "创建房间失败"}}