{"education": [{"policy_id": "build_university", "name": "建设大学", "description": "在指定地区建设大学，提高科技研发速度，吸引人才流入", "policy_type": "education", "cost_resources": {"money": 5000, "materials": 2000}, "cost_policy_points": 3, "required_tech_level": 2, "effects": [{"effect_type": "tech_speed", "target": "territory", "value": 0.3, "duration": 0, "description": "科技研发速度+30%"}, {"effect_type": "population_growth", "target": "territory", "value": 0.2, "duration": 0, "description": "人口增长+20%"}, {"effect_type": "education_level", "target": "territory", "value": 0.4, "duration": 0, "description": "教育水平+40%"}]}, {"policy_id": "vocational_training", "name": "职业培训计划", "description": "开展大规模职业培训，提高劳动力技能和生产效率", "policy_type": "education", "cost_resources": {"money": 2000}, "cost_policy_points": 2, "effects": [{"effect_type": "production_efficiency", "target": "territory", "value": 0.25, "duration": 10, "description": "生产效率+25%，持续10回合"}, {"effect_type": "unemployment", "target": "territory", "value": -0.15, "duration": 5, "description": "失业率-15%，持续5回合"}], "max_implementations": 3, "cooldown_turns": 5}], "industry": [{"policy_id": "attract_investment", "name": "招商引资", "description": "制定优惠政策吸引企业投资，增加税收收入和就业机会", "policy_type": "industry", "cost_resources": {"money": 3000}, "cost_policy_points": 2, "effects": [{"effect_type": "tax_income", "target": "territory", "value": 0.4, "duration": 0, "description": "税收收入+40%"}, {"effect_type": "employment", "target": "territory", "value": 0.3, "duration": 0, "description": "就业率+30%"}, {"effect_type": "gdp_growth", "target": "territory", "value": 0.2, "duration": 0, "description": "GDP增长+20%"}], "max_implementations": 2, "cooldown_turns": 3}, {"policy_id": "tech_park", "name": "科技园区建设", "description": "建设科技园区，发展高新技术产业，提升科技等级", "policy_type": "industry", "cost_resources": {"money": 8000, "materials": 3000, "technology": 100}, "cost_policy_points": 4, "required_tech_level": 3, "effects": [{"effect_type": "tech_level", "target": "territory", "value": 1, "duration": 0, "description": "科技等级+1"}, {"effect_type": "innovation", "target": "territory", "value": 0.5, "duration": 0, "description": "创新能力+50%"}, {"effect_type": "high_tech_jobs", "target": "territory", "value": 0.6, "duration": 0, "description": "高技术就业+60%"}]}], "infrastructure": [{"policy_id": "transport_infrastructure", "name": "交通基础设施", "description": "建设道路、铁路等交通设施，提高贸易效率", "policy_type": "infrastructure", "cost_resources": {"money": 4000, "materials": 5000}, "cost_policy_points": 3, "effects": [{"effect_type": "trade_efficiency", "target": "territory", "value": 0.3, "duration": 0, "description": "贸易效率+30%"}, {"effect_type": "transport_cost", "target": "territory", "value": -0.2, "duration": 0, "description": "运输成本-20%"}, {"effect_type": "connectivity", "target": "territory", "value": 0.4, "duration": 0, "description": "连通性+40%"}]}, {"policy_id": "energy_infrastructure", "name": "能源基础设施", "description": "建设发电厂、电网等能源设施，支持工业发展", "policy_type": "infrastructure", "cost_resources": {"money": 6000, "materials": 4000}, "cost_policy_points": 3, "required_tech_level": 2, "effects": [{"effect_type": "energy_capacity", "target": "territory", "value": 0.5, "duration": 0, "description": "能源产能+50%"}, {"effect_type": "industrial_capacity", "target": "territory", "value": 0.3, "duration": 0, "description": "工业产能+30%"}, {"effect_type": "energy_cost", "target": "territory", "value": -0.25, "duration": 0, "description": "能源成本-25%"}]}], "social": [{"policy_id": "healthcare_system", "name": "医疗保障体系", "description": "建立完善的医疗保障体系，提高人口健康水平", "policy_type": "social", "cost_resources": {"money": 3500}, "cost_policy_points": 2, "effects": [{"effect_type": "health_level", "target": "territory", "value": 0.3, "duration": 0, "description": "健康水平+30%"}, {"effect_type": "life_expectancy", "target": "territory", "value": 0.1, "duration": 0, "description": "预期寿命+10%"}, {"effect_type": "productivity", "target": "territory", "value": 0.15, "duration": 0, "description": "生产力+15%"}]}, {"policy_id": "affordable_housing", "name": "保障性住房", "description": "建设保障性住房，改善居民居住条件", "policy_type": "social", "cost_resources": {"money": 4000, "materials": 3000}, "cost_policy_points": 2, "effects": [{"effect_type": "living_standard", "target": "territory", "value": 0.25, "duration": 0, "description": "生活水平+25%"}, {"effect_type": "poverty_index", "target": "territory", "value": -0.2, "duration": 0, "description": "贫困指数-20%"}, {"effect_type": "population_satisfaction", "target": "territory", "value": 0.3, "duration": 0, "description": "人口满意度+30%"}]}], "environment": [{"policy_id": "environmental_protection", "name": "环境保护计划", "description": "实施环境保护措施，实现可持续发展", "policy_type": "environment", "cost_resources": {"money": 2500, "technology": 50}, "cost_policy_points": 2, "required_tech_level": 2, "effects": [{"effect_type": "environmental_quality", "target": "territory", "value": 0.4, "duration": 0, "description": "环境质量+40%"}, {"effect_type": "sustainability", "target": "territory", "value": 0.3, "duration": 0, "description": "可持续性+30%"}, {"effect_type": "tourism", "target": "territory", "value": 0.2, "duration": 0, "description": "旅游业+20%"}]}]}