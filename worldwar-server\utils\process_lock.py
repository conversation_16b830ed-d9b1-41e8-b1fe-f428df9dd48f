#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进程锁管理器 - 防止多开
Process Lock Manager - Prevent Multiple Instances
"""

import os
import sys
import time
import atexit
from pathlib import Path
from typing import Optional

# Windows兼容性处理
try:
    import fcntl
    HAS_FCNTL = True
except ImportError:
    HAS_FCNTL = False

if sys.platform == "win32":
    import msvcrt


class ProcessLock:
    """进程锁 - 防止程序多开"""
    
    def __init__(self, lock_name: str, lock_dir: str = "locks"):
        """
        初始化进程锁
        
        Args:
            lock_name: 锁文件名称
            lock_dir: 锁文件目录
        """
        self.lock_name = lock_name
        self.lock_dir = Path(lock_dir)
        self.lock_file = self.lock_dir / f"{lock_name}.lock"
        self.lock_fd: Optional[int] = None
        self.is_locked = False
        
        # 确保锁目录存在
        self.lock_dir.mkdir(exist_ok=True)
        
        # 注册退出时清理
        atexit.register(self.release)
    
    def acquire(self, timeout: int = 0) -> bool:
        """
        获取进程锁

        Args:
            timeout: 超时时间（秒），0表示不等待

        Returns:
            是否成功获取锁
        """
        try:
            if sys.platform == "win32":
                return self._acquire_windows(timeout)
            else:
                return self._acquire_unix(timeout)
        except Exception as e:
            print(f"获取进程锁失败: {e}")
            return False

    def _acquire_windows(self, timeout: int = 0) -> bool:
        """Windows下获取进程锁"""
        try:
            # 如果锁文件存在，先检查是否是僵尸锁
            if self.lock_file.exists():
                if self._is_zombie_lock():
                    # 清理僵尸锁文件
                    try:
                        self.lock_file.unlink()
                    except:
                        pass

            # 尝试创建锁文件
            start_time = time.time()
            while True:
                try:
                    # 使用独占模式创建文件
                    self.lock_fd = os.open(str(self.lock_file), os.O_CREAT | os.O_WRONLY | os.O_EXCL)

                    # 写入进程信息
                    process_info = f"PID: {os.getpid()}\nSTART_TIME: {time.strftime('%Y-%m-%d %H:%M:%S')}\n"
                    os.write(self.lock_fd, process_info.encode('utf-8'))
                    os.fsync(self.lock_fd)

                    self.is_locked = True
                    return True

                except OSError as e:
                    # 文件已存在或被占用
                    if timeout <= 0:
                        return False

                    # 检查超时
                    if time.time() - start_time >= timeout:
                        return False

                    time.sleep(0.1)

        except Exception as e:
            print(f"Windows进程锁获取失败: {e}")
            return False

    def _is_zombie_lock(self) -> bool:
        """检查是否是僵尸锁文件"""
        try:
            with open(self.lock_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 提取PID
            for line in content.split('\n'):
                if line.startswith('PID:'):
                    pid = int(line.split(':')[1].strip())

                    # 检查进程是否存在
                    try:
                        # Windows下检查进程是否存在
                        import subprocess
                        result = subprocess.run(['tasklist', '/FI', f'PID eq {pid}'],
                                              capture_output=True, text=True)
                        return str(pid) not in result.stdout
                    except:
                        # 如果无法检查，假设进程不存在
                        return True

            return True  # 无法解析PID，认为是僵尸锁

        except Exception:
            return True  # 读取失败，认为是僵尸锁

    def _acquire_unix(self, timeout: int = 0) -> bool:
        """Unix/Linux下获取进程锁"""
        if not HAS_FCNTL:
            return False

        try:
            # 打开锁文件
            self.lock_fd = os.open(str(self.lock_file), os.O_CREAT | os.O_WRONLY | os.O_TRUNC)

            # 尝试获取文件锁
            start_time = time.time()
            while True:
                try:
                    fcntl.flock(self.lock_fd, fcntl.LOCK_EX | fcntl.LOCK_NB)

                    # 写入进程信息
                    process_info = f"PID: {os.getpid()}\nSTART_TIME: {time.strftime('%Y-%m-%d %H:%M:%S')}\n"
                    os.write(self.lock_fd, process_info.encode('utf-8'))
                    os.fsync(self.lock_fd)

                    self.is_locked = True
                    return True

                except (IOError, OSError):
                    # 锁被占用
                    if timeout <= 0:
                        return False

                    # 检查超时
                    if time.time() - start_time >= timeout:
                        return False

                    time.sleep(0.1)

        except Exception:
            return False
    
    def release(self):
        """释放进程锁"""
        if self.is_locked and self.lock_fd is not None:
            try:
                if sys.platform == "win32":
                    # Windows下直接关闭文件
                    os.close(self.lock_fd)
                else:
                    # Unix/Linux下先解锁再关闭
                    if HAS_FCNTL:
                        fcntl.flock(self.lock_fd, fcntl.LOCK_UN)
                    os.close(self.lock_fd)

                # 删除锁文件
                if self.lock_file.exists():
                    self.lock_file.unlink()

                self.is_locked = False
                self.lock_fd = None

            except Exception as e:
                print(f"释放进程锁失败: {e}")
    
    def is_running(self) -> bool:
        """检查是否有其他实例正在运行"""
        if not self.lock_file.exists():
            return False

        # 检查是否是僵尸锁
        if self._is_zombie_lock():
            try:
                self.lock_file.unlink()
            except:
                pass
            return False

        return True
    
    def get_running_info(self) -> Optional[str]:
        """获取正在运行的实例信息"""
        if not self.lock_file.exists():
            return None
        
        try:
            with open(self.lock_file, 'r', encoding='utf-8') as f:
                return f.read().strip()
        except Exception:
            return None
    
    def __enter__(self):
        """上下文管理器入口"""
        if not self.acquire():
            raise RuntimeError(f"无法获取进程锁: {self.lock_name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.release()


def check_single_instance(app_name: str) -> bool:
    """
    检查是否为单实例运行
    
    Args:
        app_name: 应用程序名称
        
    Returns:
        是否为单实例
    """
    lock = ProcessLock(app_name)
    
    if lock.is_running():
        running_info = lock.get_running_info()
        print(f"❌ 检测到 {app_name} 已在运行!")
        print(f"📋 运行信息:")
        if running_info:
            print(running_info)
        print(f"⚠️ 请先关闭现有实例，或等待其自动退出。")
        return False
    
    return lock.acquire()


def create_process_lock(app_name: str) -> ProcessLock:
    """
    创建进程锁
    
    Args:
        app_name: 应用程序名称
        
    Returns:
        进程锁对象
    """
    return ProcessLock(app_name)
