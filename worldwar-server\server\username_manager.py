#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户名管理系统
Username Management System

提供用户名验证、冲突检查、随机用户名生成等功能
Provides username validation, conflict checking, random username generation, etc.
"""

import re
import random
import threading
from typing import Dict, List, Optional, Set, Any
from dataclasses import dataclass
from datetime import datetime, timedelta

@dataclass
class UsernameValidationResult:
    """用户名验证结果"""
    is_valid: bool
    error_code: Optional[str] = None
    error_message: Optional[str] = None
    suggestions: List[str] = None

class UsernameManager:
    """用户名管理器"""
    
    def __init__(self):
        """初始化用户名管理器"""
        # 线程安全锁
        self.lock = threading.RLock()
        
        # 已使用的用户名集合
        self.used_usernames: Set[str] = set()
        
        # 用户名使用历史（用于防止短时间内重复使用）
        self.username_history: Dict[str, datetime] = {}
        
        # 禁用词列表
        self.forbidden_words = {
            "admin", "administrator", "root", "system", "server", "bot", "ai",
            "null", "undefined", "test", "guest", "anonymous", "user", "player",
            "管理员", "系统", "机器人", "测试", "游客", "匿名", "用户", "玩家"
        }
        
        # 随机用户名词库
        self.adjectives = [
            "勇敢", "智慧", "神秘", "强大", "敏捷", "冷静", "热情", "坚定",
            "Brave", "Wise", "Mysterious", "Mighty", "Swift", "Cool", "Fierce", "Bold"
        ]
        
        self.nouns = [
            "战士", "法师", "游侠", "刺客", "骑士", "弓手", "盗贼", "守护者",
            "Warrior", "Mage", "Ranger", "Assassin", "Knight", "Archer", "Rogue", "Guardian"
        ]
        
        # 导入日志
        from shared.enhanced_logger import get_server_logger
        self.logger = get_server_logger("UsernameManager")
        
        self.logger.info("用户名管理器初始化完成")
    
    def validate_username(self, username: str) -> UsernameValidationResult:
        """
        验证用户名
        
        Args:
            username: 要验证的用户名
            
        Returns:
            验证结果
        """
        if not username:
            return UsernameValidationResult(
                is_valid=False,
                error_code="empty_username",
                error_message="用户名不能为空"
            )
        
        username = username.strip()
        
        # 长度检查
        if len(username) < 2:
            return UsernameValidationResult(
                is_valid=False,
                error_code="too_short",
                error_message="用户名至少需要2个字符"
            )
        
        if len(username) > 20:
            return UsernameValidationResult(
                is_valid=False,
                error_code="too_long",
                error_message="用户名不能超过20个字符"
            )
        
        # 字符检查（允许中文、英文、数字、下划线、连字符）
        if not re.match(r'^[\u4e00-\u9fff\w\-]+$', username):
            return UsernameValidationResult(
                is_valid=False,
                error_code="invalid_characters",
                error_message="用户名只能包含中文、英文、数字、下划线和连字符"
            )
        
        # 禁用词检查
        username_lower = username.lower()
        for forbidden in self.forbidden_words:
            if forbidden in username_lower:
                return UsernameValidationResult(
                    is_valid=False,
                    error_code="forbidden_word",
                    error_message=f"用户名不能包含禁用词: {forbidden}"
                )
        
        # 特殊模式检查（防止纯数字、纯符号等）
        if username.isdigit():
            return UsernameValidationResult(
                is_valid=False,
                error_code="only_numbers",
                error_message="用户名不能只包含数字"
            )
        
        if re.match(r'^[\-_]+$', username):
            return UsernameValidationResult(
                is_valid=False,
                error_code="only_symbols",
                error_message="用户名不能只包含符号"
            )
        
        return UsernameValidationResult(is_valid=True)
    
    def check_availability(self, username: str) -> bool:
        """
        检查用户名是否可用
        
        Args:
            username: 要检查的用户名
            
        Returns:
            是否可用
        """
        with self.lock:
            username = username.strip()
            
            # 检查是否正在使用
            if username in self.used_usernames:
                return False
            
            # 检查历史使用记录（防止短时间内重复使用）
            if username in self.username_history:
                last_used = self.username_history[username]
                if datetime.now() - last_used < timedelta(minutes=5):
                    return False
            
            return True

    def reserve_username(self, username: str) -> bool:
        """
        预留用户名

        Args:
            username: 要预留的用户名

        Returns:
            是否成功预留
        """
        with self.lock:
            username = username.strip()

            if not self.check_availability(username):
                return False

            self.used_usernames.add(username)
            self.logger.info(f"预留用户名: {username}")
            return True

    def release_username(self, username: str):
        """
        释放用户名

        Args:
            username: 要释放的用户名
        """
        with self.lock:
            username = username.strip()

            if username in self.used_usernames:
                self.used_usernames.remove(username)
                self.username_history[username] = datetime.now()
                self.logger.info(f"释放用户名: {username}")

    def generate_random_username(self, language: str = "mixed") -> str:
        """
        生成随机用户名

        Args:
            language: 语言偏好 ("chinese", "english", "mixed")

        Returns:
            随机生成的用户名
        """
        max_attempts = 50

        for attempt in range(max_attempts):
            if language == "chinese":
                # 只使用中文词汇
                adj = random.choice([adj for adj in self.adjectives if self._is_chinese(adj)])
                noun = random.choice([noun for noun in self.nouns if self._is_chinese(noun)])
            elif language == "english":
                # 只使用英文词汇
                adj = random.choice([adj for adj in self.adjectives if not self._is_chinese(adj)])
                noun = random.choice([noun for noun in self.nouns if not self._is_chinese(noun)])
            else:
                # 混合使用
                adj = random.choice(self.adjectives)
                noun = random.choice(self.nouns)

            # 生成基础用户名
            base_username = f"{adj}{noun}"

            # 如果可用，直接返回
            if self.check_availability(base_username):
                return base_username

            # 如果不可用，添加数字后缀
            for i in range(1, 100):
                numbered_username = f"{base_username}{i}"
                if self.check_availability(numbered_username):
                    return numbered_username

        # 如果所有尝试都失败，生成一个基于时间戳的用户名
        timestamp = str(int(datetime.now().timestamp()))[-6:]
        fallback_username = f"Player{timestamp}"

        return fallback_username

    def suggest_usernames(self, original_username: str, count: int = 5) -> List[str]:
        """
        为已被占用的用户名提供建议

        Args:
            original_username: 原始用户名
            count: 建议数量

        Returns:
            建议的用户名列表
        """
        suggestions = []
        original = original_username.strip()

        # 策略1: 添加数字后缀
        for i in range(1, count + 1):
            suggestion = f"{original}{i}"
            if self.check_availability(suggestion):
                suggestions.append(suggestion)

        # 策略2: 添加下划线和数字
        if len(suggestions) < count:
            for i in range(1, count + 1):
                suggestion = f"{original}_{i}"
                if self.check_availability(suggestion):
                    suggestions.append(suggestion)

        # 策略3: 在前面添加形容词
        if len(suggestions) < count:
            for adj in random.sample(self.adjectives, min(len(self.adjectives), count)):
                suggestion = f"{adj}{original}"
                if len(suggestion) <= 20 and self.check_availability(suggestion):
                    suggestions.append(suggestion)

        # 策略4: 生成完全随机的用户名
        while len(suggestions) < count:
            random_username = self.generate_random_username()
            if random_username not in suggestions:
                suggestions.append(random_username)

        return suggestions[:count]

    def _is_chinese(self, text: str) -> bool:
        """检查文本是否包含中文字符"""
        return bool(re.search(r'[\u4e00-\u9fff]', text))

    def get_username_stats(self) -> Dict[str, Any]:
        """
        获取用户名统计信息

        Returns:
            统计信息字典
        """
        with self.lock:
            return {
                "total_used": len(self.used_usernames),
                "history_count": len(self.username_history),
                "forbidden_words_count": len(self.forbidden_words),
                "adjectives_count": len(self.adjectives),
                "nouns_count": len(self.nouns)
            }

    def cleanup_history(self, max_age_hours: int = 24):
        """
        清理过期的用户名历史记录

        Args:
            max_age_hours: 最大保留时间（小时）
        """
        with self.lock:
            cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
            expired_usernames = [
                username for username, timestamp in self.username_history.items()
                if timestamp < cutoff_time
            ]

            for username in expired_usernames:
                del self.username_history[username]

            if expired_usernames:
                self.logger.info(f"清理了 {len(expired_usernames)} 个过期的用户名历史记录")
