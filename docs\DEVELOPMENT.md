# 🛠️ 开发指南 / Development Guide

## 🚀 开发环境设置 / Development Environment Setup

### 📋 系统要求 / System Requirements
- **Python**: 3.7+ (推荐 3.9+)
- **操作系统**: Windows 10+, Ubuntu 18.04+, macOS 10.15+
- **内存**: 最少 4GB RAM
- **存储**: 最少 1GB 可用空间
- **网络**: 稳定的网络连接

### 🔧 开发工具 / Development Tools

#### 必需工具 / Required Tools
```bash
# Python包管理器
pip install --upgrade pip

# 虚拟环境
python -m venv venv

# 代码格式化
pip install black flake8 isort

# 测试框架
pip install pytest pytest-cov

# 类型检查
pip install mypy
```

#### 推荐IDE / Recommended IDEs
- **VS Code**: 轻量级，插件丰富
- **PyCharm**: 功能强大，专业Python IDE
- **Vim/Neovim**: 高效的命令行编辑器

### 📦 项目设置 / Project Setup

1. **克隆项目 / Clone Project**
   ```bash
   git clone https://github.com/your-repo/worldwar.git
   cd worldwar
   ```

2. **创建虚拟环境 / Create Virtual Environment**
   ```bash
   python -m venv venv
   
   # 激活虚拟环境
   # Windows
   venv\Scripts\activate
   # Linux/Mac
   source venv/bin/activate
   ```

3. **安装开发依赖 / Install Development Dependencies**
   ```bash
   # 服务器端依赖
   cd worldwar-server
   pip install -r requirements.txt
   pip install -r requirements-dev.txt
   
   # 客户端依赖
   cd ../worldwar-client
   pip install -r requirements.txt
   pip install -r requirements-dev.txt
   ```

4. **配置开发环境 / Configure Development Environment**
   ```bash
   # 复制配置模板
   cp worldwar-server/config/server.ini.example worldwar-server/config/server.ini
   cp worldwar-client/config/client.ini.example worldwar-client/config/client.ini
   
   # 编辑配置文件
   # 设置 debug = true
   ```

## 📁 项目结构 / Project Structure

### 🏗️ 目录结构 / Directory Structure
```
worldwar/
├── worldwar-server/           # 服务器端项目
│   ├── server/               # 服务器核心模块
│   │   ├── game_server.py    # 主服务器类
│   │   ├── player_session.py # 玩家会话管理
│   │   ├── room_manager.py   # 房间管理
│   │   ├── message_processor.py # 消息处理
│   │   └── username_manager.py  # 用户名管理
│   ├── shared/               # 共享组件
│   │   ├── enhanced_logger.py # 日志系统
│   │   ├── language_manager.py # 语言管理
│   │   ├── secure_protocol.py # 安全协议
│   │   └── message_manager.py # 消息管理
│   ├── utils/                # 工具模块
│   │   ├── multi_instance_guard.py # 多开防护
│   │   └── debug_manager.py  # 调试管理
│   ├── config/               # 配置文件
│   ├── logs/                 # 日志文件
│   └── tests/                # 测试文件
├── worldwar-client/          # 客户端项目
│   ├── client/               # 客户端核心模块
│   │   ├── core/             # 核心功能
│   │   ├── services/         # 服务层
│   │   ├── ui/               # 用户界面
│   │   └── utils/            # 工具函数
│   ├── shared/               # 共享组件
│   ├── config/               # 配置文件
│   ├── logs/                 # 日志文件
│   └── tests/                # 测试文件
└── docs/                     # 项目文档
```

### 🔧 模块职责 / Module Responsibilities

#### 服务器端模块 / Server Modules
- **game_server.py**: 主服务器，处理客户端连接
- **player_session.py**: 玩家会话和认证管理
- **room_manager.py**: 游戏房间创建和管理
- **message_processor.py**: 消息路由和处理
- **username_manager.py**: 用户名验证和管理

#### 客户端模块 / Client Modules
- **client_core.py**: 客户端主要逻辑
- **network_service.py**: 网络通信服务
- **ui_manager.py**: 用户界面管理
- **username_helper.py**: 用户名辅助功能

## 🔄 开发流程 / Development Workflow

### 🌿 分支管理 / Branch Management
```bash
# 主分支
main          # 稳定版本
develop       # 开发版本

# 功能分支
feature/new-game-mode     # 新游戏模式
feature/chat-system       # 聊天系统
feature/ai-player         # AI玩家

# 修复分支
hotfix/connection-bug     # 连接问题修复
hotfix/security-patch     # 安全补丁
```

### 📝 提交规范 / Commit Convention
```bash
# 提交消息格式
<type>(<scope>): <description>

# 类型 (type)
feat:     新功能
fix:      错误修复
docs:     文档更新
style:    代码格式
refactor: 代码重构
test:     测试相关
chore:    构建/工具

# 示例
feat(server): add username validation
fix(client): resolve connection timeout issue
docs(readme): update installation guide
```

### 🧪 测试流程 / Testing Workflow

#### 单元测试 / Unit Tests
```bash
# 运行所有测试
pytest

# 运行特定模块测试
pytest worldwar-server/tests/test_username_manager.py

# 生成覆盖率报告
pytest --cov=server --cov-report=html
```

#### 集成测试 / Integration Tests
```bash
# 启动测试服务器
python worldwar-server/server_main.py --debug

# 运行多客户端测试
python worldwar-client/client_main.py --multi-instance --instances 3
```

#### 手动测试 / Manual Testing
```bash
# 测试检查清单
□ 服务器启动正常
□ 客户端连接成功
□ 用户名验证工作
□ 房间创建/加入功能
□ 消息发送/接收
□ 断线重连机制
□ 多语言切换
□ 日志记录正常
```

## 🎨 代码规范 / Code Standards

### 📏 代码风格 / Code Style

#### Python代码规范 / Python Code Standards
```python
# 使用 Black 格式化
black worldwar-server/ worldwar-client/

# 使用 isort 排序导入
isort worldwar-server/ worldwar-client/

# 使用 flake8 检查代码质量
flake8 worldwar-server/ worldwar-client/
```

#### 命名规范 / Naming Conventions
```python
# 类名：大驼峰命名
class GameServer:
    pass

# 函数名：小写下划线
def handle_message():
    pass

# 常量：大写下划线
MAX_PLAYERS = 8

# 私有方法：下划线开头
def _internal_method():
    pass
```

### 📝 文档规范 / Documentation Standards

#### 函数文档 / Function Documentation
```python
def validate_username(self, username: str) -> UsernameValidationResult:
    """
    验证用户名格式和可用性
    
    Args:
        username: 要验证的用户名
        
    Returns:
        验证结果对象，包含是否有效和错误信息
        
    Raises:
        ValueError: 当用户名为None时抛出
        
    Example:
        >>> manager = UsernameManager()
        >>> result = manager.validate_username("Player1")
        >>> print(result.is_valid)
        True
    """
```

#### 类文档 / Class Documentation
```python
class UsernameManager:
    """
    用户名管理器
    
    提供用户名验证、冲突检查、随机生成等功能。
    支持线程安全操作和历史记录管理。
    
    Attributes:
        used_usernames: 已使用的用户名集合
        username_history: 用户名使用历史
        
    Example:
        >>> manager = UsernameManager()
        >>> manager.reserve_username("Player1")
        True
    """
```

### 🔒 安全规范 / Security Standards

#### 输入验证 / Input Validation
```python
# 总是验证用户输入
def process_message(self, message: str):
    if not message or len(message) > MAX_MESSAGE_LENGTH:
        raise ValueError("Invalid message")
    
    # 清理和验证消息内容
    cleaned_message = self._sanitize_input(message)
    return cleaned_message
```

#### 错误处理 / Error Handling
```python
# 使用具体的异常类型
try:
    result = self.process_request(data)
except ValidationError as e:
    self.logger.warning(f"Validation failed: {e}")
    return {"error": "invalid_input"}
except NetworkError as e:
    self.logger.error(f"Network error: {e}")
    return {"error": "network_failure"}
except Exception as e:
    self.logger.critical(f"Unexpected error: {e}")
    return {"error": "internal_error"}
```

## 🚀 部署和发布 / Deployment and Release

### 📦 构建流程 / Build Process
```bash
# 1. 运行测试
pytest

# 2. 代码质量检查
flake8 worldwar-server/ worldwar-client/
mypy worldwar-server/ worldwar-client/

# 3. 生成文档
sphinx-build -b html docs/ docs/_build/

# 4. 打包发布
python setup.py sdist bdist_wheel
```

### 🏷️ 版本管理 / Version Management
```bash
# 语义化版本控制
MAJOR.MINOR.PATCH

# 示例
3.0.0  # 主要版本
3.1.0  # 次要版本（新功能）
3.1.1  # 补丁版本（错误修复）
```

### 📋 发布检查清单 / Release Checklist
```bash
□ 所有测试通过
□ 代码质量检查通过
□ 文档更新完成
□ 版本号更新
□ CHANGELOG.md 更新
□ 安全漏洞扫描
□ 性能测试通过
□ 多平台兼容性测试
□ 用户手册更新
□ 发布说明准备
```

## 🤝 贡献指南 / Contribution Guidelines

### 📝 提交贡献 / Submitting Contributions

1. **Fork项目 / Fork the Project**
2. **创建功能分支 / Create Feature Branch**
   ```bash
   git checkout -b feature/amazing-feature
   ```

3. **提交更改 / Commit Changes**
   ```bash
   git commit -m "feat: add amazing feature"
   ```

4. **推送分支 / Push Branch**
   ```bash
   git push origin feature/amazing-feature
   ```

5. **创建Pull Request / Create Pull Request**

### 🔍 代码审查 / Code Review

#### 审查要点 / Review Points
- **功能正确性**: 代码是否实现了预期功能
- **代码质量**: 是否遵循代码规范
- **性能影响**: 是否影响系统性能
- **安全性**: 是否存在安全漏洞
- **测试覆盖**: 是否有足够的测试
- **文档完整**: 是否有相应的文档

#### 审查流程 / Review Process
1. **自动检查**: CI/CD自动运行测试和检查
2. **同行审查**: 至少一个开发者审查代码
3. **测试验证**: 在测试环境验证功能
4. **文档审查**: 检查文档更新
5. **最终批准**: 项目维护者最终批准

---

**🛠️ 遵循开发规范，共同构建高质量的游戏！**
**Follow development standards to build high-quality games together!**
