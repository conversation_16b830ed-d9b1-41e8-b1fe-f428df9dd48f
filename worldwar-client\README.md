# 🎮 世界大战游戏客户端
## World War Game Client

**版本**: v3.0  
**类型**: 独立客户端应用  
**语言**: Python 3.8+

---

## 📋 项目简介

世界大战游戏的独立客户端，提供完整的游戏界面和网络通信功能，支持连接到游戏服务器进行多人游戏。

### 主要功能
- 🔐 安全的服务器连接和认证
- 🎮 完整的游戏界面
- 🏠 房间创建和加入
- 💬 实时聊天系统
- 🌍 多语言支持（中文/英文）
- 📊 游戏状态显示
- 📝 详细的操作日志

---

## 🚀 快速开始

### 环境要求
- Python 3.8 或更高版本
- 操作系统：Windows/Linux/macOS

### 安装依赖
```bash
pip install -r requirements.txt
```

### 启动客户端
```bash
# 使用默认配置启动
python client_main.py

# 指定服务器地址
python client_main.py --server localhost:8888

# 启用调试模式
python client_main.py --debug
```

### 配置文件
- `config/client_config.ini` - 客户端主配置
- `config/settings.json` - 用户设置
- `languages/` - 语言包文件

---

## 📁 项目结构

```
worldwar-client/
├── client_main.py          # 客户端启动入口
├── requirements.txt        # 依赖包列表
├── config/                 # 配置文件
│   ├── client_config.ini
│   └── settings.json
├── client/                 # 客户端核心
│   ├── main.py             # 主程序
│   ├── core/               # 核心组件
│   │   ├── new_client_core.py
│   │   ├── connection_manager.py
│   │   ├── operation_manager.py
│   │   └── player_manager.py
│   ├── network/            # 网络组件
│   │   ├── secure_client.py
│   │   └── message_handler.py
│   ├── services/           # 服务组件
│   │   └── network_service.py
│   ├── ui/                 # 用户界面
│   │   └── console_ui.py
│   └── game/               # 游戏逻辑
│       └── game_client.py
├── shared/                 # 共享组件
│   ├── message_types.py    # 消息类型定义
│   ├── protocol.py         # 通信协议
│   ├── secure_protocol.py  # 安全协议
│   ├── security_manager.py # 安全管理
│   └── message_manager.py  # 消息管理
├── languages/              # 语言包
│   ├── chinese.json        # 中文语言包
│   └── english.json        # 英文语言包
├── utils/                  # 工具模块
│   ├── logger.py           # 日志系统
│   └── encoding_utils.py   # 编码工具
└── logs/                   # 日志文件
    ├── client/             # 客户端日志
    └── latest_client.log   # 最新客户端日志
```

---

## 🔧 配置说明

### 客户端配置 (client_config.ini)
```ini
[connection]
default_server = localhost
default_port = 8888
connection_timeout = 10
reconnect_attempts = 3

[ui]
language = chinese
theme = default
auto_save_settings = true

[logging]
level = INFO
log_to_file = true
log_to_console = false
```

### 用户设置 (settings.json)
```json
{
  "player_name": "",
  "language": "chinese",
  "server_history": [
    "localhost:8888"
  ],
  "ui_preferences": {
    "auto_connect": false,
    "save_password": false
  }
}
```

---

## 🎮 游戏操作

### 连接服务器
1. 启动客户端
2. 选择"连接服务器"
3. 输入服务器地址和端口
4. 输入玩家名称
5. 开始游戏

### 房间操作
- **创建房间**: 设置房间名称、最大玩家数、游戏模式
- **加入房间**: 输入房间ID或从列表选择
- **房间聊天**: 与其他玩家实时交流
- **准备游戏**: 切换准备状态
- **开始游戏**: 房主可以开始游戏

### 游戏功能
- **实时状态**: 查看游戏进度和玩家状态
- **操作记录**: 所有操作都有详细记录
- **断线重连**: 自动重连功能
- **多语言**: 支持中英文切换

---

## 🌍 多语言支持

### 语言切换
在主菜单中选择"语言设置"可以切换界面语言。

### 支持的语言
- 🇨🇳 简体中文 (chinese.json)
- 🇺🇸 English (english.json)

### 添加新语言
1. 复制现有语言文件
2. 翻译所有文本内容
3. 在配置中添加新语言选项

---

## 📊 日志和调试

### 日志文件
- `logs/client/client_YYYYMMDD_HHMMSS.log` - 详细客户端日志
- `logs/latest_client.log` - 最新日志的快捷链接

### 调试模式
```bash
# 启用详细日志
python client_main.py --debug --verbose

# 查看网络通信
python client_main.py --debug --trace-network
```

---

## 🔒 安全特性

- **加密通信**: 与服务器的所有通信都经过加密
- **身份验证**: 安全的玩家身份验证
- **会话保护**: 防止会话劫持
- **数据完整性**: 消息完整性验证

---

## 🐛 故障排除

### 常见问题

1. **无法连接服务器**
   - 检查服务器地址和端口
   - 确认服务器正在运行
   - 检查防火墙设置

2. **游戏卡顿或延迟**
   - 检查网络连接
   - 查看客户端日志
   - 尝试重新连接

3. **界面显示问题**
   - 检查语言设置
   - 重置用户配置
   - 更新客户端版本

### 日志分析
```bash
# 查看最新日志
tail -f logs/latest_client.log

# 搜索连接问题
grep "connection" logs/client/*.log

# 查看错误信息
grep "ERROR" logs/client/*.log
```

---

## 🎯 快捷键

- `Ctrl+C` - 退出程序
- `Ctrl+R` - 重新连接
- `Ctrl+L` - 切换语言
- `F1` - 显示帮助

---

## 📞 技术支持

如有问题，请：
1. 查看客户端日志文件
2. 检查网络连接
3. 确认服务器状态
4. 验证配置文件

---

## 📄 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。
