#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地形游戏初始化器 - 将生成的地形数据转换为游戏状态
Terrain Game Initializer - Converts generated terrain data into game state
"""

import logging
import random
import math
from typing import Dict, Any, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

from game_logic.economic_system import Territory
from game_logic.player_manager import Player


class StartingPositionStrategy(Enum):
    """起始位置策略"""
    BALANCED = "balanced"        # 平衡分配
    RANDOM = "random"           # 随机分配
    CLUSTERED = "clustered"     # 集群分配
    COASTAL = "coastal"         # 沿海优先


@dataclass
class GameTerrainCell:
    """游戏地形单元"""
    x: int
    y: int
    height: float
    temperature: float
    precipitation: float
    terrain_type: str
    biome: str
    resources: Dict[str, float]
    is_water: bool
    is_mountain: bool
    strategic_value: float


@dataclass
class StartingPosition:
    """玩家起始位置"""
    player_id: str
    center_cell: Tuple[int, int]
    territory_cells: List[Tuple[int, int]]
    starting_resources: Dict[str, float]
    starting_population: int
    capital_city: Optional[Dict[str, Any]] = None


class TerrainGameInitializer:
    """地形游戏初始化器"""
    
    def __init__(self):
        """初始化地形游戏初始化器"""
        self.logger = logging.getLogger(__name__)
        
        # 游戏平衡参数
        self.balance_config = {
            "starting_territory_size": 5,      # 起始领土半径
            "min_distance_between_players": 10, # 玩家间最小距离
            "base_starting_population": 100000, # 基础起始人口
            "base_starting_resources": {        # 基础起始资源
                "gold": 1000,
                "food": 500,
                "production": 200,
                "research": 100
            }
        }    
    d
ef initialize_game_from_terrain(self, terrain_data: Dict[str, Any], 
                                   players: List[str],
                                   game_settings: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        从地形数据初始化游戏状态
        
        Args:
            terrain_data: 地形生成器产生的地形数据
            players: 玩家列表
            game_settings: 游戏设置
            
        Returns:
            初始化的游戏状态数据
        """
        self.logger.info(f"开始从地形数据初始化游戏，玩家数: {len(players)}")
        
        try:
            # 解析地形数据
            terrain_cells = self._parse_terrain_data(terrain_data)
            
            # 分析地形特征
            terrain_analysis = self._analyze_terrain(terrain_cells)
            
            # 确定玩家起始位置
            starting_positions = self._determine_starting_positions(
                terrain_cells, players, terrain_analysis
            )
            
            # 生成初始领土
            initial_territories = self._generate_initial_territories(
                terrain_cells, starting_positions
            )
            
            # 放置初始城市
            initial_cities = self._place_initial_cities(
                terrain_cells, starting_positions
            )
            
            # 分配初始资源
            resource_allocation = self._allocate_initial_resources(
                terrain_cells, starting_positions, terrain_analysis
            )
            
            # 创建游戏平衡调整
            balance_adjustments = self._calculate_balance_adjustments(
                starting_positions, terrain_analysis
            )
            
            # 组装游戏初始化数据
            game_init_data = {
                "terrain_info": {
                    "type": terrain_data.get("type", "unknown"),
                    "size": terrain_data.get("size", (512, 512)),
                    "cells": len(terrain_cells),
                    "analysis": terrain_analysis
                },
                "players": {
                    player_id: {
                        "starting_position": pos,
                        "initial_territories": initial_territories[player_id],
                        "initial_cities": initial_cities[player_id],
                        "starting_resources": resource_allocation[player_id],
                        "balance_adjustments": balance_adjustments[player_id]
                    }
                    for player_id, pos in zip(players, starting_positions)
                }
            }
            
            self.logger.info("游戏初始化数据生成完成")
            return game_init_data
            
        except Exception as e:
            self.logger.error(f"从地形数据初始化游戏失败: {e}")
            raise   
 
    def _parse_terrain_data(self, terrain_data: Dict[str, Any]) -> List[GameTerrainCell]:
        """解析地形数据为游戏地形单元"""
        terrain_cells = []
        
        if terrain_data.get("type") == "perlin":
            # 处理柏林噪声地形数据
            heightmap = terrain_data.get("heightmap", [])
            temperature_map = terrain_data.get("temperature_map", [])
            precipitation_map = terrain_data.get("precipitation_map", [])
            
            height = len(heightmap)
            width = len(heightmap[0]) if heightmap else 0
            
            for y in range(height):
                for x in range(width):
                    cell_height = heightmap[y][x]
                    cell_temp = temperature_map[y][x] if y < len(temperature_map) and x < len(temperature_map[y]) else 0.5
                    cell_precip = precipitation_map[y][x] if y < len(precipitation_map) and x < len(precipitation_map[y]) else 0.5
                    
                    # 确定地形类型和生物群系
                    terrain_type, biome = self._determine_terrain_and_biome(
                        cell_height, cell_temp, cell_precip
                    )
                    
                    # 计算资源分布
                    resources = self._calculate_cell_resources(
                        terrain_type, biome, cell_height, cell_temp, cell_precip
                    )
                    
                    # 计算战略价值
                    strategic_value = self._calculate_strategic_value(
                        x, y, cell_height, terrain_type, width, height
                    )
                    
                    cell = GameTerrainCell(
                        x=x, y=y,
                        height=cell_height,
                        temperature=cell_temp,
                        precipitation=cell_precip,
                        terrain_type=terrain_type,
                        biome=biome,
                        resources=resources,
                        is_water=cell_height < 0.3,  # 海平面以下
                        is_mountain=cell_height > 0.7,  # 山地阈值
                        strategic_value=strategic_value
                    )
                    terrain_cells.append(cell)
        
        elif terrain_data.get("type") == "realworld":
            # 处理真实世界地形数据
            terrain_cells = self._parse_realworld_terrain(terrain_data)
        
        self.logger.info(f"解析了 {len(terrain_cells)} 个地形单元")
        return terrain_cells    

    def _determine_terrain_and_biome(self, height: float, temperature: float, 
                                   precipitation: float) -> Tuple[str, str]:
        """根据高度、温度、降水确定地形类型和生物群系"""
        # 确定基础地形类型
        if height < 0.3:
            terrain_type = "water"
            biome = "ocean" if height < 0.2 else "shallow_water"
        elif height > 0.7:
            terrain_type = "mountains"
            biome = "alpine" if temperature < 0.3 else "highland"
        elif height > 0.5:
            terrain_type = "hills"
            biome = "highland" if temperature < 0.4 else "temperate_hills"
        else:
            terrain_type = "plains"
            
            # 根据温度和降水确定平原生物群系
            if temperature > 0.7:
                if precipitation < 0.3:
                    biome = "desert"
                elif precipitation > 0.7:
                    biome = "tropical_rainforest"
                else:
                    biome = "savanna"
            elif temperature > 0.4:
                if precipitation < 0.4:
                    biome = "grassland"
                elif precipitation > 0.6:
                    biome = "temperate_forest"
                else:
                    biome = "temperate_plains"
            else:
                if precipitation < 0.4:
                    biome = "tundra"
                else:
                    biome = "boreal_forest"
        
        return terrain_type, biome
    
    def _calculate_cell_resources(self, terrain_type: str, biome: str, 
                                height: float, temperature: float, 
                                precipitation: float) -> Dict[str, float]:
        """计算地形单元的资源分布"""
        resources = {"food": 0.0, "production": 0.0, "gold": 0.0, "research": 0.0}
        
        # 基础资源根据生物群系
        biome_resources = {
            "ocean": {"food": 0.3},
            "shallow_water": {"food": 0.5},
            "desert": {"gold": 0.4, "production": 0.2},
            "grassland": {"food": 0.6, "production": 0.3},
            "temperate_plains": {"food": 0.5, "production": 0.4},
            "temperate_forest": {"food": 0.4, "production": 0.3, "research": 0.3},
            "tropical_rainforest": {"food": 0.7, "research": 0.5},
            "savanna": {"food": 0.5, "production": 0.2},
            "tundra": {"research": 0.3},
            "boreal_forest": {"food": 0.3, "production": 0.4, "research": 0.4},
            "alpine": {"research": 0.2},
            "highland": {"production": 0.3, "gold": 0.3},
            "temperate_hills": {"production": 0.4, "gold": 0.2}
        }
        
        base_resources = biome_resources.get(biome, {})
        for resource, value in base_resources.items():
            resources[resource] = value
        
        # 高度影响
        if height > 0.5:  # 高地有更多矿物资源
            resources["gold"] += (height - 0.5) * 0.4
            resources["production"] += (height - 0.5) * 0.2
        
        # 温度影响
        if 0.4 < temperature < 0.8:  # 温带最适合农业
            resources["food"] += 0.2
        
        # 降水影响
        if precipitation > 0.6:  # 高降水有利于农业和森林
            resources["food"] += 0.1
            resources["research"] += 0.1
        
        # 添加随机变化
        for resource in resources:
            resources[resource] *= (0.8 + random.random() * 0.4)  # ±20% 随机变化
            resources[resource] = max(0.0, min(1.0, resources[resource]))  # 限制在0-1范围
        
        return resources 
   
    def _calculate_strategic_value(self, x: int, y: int, height: float, 
                                 terrain_type: str, width: int, height_map: int) -> float:
        """计算地形单元的战略价值"""
        strategic_value = 0.0
        
        # 海岸线价值高
        if terrain_type != "water":
            # 检查周围是否有水域
            for dx in [-1, 0, 1]:
                for dy in [-1, 0, 1]:
                    nx, ny = x + dx, y + dy
                    if 0 <= nx < width and 0 <= ny < height_map:
                        # 这里需要检查相邻单元是否为水域
                        # 简化处理：假设边缘区域有更高战略价值
                        if nx == 0 or nx == width-1 or ny == 0 or ny == height_map-1:
                            strategic_value += 0.2
        
        # 高地战略价值
        if height > 0.6:
            strategic_value += (height - 0.6) * 0.5
        
        # 中心位置价值
        center_x, center_y = width // 2, height_map // 2
        distance_from_center = math.sqrt((x - center_x)**2 + (y - center_y)**2)
        max_distance = math.sqrt(center_x**2 + center_y**2)
        centrality = 1.0 - (distance_from_center / max_distance)
        strategic_value += centrality * 0.3
        
        return min(1.0, strategic_value)
    
    def _analyze_terrain(self, terrain_cells: List[GameTerrainCell]) -> Dict[str, Any]:
        """分析地形特征"""
        total_cells = len(terrain_cells)
        if total_cells == 0:
            return {}
        
        # 统计地形类型分布
        terrain_counts = {}
        biome_counts = {}
        water_cells = 0
        mountain_cells = 0
        total_resources = {"food": 0, "production": 0, "gold": 0, "research": 0}
        
        for cell in terrain_cells:
            terrain_counts[cell.terrain_type] = terrain_counts.get(cell.terrain_type, 0) + 1
            biome_counts[cell.biome] = biome_counts.get(cell.biome, 0) + 1
            
            if cell.is_water:
                water_cells += 1
            if cell.is_mountain:
                mountain_cells += 1
            
            for resource, value in cell.resources.items():
                total_resources[resource] += value
        
        # 计算比例
        terrain_ratios = {k: v / total_cells for k, v in terrain_counts.items()}
        biome_ratios = {k: v / total_cells for k, v in biome_counts.items()}
        
        analysis = {
            "total_cells": total_cells,
            "terrain_distribution": terrain_ratios,
            "biome_distribution": biome_ratios,
            "water_ratio": water_cells / total_cells,
            "mountain_ratio": mountain_cells / total_cells,
            "land_ratio": (total_cells - water_cells) / total_cells,
            "average_resources": {k: v / total_cells for k, v in total_resources.items()},
            "terrain_quality": self._assess_terrain_quality(terrain_ratios, biome_ratios)
        }
        
        return analysis 
   
    def _assess_terrain_quality(self, terrain_ratios: Dict[str, float], 
                               biome_ratios: Dict[str, float]) -> str:
        """评估地形质量"""
        # 简单的地形质量评估
        land_ratio = terrain_ratios.get("plains", 0) + terrain_ratios.get("hills", 0)
        water_ratio = terrain_ratios.get("water", 0)
        
        if land_ratio > 0.7 and water_ratio < 0.2:
            return "continental"  # 大陆性
        elif water_ratio > 0.4:
            return "archipelago"  # 群岛
        elif terrain_ratios.get("mountains", 0) > 0.3:
            return "mountainous"  # 山地
        else:
            return "balanced"     # 平衡
    
    def _determine_starting_positions(self, terrain_cells: List[GameTerrainCell], 
                                    players: List[str], 
                                    terrain_analysis: Dict[str, Any]) -> List[StartingPosition]:
        """确定玩家起始位置"""
        self.logger.info(f"为 {len(players)} 个玩家确定起始位置")
        
        # 找到合适的起始位置候选点
        suitable_cells = [
            cell for cell in terrain_cells
            if not cell.is_water and not cell.is_mountain and 
            cell.resources.get("food", 0) > 0.2  # 需要基本的食物资源
        ]
        
        if len(suitable_cells) < len(players):
            self.logger.warning("合适的起始位置不足，降低标准")
            suitable_cells = [cell for cell in terrain_cells if not cell.is_water]
        
        # 使用平衡策略分配起始位置
        starting_positions = []
        used_positions = set()
        
        for i, player_id in enumerate(players):
            best_cell = self._find_best_starting_cell(
                suitable_cells, used_positions, len(players), i
            )
            
            if best_cell:
                # 计算起始领土
                territory_cells = self._calculate_starting_territory(
                    terrain_cells, best_cell, self.balance_config["starting_territory_size"]
                )
                
                # 计算起始资源
                starting_resources = self._calculate_starting_resources(
                    terrain_cells, territory_cells, best_cell
                )
                
                position = StartingPosition(
                    player_id=player_id,
                    center_cell=(best_cell.x, best_cell.y),
                    territory_cells=territory_cells,
                    starting_resources=starting_resources,
                    starting_population=self.balance_config["base_starting_population"]
                )
                
                starting_positions.append(position)
                used_positions.add((best_cell.x, best_cell.y))
                
                # 标记周围区域为已使用
                min_distance = self.balance_config["min_distance_between_players"]
                for cell in terrain_cells:
                    distance = math.sqrt((cell.x - best_cell.x)**2 + (cell.y - best_cell.y)**2)
                    if distance < min_distance:
                        used_positions.add((cell.x, cell.y))
        
        self.logger.info(f"成功确定了 {len(starting_positions)} 个起始位置")
        return starting_positions   
 
    def _find_best_starting_cell(self, suitable_cells: List[GameTerrainCell], 
                               used_positions: set, total_players: int, 
                               player_index: int) -> Optional[GameTerrainCell]:
        """找到最佳起始单元"""
        available_cells = [
            cell for cell in suitable_cells 
            if (cell.x, cell.y) not in used_positions
        ]
        
        if not available_cells:
            return None
        
        # 评分函数：综合考虑资源、战略价值和分布
        def score_cell(cell):
            resource_score = sum(cell.resources.values())
            strategic_score = cell.strategic_value
            
            # 分布均匀性加分
            distribution_score = 0
            if total_players > 1:
                # 简化的分布计算
                ideal_spacing = 1.0 / total_players
                current_spacing = (player_index + 1) / total_players
                distribution_score = 1.0 - abs(current_spacing - ideal_spacing)
            
            return resource_score * 0.4 + strategic_score * 0.4 + distribution_score * 0.2
        
        # 选择得分最高的单元
        best_cell = max(available_cells, key=score_cell)
        return best_cell
    
    def _calculate_starting_territory(self, terrain_cells: List[GameTerrainCell], 
                                    center_cell: GameTerrainCell, 
                                    territory_size: int) -> List[Tuple[int, int]]:
        """计算起始领土范围"""
        territory_cells = []
        
        # 创建坐标到单元的映射
        cell_map = {(cell.x, cell.y): cell for cell in terrain_cells}
        
        # 从中心开始扩展领土
        to_check = [(center_cell.x, center_cell.y)]
        checked = set()
        
        while to_check and len(territory_cells) < territory_size * territory_size:
            x, y = to_check.pop(0)
            if (x, y) in checked:
                continue
            
            checked.add((x, y))
            cell = cell_map.get((x, y))
            
            if cell and not cell.is_water:  # 只包含陆地
                territory_cells.append((x, y))
                
                # 添加相邻单元到检查列表
                for dx in [-1, 0, 1]:
                    for dy in [-1, 0, 1]:
                        if dx == 0 and dy == 0:
                            continue
                        nx, ny = x + dx, y + dy
                        if (nx, ny) not in checked and (nx, ny) in cell_map:
                            distance = math.sqrt((nx - center_cell.x)**2 + (ny - center_cell.y)**2)
                            if distance <= territory_size:
                                to_check.append((nx, ny))
        
        return territory_cells    
 
   def _calculate_starting_resources(self, terrain_cells: List[GameTerrainCell], 
                                    territory_cells: List[Tuple[int, int]], 
                                    center_cell: GameTerrainCell) -> Dict[str, float]:
        """计算起始资源"""
        # 基础资源
        starting_resources = self.balance_config["base_starting_resources"].copy()
        
        # 根据领土资源调整
        cell_map = {(cell.x, cell.y): cell for cell in terrain_cells}
        territory_resource_bonus = {"food": 0, "production": 0, "gold": 0, "research": 0}
        
        for x, y in territory_cells:
            cell = cell_map.get((x, y))
            if cell:
                for resource, value in cell.resources.items():
                    territory_resource_bonus[resource] += value * 10  # 放大系数
        
        # 应用资源加成
        for resource, bonus in territory_resource_bonus.items():
            starting_resources[resource] += bonus
        
        return starting_resources
    
    def _generate_initial_territories(self, terrain_cells: List[GameTerrainCell], 
                                    starting_positions: List[StartingPosition]) -> Dict[str, List[Dict[str, Any]]]:
        """生成初始领土"""
        initial_territories = {}
        cell_map = {(cell.x, cell.y): cell for cell in terrain_cells}
        
        for position in starting_positions:
            territories = []
            
            # 为每个领土单元创建Territory对象数据
            for i, (x, y) in enumerate(position.territory_cells):
                cell = cell_map.get((x, y))
                if cell:
                    territory_data = {
                        "territory_id": f"{position.player_id}_territory_{i}",
                        "name": f"{position.player_id}的领土{i+1}",
                        "owner": position.player_id,
                        "position": (x, y),
                        "terrain_type": cell.terrain_type,
                        "biome": cell.biome,
                        "population": position.starting_population // len(position.territory_cells),
                        "resources": cell.resources.copy(),
                        "development_level": 0.1,  # 初始发展水平
                        "is_capital": (x, y) == position.center_cell
                    }
                    territories.append(territory_data)
            
            initial_territories[position.player_id] = territories
        
        return initial_territories
    
    def _place_initial_cities(self, terrain_cells: List[GameTerrainCell], 
                            starting_positions: List[StartingPosition]) -> Dict[str, List[Dict[str, Any]]]:
        """放置初始城市"""
        initial_cities = {}
        cell_map = {(cell.x, cell.y): cell for cell in terrain_cells}
        
        for position in starting_positions:
            cities = []
            
            # 在起始位置放置首都
            center_cell = cell_map.get(position.center_cell)
            if center_cell:
                capital = {
                    "city_id": f"{position.player_id}_capital",
                    "name": f"{position.player_id}的首都",
                    "owner": position.player_id,
                    "position": position.center_cell,
                    "population": position.starting_population // 2,  # 一半人口在首都
                    "is_capital": True,
                    "development_level": 0.3,
                    "buildings": ["palace", "granary", "barracks"],
                    "terrain_bonuses": self._calculate_city_terrain_bonuses(center_cell)
                }
                cities.append(capital)
                position.capital_city = capital
            
            initial_cities[position.player_id] = cities
        
        return initial_cities  
  
    def _calculate_city_terrain_bonuses(self, cell: GameTerrainCell) -> Dict[str, float]:
        """计算城市地形加成"""
        bonuses = {}
        
        # 根据地形类型给予不同加成
        terrain_bonuses = {
            "plains": {"food": 1.2, "production": 1.0},
            "hills": {"production": 1.3, "gold": 1.1},
            "mountains": {"gold": 1.5, "production": 0.8},
            "forest": {"food": 1.1, "research": 1.2},
            "desert": {"gold": 1.2, "food": 0.7},
            "tundra": {"research": 1.1, "food": 0.8},
            "coast": {"food": 1.3, "gold": 1.1}
        }
        
        terrain_multipliers = terrain_bonuses.get(cell.terrain_type, {})
        
        for resource, multiplier in terrain_multipliers.items():
            if multiplier > 1.0:
                bonuses[resource] = multiplier - 1.0  # 转换为加成百分比
        
        return bonuses
    
    def _allocate_initial_resources(self, terrain_cells: List[GameTerrainCell], 
                                  starting_positions: List[StartingPosition], 
                                  terrain_analysis: Dict[str, Any]) -> Dict[str, Dict[str, float]]:
        """分配初始资源"""
        resource_allocation = {}
        
        for position in starting_positions:
            # 使用已计算的起始资源
            resource_allocation[position.player_id] = position.starting_resources.copy()
        
        return resource_allocation
    
    def _calculate_balance_adjustments(self, starting_positions: List[StartingPosition], 
                                     terrain_analysis: Dict[str, Any]) -> Dict[str, Dict[str, float]]:
        """计算游戏平衡调整"""
        balance_adjustments = {}
        
        # 计算每个玩家的起始优势/劣势
        for position in starting_positions:
            adjustments = {}
            
            # 计算资源总量
            total_resources = sum(position.starting_resources.values())
            
            # 与平均值比较
            avg_resources = sum(sum(pos.starting_resources.values()) for pos in starting_positions) / len(starting_positions)
            
            if total_resources < avg_resources * 0.9:
                # 资源不足，给予补偿
                adjustments["resource_bonus"] = 0.1
                adjustments["development_bonus"] = 0.05
            elif total_resources > avg_resources * 1.1:
                # 资源过多，给予惩罚
                adjustments["resource_penalty"] = 0.05
            
            balance_adjustments[position.player_id] = adjustments
        
        return balance_adjustments
    
    def _parse_realworld_terrain(self, terrain_data: Dict[str, Any]) -> List[GameTerrainCell]:
        """解析真实世界地形数据（简化实现）"""
        terrain_cells = []
        
        # 获取地理数据
        geo_data = terrain_data.get("geographic_data", {})
        cities = terrain_data.get("cities", [])
        resource_dist = terrain_data.get("resource_distribution", {})
        
        # 创建基于真实世界数据的虚拟地形网格
        grid_size = 32  # 32x32 网格表示真实世界地区
        
        for y in range(grid_size):
            for x in range(grid_size):
                # 基于真实世界数据生成地形特征
                cell_height = 0.4 + random.random() * 0.4  # 简化的高度
                cell_temp = 0.5 + random.random() * 0.3    # 简化的温度
                cell_precip = 0.4 + random.random() * 0.4  # 简化的降水
                
                # 确定地形类型
                terrain_type = "plains"  # 简化处理
                biome = "temperate_plains"
                
                # 基于真实世界资源分布计算资源
                resources = {
                    "food": resource_dist.get("agricultural_potential", 0.5),
                    "production": resource_dist.get("industrial_capacity", 0.4),
                    "gold": resource_dist.get("mineral_deposits", 0.3),
                    "research": 0.3
                }
                
                # 添加随机变化
                for resource in resources:
                    resources[resource] *= (0.8 + random.random() * 0.4)
                
                cell = GameTerrainCell(
                    x=x, y=y,
                    height=cell_height,
                    temperature=cell_temp,
                    precipitation=cell_precip,
                    terrain_type=terrain_type,
                    biome=biome,
                    resources=resources,
                    is_water=False,
                    is_mountain=False,
                    strategic_value=0.5
                )
                terrain_cells.append(cell)
        
        return terrain_cells