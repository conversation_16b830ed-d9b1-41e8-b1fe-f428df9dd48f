"""
真实世界地形生成器 - 基于真实世界数据生成游戏地形
整合地理、经济、人口等多维度数据
"""

import asyncio
import logging
import math
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

from .api_client import APIClient, WORLD_BANK_INDICATORS
from .data_cache import DataCache


@dataclass
class GeographicRegion:
    """地理区域数据模型"""
    name: str
    country_code: str
    coordinates: Tuple[float, float]  # (纬度, 经度)
    area_km2: float
    population: int
    elevation: float
    climate_zone: str
    terrain_type: str


@dataclass
class EconomicData:
    """经济数据模型"""
    gdp_per_capita: float
    poverty_rate: float
    unemployment_rate: float
    inflation_rate: float
    urban_population_percent: float
    literacy_rate: float
    life_expectancy: float


@dataclass
class ResourceDistribution:
    """资源分布数据模型"""
    oil_reserves: float
    mineral_deposits: float
    agricultural_potential: float
    water_resources: float
    renewable_energy_potential: float
    industrial_capacity: float


@dataclass
class CityData:
    """城市数据模型"""
    name: str
    population: int
    coordinates: Tuple[float, float]
    economic_importance: float
    infrastructure_level: float
    strategic_value: float


@dataclass
class RealWorldTerrainData:
    """真实世界地形数据"""
    region: GeographicRegion
    economic_data: EconomicData
    resource_distribution: ResourceDistribution
    cities: List[CityData]
    strategic_locations: List[Dict[str, Any]]
    game_balance_factors: Dict[str, float]


class RealWorldDataError(Exception):
    """真实世界数据错误"""
    pass


class RealWorldGenerator:
    """真实世界地形生成器"""
    
    def __init__(self, api_client: APIClient = None, cache: DataCache = None):
        """
        初始化真实世界生成器
        
        Args:
            api_client: API客户端实例
            cache: 数据缓存实例
        """
        self.api_client = api_client
        self.cache = cache
        self.logger = logging.getLogger(__name__)
        
        # 支持的地区配置
        self.supported_regions = {
            "california": {
                "name": "加利福尼亚州",
                "country": "US",
                "country_code": "US",
                "state": "CA",
                "search_terms": ["California", "Los Angeles", "San Francisco"],
                "center_coords": (36.7783, -119.4179),
                "area_km2": 423970,
                "climate_zones": ["Mediterranean", "Desert", "Alpine"],
                "major_cities": ["Los Angeles", "San Francisco", "San Diego", "Sacramento"]
            },
            "guangzhou": {
                "name": "广州市",
                "country": "China",
                "country_code": "CN",
                "province": "广东",
                "search_terms": ["Guangzhou", "Canton"],
                "center_coords": (23.1291, 113.2644),
                "area_km2": 7434,
                "climate_zones": ["Subtropical"],
                "major_cities": ["Guangzhou", "Shenzhen", "Dongguan", "Foshan"]
            },
            "tokyo": {
                "name": "东京都",
                "country": "Japan",
                "country_code": "JP",
                "prefecture": "東京都",
                "search_terms": ["Tokyo", "東京"],
                "center_coords": (35.6762, 139.6503),
                "area_km2": 2194,
                "climate_zones": ["Humid subtropical"],
                "major_cities": ["Tokyo", "Yokohama", "Kawasaki", "Saitama"]
            },
            "london": {
                "name": "伦敦",
                "country": "United Kingdom",
                "country_code": "GB",
                "region": "England",
                "search_terms": ["London", "Greater London"],
                "center_coords": (51.5074, -0.1278),
                "area_km2": 1572,
                "climate_zones": ["Oceanic"],
                "major_cities": ["London", "Birmingham", "Manchester", "Liverpool"]
            },
            "sydney": {
                "name": "悉尼",
                "country": "Australia",
                "country_code": "AU",
                "state": "NSW",
                "search_terms": ["Sydney", "New South Wales"],
                "center_coords": (-33.8688, 151.2093),
                "area_km2": 12368,
                "climate_zones": ["Humid subtropical"],
                "major_cities": ["Sydney", "Melbourne", "Brisbane", "Perth"]
            }
        }
        
        # 游戏平衡参数
        self.balance_factors = {
            "population_to_manpower": 0.02,  # 人口转换为兵力的比例
            "gdp_to_resources": 0.001,       # GDP转换为游戏资源的比例
            "area_to_territory": 0.1,        # 面积转换为游戏领土的比例
            "urban_bonus": 1.5,              # 城市化程度加成
            "tech_bonus": 1.2,               # 科技水平加成
            "strategic_multiplier": 2.0      # 战略位置重要性倍数
        }
    
    async def generate_region_data(self, region_name: str) -> RealWorldTerrainData:
        """
        生成指定地区的完整数据
        
        Args:
            region_name: 地区名称（支持的地区之一）
        
        Returns:
            完整的真实世界地形数据
        """
        if region_name not in self.supported_regions:
            raise RealWorldDataError(f"不支持的地区: {region_name}")
        
        region_config = self.supported_regions[region_name]
        self.logger.info(f"开始生成 {region_config['name']} 的地形数据")
        
        try:
            # 并行获取各种数据
            tasks = [
                self._fetch_geographic_data(region_config),
                self._fetch_economic_data(region_config),
                self._fetch_city_data(region_config),
                self._fetch_strategic_data(region_config)
            ]
            
            geo_data, economic_data, cities_data, strategic_data = await asyncio.gather(*tasks)
            
            # 生成资源分布
            resource_distribution = self._calculate_resource_distribution(
                geo_data, economic_data, region_config
            )
            
            # 计算游戏平衡因子
            balance_factors = self._calculate_game_balance(
                geo_data, economic_data, cities_data, region_config
            )
            
            # 组装完整数据
            terrain_data = RealWorldTerrainData(
                region=geo_data,
                economic_data=economic_data,
                resource_distribution=resource_distribution,
                cities=cities_data,
                strategic_locations=strategic_data,
                game_balance_factors=balance_factors
            )
            
            self.logger.info(f"成功生成 {region_config['name']} 的地形数据")
            return terrain_data
        
        except Exception as e:
            self.logger.error(f"生成地区数据失败: {region_name} - {str(e)}")
            raise RealWorldDataError(f"生成地区数据失败: {str(e)}")
    
    async def _fetch_geographic_data(self, region_config: Dict[str, Any]) -> GeographicRegion:
        """获取地理数据"""
        cache_key = f"geo_data_{region_config['name']}"
        
        # 尝试从缓存获取
        if self.cache:
            cached_data = await self.cache.get(cache_key)
            if cached_data:
                return GeographicRegion(**cached_data)
        
        # 从API获取
        if not self.api_client:
            # 使用默认数据
            return self._get_default_geographic_data(region_config)
        
        try:
            # 获取地理特征
            search_term = region_config['search_terms'][0]
            geo_features = await self.api_client.fetch_geographic_features(
                search_term, region_config['country_code']
            )
            
            # 获取国家数据
            country_data = await self.api_client.fetch_country_data(region_config['country_code'])
            
            # 处理地理数据
            coordinates = region_config['center_coords']
            elevation = 0
            terrain_type = "mixed"
            climate_zone = region_config.get('climate_zones', ['temperate'])[0]
            
            if geo_features['results']:
                result = geo_features['results'][0]
                if 'lat' in result and 'lon' in result:
                    coordinates = (float(result['lat']), float(result['lon']))
                
                # 从扩展标签获取海拔
                if 'extratags' in result and 'ele' in result['extratags']:
                    try:
                        elevation = float(result['extratags']['ele'])
                    except (ValueError, TypeError):
                        pass
            
            # 确定地形类型
            terrain_type = self._determine_terrain_type(region_config, elevation)
            
            geo_data = GeographicRegion(
                name=region_config['name'],
                country_code=region_config['country_code'],
                coordinates=coordinates,
                area_km2=region_config['area_km2'],
                population=country_data.get('population', 0),
                elevation=elevation,
                climate_zone=climate_zone,
                terrain_type=terrain_type
            )
            
            # 缓存数据
            if self.cache:
                await self.cache.set(cache_key, asdict(geo_data), ttl=86400)  # 24小时
            
            return geo_data
        
        except Exception as e:
            self.logger.warning(f"获取地理数据失败，使用默认数据: {str(e)}")
            return self._get_default_geographic_data(region_config)
    
    async def _fetch_economic_data(self, region_config: Dict[str, Any]) -> EconomicData:
        """获取经济数据"""
        cache_key = f"economic_data_{region_config['country_code']}"
        
        # 尝试从缓存获取
        if self.cache:
            cached_data = await self.cache.get(cache_key)
            if cached_data:
                return EconomicData(**cached_data)
        
        # 从API获取
        if not self.api_client:
            return self._get_default_economic_data(region_config)
        
        try:
            # 获取多个经济指标
            indicators = [
                "NY.GDP.PCAP.CD",    # GDP per capita
                "SI.POV.NAHC",       # Poverty rate
                "SL.UEM.TOTL.ZS",    # Unemployment rate
                "FP.CPI.TOTL.ZG",    # Inflation rate
                "SP.URB.TOTL.IN.ZS", # Urban population
                "SE.ADT.LITR.ZS",    # Literacy rate
                "SP.DYN.LE00.IN"     # Life expectancy
            ]
            
            economic_indicators = await self.api_client.fetch_multiple_indicators(
                indicators, region_config['country_code']
            )
            
            # 解析经济数据
            def get_latest_value(indicator_data, default=0):
                if 'data' in indicator_data and indicator_data['data']:
                    for entry in indicator_data['data']:
                        if entry and 'value' in entry and entry['value'] is not None:
                            return float(entry['value'])
                return default
            
            economic_data = EconomicData(
                gdp_per_capita=get_latest_value(
                    economic_indicators.get("NY.GDP.PCAP.CD", {}), 25000
                ),
                poverty_rate=get_latest_value(
                    economic_indicators.get("SI.POV.NAHC", {}), 10.0
                ),
                unemployment_rate=get_latest_value(
                    economic_indicators.get("SL.UEM.TOTL.ZS", {}), 5.0
                ),
                inflation_rate=get_latest_value(
                    economic_indicators.get("FP.CPI.TOTL.ZG", {}), 2.0
                ),
                urban_population_percent=get_latest_value(
                    economic_indicators.get("SP.URB.TOTL.IN.ZS", {}), 70.0
                ),
                literacy_rate=get_latest_value(
                    economic_indicators.get("SE.ADT.LITR.ZS", {}), 95.0
                ),
                life_expectancy=get_latest_value(
                    economic_indicators.get("SP.DYN.LE00.IN", {}), 75.0
                )
            )
            
            # 缓存数据
            if self.cache:
                await self.cache.set(cache_key, asdict(economic_data), ttl=86400)
            
            return economic_data
        
        except Exception as e:
            self.logger.warning(f"获取经济数据失败，使用默认数据: {str(e)}")
            return self._get_default_economic_data(region_config)
    
    async def _fetch_city_data(self, region_config: Dict[str, Any]) -> List[CityData]:
        """获取城市数据"""
        cache_key = f"cities_data_{region_config['name']}"
        
        # 尝试从缓存获取
        if self.cache:
            cached_data = await self.cache.get(cache_key)
            if cached_data:
                return [CityData(**city) for city in cached_data]
        
        cities = []
        
        if self.api_client:
            try:
                # 获取主要城市数据
                for city_name in region_config.get('major_cities', []):
                    city_data = await self.api_client.fetch_city_data(
                        city_name, region_config['country_code']
                    )
                    
                    if city_data['coordinates']:
                        # 计算城市重要性指标
                        population = city_data.get('population', 100000)
                        economic_importance = self._calculate_economic_importance(
                            population, region_config
                        )
                        infrastructure_level = self._calculate_infrastructure_level(
                            city_name, region_config
                        )
                        strategic_value = self._calculate_strategic_value(
                            city_data['coordinates'], region_config
                        )
                        
                        city = CityData(
                            name=city_data['name'],
                            population=population,
                            coordinates=city_data['coordinates'],
                            economic_importance=economic_importance,
                            infrastructure_level=infrastructure_level,
                            strategic_value=strategic_value
                        )
                        cities.append(city)
                
                # 缓存数据
                if self.cache and cities:
                    cities_dict = [asdict(city) for city in cities]
                    await self.cache.set(cache_key, cities_dict, ttl=86400)
            
            except Exception as e:
                self.logger.warning(f"获取城市数据失败: {str(e)}")
        
        # 如果没有获取到城市数据，使用默认数据
        if not cities:
            cities = self._get_default_city_data(region_config)
        
        return cities
    
    async def _fetch_strategic_data(self, region_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取战略位置数据"""
        strategic_locations = []
        
        # 基于地理位置确定战略要点
        coords = region_config['center_coords']
        
        # 港口城市（靠近海岸）
        if self._is_coastal_region(region_config):
            strategic_locations.append({
                "type": "port",
                "name": f"{region_config['name']}主要港口",
                "coordinates": coords,
                "importance": 0.8,
                "military_value": 0.7,
                "economic_value": 0.9
            })
        
        # 山脉要塞（高海拔地区）
        if region_config.get('climate_zones', []):
            if any('Alpine' in zone or 'Mountain' in zone for zone in region_config['climate_zones']):
                strategic_locations.append({
                    "type": "mountain_pass",
                    "name": f"{region_config['name']}山口要塞",
                    "coordinates": (coords[0] + 0.1, coords[1] + 0.1),
                    "importance": 0.6,
                    "military_value": 0.8,
                    "economic_value": 0.4
                })
        
        # 交通枢纽
        strategic_locations.append({
            "type": "transport_hub",
            "name": f"{region_config['name']}交通枢纽",
            "coordinates": coords,
            "importance": 0.7,
            "military_value": 0.6,
            "economic_value": 0.8
        })
        
        return strategic_locations
    
    def _calculate_resource_distribution(self, geo_data: GeographicRegion, 
                                       economic_data: EconomicData,
                                       region_config: Dict[str, Any]) -> ResourceDistribution:
        """计算资源分布"""
        # 基于地理和经济数据计算资源分布
        
        # 石油储量（基于地理位置和经济发展水平）
        oil_base = 0.3
        if region_config['country_code'] in ['US', 'CA', 'AU']:  # 石油丰富国家
            oil_base = 0.7
        elif region_config['country_code'] in ['CN', 'JP', 'GB']:  # 石油进口国
            oil_base = 0.2
        
        oil_reserves = oil_base * (1 + economic_data.gdp_per_capita / 100000)
        
        # 矿物储量（基于地形和面积）
        mineral_base = 0.4
        if geo_data.terrain_type in ['mountainous', 'mixed']:
            mineral_base = 0.6
        mineral_deposits = mineral_base * math.log10(geo_data.area_km2 + 1) / 5
        
        # 农业潜力（基于气候和面积）
        agricultural_base = 0.5
        if geo_data.climate_zone in ['Mediterranean', 'Subtropical', 'Temperate']:
            agricultural_base = 0.7
        elif geo_data.climate_zone in ['Desert', 'Arctic']:
            agricultural_base = 0.2
        
        agricultural_potential = agricultural_base * (
            1 - economic_data.urban_population_percent / 100
        )
        
        # 水资源（基于气候和地理位置）
        water_base = 0.6
        if self._is_coastal_region(region_config):
            water_base = 0.8
        if geo_data.climate_zone in ['Desert']:
            water_base = 0.3
        
        water_resources = water_base
        
        # 可再生能源潜力（基于技术水平和政策）
        renewable_base = 0.4
        if economic_data.gdp_per_capita > 30000:  # 发达地区
            renewable_base = 0.7
        
        renewable_energy_potential = renewable_base * (
            economic_data.literacy_rate / 100
        )
        
        # 工业产能（基于城市化和经济发展）
        industrial_capacity = (
            economic_data.urban_population_percent / 100 * 0.6 +
            min(economic_data.gdp_per_capita / 50000, 1.0) * 0.4
        )
        
        return ResourceDistribution(
            oil_reserves=min(oil_reserves, 1.0),
            mineral_deposits=min(mineral_deposits, 1.0),
            agricultural_potential=min(agricultural_potential, 1.0),
            water_resources=min(water_resources, 1.0),
            renewable_energy_potential=min(renewable_energy_potential, 1.0),
            industrial_capacity=min(industrial_capacity, 1.0)
        )
    
    def _calculate_game_balance(self, geo_data: GeographicRegion,
                              economic_data: EconomicData,
                              cities_data: List[CityData],
                              region_config: Dict[str, Any]) -> Dict[str, float]:
        """计算游戏平衡因子"""
        total_population = sum(city.population for city in cities_data)
        if total_population == 0:
            total_population = geo_data.population
        
        # 基础军事力量
        base_military_power = (
            total_population * self.balance_factors['population_to_manpower']
        )
        
        # 经济实力
        economic_power = (
            economic_data.gdp_per_capita * total_population / 1000000 *
            self.balance_factors['gdp_to_resources']
        )
        
        # 领土控制力
        territory_control = (
            geo_data.area_km2 * self.balance_factors['area_to_territory']
        )
        
        # 科技水平
        tech_level = (
            economic_data.literacy_rate / 100 * 0.4 +
            min(economic_data.gdp_per_capita / 50000, 1.0) * 0.6
        ) * self.balance_factors['tech_bonus']
        
        # 城市化加成
        urban_bonus = (
            economic_data.urban_population_percent / 100 *
            self.balance_factors['urban_bonus']
        )
        
        # 战略位置重要性
        strategic_importance = len(cities_data) / 10 * self.balance_factors['strategic_multiplier']
        
        return {
            'military_power': min(base_military_power, 100.0),
            'economic_power': min(economic_power, 100.0),
            'territory_control': min(territory_control, 100.0),
            'technology_level': min(tech_level, 1.0),
            'urban_development': min(urban_bonus, 2.0),
            'strategic_value': min(strategic_importance, 5.0),
            'overall_strength': min(
                (base_military_power + economic_power + territory_control) / 3, 100.0
            )
        }
    
    def _get_default_geographic_data(self, region_config: Dict[str, Any]) -> GeographicRegion:
        """获取默认地理数据"""
        return GeographicRegion(
            name=region_config['name'],
            country_code=region_config['country_code'],
            coordinates=region_config['center_coords'],
            area_km2=region_config['area_km2'],
            population=1000000,  # 默认人口
            elevation=100.0,     # 默认海拔
            climate_zone=region_config.get('climate_zones', ['temperate'])[0],
            terrain_type='mixed'
        )
    
    def _get_default_economic_data(self, region_config: Dict[str, Any]) -> EconomicData:
        """获取默认经济数据"""
        # 根据国家代码设置不同的默认值
        defaults = {
            'US': {'gdp': 60000, 'poverty': 12, 'unemployment': 4, 'urban': 82},
            'CN': {'gdp': 12000, 'poverty': 2, 'unemployment': 4, 'urban': 64},
            'JP': {'gdp': 40000, 'poverty': 16, 'unemployment': 3, 'urban': 92},
            'GB': {'gdp': 45000, 'poverty': 12, 'unemployment': 4, 'urban': 84},
            'AU': {'gdp': 55000, 'poverty': 13, 'unemployment': 5, 'urban': 86}
        }
        
        country_defaults = defaults.get(region_config['country_code'], 
                                      {'gdp': 25000, 'poverty': 10, 'unemployment': 5, 'urban': 70})
        
        return EconomicData(
            gdp_per_capita=country_defaults['gdp'],
            poverty_rate=country_defaults['poverty'],
            unemployment_rate=country_defaults['unemployment'],
            inflation_rate=2.0,
            urban_population_percent=country_defaults['urban'],
            literacy_rate=95.0,
            life_expectancy=78.0
        )
    
    def _get_default_city_data(self, region_config: Dict[str, Any]) -> List[CityData]:
        """获取默认城市数据"""
        cities = []
        coords = region_config['center_coords']
        
        for i, city_name in enumerate(region_config.get('major_cities', [])[:3]):
            # 为每个城市生成略微不同的坐标
            city_coords = (
                coords[0] + (i - 1) * 0.1,
                coords[1] + (i - 1) * 0.1
            )
            
            # 根据城市在列表中的位置确定规模
            population = 1000000 * (4 - i) if i < 3 else 500000
            
            city = CityData(
                name=city_name,
                population=population,
                coordinates=city_coords,
                economic_importance=0.8 - i * 0.1,
                infrastructure_level=0.7 - i * 0.05,
                strategic_value=0.6 - i * 0.1
            )
            cities.append(city)
        
        return cities
    
    def _determine_terrain_type(self, region_config: Dict[str, Any], elevation: float) -> str:
        """确定地形类型"""
        if elevation > 1000:
            return 'mountainous'
        elif elevation < 50 and self._is_coastal_region(region_config):
            return 'coastal'
        elif any('Desert' in zone for zone in region_config.get('climate_zones', [])):
            return 'desert'
        else:
            return 'mixed'
    
    def _is_coastal_region(self, region_config: Dict[str, Any]) -> bool:
        """判断是否为沿海地区"""
        coastal_regions = ['california', 'tokyo', 'london', 'sydney']
        return region_config.get('name', '').lower() in [r.lower() for r in coastal_regions]
    
    def _calculate_economic_importance(self, population: int, region_config: Dict[str, Any]) -> float:
        """计算经济重要性"""
        # 基于人口规模和地区特征
        base_importance = min(population / 1000000, 10) / 10  # 标准化到0-1
        
        # 首都或主要城市加成
        if region_config.get('major_cities') and len(region_config['major_cities']) > 0:
            base_importance *= 1.2
        
        return min(base_importance, 1.0)
    
    def _calculate_infrastructure_level(self, city_name: str, region_config: Dict[str, Any]) -> float:
        """计算基础设施水平"""
        # 基于国家发展水平
        developed_countries = ['US', 'JP', 'GB', 'AU']
        if region_config['country_code'] in developed_countries:
            return 0.8
        elif region_config['country_code'] == 'CN':
            return 0.7
        else:
            return 0.6
    
    def _calculate_strategic_value(self, coordinates: Tuple[float, float], 
                                 region_config: Dict[str, Any]) -> float:
        """计算战略价值"""
        # 基于地理位置的战略重要性
        lat, lon = coordinates
        
        # 沿海城市战略价值更高
        if self._is_coastal_region(region_config):
            return 0.8
        
        # 内陆交通枢纽
        return 0.6
    
    def get_supported_regions(self) -> List[str]:
        """获取支持的地区列表"""
        return list(self.supported_regions.keys())
    
    def get_region_info(self, region_name: str) -> Optional[Dict[str, Any]]:
        """获取地区基本信息"""
        return self.supported_regions.get(region_name)
    
    async def validate_region_data(self, terrain_data: RealWorldTerrainData) -> Dict[str, Any]:
        """验证地区数据的完整性和合理性"""
        validation_results = {
            'is_valid': True,
            'warnings': [],
            'errors': []
        }
        
        # 检查基本数据完整性
        if not terrain_data.region.name:
            validation_results['errors'].append("地区名称不能为空")
        
        if terrain_data.region.population <= 0:
            validation_results['warnings'].append("人口数据异常")
        
        if terrain_data.economic_data.gdp_per_capita <= 0:
            validation_results['warnings'].append("GDP数据异常")
        
        # 检查资源分布合理性
        resources = terrain_data.resource_distribution
        total_resources = (
            resources.oil_reserves + resources.mineral_deposits +
            resources.agricultural_potential + resources.water_resources +
            resources.renewable_energy_potential + resources.industrial_capacity
        )
        
        if total_resources < 1.0:
            validation_results['warnings'].append("资源总量偏低，可能影响游戏平衡")
        elif total_resources > 5.0:
            validation_results['warnings'].append("资源总量过高，可能影响游戏平衡")
        
        # 检查城市数据
        if not terrain_data.cities:
            validation_results['warnings'].append("没有城市数据")
        
        # 检查游戏平衡
        balance = terrain_data.game_balance_factors
        if balance.get('overall_strength', 0) < 10:
            validation_results['warnings'].append("整体实力偏低")
        elif balance.get('overall_strength', 0) > 90:
            validation_results['warnings'].append("整体实力过高")
        
        validation_results['is_valid'] = len(validation_results['errors']) == 0
        
        return validation_results