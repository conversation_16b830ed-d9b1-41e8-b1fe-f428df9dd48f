#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
房间ID生成器模块
Room ID Generator Module
"""

import uuid
import time
import random
import string
import hashlib
from typing import Set, Optional
from enum import Enum


class RoomIdType(Enum):
    """房间ID类型枚举"""
    NUMERIC = "numeric"           # 纯数字ID: 123456
    ALPHANUMERIC = "alphanumeric" # 字母数字混合: ABC123
    UUID = "uuid"                 # UUID格式: 550e8400-e29b-41d4-a716-************
    READABLE = "readable"         # 可读格式: GAME-2025-001
    SHORT = "short"               # 短ID: G1A2B3


class RoomIdGenerator:
    """房间ID生成器"""
    
    def __init__(self):
        """初始化ID生成器"""
        self.used_ids: Set[str] = set()
        self.numeric_counter = 100000  # 6位数字起始
        self.readable_counter = 1
        
        # 导入日志
        from shared.enhanced_logger import get_server_logger
        self.logger = get_server_logger("RoomIdGenerator")
        
        self.logger.info("房间ID生成器初始化完成 / Room ID generator initialized")
    
    def generate_id(self, id_type: RoomIdType = RoomIdType.ALPHANUMERIC, 
                   max_attempts: int = 100) -> Optional[str]:
        """
        生成唯一的房间ID
        
        Args:
            id_type: ID类型
            max_attempts: 最大尝试次数
            
        Returns:
            生成的唯一ID，如果失败返回None
        """
        for attempt in range(max_attempts):
            if id_type == RoomIdType.NUMERIC:
                room_id = self._generate_numeric_id()
            elif id_type == RoomIdType.ALPHANUMERIC:
                room_id = self._generate_alphanumeric_id()
            elif id_type == RoomIdType.UUID:
                room_id = self._generate_uuid_id()
            elif id_type == RoomIdType.READABLE:
                room_id = self._generate_readable_id()
            elif id_type == RoomIdType.SHORT:
                room_id = self._generate_short_id()
            else:
                self.logger.error(f"不支持的ID类型: {id_type}")
                return None
            
            # 检查ID是否已存在
            if not self.is_id_exists(room_id):
                self.register_id(room_id)
                self.logger.debug(f"生成房间ID: {room_id} (类型: {id_type.value})")
                return room_id
            
            self.logger.warning(f"ID冲突，重新生成 (尝试 {attempt + 1}/{max_attempts}): {room_id}")
        
        self.logger.error(f"生成唯一ID失败，已尝试 {max_attempts} 次")
        return None
    
    def _generate_numeric_id(self) -> str:
        """生成纯数字ID"""
        self.numeric_counter += 1
        return str(self.numeric_counter)
    
    def _generate_alphanumeric_id(self) -> str:
        """生成字母数字混合ID"""
        # 格式: ROOM + 时间戳后4位 + 随机字符
        timestamp_suffix = str(int(time.time()))[-4:]
        random_chars = ''.join(random.choices(string.ascii_uppercase + string.digits, k=3))
        return f"ROOM{timestamp_suffix}{random_chars}"
    
    def _generate_uuid_id(self) -> str:
        """生成UUID格式ID"""
        return str(uuid.uuid4())
    
    def _generate_readable_id(self) -> str:
        """生成可读格式ID"""
        # 格式: GAME-YYYY-NNN
        year = time.strftime("%Y")
        counter_str = f"{self.readable_counter:03d}"
        self.readable_counter += 1
        return f"GAME-{year}-{counter_str}"
    
    def _generate_short_id(self) -> str:
        """生成短ID"""
        # 格式: 1个字母 + 3个数字 + 2个字母
        letter1 = random.choice(string.ascii_uppercase)
        numbers = ''.join(random.choices(string.digits, k=3))
        letters2 = ''.join(random.choices(string.ascii_uppercase, k=2))
        return f"{letter1}{numbers}{letters2}"
    
    def is_id_exists(self, room_id: str) -> bool:
        """检查ID是否已存在"""
        return room_id in self.used_ids
    
    def register_id(self, room_id: str):
        """注册ID为已使用"""
        self.used_ids.add(room_id)
        self.logger.debug(f"注册房间ID: {room_id}")
    
    def release_id(self, room_id: str):
        """释放ID（房间关闭时调用）"""
        if room_id in self.used_ids:
            self.used_ids.remove(room_id)
            self.logger.debug(f"释放房间ID: {room_id}")
    
    def get_used_ids_count(self) -> int:
        """获取已使用的ID数量"""
        return len(self.used_ids)
    
    def validate_id_format(self, room_id: str, id_type: RoomIdType) -> bool:
        """验证ID格式是否正确"""
        if not room_id:
            return False
        
        try:
            if id_type == RoomIdType.NUMERIC:
                return room_id.isdigit() and len(room_id) >= 6
            
            elif id_type == RoomIdType.ALPHANUMERIC:
                return (room_id.startswith("ROOM") and 
                       len(room_id) == 11 and 
                       room_id[4:].isalnum())
            
            elif id_type == RoomIdType.UUID:
                uuid.UUID(room_id)  # 会抛出异常如果格式不正确
                return True
            
            elif id_type == RoomIdType.READABLE:
                parts = room_id.split("-")
                return (len(parts) == 3 and 
                       parts[0] == "GAME" and 
                       parts[1].isdigit() and len(parts[1]) == 4 and
                       parts[2].isdigit() and len(parts[2]) == 3)
            
            elif id_type == RoomIdType.SHORT:
                return (len(room_id) == 6 and
                       room_id[0].isupper() and room_id[0].isalpha() and
                       room_id[1:4].isdigit() and
                       room_id[4:6].isupper() and room_id[4:6].isalpha())
            
            return False
            
        except Exception:
            return False
    
    def generate_custom_id(self, prefix: str = "", suffix: str = "", 
                          length: int = 8) -> Optional[str]:
        """
        生成自定义格式的ID
        
        Args:
            prefix: ID前缀
            suffix: ID后缀
            length: 中间随机部分的长度
            
        Returns:
            生成的自定义ID
        """
        max_attempts = 50
        
        for attempt in range(max_attempts):
            # 生成中间的随机部分
            middle_chars = ''.join(random.choices(
                string.ascii_uppercase + string.digits, k=length
            ))
            
            room_id = f"{prefix}{middle_chars}{suffix}"
            
            if not self.is_id_exists(room_id):
                self.register_id(room_id)
                self.logger.debug(f"生成自定义房间ID: {room_id}")
                return room_id
        
        self.logger.error(f"生成自定义ID失败，已尝试 {max_attempts} 次")
        return None
    
    def get_id_statistics(self) -> dict:
        """获取ID生成统计信息"""
        return {
            "total_used_ids": len(self.used_ids),
            "numeric_counter": self.numeric_counter,
            "readable_counter": self.readable_counter,
            "available_id_types": [t.value for t in RoomIdType]
        }
    
    def clear_unused_ids(self, active_room_ids: Set[str]):
        """清理不再使用的ID"""
        unused_ids = self.used_ids - active_room_ids
        for room_id in unused_ids:
            self.release_id(room_id)
        
        if unused_ids:
            self.logger.info(f"清理了 {len(unused_ids)} 个未使用的房间ID")


# 全局ID生成器实例
_room_id_generator = None


def get_room_id_generator() -> RoomIdGenerator:
    """获取全局房间ID生成器实例"""
    global _room_id_generator
    if _room_id_generator is None:
        _room_id_generator = RoomIdGenerator()
    return _room_id_generator
