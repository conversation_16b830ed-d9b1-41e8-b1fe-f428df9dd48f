# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['client_main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('client/languages/*.json', 'client/languages'),
        ('config/*.ini', 'config'),
        ('shared', 'shared'),
        ('client', 'client'),
        ('utils', 'utils')
    ],
    hiddenimports=[
        'shared.enhanced_logger',
        'shared.language_manager',
        'shared.security_manager',
        'client.core.new_client_core',
        'client.interface.interface_manager',
        'client.interface.menu_interface',
        'client.interface.game_interface',
        'client.interface.settings_interface',
        'client.network.network_client',
        'utils.process_lock',
        'utils.signal_handler',
        'json',
        'socket',
        'threading',
        'hashlib',
        'hmac',
        'secrets',
        'configparser',
        'pathlib'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='WorldWarClient',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    cofile=None,
    icon=None,
)
