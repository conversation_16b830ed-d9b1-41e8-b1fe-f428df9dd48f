#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强日志记录器模块
Enhanced Logger Module

重构版本，使用模块化设计
Refactored version with modular design
"""

import logging
import sys
from typing import Dict
import threading

# 导入新的模块化组件
try:
    from shared.log_config import LogConfig
    from shared.log_archiver import LogArchiver
except ImportError:
    # 如果新模块不可用，使用简化版本
    LogConfig = None
    LogArchiver = None

class EnhancedLogger:
    """增强的日志记录器，支持分离的服务器和客户端日志"""

    _loggers: Dict[str, logging.Logger] = {}
    _lock = threading.Lock()
    _archiver = LogArchiver() if LogArchiver else None

    @classmethod
    def archive_latest_log(cls, log_type: str = "server") -> bool:
        """
        归档latest.log文件

        Args:
            log_type: 日志类型

        Returns:
            是否成功归档
        """
        if cls._archiver:
            return cls._archiver.archive_latest_log(log_type)
        else:
            # 简化版本的归档逻辑
            return cls._simple_archive(log_type)

    @classmethod
    def _simple_archive(cls, log_type: str) -> bool:
        """简化版本的归档逻辑"""
        try:
            from pathlib import Path
            from datetime import datetime
            import shutil

            log_dir = Path("logs")
            if log_type == "server":
                log_dir = log_dir / "server"
            elif log_type == "client":
                log_dir = log_dir / "client"

            latest_log = log_dir / "latest.log"

            if latest_log.exists():
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                archive_name = f"game_{timestamp}.log"
                archive_path = log_dir / archive_name
                shutil.move(str(latest_log), str(archive_path))
                print(f"日志已归档: {archive_path}")
                return True
        except Exception as e:
            print(f"日志归档失败: {e}")
        return False
    
    @classmethod
    def get_logger(cls, name: str,
                   log_type: str = "server",
                   level: str = "INFO",
                   log_to_file: bool = True,
                   log_to_console: bool = True) -> logging.Logger:
        """
        获取或创建日志记录器

        Args:
            name: 日志记录器名称
            log_type: 日志类型 ("server", "client")
            level: 日志级别
            log_to_file: 是否记录到文件
            log_to_console: 是否输出到控制台

        Returns:
            配置好的日志记录器
        """
        with cls._lock:
            logger_key = f"{log_type}_{name}"

            if logger_key in cls._loggers:
                return cls._loggers[logger_key]

            # 使用LogConfig创建日志记录器（如果可用）
            if LogConfig:
                logger = LogConfig.create_logger(
                    name=logger_key,
                    log_type=log_type,
                    level=level,
                    log_to_file=log_to_file,
                    log_to_console=log_to_console
                )
            else:
                # 使用简化版本创建日志记录器
                logger = cls._create_simple_logger(
                    name, log_type, level, log_to_file, log_to_console
                )

            cls._loggers[logger_key] = logger
            return logger
    
    @classmethod
    def _create_simple_logger(cls, name: str, log_type: str, level: str,
                             log_to_file: bool, log_to_console: bool) -> logging.Logger:
        """创建简化版本的日志记录器"""
        from pathlib import Path

        # 创建日志目录结构
        base_log_dir = Path("logs")
        base_log_dir.mkdir(exist_ok=True)

        if log_type in ["server", "client"]:
            log_dir = base_log_dir / log_type
            log_dir.mkdir(exist_ok=True)
        else:
            log_dir = base_log_dir

        # 创建logger
        logger = logging.getLogger(f"{log_type}_{name}")
        logger.setLevel(getattr(logging, level.upper()))

        # 清除现有的处理器
        logger.handlers.clear()

        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # 控制台处理器
        if log_to_console:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(getattr(logging, level.upper()))
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)

        # 文件处理器
        if log_to_file:
            latest_log = log_dir / "latest.log"

            # 如果文件太大，先归档
            if latest_log.exists() and latest_log.stat().st_size > 10 * 1024 * 1024:  # 10MB
                cls._simple_archive(log_type)

            file_handler = logging.FileHandler(latest_log, encoding='utf-8')
            file_handler.setLevel(getattr(logging, level.upper()))
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)

        # 防止重复日志
        logger.propagate = False

        return logger

    @classmethod
    def cleanup_old_logs(cls, log_type: str = "server", keep_count: int = 5) -> int:
        """
        清理旧的日志文件

        Args:
            log_type: 日志类型
            keep_count: 保留的文件数量

        Returns:
            清理的文件数量
        """
        if cls._archiver:
            cls._archiver.max_archive_count = keep_count
            return cls._archiver.cleanup_old_logs(log_type)
        else:
            return cls._simple_cleanup(log_type, keep_count)

    @classmethod
    def _simple_cleanup(cls, log_type: str, keep_count: int) -> int:
        """简化版本的清理逻辑"""
        try:
            from pathlib import Path

            log_dir = Path("logs")
            if log_type == "server":
                log_dir = log_dir / "server"
            elif log_type == "client":
                log_dir = log_dir / "client"

            if not log_dir.exists():
                return 0

            # 获取所有日志文件（排除latest.log）
            log_files = []
            for file_path in log_dir.glob("*.log"):
                if file_path.name != "latest.log":
                    log_files.append(file_path)

            # 按修改时间排序（最新的在前）
            log_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

            # 删除多余的文件
            files_to_delete = log_files[keep_count:]
            cleaned_count = 0

            for file_path in files_to_delete:
                try:
                    file_path.unlink()
                    cleaned_count += 1
                    print(f"删除旧日志: {file_path.name}")
                except Exception as e:
                    print(f"删除文件失败: {e}")

            if cleaned_count > 0:
                print(f"清理完成，删除了 {cleaned_count} 个旧日志文件")

            return cleaned_count

        except Exception as e:
            print(f"清理日志失败: {e}")
            return 0


class GameLogger:
    """游戏专用日志记录器类（兼容性包装）"""

    def __init__(self, name: str = "WorldWarGame",
                 log_type: str = "server",
                 level: str = "INFO",
                 log_to_console: bool = False):
        """
        初始化游戏日志记录器

        Args:
            name: 日志记录器名称
            log_type: 日志类型 ("server", "client")
            level: 日志级别
            log_to_console: 是否输出到控制台
        """
        self.logger = EnhancedLogger.get_logger(
            name=name,
            log_type=log_type,
            level=level,
            log_to_file=True,
            log_to_console=log_to_console
        )
        self.game_events = []  # 存储游戏事件用于回放
    
    def info(self, message: str, game_event: bool = False):
        """记录信息日志"""
        self.logger.info(message)
        if game_event:
            from datetime import datetime
            self.game_events.append({
                'timestamp': datetime.now(),
                'level': 'INFO',
                'message': message
            })
    
    def debug(self, message: str, game_event: bool = False):
        """记录调试日志"""
        self.logger.debug(message)
        if game_event:
            from datetime import datetime
            self.game_events.append({
                'timestamp': datetime.now(),
                'level': 'DEBUG',
                'message': message
            })

    def warning(self, message: str, game_event: bool = False):
        """记录警告日志"""
        self.logger.warning(message)
        if game_event:
            from datetime import datetime
            self.game_events.append({
                'timestamp': datetime.now(),
                'level': 'WARNING',
                'message': message
            })

    def error(self, message: str, game_event: bool = False):
        """记录错误日志"""
        self.logger.error(message)
        if game_event:
            from datetime import datetime
            self.game_events.append({
                'timestamp': datetime.now(),
                'level': 'ERROR',
                'message': message
            })

    def critical(self, message: str, game_event: bool = False):
        """记录严重错误日志"""
        self.logger.critical(message)
        if game_event:
            from datetime import datetime
            self.game_events.append({
                'timestamp': datetime.now(),
                'level': 'CRITICAL',
                'message': message
            })
    
    def get_game_events(self) -> list:
        """获取游戏事件列表"""
        return self.game_events.copy()
    
    def clear_game_events(self):
        """清除游戏事件列表"""
        self.game_events.clear()


def _safe_archive_if_needed(log_type: str) -> bool:
    """
    安全地归档日志文件（如果需要）

    Args:
        log_type: 日志类型

    Returns:
        是否成功归档
    """
    try:
        from pathlib import Path
        from datetime import datetime
        import shutil
        import os

        log_dir = Path("logs")
        if log_type == "server":
            log_dir = log_dir / "server"
        elif log_type == "client":
            log_dir = log_dir / "client"

        latest_log = log_dir / "latest.log"

        # 检查文件是否存在且大小超过限制
        if latest_log.exists():
            file_size = latest_log.stat().st_size
            # 只有当文件大于5MB时才归档
            if file_size > 5 * 1024 * 1024:  # 5MB
                try:
                    # 尝试重命名而不是移动，避免文件占用问题
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    archive_name = f"server_{timestamp}.log"
                    archive_path = log_dir / archive_name

                    # 使用复制+删除而不是移动，更安全
                    shutil.copy2(str(latest_log), str(archive_path))

                    # 清空原文件而不是删除
                    with open(latest_log, 'w', encoding='utf-8') as f:
                        f.write("")

                    print(f"日志已归档: {archive_path}")
                    return True

                except (OSError, PermissionError) as e:
                    # 如果归档失败，不影响程序运行
                    print(f"日志归档跳过: {e}")
                    return False

        return True

    except Exception as e:
        # 归档失败不应该影响程序运行
        print(f"日志归档检查失败: {e}")
        return False


# 便捷函数
def get_server_logger(name: str = "GameServer",
                     level: str = "INFO",
                     log_to_console: bool = True) -> GameLogger:
    """获取服务器日志记录器（默认启用控制台输出）"""
    # 安全地归档旧日志（如果需要）
    _safe_archive_if_needed("server")
    return GameLogger(name, "server", level, log_to_console)


def get_client_logger(name: str = "GameClient",
                     level: str = "INFO",
                     log_to_console: bool = True) -> GameLogger:
    """获取客户端日志记录器（默认启用控制台输出）"""
    # 安全地归档旧日志（如果需要）
    _safe_archive_if_needed("client")
    return GameLogger(name, "client", level, log_to_console)


def get_general_logger(name: str = "General",
                      level: str = "INFO",
                      log_to_console: bool = False) -> GameLogger:
    """获取通用日志记录器"""
    return GameLogger(name, "server", level, log_to_console)


# 重定向print输出到日志
class LogRedirector:
    """重定向print输出到日志文件"""
    
    def __init__(self, logger: logging.Logger, level: int = logging.INFO):
        self.logger = logger
        self.level = level
        self.buffer = ""
    
    def write(self, message: str):
        """写入消息"""
        if message.strip():  # 忽略空行
            self.logger.log(self.level, message.strip())
    
    def flush(self):
        """刷新缓冲区"""
        pass


def redirect_print_to_log(log_type: str = "general", 
                         logger_name: str = "PrintRedirect"):
    """
    将print输出重定向到日志文件
    
    Args:
        log_type: 日志类型 ("server", "client", "general")
        logger_name: 日志记录器名称
    """
    logger = EnhancedLogger.get_logger(
        name=logger_name,
        log_type=log_type,
        level="INFO",
        log_to_file=True,
        log_to_console=False
    )
    
    # 重定向stdout和stderr
    sys.stdout = LogRedirector(logger, logging.INFO)
    sys.stderr = LogRedirector(logger, logging.ERROR)
