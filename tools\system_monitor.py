#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统监控和管理工具
System Monitoring and Management Tool

提供服务器状态监控、游戏房间管理和玩家状态监控功能
Provides server status monitoring, game room management and player status monitoring
"""

import json
import socket
import threading
import time
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict
import argparse

# 尝试导入性能监控器
try:
    from performance_monitor import PerformanceMonitor
except ImportError:
    PerformanceMonitor = None

@dataclass
class ServerStatus:
    """服务器状态"""
    timestamp: float
    is_running: bool
    port: int
    active_connections: int
    total_rooms: int
    active_rooms: int
    total_players: int
    uptime_seconds: float
    cpu_percent: float
    memory_percent: float
    error_count: int
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

@dataclass
class RoomStatus:
    """房间状态"""
    room_id: str
    room_name: str
    status: str  # waiting, playing, finished
    player_count: int
    max_players: int
    created_at: float
    started_at: Optional[float]
    host_player: str
    terrain_type: Optional[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

@dataclass
class PlayerStatus:
    """玩家状态"""
    player_id: str
    username: str
    session_id: str
    status: str  # connected, in_room, playing, disconnected
    room_id: Optional[str]
    connected_at: float
    last_activity: float
    ip_address: str
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, server_host: str = "localhost", server_port: int = 8888):
        """
        初始化系统监控器
        
        Args:
            server_host: 服务器主机
            server_port: 服务器端口
        """
        self.server_host = server_host
        self.server_port = server_port
        
        # 监控数据
        self.server_status_history = []
        self.room_status_cache = {}
        self.player_status_cache = {}
        self.alert_history = []
        
        # 性能监控器
        self.performance_monitor = None
        if PerformanceMonitor:
            self.performance_monitor = PerformanceMonitor(collection_interval=10.0)
        
        # 控制变量
        self.is_monitoring = False
        self.monitor_thread = None
        self.lock = threading.Lock()
        
        # 告警配置
        self.alert_config = {
            'max_response_time_ms': 1000,
            'max_error_rate_percent': 5.0,
            'max_memory_percent': 85.0,
            'max_cpu_percent': 80.0,
            'min_free_disk_gb': 1.0
        }
        
        # 服务器启动时间
        self.server_start_time = None
    
    def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            print("系统监控已在运行中")
            return
        
        self.is_monitoring = True
        
        # 启动性能监控
        if self.performance_monitor:
            self.performance_monitor.start_monitoring()
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        
        print(f"系统监控已启动，监控服务器: {self.server_host}:{self.server_port}")
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        
        # 停止性能监控
        if self.performance_monitor:
            self.performance_monitor.stop_monitoring()
        
        # 等待监控线程结束
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        print("系统监控已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                # 检查服务器状态
                server_status = self._check_server_status()
                
                with self.lock:
                    self.server_status_history.append(server_status)
                    
                    # 保持历史记录在合理范围内
                    if len(self.server_status_history) > 1000:
                        self.server_status_history = self.server_status_history[-1000:]
                
                # 检查告警条件
                self._check_alerts(server_status)
                
                # 更新房间和玩家状态
                self._update_room_and_player_status()
                
                time.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                print(f"监控循环出错: {e}")
                time.sleep(30)
    
    def _check_server_status(self) -> ServerStatus:
        """检查服务器状态"""
        current_time = time.time()
        
        # 检查服务器是否在运行
        is_running = self._is_server_running()
        
        # 获取性能数据
        cpu_percent = 0.0
        memory_percent = 0.0
        active_connections = 0
        
        if self.performance_monitor:
            perf_stats = self.performance_monitor.get_current_stats()
            if perf_stats:
                cpu_percent = perf_stats.get('cpu_percent', 0.0)
                memory_percent = perf_stats.get('memory_percent', 0.0)
                active_connections = perf_stats.get('active_connections', 0)
        
        # 计算运行时间
        uptime_seconds = 0.0
        if self.server_start_time:
            uptime_seconds = current_time - self.server_start_time
        elif is_running and not hasattr(self, '_first_running_check'):
            self.server_start_time = current_time
            self._first_running_check = True
        
        # 统计房间和玩家
        total_rooms = len(self.room_status_cache)
        active_rooms = len([r for r in self.room_status_cache.values() 
                           if r.status in ['waiting', 'playing']])
        total_players = len(self.player_status_cache)
        
        # 错误计数（从日志或其他来源获取）
        error_count = self._get_recent_error_count()
        
        return ServerStatus(
            timestamp=current_time,
            is_running=is_running,
            port=self.server_port,
            active_connections=active_connections,
            total_rooms=total_rooms,
            active_rooms=active_rooms,
            total_players=total_players,
            uptime_seconds=uptime_seconds,
            cpu_percent=cpu_percent,
            memory_percent=memory_percent,
            error_count=error_count
        )
    
    def _is_server_running(self) -> bool:
        """检查服务器是否在运行"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(5)
                result = sock.connect_ex((self.server_host, self.server_port))
                return result == 0
        except Exception:
            return False
    
    def _get_recent_error_count(self) -> int:
        """获取最近的错误计数"""
        try:
            # 尝试从日志文件中统计错误
            log_file = Path("logs/server/latest.log")
            if not log_file.exists():
                return 0
            
            error_count = 0
            cutoff_time = datetime.now() - timedelta(minutes=30)
            
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if 'ERROR' in line or 'CRITICAL' in line:
                        # 简单的时间检查
                        if any(str(cutoff_time.year) in line and 
                              str(cutoff_time.month).zfill(2) in line and
                              str(cutoff_time.day).zfill(2) in line
                              for _ in [1]):  # 简化的时间检查
                            error_count += 1
            
            return error_count
            
        except Exception:
            return 0
    
    def _update_room_and_player_status(self):
        """更新房间和玩家状态"""
        # 这里应该从服务器获取实际的房间和玩家状态
        # 由于没有直接的API接口，我们模拟一些数据
        
        # 在实际实现中，这里会通过管理接口或数据库查询获取真实数据
        pass
    
    def _check_alerts(self, server_status: ServerStatus):
        """检查告警条件"""
        alerts = []
        current_time = time.time()
        
        # 检查服务器是否运行
        if not server_status.is_running:
            alerts.append({
                'type': 'server_down',
                'severity': 'critical',
                'message': '服务器未运行',
                'timestamp': current_time
            })
        
        # 检查CPU使用率
        if server_status.cpu_percent > self.alert_config['max_cpu_percent']:
            alerts.append({
                'type': 'high_cpu',
                'severity': 'warning',
                'message': f'CPU使用率过高: {server_status.cpu_percent:.1f}%',
                'timestamp': current_time,
                'value': server_status.cpu_percent,
                'threshold': self.alert_config['max_cpu_percent']
            })
        
        # 检查内存使用率
        if server_status.memory_percent > self.alert_config['max_memory_percent']:
            alerts.append({
                'type': 'high_memory',
                'severity': 'warning',
                'message': f'内存使用率过高: {server_status.memory_percent:.1f}%',
                'timestamp': current_time,
                'value': server_status.memory_percent,
                'threshold': self.alert_config['max_memory_percent']
            })
        
        # 检查错误率
        if server_status.error_count > 10:  # 30分钟内超过10个错误
            alerts.append({
                'type': 'high_error_rate',
                'severity': 'warning',
                'message': f'错误数量过多: {server_status.error_count}',
                'timestamp': current_time,
                'value': server_status.error_count
            })
        
        # 记录告警
        with self.lock:
            self.alert_history.extend(alerts)
            
            # 保持告警历史在合理范围内
            if len(self.alert_history) > 500:
                self.alert_history = self.alert_history[-500:]
        
        # 输出告警
        for alert in alerts:
            severity_icon = "🚨" if alert['severity'] == 'critical' else "⚠️"
            print(f"{severity_icon} [{datetime.fromtimestamp(alert['timestamp']).strftime('%H:%M:%S')}] {alert['message']}")
    
    def get_server_status(self) -> Optional[ServerStatus]:
        """获取当前服务器状态"""
        with self.lock:
            if self.server_status_history:
                return self.server_status_history[-1]
            return None
    
    def get_server_history(self, hours: int = 24) -> List[ServerStatus]:
        """获取服务器历史状态"""
        cutoff_time = time.time() - (hours * 3600)
        
        with self.lock:
            return [status for status in self.server_status_history 
                   if status.timestamp >= cutoff_time]
    
    def get_recent_alerts(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取最近的告警"""
        cutoff_time = time.time() - (hours * 3600)
        
        with self.lock:
            return [alert for alert in self.alert_history 
                   if alert['timestamp'] >= cutoff_time]
    
    def get_room_list(self) -> List[RoomStatus]:
        """获取房间列表"""
        with self.lock:
            return list(self.room_status_cache.values())
    
    def get_player_list(self) -> List[PlayerStatus]:
        """获取玩家列表"""
        with self.lock:
            return list(self.player_status_cache.values())
    
    def get_system_statistics(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        current_status = self.get_server_status()
        if not current_status:
            return {}
        
        history = self.get_server_history(hours=24)
        recent_alerts = self.get_recent_alerts(hours=24)
        
        # 计算统计信息
        if history:
            cpu_values = [s.cpu_percent for s in history]
            memory_values = [s.memory_percent for s in history]
            connection_values = [s.active_connections for s in history]
            
            stats = {
                'current_status': current_status.to_dict(),
                'uptime_hours': current_status.uptime_seconds / 3600,
                'avg_cpu_24h': sum(cpu_values) / len(cpu_values),
                'max_cpu_24h': max(cpu_values),
                'avg_memory_24h': sum(memory_values) / len(memory_values),
                'max_memory_24h': max(memory_values),
                'avg_connections_24h': sum(connection_values) / len(connection_values),
                'max_connections_24h': max(connection_values),
                'total_alerts_24h': len(recent_alerts),
                'critical_alerts_24h': len([a for a in recent_alerts if a.get('severity') == 'critical']),
                'warning_alerts_24h': len([a for a in recent_alerts if a.get('severity') == 'warning'])
            }
        else:
            stats = {
                'current_status': current_status.to_dict(),
                'uptime_hours': current_status.uptime_seconds / 3600
            }
        
        return stats
    
    def generate_status_report(self) -> str:
        """生成状态报告"""
        stats = self.get_system_statistics()
        if not stats:
            return "暂无监控数据"
        
        current = stats['current_status']
        
        report_lines = [
            "=" * 60,
            "系统监控状态报告",
            "=" * 60,
            "",
            f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"监控服务器: {self.server_host}:{self.server_port}",
            "",
            "服务器状态:",
            "-" * 20,
            f"运行状态: {'✅ 运行中' if current['is_running'] else '❌ 已停止'}",
            f"运行时间: {stats.get('uptime_hours', 0):.1f} 小时",
            f"活跃连接: {current['active_connections']}",
            f"总房间数: {current['total_rooms']}",
            f"活跃房间: {current['active_rooms']}",
            f"总玩家数: {current['total_players']}",
            "",
            "系统资源:",
            "-" * 20,
            f"CPU使用率: {current['cpu_percent']:.1f}%",
            f"内存使用率: {current['memory_percent']:.1f}%",
            f"最近错误数: {current['error_count']}",
        ]
        
        # 添加24小时统计
        if 'avg_cpu_24h' in stats:
            report_lines.extend([
                "",
                "24小时统计:",
                "-" * 20,
                f"平均CPU使用率: {stats['avg_cpu_24h']:.1f}%",
                f"最高CPU使用率: {stats['max_cpu_24h']:.1f}%",
                f"平均内存使用率: {stats['avg_memory_24h']:.1f}%",
                f"最高内存使用率: {stats['max_memory_24h']:.1f}%",
                f"平均连接数: {stats['avg_connections_24h']:.0f}",
                f"最高连接数: {stats['max_connections_24h']}",
            ])
        
        # 添加告警统计
        if 'total_alerts_24h' in stats:
            report_lines.extend([
                "",
                "告警统计 (24小时):",
                "-" * 20,
                f"总告警数: {stats['total_alerts_24h']}",
                f"严重告警: {stats['critical_alerts_24h']}",
                f"警告告警: {stats['warning_alerts_24h']}",
            ])
        
        # 添加最近告警
        recent_alerts = self.get_recent_alerts(hours=6)
        if recent_alerts:
            report_lines.extend([
                "",
                "最近告警 (6小时内):",
                "-" * 20
            ])
            
            for alert in recent_alerts[-10:]:  # 显示最近10个告警
                timestamp = datetime.fromtimestamp(alert['timestamp']).strftime('%H:%M:%S')
                severity_icon = "🚨" if alert.get('severity') == 'critical' else "⚠️"
                report_lines.append(f"  {severity_icon} [{timestamp}] {alert['message']}")
        
        # 添加健康状态评估
        report_lines.extend([
            "",
            "系统健康状态:",
            "-" * 20
        ])
        
        health_issues = []
        
        if not current['is_running']:
            health_issues.append("服务器未运行")
        
        if current['cpu_percent'] > self.alert_config['max_cpu_percent']:
            health_issues.append(f"CPU使用率过高 ({current['cpu_percent']:.1f}%)")
        
        if current['memory_percent'] > self.alert_config['max_memory_percent']:
            health_issues.append(f"内存使用率过高 ({current['memory_percent']:.1f}%)")
        
        if current['error_count'] > 10:
            health_issues.append(f"错误数量过多 ({current['error_count']})")
        
        if health_issues:
            report_lines.append("⚠️  发现以下问题:")
            for issue in health_issues:
                report_lines.append(f"  - {issue}")
        else:
            report_lines.append("✅ 系统运行状态良好")
        
        report_lines.extend([
            "",
            "=" * 60,
            "报告结束",
            "=" * 60
        ])
        
        return "\n".join(report_lines)
    
    def export_monitoring_data(self, output_file: Path):
        """导出监控数据"""
        data = {
            'export_time': datetime.now().isoformat(),
            'server_host': self.server_host,
            'server_port': self.server_port,
            'alert_config': self.alert_config,
            'server_status_history': [s.to_dict() for s in self.server_status_history],
            'room_status': [r.to_dict() for r in self.room_status_cache.values()],
            'player_status': [p.to_dict() for p in self.player_status_cache.values()],
            'alert_history': self.alert_history,
            'system_statistics': self.get_system_statistics()
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"监控数据已导出到: {output_file}")
    
    def set_alert_threshold(self, metric: str, value: float):
        """设置告警阈值"""
        if metric in self.alert_config:
            self.alert_config[metric] = value
            print(f"设置告警阈值: {metric} = {value}")
        else:
            print(f"未知的告警指标: {metric}")
    
    def restart_server_monitoring(self):
        """重启服务器监控"""
        print("重启监控...")
        self.stop_monitoring()
        time.sleep(2)
        self.start_monitoring()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='系统监控和管理工具')
    parser.add_argument('--host', default='localhost', help='服务器主机地址')
    parser.add_argument('--port', type=int, default=8888, help='服务器端口')
    parser.add_argument('--duration', type=int, default=0, help='监控持续时间（秒），0表示持续监控')
    parser.add_argument('--report-interval', type=int, default=300, help='报告生成间隔（秒）')
    parser.add_argument('--export', type=Path, help='导出监控数据文件路径')
    parser.add_argument('--cpu-threshold', type=float, default=80.0, help='CPU告警阈值')
    parser.add_argument('--memory-threshold', type=float, default=85.0, help='内存告警阈值')
    
    args = parser.parse_args()
    
    # 创建系统监控器
    monitor = SystemMonitor(args.host, args.port)
    
    # 设置告警阈值
    monitor.set_alert_threshold('max_cpu_percent', args.cpu_threshold)
    monitor.set_alert_threshold('max_memory_percent', args.memory_threshold)
    
    # 启动监控
    monitor.start_monitoring()
    
    try:
        if args.duration > 0:
            # 固定时间监控
            print(f"开始监控 {args.duration} 秒...")
            time.sleep(args.duration)
        else:
            # 持续监控
            print("开始持续监控，按 Ctrl+C 停止...")
            print("可用命令:")
            print("  status - 显示当前状态")
            print("  report - 生成详细报告")
            print("  alerts - 显示最近告警")
            print("  help - 显示帮助")
            print()
            
            last_report_time = time.time()
            
            while True:
                time.sleep(1)
                
                # 定期生成简要报告
                if time.time() - last_report_time >= args.report_interval:
                    print("\n" + "="*60)
                    print("定期状态检查")
                    print("="*60)
                    
                    current_status = monitor.get_server_status()
                    if current_status:
                        status_icon = "✅" if current_status.is_running else "❌"
                        print(f"{status_icon} 服务器状态: {'运行中' if current_status.is_running else '已停止'}")
                        print(f"📊 CPU: {current_status.cpu_percent:.1f}% | "
                              f"内存: {current_status.memory_percent:.1f}% | "
                              f"连接: {current_status.active_connections} | "
                              f"房间: {current_status.active_rooms}/{current_status.total_rooms}")
                        
                        # 显示最近告警
                        recent_alerts = monitor.get_recent_alerts(hours=1)
                        if recent_alerts:
                            print(f"⚠️  最近1小时内有 {len(recent_alerts)} 个告警")
                    
                    last_report_time = time.time()
    
    except KeyboardInterrupt:
        print("\n收到停止信号...")
    
    finally:
        # 停止监控
        monitor.stop_monitoring()
        
        # 生成最终报告
        print("\n生成最终报告...")
        final_report = monitor.generate_status_report()
        print(final_report)
        
        # 导出数据
        if args.export:
            monitor.export_monitoring_data(args.export)
        
        print("系统监控已结束")

if __name__ == '__main__':
    main()