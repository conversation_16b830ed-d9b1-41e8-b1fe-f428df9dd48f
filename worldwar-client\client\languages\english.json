{"language_name": "English", "client": {"initialized": "Client initialized", "server_info": "Server info: {host}:{port}", "username_info": "Username: {username}", "username_generated": "Generated username: {username}", "disconnecting": "Disconnecting...", "program_error": "Program error: {error}", "program_exited": "Program exited", "starting": "🚀 Starting client...", "connection_failed": "Connection failed", "interrupted": "🛑 Program interrupted by user"}, "connection": {"failed": "Connection failed: {error}", "timeout": "Connection timeout, attempting reconnect...", "error": "Connection error occurred", "reconnecting": "Attempting reconnect ({attempt}/{max_attempts})...", "reconnect_success": "Reconnection successful", "reconnect_failed": "Reconnection failed", "status": "Connection status: {status}", "username": "Username: {username}", "session_id": "Session ID: {session_id}", "server": "Server: {host}:{port}"}, "messages": {"send_failed": "Failed to send message: {error}", "json_parse_error": "JSON parse error: {error}", "receive_error": "Message receive error: {error}", "handler_error": "Message handler error: {error}", "unknown_type": "Unknown message type: {type}", "welcome_received": "Received server welcome message", "ui_callback_error": "UI update callback error: {error}", "heartbeat_error": "Heartbeat thread error: {error}"}, "room": {"created_success": "Room created successfully: {room_name}", "game_starting": "Game is starting!", "game_started": "Game started!"}, "errors": {"server_error": "Server error: {message}", "unknown_error": "Unknown error"}}