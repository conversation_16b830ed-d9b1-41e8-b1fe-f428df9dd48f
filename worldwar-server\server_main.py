#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
世界大战游戏服务器主程序 - 重构版
World War Game Server Main - Refactored
"""


import sys
import argparse
import signal
import time
import socket
import threading
import json
import uuid
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入语言管理器
from shared.enhanced_language_manager import get_enhanced_language_manager, create_enhanced_language_manager
# 导入消息处理器和房间管理器
from server.message_processor import MessageProcessor

from server.player_session import PlayerSession, SessionManager
from shared.secure_protocol import SecureProtocol
from shared.message_manager import ServerMessageManager
from server.welcome_manager import WelcomeManager
from utils.debug_manager import DebugManager
from utils.multi_instance_guard import check_and_prevent_multi_instance

class SimpleGameServer:
    """完整功能的游戏服务器"""

    def __init__(self, host="localhost", port=8888, debug=False):
        self.host = host
        self.port = port
        self.running = False
        self._is_shutting_down = False
        self.server_socket = None
        
        # 初始化会话管理器
        self.session_manager = SessionManager()

        # 初始化语言管理器
        self.language_manager = get_enhanced_language_manager("server")

        # 初始化欢迎语管理器
        self.welcome_manager = WelcomeManager(self.language_manager)

        # 初始化调试管理器
        self.debug_manager = DebugManager()

        # 初始化增强房间管理器
        from server.enhanced_room_manager import EnhancedRoomManager
        self.room_manager = EnhancedRoomManager()

        # 初始化安全协议
        self.protocol = SecureProtocol("server")

        # 初始化消息管理器
        self.message_manager = ServerMessageManager()

        # 初始化消息处理器
        self.message_processor = MessageProcessor(
            self.protocol,
            self.room_manager,
            self.session_manager,
            self.message_manager
        )

        if debug:
            self.debug_manager.enable_debug()
            self.debug_manager.enable_verbose()

        # 显示启动横幅
        self._show_startup_banner()

    def _show_startup_banner(self):
        """显示启动横幅"""
        # 获取语言偏好
        language = self.language_manager.current_language
        if language == "english":
            lang_pref = "english"
        else:
            lang_pref = "chinese"

        # 显示ASCII艺术字（如果启用详细模式）
        if self.debug_manager.verbose_enabled:
            print(self.welcome_manager.get_startup_ascii_art())

        # 显示服务器横幅
        banner = self.welcome_manager.generate_server_banner(lang_pref)
        print(banner)

        # 显示调试信息（如果启用调试模式）
        if self.debug_manager.debug_enabled:
            print(f"\n{self.language_manager.get_text('server.debug_enabled')}")
            debug_info = self.debug_manager.get_debug_status()
            print(self.language_manager.get_text('server.debug_modules', modules=', '.join(debug_info['debug_modules'])))
            print(self.language_manager.get_text('server.debug_log_level', level=debug_info['log_level']))

        print()
    
    def start_server(self):
        """启动服务器"""
        try:
            # 创建服务器套接字
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            # 设置套接字超时，让accept()能响应Ctrl+C
            self.server_socket.settimeout(1.0)  # 1秒超时
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(5)
            
            self.running = True
            
            print("=" * 60)
            print(self.language_manager.get_text('server.server_started'))
            print(self.language_manager.get_text('server.server_address', host=self.host, port=self.port))
            print(self.language_manager.get_text('server.waiting_connections'))
            print(self.language_manager.get_text('server.stop_instruction'))
            print("=" * 60)
            
            # 主循环
            while self.running:
                try:
                    client_socket, client_address = self.server_socket.accept()
                    print(self.language_manager.get_text('server.new_client', address=client_address))

                    # 为每个客户端创建线程
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket, client_address)
                    )
                    client_thread.daemon = True
                    client_thread.start()

                except socket.timeout:
                    # 超时是正常的，继续循环检查self.running
                    continue
                except socket.error:
                    if self.running:
                        print(self.language_manager.get_text('server.socket_error'))
                    break
                except KeyboardInterrupt:
                    print(f"\n{self.language_manager.get_text('server.keyboard_interrupt_received')}")
                    break
                    
        except Exception as e:
            print(self.language_manager.get_text('server.startup_error', error=str(e)))
        finally:
            self.shutdown()
    
    def handle_client(self, client_socket, client_address):
        """处理客户端连接"""
        session = None
        try:
            # 创建玩家会话
            session = self.session_manager.create_session(
                str(uuid.uuid4()), client_socket, client_address
            )
            # 生成增强的欢迎消息
            language = self.language_manager.current_language
            if language == "english":
                lang_pref = "english"
            else:
                lang_pref = "chinese"

            welcome_data = self.welcome_manager.generate_client_welcome("", lang_pref)

            # 记录调试信息
            self.debug_manager.log_network_activity("client_connected", {
                "address": str(client_address),
                "welcome_sent": True
            })

            # 设置客户端套接字超时，避免阻塞Ctrl+C
            client_socket.settimeout(1.0)  # 1秒超时

            # 简单的消息发送（这里可以扩展为JSON协议）
            welcome_json = json.dumps(welcome_data, ensure_ascii=False)
            client_socket.send(welcome_json.encode('utf-8'))

            # 保持连接
            while self.running and session.is_connected:
                try:
                    data = client_socket.recv(1024)
                    if not data:
                        break

                    # 处理客户端消息
                    message_text = data.decode('utf-8')
                    print(self.language_manager.get_text('server.client_message', address=client_address, message=message_text))

                    # 记录用户操作调试信息
                    self.debug_manager.log_user_action(
                        str(client_address),
                        "send_message",
                        {"message_length": len(message_text)}
                    )

                    # 增加请求计数
                    self.debug_manager.increment_request_count()

                    # 解析消息并使用消息处理器处理
                    try:
                        # 尝试解析JSON消息
                        message_data = json.loads(message_text)
                        self.message_processor.process_message(session, message_data)
                    except json.JSONDecodeError:
                        # 如果不是JSON，处理为简单文本消息
                        self._handle_text_command(session, message_text)

                except socket.timeout:
                    # 超时是正常的，继续循环检查self.running
                    continue
                except socket.error:
                    break
                    
        except Exception as e:
            print(self.language_manager.get_text('server.client_error', address=client_address, error=str(e)))
        finally:
            # 清理会话
            if session:
                # 从房间中移除玩家
                if session.current_room:
                    room = self.room_manager.get_room(session.current_room)
                    if room:
                        room.remove_player(session.session_id)
                
                # 使用SessionManager移除会话
                self.session_manager.remove_session(session.session_id)

            try:
                client_socket.close()
                print(self.language_manager.get_text('server.client_disconnected', address=client_address))
            except:
                pass

    def _handle_text_command(self, session: PlayerSession, message_text: str):
        """处理基于文本的命令"""
        client_socket = session.socket

        if message_text.startswith("join:"):
            # 处理认证消息
            username = message_text[5:].strip()
            auth_result = self.session_manager.authenticate_session(session.session_id, username)
            if auth_result["success"]:
                response = {
                    "type": "authentication_success",
                    "data": {"message": f"认证成功，欢迎 {auth_result['player_name']}!"}
                }
            else:
                response = {
                    "type": "authentication_failed",
                    "data": {
                        "message": auth_result["message"],
                        "error": auth_result["error"],
                        "suggestions": auth_result.get("suggestions", [])
                    }
                }
            response_json = json.dumps(response, ensure_ascii=False)
            client_socket.send(response_json.encode('utf-8'))

        elif message_text.startswith("create "):
            # 处理创建房间命令
            room_name = message_text[7:].strip()
            room = self.room_manager.create_room(
                room_name, session.session_id, 8, "classic", "normal"
            )
            if room:
                join_result = room.add_player(session)
                if join_result["success"]:
                    session.current_room = room.room_id
                    response_data = {
                        "type": "room_created",
                        "data": {
                            "success": True,
                            "room_info": {
                                "room_id": room.room_id,
                                "room_name": room_name,
                                "is_host": True,
                                "max_players": 8,
                                "game_mode": "classic",
                                "difficulty": "normal",
                                "status": "waiting",
                                "host_player": session.session_id
                            }
                        }
                    }
                    response_json = json.dumps(response_data, ensure_ascii=False)
                    client_socket.send(response_json.encode('utf-8'))
                    print(self.language_manager.get_text('server.room_created_success', room_name=room_name, room_id=room.room_id))
                else:
                    error_response = {
                        "type": "error",
                        "data": {"message": f"加入房间失败: {join_result['message']}"}
                    }
                    response_json = json.dumps(error_response, ensure_ascii=False)
                    client_socket.send(response_json.encode('utf-8'))
            else:
                error_response = {
                    "type": "error",
                    "data": {"message": "创建房间失败"}
                }
                response_json = json.dumps(error_response, ensure_ascii=False)
                client_socket.send(response_json.encode('utf-8'))

        elif message_text == "get_room_list":
            # 处理获取房间列表命令
            room_list = self.room_manager.get_room_list()
            response_data = {
                "type": "room_list",
                "data": {"rooms": room_list}
            }
            response_json = json.dumps(response_data, ensure_ascii=False)
            client_socket.send(response_json.encode('utf-8'))
            print(self.language_manager.get_text('server.room_list_sent', count=len(room_list)))

        else:
            # 其他消息回显
            response_data = {
                "type": "server_response",
                "data": {
                    "message": f"服务器收到: {message_text}",
                    "timestamp": time.time()
                }
            }
            response_json = json.dumps(response_data, ensure_ascii=False)
            client_socket.send(response_json.encode('utf-8'))
    
    def shutdown(self):
        """关闭服务器"""
        if self._is_shutting_down:
            return
        self._is_shutting_down = True

        print(f"\n{self.language_manager.get_text('server.shutting_down')}")

        self.running = False

        # 关闭所有客户端连接
        for session in self.session_manager.get_active_sessions().values():
            try:
                session.disconnect()
            except:
                pass

        # 关闭服务器套接字
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass

        print(self.language_manager.get_text('server.server_stopped'))

# 全局服务器实例和退出标志
server_instance = None
_shutting_down = False

def signal_handler(signum, _frame):
    """信号处理器 - 确保Ctrl+C能正常工作"""
    global _shutting_down

    # 防止重复调用
    if _shutting_down:
        return
    _shutting_down = True

    # 创建临时语言管理器用于信号处理
    temp_lang_manager = create_enhanced_language_manager("server")
    print(f"\n{temp_lang_manager.get_text('server.shutdown_signal_received', signal=signum)}")

    # 如果有全局服务器实例，先关闭它
    global server_instance
    if server_instance:
        try:
            server_instance.shutdown()
        except Exception as e:
            print(temp_lang_manager.get_text('server.shutdown_warning', error=str(e)))

    # 进程锁将通过 atexit 自动释放，无需手动处理

    print(temp_lang_manager.get_text('server.shutdown_complete'))
    sys.exit(0)

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="世界大战游戏服务器 / World War Game Server",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法 / Example Usage:
  %(prog)s                          # 使用默认配置启动
  %(prog)s --host 0.0.0.0 --port 8888  # 指定地址和端口
  %(prog)s --debug                  # 启用调试模式
        """
    )
    
    parser.add_argument(
        "--host",
        default="localhost",
        help="服务器绑定地址 (默认: localhost)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=8888,
        help="服务器端口 (默认: 8888)"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="启用调试模式"
    )

    parser.add_argument(
        "--verbose",
        action="store_true",
        help="启用详细输出模式"
    )

    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        default="INFO",
        help="设置日志级别 (默认: INFO)"
    )

    parser.add_argument(
        "--debug-modules",
        nargs="*",
        choices=["network", "database", "game_logic", "user_actions",
                "performance", "security", "ai", "world_gen"],
        help="启用特定模块的调试"
    )

    parser.add_argument(
        "--allow-multi",
        action="store_true",
        help="允许多个服务器实例同时运行"
    )

    parser.add_argument(
        "--version",
        action="version",
        version="世界大战游戏服务器 v3.0"
    )
    
    return parser.parse_args()

def main():
    """主函数"""
    # 修复Windows控制台Unicode输出问题
    if sys.stdout.encoding != 'utf-8':
        try:
            sys.stdout.reconfigure(encoding='utf-8')
        except TypeError:
            # 在某些环境下 reconfigure 可能不可用，尝试其他方法
            import codecs
            sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
            
    # 设置信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 解析命令行参数
    args = parse_arguments()

    # 多开防护检查
    try:
        if not check_and_prevent_multi_instance("WorldWarServer", args.allow_multi):
            sys.exit(1)
    except Exception as e:
        temp_lang_manager = create_enhanced_language_manager("server")
        print(temp_lang_manager.get_text('server.multi_instance_warning', error=str(e)))
        # 继续启动，不退出
    
    # 显示启动信息
    print("=" * 60)
    print("🎮 世界大战游戏服务器 / World War Game Server")
    print("📡 版本: v3.0")
    print(f"🌐 地址: {args.host}:{args.port}")
    print("=" * 60)
    
    # 创建并启动服务器
    global server_instance
    server_instance = SimpleGameServer(args.host, args.port, args.debug)
    server = server_instance

    # 应用调试设置
    if args.verbose:
        server.debug_manager.enable_verbose()

    if args.log_level:
        server.debug_manager.set_log_level(args.log_level)

    if args.debug_modules:
        server.debug_manager.enable_debug(args.debug_modules)

    # 显示调试报告（如果启用调试模式）
    if args.debug or args.verbose:
        print("\n" + server.debug_manager.generate_debug_report())
    
    try:
        server.start_server()
    except KeyboardInterrupt:
        temp_lang_manager = create_enhanced_language_manager("server")
        print(f"\n{temp_lang_manager.get_text('server.interrupt_signal')}")
    except Exception as e:
        temp_lang_manager = create_enhanced_language_manager("server")
        print(temp_lang_manager.get_text('server.runtime_error', error=str(e)))
    finally:
        server.shutdown()

if __name__ == "__main__":
    main()
