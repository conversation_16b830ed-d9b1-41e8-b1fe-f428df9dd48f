#!/usr/bin/env python3
import sys
sys.path.append('worldwar-server')

from world.terrain.perlin_generator import PerlinTerrainGenerator, TerrainConfig

# Create a small terrain generator for testing
config = TerrainConfig(size=(32, 32))
generator = PerlinTerrainGenerator(seed=12345, terrain_config=config)

print("Generating heightmap...")
heightmap = generator.generate_heightmap()
print(f"Heightmap shape: {heightmap.shape}")
print(f"Heightmap range: {heightmap.min():.3f} - {heightmap.max():.3f}")

print("Generating temperature map...")
temp_map = generator.generate_temperature_map(heightmap)
print(f"Temperature range: {temp_map.min():.3f} - {temp_map.max():.3f}")

print("Getting terrain stats...")
stats = generator.get_terrain_stats(heightmap)
print(f"Water ratio: {stats['water_ratio']:.3f}")
print(f"Land ratio: {stats['land_ratio']:.3f}")

print("Creating region...")
region = generator.create_region("test", (16, 16), 10)
print(f"Region created with size: {region['size']}")

print("✓ Perlin noise generator working correctly!")