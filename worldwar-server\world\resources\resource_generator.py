"""
资源分布生成器
Resource Distribution Generator

根据地形类型和生物群系分配自然资源，实现资源丰富度和稀有度计算，添加资源分布的平衡性检查。
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass
from enum import Enum
import logging
from perlin_noise import PerlinNoise
import json

# 导入地形和生物群系类型
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from terrain.terrain_types import (
    BiomeType, TerrainType, 
    BIOME_PROPERTIES, TERRAIN_PROPERTIES
)

logger = logging.getLogger(__name__)


class ResourceType(Enum):
    """资源类型枚举"""
    # 基础资源
    AGRICULTURE = "agriculture"      # 农业
    LIVESTOCK = "livestock"          # 畜牧业
    FISH = "fish"                   # 渔业
    WOOD = "wood"                   # 木材
    WATER = "water"                 # 水资源
    
    # 矿物资源
    STONE = "stone"                 # 石材
    IRON = "iron"                   # 铁矿
    COAL = "coal"                   # 煤炭
    OIL = "oil"                     # 石油
    GAS = "gas"                     # 天然气
    GOLD = "gold"                   # 黄金
    RARE_METALS = "rare_metals"     # 稀有金属
    RARE_MINERALS = "rare_minerals" # 稀有矿物
    
    # 能源资源
    SOLAR = "solar"                 # 太阳能
    WIND = "wind"                   # 风能
    HYDROELECTRIC = "hydroelectric" # 水电
    
    # 特殊资源
    TOURISM = "tourism"             # 旅游业
    TRADE = "trade"                 # 贸易
    BIODIVERSITY = "biodiversity"   # 生物多样性
    MEDICINE = "medicine"           # 药材
    OXYGEN = "oxygen"               # 氧气产出
    CARBON_STORAGE = "carbon_storage"  # 碳储存


@dataclass
class ResourceProperties:
    """资源属性"""
    name: str                       # 资源名称
    rarity: float                   # 稀有度 (0-1, 1最稀有)
    base_abundance: float           # 基础丰富度
    extraction_difficulty: float    # 开采难度 (0-1)
    renewable: bool                 # 是否可再生
    strategic_value: float          # 战略价值 (0-1)
    market_volatility: float        # 市场波动性 (0-1)
    environmental_impact: float     # 环境影响 (0-1)
    description: str                # 描述


@dataclass
class ResourceDistributionConfig:
    """资源分布配置"""
    base_noise_scale: float = 0.01          # 基础噪声缩放
    rarity_noise_scale: float = 0.005       # 稀有度噪声缩放
    cluster_strength: float = 0.3           # 聚集强度
    minimum_abundance: float = 0.01         # 最小丰富度
    maximum_abundance: float = 1.0          # 最大丰富度
    balance_factor: float = 0.8             # 平衡因子
    strategic_bonus: float = 0.2            # 战略资源加成


# 资源属性定义
RESOURCE_PROPERTIES: Dict[ResourceType, ResourceProperties] = {
    # 基础资源
    ResourceType.AGRICULTURE: ResourceProperties(
        name="农业", rarity=0.1, base_abundance=0.6, extraction_difficulty=0.2,
        renewable=True, strategic_value=0.8, market_volatility=0.3,
        environmental_impact=0.3, description="农作物和粮食生产"
    ),
    ResourceType.LIVESTOCK: ResourceProperties(
        name="畜牧业", rarity=0.2, base_abundance=0.5, extraction_difficulty=0.3,
        renewable=True, strategic_value=0.6, market_volatility=0.4,
        environmental_impact=0.4, description="牲畜养殖和畜产品"
    ),
    ResourceType.FISH: ResourceProperties(
        name="渔业", rarity=0.3, base_abundance=0.4, extraction_difficulty=0.4,
        renewable=True, strategic_value=0.5, market_volatility=0.5,
        environmental_impact=0.5, description="海洋和淡水渔业资源"
    ),
    ResourceType.WOOD: ResourceProperties(
        name="木材", rarity=0.2, base_abundance=0.5, extraction_difficulty=0.3,
        renewable=True, strategic_value=0.4, market_volatility=0.3,
        environmental_impact=0.6, description="森林木材资源"
    ),
    ResourceType.WATER: ResourceProperties(
        name="水资源", rarity=0.4, base_abundance=0.7, extraction_difficulty=0.2,
        renewable=True, strategic_value=0.9, market_volatility=0.2,
        environmental_impact=0.3, description="淡水资源"
    ),
    
    # 矿物资源
    ResourceType.STONE: ResourceProperties(
        name="石材", rarity=0.1, base_abundance=0.8, extraction_difficulty=0.4,
        renewable=False, strategic_value=0.3, market_volatility=0.2,
        environmental_impact=0.5, description="建筑用石材"
    ),
    ResourceType.IRON: ResourceProperties(
        name="铁矿", rarity=0.4, base_abundance=0.4, extraction_difficulty=0.6,
        renewable=False, strategic_value=0.8, market_volatility=0.4,
        environmental_impact=0.7, description="铁矿石资源"
    ),
    ResourceType.COAL: ResourceProperties(
        name="煤炭", rarity=0.5, base_abundance=0.3, extraction_difficulty=0.7,
        renewable=False, strategic_value=0.7, market_volatility=0.6,
        environmental_impact=0.8, description="煤炭能源资源"
    ),
    ResourceType.OIL: ResourceProperties(
        name="石油", rarity=0.8, base_abundance=0.2, extraction_difficulty=0.8,
        renewable=False, strategic_value=0.95, market_volatility=0.9,
        environmental_impact=0.9, description="石油资源"
    ),
    ResourceType.GAS: ResourceProperties(
        name="天然气", rarity=0.7, base_abundance=0.25, extraction_difficulty=0.7,
        renewable=False, strategic_value=0.85, market_volatility=0.8,
        environmental_impact=0.7, description="天然气资源"
    ),
    ResourceType.GOLD: ResourceProperties(
        name="黄金", rarity=0.9, base_abundance=0.1, extraction_difficulty=0.9,
        renewable=False, strategic_value=0.8, market_volatility=0.7,
        environmental_impact=0.8, description="贵金属黄金"
    ),
    ResourceType.RARE_METALS: ResourceProperties(
        name="稀有金属", rarity=0.95, base_abundance=0.05, extraction_difficulty=0.95,
        renewable=False, strategic_value=0.9, market_volatility=0.8,
        environmental_impact=0.9, description="稀有金属资源"
    ),
    ResourceType.RARE_MINERALS: ResourceProperties(
        name="稀有矿物", rarity=0.9, base_abundance=0.08, extraction_difficulty=0.9,
        renewable=False, strategic_value=0.85, market_volatility=0.75,
        environmental_impact=0.85, description="稀有矿物资源"
    ),
    
    # 能源资源
    ResourceType.SOLAR: ResourceProperties(
        name="太阳能", rarity=0.2, base_abundance=0.6, extraction_difficulty=0.3,
        renewable=True, strategic_value=0.7, market_volatility=0.3,
        environmental_impact=0.1, description="太阳能发电潜力"
    ),
    ResourceType.WIND: ResourceProperties(
        name="风能", rarity=0.3, base_abundance=0.5, extraction_difficulty=0.4,
        renewable=True, strategic_value=0.6, market_volatility=0.4,
        environmental_impact=0.2, description="风力发电潜力"
    ),
    ResourceType.HYDROELECTRIC: ResourceProperties(
        name="水电", rarity=0.6, base_abundance=0.3, extraction_difficulty=0.6,
        renewable=True, strategic_value=0.8, market_volatility=0.3,
        environmental_impact=0.5, description="水力发电潜力"
    ),
    
    # 特殊资源
    ResourceType.TOURISM: ResourceProperties(
        name="旅游业", rarity=0.4, base_abundance=0.4, extraction_difficulty=0.3,
        renewable=True, strategic_value=0.5, market_volatility=0.6,
        environmental_impact=0.3, description="旅游资源潜力"
    ),
    ResourceType.TRADE: ResourceProperties(
        name="贸易", rarity=0.5, base_abundance=0.3, extraction_difficulty=0.2,
        renewable=True, strategic_value=0.7, market_volatility=0.5,
        environmental_impact=0.2, description="贸易枢纽潜力"
    ),
    ResourceType.BIODIVERSITY: ResourceProperties(
        name="生物多样性", rarity=0.7, base_abundance=0.2, extraction_difficulty=0.1,
        renewable=True, strategic_value=0.6, market_volatility=0.3,
        environmental_impact=-0.5, description="生物多样性价值"
    ),
    ResourceType.MEDICINE: ResourceProperties(
        name="药材", rarity=0.8, base_abundance=0.15, extraction_difficulty=0.5,
        renewable=True, strategic_value=0.7, market_volatility=0.6,
        environmental_impact=0.3, description="天然药材资源"
    ),
    ResourceType.OXYGEN: ResourceProperties(
        name="氧气产出", rarity=0.3, base_abundance=0.5, extraction_difficulty=0.1,
        renewable=True, strategic_value=0.8, market_volatility=0.2,
        environmental_impact=-0.8, description="氧气生产能力"
    ),
    ResourceType.CARBON_STORAGE: ResourceProperties(
        name="碳储存", rarity=0.4, base_abundance=0.4, extraction_difficulty=0.1,
        renewable=True, strategic_value=0.6, market_volatility=0.4,
        environmental_impact=-0.9, description="碳储存能力"
    )
}


class ResourceGenerator:
    """资源分布生成器"""
    
    def __init__(self, seed: Optional[int] = None,
                 config: Optional[ResourceDistributionConfig] = None):
        """
        初始化资源生成器
        
        Args:
            seed: 随机种子
            config: 资源分布配置
        """
        self.seed = seed or 12345
        self.config = config or ResourceDistributionConfig()
        
        # 为每种资源创建噪声生成器
        self.resource_noises = {}
        for i, resource_type in enumerate(ResourceType):
            self.resource_noises[resource_type] = PerlinNoise(
                octaves=4, seed=self.seed + i * 1000
            )
        
        # 创建聚集噪声生成器
        self.cluster_noise = PerlinNoise(octaves=2, seed=self.seed + 10000)
        
        logger.info(f"ResourceGenerator initialized with seed: {self.seed}")
    
    def generate_resource_distribution(self, 
                                     heightmap: np.ndarray,
                                     temperature_map: np.ndarray,
                                     precipitation_map: np.ndarray,
                                     biome_map: np.ndarray,
                                     terrain_map: np.ndarray) -> Dict[str, np.ndarray]:
        """
        生成资源分布地图
        
        Args:
            heightmap: 高度图
            temperature_map: 温度图
            precipitation_map: 降水图
            biome_map: 生物群系地图
            terrain_map: 地形类型地图
            
        Returns:
            Dict[str, np.ndarray]: 各种资源的分布地图
        """
        height, width = heightmap.shape
        resource_maps = {}
        
        logger.info(f"Generating resource distribution for {width}x{height} map")
        
        # 为每种资源生成分布地图
        for resource_type in ResourceType:
            resource_map = self._generate_single_resource_distribution(
                resource_type, heightmap, temperature_map, precipitation_map,
                biome_map, terrain_map
            )
            resource_maps[resource_type.value] = resource_map
        
        # 应用平衡性调整
        resource_maps = self._apply_balance_adjustments(resource_maps)
        
        logger.info("Resource distribution generation completed")
        return resource_maps
    
    def _generate_single_resource_distribution(self,
                                             resource_type: ResourceType,
                                             heightmap: np.ndarray,
                                             temperature_map: np.ndarray,
                                             precipitation_map: np.ndarray,
                                             biome_map: np.ndarray,
                                             terrain_map: np.ndarray) -> np.ndarray:
        """
        生成单种资源的分布
        
        Args:
            resource_type: 资源类型
            heightmap: 高度图
            temperature_map: 温度图
            precipitation_map: 降水图
            biome_map: 生物群系地图
            terrain_map: 地形类型地图
            
        Returns:
            np.ndarray: 资源分布地图
        """
        height, width = heightmap.shape
        resource_map = np.zeros((height, width))
        
        resource_props = RESOURCE_PROPERTIES[resource_type]
        noise_gen = self.resource_noises[resource_type]
        
        for y in range(height):
            for x in range(width):
                # 基础环境适应性
                env_suitability = self._calculate_environmental_suitability(
                    resource_type, heightmap[y, x], temperature_map[y, x],
                    precipitation_map[y, x]
                )
                
                # 生物群系适应性
                biome_suitability = self._calculate_biome_suitability(
                    resource_type, biome_map[y, x]
                )
                
                # 地形适应性
                terrain_suitability = self._calculate_terrain_suitability(
                    resource_type, terrain_map[y, x]
                )
                
                # 基础噪声
                base_noise = noise_gen([x * self.config.base_noise_scale,
                                      y * self.config.base_noise_scale])
                base_noise = (base_noise + 1) / 2  # 归一化到[0, 1]
                
                # 稀有度噪声
                rarity_noise = noise_gen([x * self.config.rarity_noise_scale,
                                        y * self.config.rarity_noise_scale])
                rarity_noise = (rarity_noise + 1) / 2
                
                # 聚集效应
                cluster_effect = self.cluster_noise([x * 0.02, y * 0.02])
                cluster_effect = (cluster_effect + 1) / 2
                cluster_bonus = cluster_effect * self.config.cluster_strength
                
                # 综合计算资源丰富度
                abundance = (
                    resource_props.base_abundance * 0.3 +
                    env_suitability * 0.25 +
                    biome_suitability * 0.25 +
                    terrain_suitability * 0.2
                ) * base_noise
                
                # 应用稀有度调整
                rarity_factor = 1.0 - resource_props.rarity * rarity_noise
                abundance *= rarity_factor
                
                # 应用聚集加成
                abundance += cluster_bonus
                
                # 应用战略资源加成
                if resource_props.strategic_value > 0.8:
                    abundance *= (1.0 + self.config.strategic_bonus)
                
                # 限制在合理范围内
                abundance = np.clip(abundance, 
                                  self.config.minimum_abundance,
                                  self.config.maximum_abundance)
                
                resource_map[y, x] = abundance
        
        return resource_map
    
    def _calculate_environmental_suitability(self,
                                           resource_type: ResourceType,
                                           elevation: float,
                                           temperature: float,
                                           precipitation: float) -> float:
        """
        计算环境适应性
        
        Args:
            resource_type: 资源类型
            elevation: 海拔
            temperature: 温度
            precipitation: 降水
            
        Returns:
            float: 环境适应性分数 (0-1)
        """
        # 基于资源类型的环境偏好
        suitability = 0.5  # 基础适应性
        
        if resource_type == ResourceType.AGRICULTURE:
            # 农业偏好中等海拔、温和气候、适中降水
            suitability = (
                (1.0 - abs(elevation - 0.4)) * 0.4 +
                (1.0 - abs(temperature - 0.6)) * 0.3 +
                (1.0 - abs(precipitation - 0.6)) * 0.3
            )
        elif resource_type == ResourceType.LIVESTOCK:
            # 畜牧业偏好草原环境
            suitability = (
                (1.0 - abs(elevation - 0.5)) * 0.3 +
                (1.0 - abs(temperature - 0.5)) * 0.4 +
                (1.0 - abs(precipitation - 0.4)) * 0.3
            )
        elif resource_type == ResourceType.FISH:
            # 渔业偏好低海拔（水域）
            suitability = max(0, 1.0 - elevation * 2)
        elif resource_type == ResourceType.WOOD:
            # 木材偏好森林环境
            suitability = (
                (1.0 - abs(elevation - 0.5)) * 0.2 +
                temperature * 0.3 +
                precipitation * 0.5
            )
        elif resource_type == ResourceType.OIL:
            # 石油偏好特定地质条件
            suitability = (
                (1.0 - abs(elevation - 0.3)) * 0.4 +
                (1.0 - temperature) * 0.3 +
                (1.0 - precipitation) * 0.3
            )
        elif resource_type == ResourceType.SOLAR:
            # 太阳能偏好高温低降水
            suitability = temperature * 0.7 + (1.0 - precipitation) * 0.3
        elif resource_type == ResourceType.WIND:
            # 风能偏好开阔地带和高海拔
            suitability = elevation * 0.6 + (1.0 - precipitation) * 0.4
        elif resource_type == ResourceType.HYDROELECTRIC:
            # 水电偏好高海拔和高降水
            suitability = elevation * 0.6 + precipitation * 0.4
        elif resource_type in [ResourceType.IRON, ResourceType.COAL, 
                              ResourceType.RARE_METALS, ResourceType.RARE_MINERALS]:
            # 矿物资源偏好山地
            suitability = elevation * 0.8 + (1.0 - temperature) * 0.2
        elif resource_type == ResourceType.TOURISM:
            # 旅游业偏好多样化环境
            diversity = abs(elevation - 0.5) + abs(temperature - 0.5)
            suitability = diversity * 0.6 + precipitation * 0.4
        
        return np.clip(suitability, 0.0, 1.0)
    
    def _calculate_biome_suitability(self,
                                   resource_type: ResourceType,
                                   biome_index: int) -> float:
        """
        计算生物群系适应性
        
        Args:
            resource_type: 资源类型
            biome_index: 生物群系索引
            
        Returns:
            float: 生物群系适应性分数 (0-1)
        """
        if biome_index >= len(list(BiomeType)):
            return 0.5
        
        biome_type = list(BiomeType)[biome_index]
        biome_props = BIOME_PROPERTIES[biome_type]
        
        # 检查生物群系的资源丰富度
        resource_key = resource_type.value
        if resource_key in biome_props.resource_abundance:
            # 归一化资源丰富度到0-1范围
            abundance = biome_props.resource_abundance[resource_key]
            return min(1.0, abundance / 4.0)  # 假设最大丰富度为4
        
        return 0.3  # 默认适应性
    
    def _calculate_terrain_suitability(self,
                                     resource_type: ResourceType,
                                     terrain_index: int) -> float:
        """
        计算地形适应性
        
        Args:
            resource_type: 资源类型
            terrain_index: 地形类型索引
            
        Returns:
            float: 地形适应性分数 (0-1)
        """
        if terrain_index >= len(list(TerrainType)):
            return 0.5
        
        terrain_type = list(TerrainType)[terrain_index]
        terrain_props = TERRAIN_PROPERTIES[terrain_type]
        
        # 检查地形的资源倍数
        resource_key = resource_type.value
        if resource_key in terrain_props.resource_multiplier:
            multiplier = terrain_props.resource_multiplier[resource_key]
            return min(1.0, multiplier / 3.0)  # 归一化
        
        return 0.4  # 默认适应性
    
    def _apply_balance_adjustments(self, 
                                 resource_maps: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
        """
        应用平衡性调整
        
        Args:
            resource_maps: 原始资源分布地图
            
        Returns:
            Dict[str, np.ndarray]: 平衡调整后的资源分布地图
        """
        balanced_maps = resource_maps.copy()
        
        # 计算总体资源密度
        total_density = sum(np.mean(resource_map) 
                          for resource_map in resource_maps.values())
        
        # 如果总密度过高，进行全局缩放
        if total_density > len(ResourceType) * 0.5:
            scale_factor = (len(ResourceType) * 0.5) / total_density
            for resource_key in balanced_maps:
                balanced_maps[resource_key] *= scale_factor
        
        # 确保战略资源的稀有性
        strategic_resources = [
            ResourceType.OIL.value,
            ResourceType.RARE_METALS.value,
            ResourceType.RARE_MINERALS.value,
            ResourceType.GOLD.value
        ]
        
        for resource_key in strategic_resources:
            if resource_key in balanced_maps:
                resource_map = balanced_maps[resource_key]
                # 应用更严格的稀有性
                threshold = np.percentile(resource_map, 80)
                mask = resource_map < threshold
                resource_map[mask] *= 0.5
                balanced_maps[resource_key] = resource_map
        
        return balanced_maps
    
    def calculate_resource_balance_score(self, 
                                       resource_maps: Dict[str, np.ndarray]) -> Dict[str, float]:
        """
        计算资源平衡性分数
        
        Args:
            resource_maps: 资源分布地图
            
        Returns:
            Dict[str, float]: 平衡性分数
        """
        balance_scores = {}
        
        # 计算每种资源的统计信息
        for resource_key, resource_map in resource_maps.items():
            mean_abundance = np.mean(resource_map)
            std_abundance = np.std(resource_map)
            max_abundance = np.max(resource_map)
            min_abundance = np.min(resource_map)
            
            # 计算分布均匀性（标准差越小越均匀）
            uniformity = 1.0 - min(1.0, std_abundance / (mean_abundance + 1e-6))
            
            # 计算稀有性（最大值与平均值的比例）
            rarity = max_abundance / (mean_abundance + 1e-6)
            
            # 计算可获得性（非零像素比例）
            availability = np.sum(resource_map > 0.01) / resource_map.size
            
            # 综合平衡分数
            balance_score = (uniformity * 0.4 + 
                           min(1.0, 2.0 / rarity) * 0.3 + 
                           availability * 0.3)
            
            balance_scores[resource_key] = {
                'balance_score': balance_score,
                'uniformity': uniformity,
                'rarity': rarity,
                'availability': availability,
                'mean_abundance': mean_abundance,
                'std_abundance': std_abundance
            }
        
        return balance_scores
    
    def get_resource_statistics(self, 
                              resource_maps: Dict[str, np.ndarray]) -> Dict[str, Dict]:
        """
        获取资源统计信息
        
        Args:
            resource_maps: 资源分布地图
            
        Returns:
            Dict[str, Dict]: 详细统计信息
        """
        statistics = {}
        
        for resource_key, resource_map in resource_maps.items():
            resource_type = ResourceType(resource_key)
            resource_props = RESOURCE_PROPERTIES[resource_type]
            
            # 基础统计
            stats = {
                'mean': float(np.mean(resource_map)),
                'std': float(np.std(resource_map)),
                'min': float(np.min(resource_map)),
                'max': float(np.max(resource_map)),
                'median': float(np.median(resource_map)),
                'total': float(np.sum(resource_map)),
                'coverage': float(np.sum(resource_map > 0.01) / resource_map.size),
                'hotspots': int(np.sum(resource_map > np.percentile(resource_map, 90))),
                'resource_properties': {
                    'rarity': resource_props.rarity,
                    'renewable': resource_props.renewable,
                    'strategic_value': resource_props.strategic_value,
                    'extraction_difficulty': resource_props.extraction_difficulty
                }
            }
            
            statistics[resource_key] = stats
        
        return statistics
    
    def export_resource_data(self, 
                           resource_maps: Dict[str, np.ndarray],
                           filename: str):
        """
        导出资源数据
        
        Args:
            resource_maps: 资源分布地图
            filename: 输出文件名
        """
        # 获取统计信息和平衡分数
        statistics = self.get_resource_statistics(resource_maps)
        balance_scores = self.calculate_resource_balance_score(resource_maps)
        
        # 创建导出数据
        export_data = {
            'metadata': {
                'seed': self.seed,
                'generation_time': str(np.datetime64('now')),
                'total_resources': len(resource_maps),
                'map_size': list(next(iter(resource_maps.values())).shape)
            },
            'resource_maps': {
                key: resource_map.tolist() 
                for key, resource_map in resource_maps.items()
            },
            'statistics': statistics,
            'balance_scores': balance_scores,
            'resource_properties': {
                resource_type.value: {
                    'name': props.name,
                    'rarity': props.rarity,
                    'renewable': props.renewable,
                    'strategic_value': props.strategic_value,
                    'description': props.description
                }
                for resource_type, props in RESOURCE_PROPERTIES.items()
            }
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Resource data exported to {filename}")


def create_default_resource_generator(seed: Optional[int] = None) -> ResourceGenerator:
    """
    创建默认配置的资源生成器
    
    Args:
        seed: 随机种子
        
    Returns:
        ResourceGenerator: 资源生成器实例
    """
    return ResourceGenerator(seed=seed)


def create_balanced_resource_generator(seed: Optional[int] = None) -> ResourceGenerator:
    """
    创建平衡配置的资源生成器
    
    Args:
        seed: 随机种子
        
    Returns:
        ResourceGenerator: 平衡资源生成器实例
    """
    config = ResourceDistributionConfig(
        cluster_strength=0.2,
        balance_factor=0.9,
        strategic_bonus=0.1,
        minimum_abundance=0.02,
        maximum_abundance=0.8
    )
    
    return ResourceGenerator(seed=seed, config=config)


def create_realistic_resource_generator(seed: Optional[int] = None) -> ResourceGenerator:
    """
    创建真实感资源生成器
    
    Args:
        seed: 随机种子
        
    Returns:
        ResourceGenerator: 真实感资源生成器实例
    """
    config = ResourceDistributionConfig(
        base_noise_scale=0.008,
        rarity_noise_scale=0.003,
        cluster_strength=0.4,
        balance_factor=0.7,
        strategic_bonus=0.3,
        minimum_abundance=0.005,
        maximum_abundance=1.2
    )
    
    return ResourceGenerator(seed=seed, config=config)