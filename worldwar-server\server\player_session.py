#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
玩家会话管理模块
Player Session Management Module
"""

import socket
import time
import threading
from typing import Optional, Dict, Any, List
from dataclasses import dataclass, field

@dataclass
class PlayerSession:
    """玩家会话类"""
    session_id: str
    socket: socket.socket
    client_address: tuple
    
    # 连接状态
    is_connected: bool = True
    is_authenticated: bool = False
    connection_time: float = field(default_factory=time.time)
    last_activity: float = field(default_factory=time.time)
    
    # 玩家信息
    player_name: str = ""
    player_id: str = ""
    
    # 游戏状态
    current_room: Optional[str] = None
    is_in_game: bool = False
    is_ready: bool = False
    
    # 会话统计
    messages_sent: int = 0
    messages_received: int = 0
    bytes_sent: int = 0
    bytes_received: int = 0
    
    # 玩家设置
    language: str = "chinese"

    # 安全握手状态
    handshake_completed: bool = False
    session_token: str = ""

    def complete_handshake(self, session_token: str = ""):
        """完成安全握手"""
        self.handshake_completed = True
        self.session_token = session_token

    def update_activity(self):
        """更新最后活动时间"""
        self.last_activity = time.time()
    
    def get_connection_duration(self) -> float:
        """获取连接持续时间（秒）"""
        return time.time() - self.connection_time
    
    def get_idle_time(self) -> float:
        """获取空闲时间（秒）"""
        return time.time() - self.last_activity
    
    def is_idle(self, timeout_seconds: int = 300) -> bool:
        """检查是否空闲超时（默认5分钟）"""
        return self.get_idle_time() > timeout_seconds
    
    def disconnect(self):
        """断开连接"""
        self.is_connected = False
        self.is_authenticated = False
        self.current_room = None
        self.is_in_game = False
        self.is_ready = False
        
        try:
            if self.socket:
                self.socket.close()
        except:
            pass
    
    def get_session_info(self) -> Dict[str, Any]:
        """获取会话信息"""
        return {
            "session_id": self.session_id,
            "player_name": self.player_name,
            "client_address": f"{self.client_address[0]}:{self.client_address[1]}",
            "is_connected": self.is_connected,
            "is_authenticated": self.is_authenticated,
            "handshake_completed": self.handshake_completed,
            "connection_duration": self.get_connection_duration(),
            "idle_time": self.get_idle_time(),
            "current_room": self.current_room,
            "is_in_game": self.is_in_game,
            "is_ready": self.is_ready,
            "messages_sent": self.messages_sent,
            "messages_received": self.messages_received,
            "language": self.language
        }

class SessionManager:
    """线程安全的会话管理器"""

    def __init__(self):
        """初始化会话管理器"""
        self.sessions: Dict[str, PlayerSession] = {}
        self.player_name_to_session: Dict[str, str] = {}  # 玩家名称到会话ID的映射

        # 线程安全锁
        self.lock = threading.RLock()

        # 导入日志
        from shared.enhanced_logger import get_server_logger
        self.logger = get_server_logger("SessionManager")

        # 初始化用户名管理器
        from server.username_manager import UsernameManager
        self.username_manager = UsernameManager()

        self.logger.info("线程安全会话管理器初始化完成 / Thread-safe session manager initialized")
    
    def create_session(self, session_id: str, socket: socket.socket,
                      client_address: tuple) -> PlayerSession:
        """创建新会话（线程安全）"""
        with self.lock:
            session = PlayerSession(
                session_id=session_id,
                socket=socket,
                client_address=client_address
            )

            self.sessions[session_id] = session
            self.logger.info(f"创建会话: {session_id} 来自 {client_address}")

            return session
    
    def get_session(self, session_id: str) -> Optional[PlayerSession]:
        """获取会话"""
        return self.sessions.get(session_id)
    
    def get_session_by_player_name(self, player_name: str) -> Optional[PlayerSession]:
        """根据玩家名称获取会话"""
        session_id = self.player_name_to_session.get(player_name)
        if session_id:
            return self.get_session(session_id)
        return None
    
    def authenticate_session(self, session_id: str, player_name: str) -> Dict[str, Any]:
        """
        认证会话（线程安全，修复竞态条件）

        Returns:
            包含认证结果的字典
        """
        with self.lock:
            session = self.get_session(session_id)
            if not session:
                return {
                    "success": False,
                    "error": "session_not_found",
                    "message": "会话不存在"
                }

            # 检查会话是否已认证
            if session.is_authenticated:
                return {
                    "success": False,
                    "error": "already_authenticated",
                    "message": "会话已认证"
                }

            # 使用用户名管理器验证玩家名称
            validation_result = self.username_manager.validate_username(player_name)
            if not validation_result.is_valid:
                return {
                    "success": False,
                    "error": validation_result.error_code,
                    "message": validation_result.error_message,
                    "suggestions": validation_result.suggestions
                }

            player_name = player_name.strip()

            # 使用用户名管理器检查可用性
            if not self.username_manager.check_availability(player_name):
                # 生成建议用户名
                suggestions = self.username_manager.suggest_usernames(player_name, 3)

                self.logger.warning(f"玩家名称 {player_name} 不可用")
                return {
                    "success": False,
                    "error": "name_taken",
                    "message": "玩家名称已被使用，请稍后重试或使用其他名称",
                    "suggestions": suggestions
                }

            # 检查是否有旧的会话映射需要清理
            if player_name in self.player_name_to_session:
                existing_session_id = self.player_name_to_session[player_name]
                existing_session = self.get_session(existing_session_id)

                # 清理旧的映射
                self.logger.info(f"清理旧会话的名称映射: {player_name} -> {existing_session_id}")
                if existing_session:
                    existing_session.disconnect()
                    self.username_manager.release_username(player_name)
                del self.player_name_to_session[player_name]

            # 预留用户名
            if not self.username_manager.reserve_username(player_name):
                return {
                    "success": False,
                    "error": "name_reservation_failed",
                    "message": "用户名预留失败，请重试"
                }

            # 设置会话信息
            session.player_name = player_name
            session.player_id = f"player_{session_id}"
            session.is_authenticated = True
            session.update_activity()

            # 添加名称映射
            self.player_name_to_session[player_name] = session_id

            self.logger.info(f"会话 {session_id} 成功认证为玩家 {player_name}")
            return {
                "success": True,
                "player_name": player_name,
                "session_id": session_id
            }
    
    def remove_session(self, session_id: str):
        """移除会话（线程安全）"""
        with self.lock:
            session = self.get_session(session_id)
            if session:
                # 释放用户名
                if session.player_name:
                    self.username_manager.release_username(session.player_name)

                # 清理玩家名称映射
                if session.player_name in self.player_name_to_session:
                    del self.player_name_to_session[session.player_name]

                # 断开连接
                session.disconnect()

                # 从会话列表中移除
                del self.sessions[session_id]

                self.logger.info(f"移除会话: {session_id}")
    
    def update_session_activity(self, session_id: str):
        """更新会话活动时间"""
        session = self.get_session(session_id)
        if session:
            session.update_activity()
    
    def get_active_sessions(self) -> Dict[str, PlayerSession]:
        """获取所有活跃会话"""
        return {
            session_id: session 
            for session_id, session in self.sessions.items() 
            if session.is_connected
        }
    
    def get_authenticated_sessions(self) -> Dict[str, PlayerSession]:
        """获取所有已认证会话"""
        return {
            session_id: session 
            for session_id, session in self.sessions.items() 
            if session.is_authenticated and session.is_connected
        }
    
    def get_sessions_in_room(self, room_id: str) -> Dict[str, PlayerSession]:
        """获取指定房间内的会话"""
        return {
            session_id: session 
            for session_id, session in self.sessions.items() 
            if session.current_room == room_id and session.is_connected
        }
    
    def cleanup_idle_sessions(self, timeout_seconds: int = 300):
        """清理空闲会话"""
        idle_sessions = []
        
        for session_id, session in self.sessions.items():
            if session.is_idle(timeout_seconds) and not session.is_in_game:
                idle_sessions.append(session_id)
        
        for session_id in idle_sessions:
            self.logger.info(f"清理空闲会话: {session_id}")
            self.remove_session(session_id)
        
        return len(idle_sessions)
    
    def cleanup_disconnected_sessions(self):
        """清理已断开连接的会话"""
        disconnected_sessions = []
        
        for session_id, session in self.sessions.items():
            if not session.is_connected:
                disconnected_sessions.append(session_id)
        
        for session_id in disconnected_sessions:
            self.remove_session(session_id)
        
        return len(disconnected_sessions)
    
    def get_session_statistics(self) -> Dict[str, Any]:
        """获取会话统计信息"""
        total_sessions = len(self.sessions)
        active_sessions = len(self.get_active_sessions())
        authenticated_sessions = len(self.get_authenticated_sessions())
        
        # 计算平均连接时间
        if active_sessions > 0:
            total_duration = sum(
                session.get_connection_duration() 
                for session in self.get_active_sessions().values()
            )
            avg_connection_time = total_duration / active_sessions
        else:
            avg_connection_time = 0
        
        # 统计消息数量
        total_messages_sent = sum(session.messages_sent for session in self.sessions.values())
        total_messages_received = sum(session.messages_received for session in self.sessions.values())
        
        return {
            "total_sessions": total_sessions,
            "active_sessions": active_sessions,
            "authenticated_sessions": authenticated_sessions,
            "average_connection_time": avg_connection_time,
            "total_messages_sent": total_messages_sent,
            "total_messages_received": total_messages_received
        }
    
    def broadcast_to_all(self, message: Dict[str, Any], 
                        authenticated_only: bool = True):
        """向所有会话广播消息"""
        from server.network_handler import NetworkHandler
        network_handler = NetworkHandler()
        
        target_sessions = (
            self.get_authenticated_sessions() if authenticated_only 
            else self.get_active_sessions()
        )
        
        success_count = 0
        for session in target_sessions.values():
            try:
                if network_handler.send_message(session.socket, message):
                    session.messages_sent += 1
                    success_count += 1
            except Exception as e:
                self.logger.error(f"广播消息到会话 {session.session_id} 失败: {e}")
        
        self.logger.info(f"广播消息到 {success_count}/{len(target_sessions)} 个会话")
        return success_count
    
    def send_to_session(self, session_id: str, message: Dict[str, Any]) -> bool:
        """向指定会话发送消息"""
        session = self.get_session(session_id)
        if not session or not session.is_connected:
            return False
        
        try:
            from server.network_handler import NetworkHandler
            network_handler = NetworkHandler()
            
            if network_handler.send_message(session.socket, message):
                session.messages_sent += 1
                return True
        except Exception as e:
            self.logger.error(f"发送消息到会话 {session_id} 失败: {e}")
        
        return False
    
    def is_player_name_available(self, player_name: str) -> bool:
        """检查玩家名称是否可用（线程安全）"""
        with self.lock:
            if player_name in self.player_name_to_session:
                session_id = self.player_name_to_session[player_name]
                session = self.get_session(session_id)

                # 如果会话存在且连接，名称不可用
                if session and session.is_connected:
                    return False

                # 清理断开连接的会话映射
                if session and not session.is_connected:
                    self.logger.info(f"清理断开连接会话的名称映射: {player_name}")
                    del self.player_name_to_session[player_name]

            return True
    
    def get_all_player_names(self) -> List[str]:
        """获取所有已认证玩家的名称"""
        return [
            session.player_name
            for session in self.get_authenticated_sessions().values()
            if session.player_name
        ]

    def generate_random_username(self, language: str = "mixed") -> str:
        """
        生成随机用户名

        Args:
            language: 语言偏好 ("chinese", "english", "mixed")

        Returns:
            随机生成的用户名
        """
        return self.username_manager.generate_random_username(language)

    def suggest_usernames(self, original_username: str, count: int = 5) -> List[str]:
        """
        为已被占用的用户名提供建议

        Args:
            original_username: 原始用户名
            count: 建议数量

        Returns:
            建议的用户名列表
        """
        return self.username_manager.suggest_usernames(original_username, count)

    def change_player_name(self, session_id: str, new_name: str) -> Dict[str, Any]:
        """
        更改玩家名称

        Args:
            session_id: 会话ID
            new_name: 新的玩家名称

        Returns:
            更改结果
        """
        with self.lock:
            session = self.get_session(session_id)
            if not session:
                return {
                    "success": False,
                    "error": "session_not_found",
                    "message": "会话不存在"
                }

            if not session.is_authenticated:
                return {
                    "success": False,
                    "error": "not_authenticated",
                    "message": "会话未认证"
                }

            # 验证新用户名
            validation_result = self.username_manager.validate_username(new_name)
            if not validation_result.is_valid:
                return {
                    "success": False,
                    "error": validation_result.error_code,
                    "message": validation_result.error_message,
                    "suggestions": validation_result.suggestions
                }

            new_name = new_name.strip()
            old_name = session.player_name

            # 如果名称相同，不需要更改
            if new_name == old_name:
                return {
                    "success": True,
                    "message": "用户名未更改",
                    "player_name": new_name
                }

            # 检查新用户名可用性
            if not self.username_manager.check_availability(new_name):
                suggestions = self.username_manager.suggest_usernames(new_name, 3)
                return {
                    "success": False,
                    "error": "name_taken",
                    "message": "新用户名已被使用",
                    "suggestions": suggestions
                }

            # 预留新用户名
            if not self.username_manager.reserve_username(new_name):
                return {
                    "success": False,
                    "error": "name_reservation_failed",
                    "message": "新用户名预留失败，请重试"
                }

            # 释放旧用户名
            if old_name:
                self.username_manager.release_username(old_name)
                if old_name in self.player_name_to_session:
                    del self.player_name_to_session[old_name]

            # 更新会话信息
            session.player_name = new_name
            self.player_name_to_session[new_name] = session_id

            self.logger.info(f"玩家 {old_name} 更改用户名为 {new_name}")

            return {
                "success": True,
                "message": "用户名更改成功",
                "old_name": old_name,
                "new_name": new_name
            }

    def get_username_stats(self) -> Dict[str, Any]:
        """获取用户名统计信息"""
        return self.username_manager.get_username_stats()
