#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强房间管理器 - 修复房间创建和管理功能
Enhanced Room Manager - Fix room creation and management functionality
"""

import time
import threading
import json
import asyncio
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
from enum import Enum
from .room_manager import RoomManager, GameRoom, RoomStatus
from shared.enhanced_logger import get_server_logger


class RoomEventType(Enum):
    """房间事件类型"""
    PLAYER_JOINED = "player_joined"
    PLAYER_LEFT = "player_left"
    PLAYER_READY_CHANGED = "player_ready_changed"
    ROOM_SETTINGS_CHANGED = "room_settings_changed"
    GAME_STARTING = "game_starting"
    GAME_STARTED = "game_started"
    ROOM_CLOSED = "room_closed"


@dataclass
class RoomEvent:
    """房间事件"""
    event_type: RoomEventType
    room_id: str
    data: Dict[str, Any]
    timestamp: float = field(default_factory=time.time)


class EnhancedRoomManager(RoomManager):
    """增强的房间管理器 - 修复连接断开问题"""
    
    def __init__(self, protocol=None):
        """初始化增强房间管理器"""
        super().__init__(protocol)
        self.logger = get_server_logger("EnhancedRoomManager")
        
        # 房间事件系统
        self.room_events: Dict[str, List[RoomEvent]] = {}
        self.event_subscribers: Dict[str, Set[str]] = {}  # room_id -> set of session_ids
        
        # 房间状态同步
        self.room_sync_lock = threading.RLock()
        self.pending_room_updates: Dict[str, Dict[str, Any]] = {}
        
        # 连接状态监控
        self.connection_monitor = ConnectionMonitor()
        
        # 启动后台任务
        self._start_background_tasks()
        
        self.logger.info("增强房间管理器初始化完成")
    
    def _start_background_tasks(self):
        """启动后台任务"""
        # 房间状态同步任务
        sync_thread = threading.Thread(target=self._room_sync_worker, daemon=True)
        sync_thread.start()
        
        # 连接监控任务
        monitor_thread = threading.Thread(target=self._connection_monitor_worker, daemon=True)
        monitor_thread.start()
    
    def create_room_safe(self, room_name: str, host_player: str, max_players: int,
                        game_mode: str, difficulty: str = "normal") -> Optional[GameRoom]:
        """
        安全创建房间 - 修复连接断开问题
        
        Returns:
            创建的房间对象，失败返回None
        """
        with self.room_sync_lock:
            try:
                self.logger.info(f"开始安全创建房间 - 房间名: {room_name}, 房主: {host_player}")
                
                # 验证参数
                validation_result = self._validate_room_parameters_enhanced(
                    room_name, max_players, host_player
                )
                if not validation_result["valid"]:
                    self.logger.error(f"房间参数验证失败: {validation_result['error']}")
                    return None
                
                # 检查房主是否已在其他房间
                if self._is_player_in_room(host_player):
                    self.logger.error(f"房主 {host_player} 已在其他房间中")
                    return None
                
                # 创建房间
                room = super().create_room(room_name, host_player, max_players, game_mode, difficulty)
                if not room:
                    self.logger.error("基础房间创建失败")
                    return None
                
                # 初始化房间事件系统
                self.room_events[room.room_id] = []
                self.event_subscribers[room.room_id] = set()
                
                # 记录房间创建事件
                self._add_room_event(room.room_id, RoomEventType.ROOM_SETTINGS_CHANGED, {
                    "action": "room_created",
                    "room_name": room_name,
                    "host_player": host_player,
                    "max_players": max_players,
                    "game_mode": game_mode,
                    "difficulty": difficulty
                })
                
                self.logger.info(f"房间创建成功 - ID: {room.room_id}")
                return room
                
            except Exception as e:
                self.logger.error(f"安全创建房间时发生异常: {e}")
                return None
    
    def join_room_safe(self, room_id: str, session, password: str = "") -> Dict[str, Any]:
        """
        安全加入房间 - 修复连接断开问题
        
        Returns:
            加入结果字典
        """
        with self.room_sync_lock:
            try:
                self.logger.info(f"玩家 {session.player_name} 尝试安全加入房间 {room_id}")
                
                # 检查玩家是否已在其他房间
                if session.current_room and session.current_room != room_id:
                    self.logger.warning(f"玩家 {session.player_name} 已在房间 {session.current_room} 中")
                    return {
                        "success": False,
                        "error": "already_in_room",
                        "message": "您已在其他房间中，请先离开当前房间"
                    }
                
                # 监控连接状态
                self.connection_monitor.monitor_session(session)
                
                # 调用基础加入房间方法
                join_result = super().join_room(room_id, session, password)
                
                if join_result["success"]:
                    # 订阅房间事件
                    self.event_subscribers[room_id].add(session.session_id)
                    
                    # 记录玩家加入事件
                    self._add_room_event(room_id, RoomEventType.PLAYER_JOINED, {
                        "player_name": session.player_name,
                        "session_id": session.session_id,
                        "player_count": len(self.get_room(room_id).players)
                    })
                    
                    # 立即同步房间状态
                    self._sync_room_state(room_id)
                    
                    self.logger.info(f"玩家 {session.player_name} 成功加入房间 {room_id}")
                
                return join_result
                
            except Exception as e:
                self.logger.error(f"安全加入房间时发生异常: {e}")
                return {
                    "success": False,
                    "error": "internal_error",
                    "message": f"加入房间时发生内部错误: {str(e)}"
                }
    
    def leave_room_safe(self, room_id: str, session) -> bool:
        """
        安全离开房间 - 修复连接断开问题
        
        Returns:
            是否成功离开
        """
        with self.room_sync_lock:
            try:
                self.logger.info(f"玩家 {session.player_name} 尝试安全离开房间 {room_id}")
                
                # 取消连接监控
                self.connection_monitor.unmonitor_session(session)
                
                # 取消订阅房间事件
                if room_id in self.event_subscribers:
                    self.event_subscribers[room_id].discard(session.session_id)
                
                # 调用基础离开房间方法
                leave_result = super().leave_room(room_id, session)
                
                if leave_result:
                    # 记录玩家离开事件
                    room = self.get_room(room_id)
                    if room:
                        self._add_room_event(room_id, RoomEventType.PLAYER_LEFT, {
                            "player_name": session.player_name,
                            "session_id": session.session_id,
                            "player_count": len(room.players)
                        })
                        
                        # 同步房间状态
                        self._sync_room_state(room_id)
                    
                    self.logger.info(f"玩家 {session.player_name} 成功离开房间 {room_id}")
                
                return leave_result
                
            except Exception as e:
                self.logger.error(f"安全离开房间时发生异常: {e}")
                return False
    
    def set_player_ready_safe(self, room_id: str, session_id: str, ready: bool):
        """
        安全设置玩家准备状态 - 修复状态同步问题
        """
        with self.room_sync_lock:
            try:
                room = self.get_room(room_id)
                if not room:
                    self.logger.error(f"房间 {room_id} 不存在")
                    return
                
                # 调用基础方法
                super().set_player_ready(room_id, session_id, ready)
                
                # 记录准备状态变更事件
                self._add_room_event(room_id, RoomEventType.PLAYER_READY_CHANGED, {
                    "session_id": session_id,
                    "ready": ready,
                    "ready_count": room.get_ready_count(),
                    "total_players": room.get_player_count()
                })
                
                # 立即同步房间状态
                self._sync_room_state(room_id)
                
                self.logger.info(f"玩家 {session_id} 准备状态已更新: {ready}")
                
            except Exception as e:
                self.logger.error(f"安全设置玩家准备状态时发生异常: {e}")
    
    def get_room_waiting_info(self, room_id: str) -> Optional[Dict[str, Any]]:
        """
        获取房间等待界面信息
        
        Returns:
            房间等待界面数据
        """
        room = self.get_room(room_id)
        if not room:
            return None
        
        # 获取玩家列表详细信息
        players_info = []
        for session_id, session in room.players.items():
            players_info.append({
                "session_id": session_id,
                "player_name": session.player_name,
                "is_ready": room.player_ready_status.get(session_id, False),
                "is_host": session_id == room.host_player,
                "connection_status": self.connection_monitor.get_connection_status(session),
                "join_time": getattr(session, 'join_time', time.time())
            })
        
        # 获取房间设置
        room_settings = {
            "room_name": room.room_name,
            "game_mode": room.game_mode,
            "difficulty": room.difficulty,
            "max_players": room.max_players,
            "is_private": room.is_private
        }
        
        # 获取游戏状态
        game_status = {
            "can_start": room.can_start_game()["can_start"],
            "start_reason": room.can_start_game()["message"],
            "countdown_active": False  # TODO: 实现倒计时状态
        }
        
        # 获取最近事件
        recent_events = self._get_recent_room_events(room_id, limit=10)
        
        return {
            "room_id": room_id,
            "status": room.status.value,
            "players": players_info,
            "settings": room_settings,
            "game_status": game_status,
            "recent_events": recent_events,
            "last_update": time.time()
        }
    
    def _validate_room_parameters_enhanced(self, room_name: str, max_players: int,
                                         host_player: str) -> Dict[str, Any]:
        """增强的房间参数验证"""
        # 调用基础验证
        base_result = super()._validate_room_parameters(room_name, max_players, host_player)
        if not base_result["valid"]:
            return base_result
        
        # 额外验证
        # 检查房间名称是否重复
        for room in self.rooms.values():
            if room.room_name == room_name and room.status != RoomStatus.CLOSED:
                return {"valid": False, "error": "房间名称已存在"}
        
        return {"valid": True, "error": None}
    
    def _is_player_in_room(self, player_id: str) -> bool:
        """检查玩家是否已在房间中"""
        for room in self.rooms.values():
            if player_id in room.players and room.status != RoomStatus.CLOSED:
                return True
        return False
    
    def _add_room_event(self, room_id: str, event_type: RoomEventType, data: Dict[str, Any]):
        """添加房间事件"""
        if room_id not in self.room_events:
            self.room_events[room_id] = []
        
        event = RoomEvent(event_type, room_id, data)
        self.room_events[room_id].append(event)
        
        # 限制事件历史长度
        if len(self.room_events[room_id]) > 100:
            self.room_events[room_id] = self.room_events[room_id][-50:]
    
    def _get_recent_room_events(self, room_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的房间事件"""
        if room_id not in self.room_events:
            return []
        
        events = self.room_events[room_id][-limit:]
        return [
            {
                "type": event.event_type.value,
                "data": event.data,
                "timestamp": event.timestamp
            }
            for event in events
        ]
    
    def _sync_room_state(self, room_id: str):
        """同步房间状态到所有订阅者"""
        if room_id not in self.event_subscribers:
            return
        
        waiting_info = self.get_room_waiting_info(room_id)
        if not waiting_info:
            return
        
        # 标记需要同步
        self.pending_room_updates[room_id] = waiting_info
    
    def _room_sync_worker(self):
        """房间状态同步工作线程"""
        while True:
            try:
                if self.pending_room_updates:
                    with self.room_sync_lock:
                        updates_to_send = self.pending_room_updates.copy()
                        self.pending_room_updates.clear()
                    
                    for room_id, update_data in updates_to_send.items():
                        self._broadcast_room_update_safe(room_id, update_data)
                
                time.sleep(0.1)  # 100ms 同步间隔
                
            except Exception as e:
                self.logger.error(f"房间状态同步工作线程错误: {e}")
                time.sleep(1)
    
    def _connection_monitor_worker(self):
        """连接监控工作线程"""
        while True:
            try:
                self.connection_monitor.check_connections()
                time.sleep(5)  # 5秒检查一次
                
            except Exception as e:
                self.logger.error(f"连接监控工作线程错误: {e}")
                time.sleep(5)
    
    def _broadcast_room_update_safe(self, room_id: str, update_data: Dict[str, Any]):
        """安全广播房间更新"""
        if room_id not in self.event_subscribers:
            return
        
        subscribers = self.event_subscribers[room_id].copy()
        failed_subscribers = []
        
        for session_id in subscribers:
            try:
                # 这里需要通过会话管理器获取会话并发送消息
                # 具体实现依赖于消息发送机制
                success = self._send_room_update_to_session(session_id, update_data)
                if not success:
                    failed_subscribers.append(session_id)
                    
            except Exception as e:
                self.logger.error(f"向会话 {session_id} 发送房间更新失败: {e}")
                failed_subscribers.append(session_id)
        
        # 清理失败的订阅者
        for session_id in failed_subscribers:
            self.event_subscribers[room_id].discard(session_id)
    
    def _send_room_update_to_session(self, session_id: str, update_data: Dict[str, Any]) -> bool:
        """向指定会话发送房间更新"""
        # 这个方法需要与消息管理器集成
        # 暂时返回True，实际实现需要在集成时完成
        return True
    
    def cleanup_room_safe(self, room_id: str):
        """安全清理房间"""
        with self.room_sync_lock:
            try:
                # 清理事件数据
                self.room_events.pop(room_id, None)
                self.event_subscribers.pop(room_id, None)
                self.pending_room_updates.pop(room_id, None)
                
                # 调用基础清理
                self._close_room(room_id)
                
                self.logger.info(f"房间 {room_id} 已安全清理")
                
            except Exception as e:
                self.logger.error(f"安全清理房间时发生异常: {e}")


class ConnectionMonitor:
    """连接状态监控器"""
    
    def __init__(self):
        self.monitored_sessions: Dict[str, Any] = {}
        self.logger = get_server_logger("ConnectionMonitor")
    
    def monitor_session(self, session):
        """开始监控会话"""
        self.monitored_sessions[session.session_id] = {
            "session": session,
            "last_check": time.time(),
            "status": "connected"
        }
    
    def unmonitor_session(self, session):
        """停止监控会话"""
        self.monitored_sessions.pop(session.session_id, None)
    
    def get_connection_status(self, session) -> str:
        """获取连接状态"""
        session_info = self.monitored_sessions.get(session.session_id)
        if not session_info:
            return "unknown"
        return session_info["status"]
    
    def check_connections(self):
        """检查所有监控的连接"""
        current_time = time.time()
        disconnected_sessions = []
        
        for session_id, info in self.monitored_sessions.items():
            try:
                session = info["session"]
                
                # 检查会话是否仍然连接
                if not session.is_connected:
                    info["status"] = "disconnected"
                    disconnected_sessions.append(session_id)
                    continue
                
                # 检查空闲时间
                if hasattr(session, 'last_activity'):
                    idle_time = current_time - session.last_activity
                    if idle_time > 300:  # 5分钟无活动
                        info["status"] = "idle"
                    else:
                        info["status"] = "active"
                
                info["last_check"] = current_time
                
            except Exception as e:
                self.logger.error(f"检查会话 {session_id} 连接状态时发生错误: {e}")
                disconnected_sessions.append(session_id)
        
        # 清理断开连接的会话
        for session_id in disconnected_sessions:
            self.monitored_sessions.pop(session_id, None)