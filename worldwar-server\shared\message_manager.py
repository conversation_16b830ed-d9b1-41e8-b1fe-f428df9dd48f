#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一消息管理器
Unified Message Manager
"""

import socket
import time
from typing import Dict, Any, Optional, Callable
from shared.enhanced_logger import get_server_logger
from shared.secure_protocol import SecureProtocol
from shared.message_types import MessageType


class MessageManager:
    """统一的消息发送和接收管理器"""

    def __init__(self, server_mode: bool = False, protocol: SecureProtocol = None):
        """
        初始化消息管理器

        Args:
            server_mode: 是否为服务器模式
            protocol: 现有的协议实例（可选）
        """
        self.logger = get_server_logger("MessageManager")
        self.server_mode = server_mode
        self.protocol = protocol or SecureProtocol(server_mode)
        
        # 消息统计
        self.messages_sent = 0
        self.messages_received = 0
        self.send_errors = 0
        self.receive_errors = 0
        
        self.logger.info(f"消息管理器初始化完成 - {'服务器' if server_mode else '客户端'}模式")
    
    def send_secure_message(self, sock: socket.socket, message_type: str, 
                           data: Dict[str, Any] = None, **kwargs) -> bool:
        """
        发送安全消息（统一接口）
        
        Args:
            sock: 套接字
            message_type: 消息类型
            data: 消息数据
            **kwargs: 额外的消息字段
            
        Returns:
            是否发送成功
        """
        try:
            # 构建完整消息
            message = {
                "type": message_type,
                "data": data or {}
            }
            
            # 添加额外字段
            message.update(kwargs)
            
            # 发送消息
            if self.protocol.send_secure_message(sock, message):
                self.messages_sent += 1
                self.logger.debug(f"发送安全消息成功: {message_type}")
                return True
            else:
                self.send_errors += 1
                self.logger.error(f"发送安全消息失败: {message_type}")
                return False
                
        except Exception as e:
            self.send_errors += 1
            self.logger.error(f"发送安全消息异常 [{message_type}]: {e}")
            return False
    
    def send_response(self, sock: socket.socket, request_type: str, 
                     success: bool = True, data: Dict[str, Any] = None, 
                     error_message: str = "", **kwargs) -> bool:
        """
        发送响应消息（统一接口）
        
        Args:
            sock: 套接字
            request_type: 原始请求类型
            success: 是否成功
            data: 响应数据
            error_message: 错误消息
            **kwargs: 额外字段
            
        Returns:
            是否发送成功
        """
        # 根据请求类型确定响应类型
        response_type = self._get_response_type(request_type)
        
        # 构建响应数据
        response_data = data or {}
        if not success and error_message:
            response_data["error"] = error_message
            response_data["success"] = False
        elif success:
            response_data["success"] = True
        
        return self.send_secure_message(sock, response_type, response_data, **kwargs)
    
    def send_error(self, sock: socket.socket, error_message: str, 
                   error_code: str = "general_error") -> bool:
        """
        发送错误消息
        
        Args:
            sock: 套接字
            error_message: 错误消息
            error_code: 错误代码
            
        Returns:
            是否发送成功
        """
        return self.send_secure_message(
            sock, 
            MessageType.ERROR, 
            {
                "message": error_message,
                "error_code": error_code,
                "timestamp": time.time()
            }
        )
    
    def receive_secure_message(self, sock: socket.socket, 
                              timeout: float = 5.0) -> Optional[Dict[str, Any]]:
        """
        接收安全消息
        
        Args:
            sock: 套接字
            timeout: 超时时间
            
        Returns:
            接收到的消息或None
        """
        try:
            message = self.protocol.receive_secure_message(sock, timeout)
            if message:
                self.messages_received += 1
                self.logger.debug(f"接收安全消息成功: {message.get('type', 'unknown')}")
                return message
            else:
                return None
                
        except Exception as e:
            self.receive_errors += 1
            self.logger.error(f"接收安全消息异常: {e}")
            return None
    
    def _get_response_type(self, request_type: str) -> str:
        """
        根据请求类型获取对应的响应类型
        
        Args:
            request_type: 请求类型
            
        Returns:
            响应类型
        """
        response_mapping = {
            MessageType.JOIN_GAME: "join_game_response",
            MessageType.CREATE_ROOM: MessageType.ROOM_CREATED,
            MessageType.JOIN_ROOM: MessageType.ROOM_JOINED,
            MessageType.LEAVE_ROOM: MessageType.ROOM_LEFT,
            MessageType.GET_ROOM_LIST: MessageType.ROOM_LIST,
            "get_game_status": "game_status",
            "start_game": "game_started",
            MessageType.PING: MessageType.PONG
        }
        
        return response_mapping.get(request_type, f"{request_type}_response")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取消息统计信息
        
        Returns:
            统计信息字典
        """
        return {
            "messages_sent": self.messages_sent,
            "messages_received": self.messages_received,
            "send_errors": self.send_errors,
            "receive_errors": self.receive_errors,
            "success_rate": (
                (self.messages_sent - self.send_errors) / max(self.messages_sent, 1) * 100
                if self.messages_sent > 0 else 0
            )
        }


class ServerMessageManager(MessageManager):
    """服务器端消息管理器"""

    def __init__(self, protocol: SecureProtocol = None):
        super().__init__(server_mode=True, protocol=protocol)
    
    def send_welcome(self, sock: socket.socket, session_id: str, 
                    server_name: str = "安全世界大战游戏服务器") -> bool:
        """发送欢迎消息"""
        return self.send_secure_message(
            sock,
            MessageType.WELCOME,
            {
                "server_name": server_name,
                "version": "2.0",
                "session_id": session_id,
                "heartbeat_interval": 30
            }
        )
    
    def send_room_list(self, sock: socket.socket, rooms: list) -> bool:
        """发送房间列表"""
        return self.send_secure_message(
            sock,
            MessageType.ROOM_LIST,
            {"rooms": rooms}
        )
    
    def send_room_created(self, sock: socket.socket, room_info: Dict[str, Any]) -> bool:
        """发送房间创建成功消息"""
        return self.send_secure_message(
            sock,
            MessageType.ROOM_CREATED,
            room_info
        )


class ClientMessageManager(MessageManager):
    """客户端消息管理器"""

    def __init__(self, protocol: SecureProtocol = None):
        super().__init__(server_mode=False, protocol=protocol)
    
    def send_join_game(self, sock: socket.socket, player_name: str) -> bool:
        """发送加入游戏请求"""
        return self.send_secure_message(
            sock,
            MessageType.JOIN_GAME,
            {"player_name": player_name}
        )
    
    def send_create_room(self, sock: socket.socket, room_name: str, 
                        max_players: int, game_mode: str) -> bool:
        """发送创建房间请求"""
        return self.send_secure_message(
            sock,
            MessageType.CREATE_ROOM,
            {
                "room_name": room_name,
                "max_players": max_players,
                "game_mode": game_mode
            }
        )
    
    def send_get_room_list(self, sock: socket.socket) -> bool:
        """发送获取房间列表请求"""
        return self.send_secure_message(
            sock,
            MessageType.GET_ROOM_LIST,
            {}
        )
