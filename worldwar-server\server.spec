# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['server_main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('languages/*.json', 'languages'),
        ('config/*.ini', 'config'),
        ('shared', 'shared'),
        ('server', 'server'),
        ('utils', 'utils'),
        ('core', 'core'),
        ('game_logic', 'game_logic'),
        ('world', 'world'),
        ('data', 'data')
    ],
    hiddenimports=[
        'shared.enhanced_logger',
        'shared.language_manager',
        'shared.security_manager',
        'server.game_server',
        'server.room_manager',
        'server.player_manager',
        'utils.process_lock',
        'utils.signal_handler',
        'core.game_core',
        'game_logic.game_state',
        'world.world_data',
        'json',
        'socket',
        'threading',
        'hashlib',
        'hmac',
        'secrets',
        'configparser',
        'pathlib'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='WorldWarServer',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    cofile=None,
    icon=None,
)
