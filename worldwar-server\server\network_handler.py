#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络处理器模块
Network Handler Module
"""

import socket
import json
import struct
import time
from typing import Dict, Any, Optional
from shared.protocol import Protocol

class NetworkHandler:
    """网络通信处理器"""
    
    # 协议常量
    HEADER_SIZE = 4
    MAX_MESSAGE_SIZE = 1024 * 1024  # 1MB
    ENCODING = "utf-8"
    
    def __init__(self):
        """初始化网络处理器"""
        # 导入日志
        from shared.enhanced_logger import get_server_logger
        self.logger = get_server_logger("NetworkHandler")

    def set_socket_options(self, sock: socket.socket):
        """设置套接字选项以提高连接稳定性"""
        try:
            # 启用地址重用
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

            # 设置TCP_NODELAY以减少延迟
            sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)

            # 设置keepalive以检测断开的连接
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)

            # 在Windows上设置keepalive参数
            import platform
            if platform.system() == "Windows":
                # Windows特定的keepalive设置
                sock.ioctl(socket.SIO_KEEPALIVE_VALS, (1, 30000, 5000))  # 30秒间隔，5秒超时
            else:
                # Linux/Unix keepalive设置
                try:
                    sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPIDLE, 30)  # 30秒后开始keepalive
                    sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPINTVL, 5)  # 5秒间隔
                    sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPCNT, 3)    # 3次重试
                except AttributeError:
                    # 某些系统可能不支持这些选项
                    pass

            # 设置发送和接收缓冲区大小
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 65536)  # 64KB发送缓冲区
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 65536)  # 64KB接收缓冲区

            self.logger.debug("套接字选项设置完成")

        except Exception as e:
            self.logger.warning(f"设置套接字选项时出现警告: {e}")
            # 不抛出异常，因为这些选项不是必需的
    
    def send_message(self, sock: socket.socket, message: Dict[str, Any]) -> bool:
        """发送消息到套接字"""
        try:
            # 添加时间戳
            message["timestamp"] = time.time()
            
            # 编码消息
            encoded_data = self._encode_message(message)
            
            # 发送所有数据
            total_sent = 0
            while total_sent < len(encoded_data):
                try:
                    sent = sock.send(encoded_data[total_sent:])
                    if sent == 0:
                        self.logger.warning("套接字连接断开")
                        return False
                    total_sent += sent
                except socket.error as e:
                    self.logger.error(f"发送数据时发生套接字错误: {e}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            return False
    
    def receive_message(self, sock: socket.socket, timeout: float = 30.0) -> Optional[Dict[str, Any]]:
        """从套接字接收消息"""
        try:
            # 设置超时
            original_timeout = sock.gettimeout()
            sock.settimeout(timeout)
            
            try:
                # 首先接收消息头（4字节）
                header_data = self._receive_exact(sock, self.HEADER_SIZE)
                if not header_data:
                    return None
                
                # 解析消息长度
                message_length = struct.unpack("!I", header_data)[0]
                
                # 验证消息长度
                if message_length > self.MAX_MESSAGE_SIZE:
                    self.logger.error(f"消息过大: {message_length} > {self.MAX_MESSAGE_SIZE}")
                    return None
                
                if message_length == 0:
                    self.logger.warning("收到空消息")
                    return None
                
                # 接收消息体
                message_data = self._receive_exact(sock, message_length)
                if not message_data:
                    return None
                
                # 解码消息
                return self._decode_message(message_data)
                
            finally:
                # 恢复原始超时设置
                sock.settimeout(original_timeout)
                
        except socket.timeout:
            # 超时不记录为错误，这是正常的
            return None
        except socket.error as e:
            self.logger.error(f"接收消息时发生套接字错误: {e}")
            return None
        except Exception as e:
            self.logger.error(f"接收消息失败: {e}")
            return None
    
    def _receive_exact(self, sock: socket.socket, length: int) -> Optional[bytes]:
        """精确接收指定长度的数据"""
        data = b""
        while len(data) < length:
            try:
                chunk = sock.recv(length - len(data))
                if not chunk:
                    if len(data) > 0:
                        self.logger.warning(f"连接意外断开，已接收 {len(data)}/{length} 字节")
                    return None
                data += chunk
            except socket.error as e:
                self.logger.error(f"接收数据时发生错误: {e}")
                return None
        
        return data
    
    def _encode_message(self, message: Dict[str, Any]) -> bytes:
        """将消息编码为字节流"""
        try:
            # 将消息转换为JSON字符串
            json_str = json.dumps(message, ensure_ascii=False, separators=(',', ':'))
            
            # 编码为字节
            message_bytes = json_str.encode(self.ENCODING)
            
            # 检查消息长度
            if len(message_bytes) > self.MAX_MESSAGE_SIZE:
                raise ValueError(f"消息过大: {len(message_bytes)} > {self.MAX_MESSAGE_SIZE}")
            
            # 创建消息头（消息长度）
            header = struct.pack("!I", len(message_bytes))
            
            # 返回头部 + 消息体
            return header + message_bytes
            
        except Exception as e:
            self.logger.error(f"消息编码失败: {e}")
            raise
    
    def _decode_message(self, data: bytes) -> Optional[Dict[str, Any]]:
        """将字节流解码为消息"""
        try:
            # 解码JSON字符串
            json_str = data.decode(self.ENCODING)
            
            # 解析JSON
            message = json.loads(json_str)
            
            # 验证消息格式
            if not isinstance(message, dict):
                self.logger.error("消息格式错误：不是字典类型")
                return None
            
            return message
            
        except UnicodeDecodeError as e:
            self.logger.error(f"消息解码失败（编码错误）: {e}")
            return None
        except json.JSONDecodeError as e:
            self.logger.error(f"消息解码失败（JSON错误）: {e}")
            return None
        except Exception as e:
            self.logger.error(f"消息解码失败: {e}")
            return None
    
    def create_response_message(self, msg_type: str, data: Dict[str, Any] = None, 
                              success: bool = True, error_message: str = "") -> Dict[str, Any]:
        """创建响应消息"""
        message = {
            "type": msg_type,
            "success": success,
            "data": data or {}
        }
        
        if not success and error_message:
            message["error"] = error_message
        
        return message
    
    def create_error_message(self, error_message: str, error_code: int = 0) -> Dict[str, Any]:
        """创建错误消息"""
        return {
            "type": "error",
            "success": False,
            "error": error_message,
            "error_code": error_code,
            "data": {}
        }
    
    def create_success_message(self, msg_type: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """创建成功消息"""
        return {
            "type": msg_type,
            "success": True,
            "data": data or {}
        }
    
    def validate_message(self, message: Dict[str, Any]) -> bool:
        """验证消息格式（使用统一的协议验证）"""
        try:
            # 使用统一的协议验证，避免重复代码
            is_valid = Protocol.validate_message(message)

            if not is_valid:
                self.logger.warning(f"消息验证失败: {message}")

            return is_valid

        except Exception as e:
            self.logger.error(f"验证消息时发生错误: {e}")
            return False
    
    def check_connection(self, sock: socket.socket) -> bool:
        """检查连接是否有效"""
        try:
            # 发送心跳消息
            ping_message = {
                "type": "ping",
                "data": {}
            }
            
            if not self.send_message(sock, ping_message):
                return False
            
            # 等待pong响应
            response = self.receive_message(sock, timeout=5.0)
            if response and response.get("type") == "pong":
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查连接时发生错误: {e}")
            return False
    
    def get_socket_info(self, sock: socket.socket) -> Dict[str, Any]:
        """获取套接字信息"""
        try:
            local_addr = sock.getsockname()
            try:
                remote_addr = sock.getpeername()
            except:
                remote_addr = ("unknown", 0)
            
            return {
                "local_address": f"{local_addr[0]}:{local_addr[1]}",
                "remote_address": f"{remote_addr[0]}:{remote_addr[1]}",
                "family": sock.family.name,
                "type": sock.type.name,
                "timeout": sock.gettimeout()
            }
        except Exception as e:
            self.logger.error(f"获取套接字信息失败: {e}")
            return {}
    
    def close_socket_safely(self, sock: socket.socket):
        """安全关闭套接字"""
        try:
            # 发送断开连接消息
            disconnect_message = {
                "type": "disconnect",
                "data": {"reason": "server_shutdown"}
            }
            
            # 尝试发送断开消息（不等待响应）
            try:
                self.send_message(sock, disconnect_message)
            except:
                pass  # 忽略发送失败
            
            # 关闭套接字
            sock.close()
            
        except Exception as e:
            self.logger.error(f"关闭套接字时发生错误: {e}")
    
    def set_socket_options(self, sock: socket.socket):
        """设置套接字选项"""
        try:
            # 启用地址重用
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            
            # 设置TCP_NODELAY（禁用Nagle算法）
            sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
            
            # 设置发送和接收缓冲区大小
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 65536)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 65536)
            
            self.logger.debug("套接字选项设置完成")
            
        except Exception as e:
            self.logger.warning(f"设置套接字选项时发生错误: {e}")
    
    def get_network_statistics(self) -> Dict[str, Any]:
        """获取网络统计信息"""
        # 这里可以添加网络统计信息的收集
        # 目前返回基本信息
        return {
            "max_message_size": self.MAX_MESSAGE_SIZE,
            "header_size": self.HEADER_SIZE,
            "encoding": self.ENCODING
        }
