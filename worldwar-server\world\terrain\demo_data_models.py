"""
地形数据模型演示脚本

展示如何使用地形数据模型创建、验证和序列化地形数据。
"""

import sys
import os
import json
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from data_models import (
    TerrainCell, City, Region, BiomeType, ResourceType,
    TerrainDataValidator, serialize_region_to_json, deserialize_region_from_json
)


def create_sample_terrain(width=5, height=5):
    """创建示例地形数据"""
    print(f"创建 {width}x{height} 的示例地形...")
    
    terrain = []
    for y in range(height):
        row = []
        for x in range(width):
            # 根据位置创建不同的地形
            if x == 0 or x == width-1 or y == 0 or y == height-1:
                # 边缘为山地
                elevation = 800 + (x + y) * 50
                biome = BiomeType.MOUNTAIN.value
                temperature = 10.0
                precipitation = 1200.0
                resources = {ResourceType.STONE.value: 0.8, ResourceType.IRON.value: 0.4}
            elif x == width//2 and y == height//2:
                # 中心为湖泊
                elevation = -5
                biome = BiomeType.OCEAN.value
                temperature = 18.0
                precipitation = 2000.0
                resources = {ResourceType.WATER.value: 1.0}
            else:
                # 其他为平原或森林
                elevation = 100 + (x * y) * 10
                biome = BiomeType.FOREST.value if (x + y) % 2 == 0 else BiomeType.PLAINS.value
                temperature = 20.0 + x * 2
                precipitation = 800.0 + y * 100
                resources = {
                    ResourceType.WOOD.value: 0.6 if biome == BiomeType.FOREST.value else 0.2,
                    ResourceType.FOOD.value: 0.4 if biome == BiomeType.PLAINS.value else 0.7,
                    ResourceType.WATER.value: 0.5
                }
            
            cell = TerrainCell(
                x=x, y=y, elevation=elevation, biome=biome,
                temperature=temperature, precipitation=precipitation,
                resources=resources
            )
            row.append(cell)
        terrain.append(row)
    
    print(f"✓ 成功创建地形，包含 {len(terrain)} 行 {len(terrain[0])} 列")
    return terrain


def create_sample_cities():
    """创建示例城市数据"""
    print("创建示例城市...")
    
    cities = [
        City(
            name="山城",
            population=800000,
            gdp_per_capita=18000,
            poverty_rate=0.15,
            coordinates=(45.5, 90.2),
            resources={ResourceType.STONE.value: 0.7, "mining": 0.8},
            infrastructure_level=0.6
        ),
        City(
            name="湖心镇",
            population=200000,
            gdp_per_capita=22000,
            poverty_rate=0.08,
            coordinates=(45.0, 90.0),
            resources={ResourceType.WATER.value: 0.9, "fishing": 0.8, "tourism": 0.6},
            infrastructure_level=0.7
        ),
        City(
            name="平原市",
            population=1500000,
            gdp_per_capita=25000,
            poverty_rate=0.06,
            coordinates=(44.8, 89.8),
            resources={ResourceType.FOOD.value: 0.9, "agriculture": 0.85, "manufacturing": 0.7},
            infrastructure_level=0.8
        )
    ]
    
    print(f"✓ 成功创建 {len(cities)} 个城市")
    for city in cities:
        print(f"  - {city.name}: 人口 {city.population:,}, GDP {city.gdp_per_capita:,}美元, {city.get_economic_level()}")
    
    return cities


def demonstrate_data_models():
    """演示数据模型的功能"""
    print("=" * 60)
    print("地形数据模型演示")
    print("=" * 60)
    
    # 1. 创建地形和城市数据
    terrain = create_sample_terrain()
    cities = create_sample_cities()
    
    # 2. 创建地区
    print("\n创建示例地区...")
    region = Region(
        name="示例山区",
        country="示例国家",
        center=(45.0, 90.0),
        terrain=terrain,
        cities=cities,
        economic_data={
            "gdp": 50000000000,  # 500亿
            "growth_rate": 0.055,
            "main_industries": ["采矿", "农业", "旅游"]
        },
        demographic_data={
            "total_population": 2500000,
            "urban_rate": 0.6,
            "education_level": 0.7
        }
    )
    
    print(f"✓ 成功创建地区: {region.name}")
    print(f"  - 国家: {region.country}")
    print(f"  - 中心坐标: {region.center}")
    print(f"  - 地形尺寸: {region.get_terrain_size()}")
    print(f"  - 城市数量: {len(region.cities)}")
    
    # 3. 验证数据
    print("\n验证地区数据...")
    try:
        region.validate()
        print("✓ 地区数据验证通过")
    except ValueError as e:
        print(f"✗ 地区数据验证失败: {e}")
    
    # 4. 统计信息
    print("\n地区统计信息:")
    print(f"  - 总人口: {region.get_total_population():,}")
    print(f"  - 平均人均GDP: ${region.get_average_gdp_per_capita():,.2f}")
    
    # 5. 资源分布
    print("\n资源分布:")
    resources = region.get_resource_summary()
    for resource, amount in sorted(resources.items()):
        print(f"  - {resource}: {amount:.3f}")
    
    # 6. 生物群系分布
    print("\n生物群系分布:")
    biomes = region.get_biome_distribution()
    for biome, count in sorted(biomes.items()):
        print(f"  - {biome}: {count} 个单元格")
    
    # 7. 数据验证
    print("\n运行数据验证器...")
    terrain_warnings = TerrainDataValidator.validate_terrain_consistency(region)
    city_warnings = TerrainDataValidator.validate_city_positions(region)
    
    if terrain_warnings:
        print("地形一致性警告:")
        for warning in terrain_warnings:
            print(f"  ⚠ {warning}")
    else:
        print("✓ 地形一致性检查通过")
    
    if city_warnings:
        print("城市位置警告:")
        for warning in city_warnings:
            print(f"  ⚠ {warning}")
    else:
        print("✓ 城市位置检查通过")
    
    # 8. 序列化测试
    print("\n测试数据序列化...")
    output_file = "sample_region.json"
    
    try:
        serialize_region_to_json(region, output_file)
        print(f"✓ 数据已序列化到: {output_file}")
        
        # 检查文件大小
        file_size = os.path.getsize(output_file)
        print(f"  文件大小: {file_size:,} 字节")
        
        # 反序列化测试
        restored_region = deserialize_region_from_json(output_file)
        print("✓ 数据反序列化成功")
        
        # 验证数据完整性
        assert restored_region.name == region.name
        assert restored_region.get_total_population() == region.get_total_population()
        assert restored_region.get_terrain_size() == region.get_terrain_size()
        print("✓ 数据完整性验证通过")
        
    except Exception as e:
        print(f"✗ 序列化测试失败: {e}")
    
    # 9. 展示具体地形单元格信息
    print("\n地形单元格示例:")
    center_x, center_y = 2, 2
    center_cell = region.get_terrain_cell(center_x, center_y)
    if center_cell:
        print(f"  位置: ({center_cell.x}, {center_cell.y})")
        print(f"  海拔: {center_cell.elevation}m")
        print(f"  生物群系: {center_cell.biome}")
        print(f"  温度: {center_cell.temperature}°C")
        print(f"  降水量: {center_cell.precipitation}mm/年")
        print(f"  适宜居住: {'是' if center_cell.is_habitable() else '否'}")
        print(f"  资源丰富度: {center_cell.get_resource_richness():.3f}")
    
    # 10. 城市详细信息
    print("\n城市详细信息:")
    for city in region.cities:
        print(f"  {city.name}:")
        print(f"    经济水平: {city.get_economic_level()}")
        print(f"    城市评分: {city.calculate_city_score():.3f}")
        print(f"    基础设施: {city.infrastructure_level:.1%}")
    
    print("\n" + "=" * 60)
    print("演示完成！")
    print("=" * 60)


if __name__ == "__main__":
    demonstrate_data_models()