# pytest 配置和共享fixtures
# pytest configuration and shared fixtures

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, AsyncMock
import json
import time

# 异步测试支持
@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环用于异步测试"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

# 临时目录fixture
@pytest.fixture
def temp_dir():
    """创建临时目录用于测试"""
    temp_path = Path(tempfile.mkdtemp())
    yield temp_path
    shutil.rmtree(temp_path, ignore_errors=True)

# 测试数据目录
@pytest.fixture
def test_data_dir():
    """测试数据目录"""
    return Path(__file__).parent / "data"

# 模拟配置
@pytest.fixture
def mock_config():
    """模拟配置对象"""
    return {
        "server": {
            "host": "localhost",
            "port": 8888,
            "max_connections": 10
        },
        "game": {
            "max_players": 8,
            "default_language": "chinese"
        },
        "terrain": {
            "default_size": (512, 512),
            "cache_enabled": True
        },
        "api": {
            "timeout": 30,
            "retry_count": 3
        }
    }

# 模拟会话
@pytest.fixture
def mock_session():
    """模拟玩家会话"""
    session = Mock()
    session.session_id = "test_session_123"
    session.player_name = "TestPlayer"
    session.authenticated = True
    session.current_room = None
    session.language = "chinese"
    session.send_message = AsyncMock()
    session.is_alive = Mock(return_value=True)
    return session

# 模拟房间
@pytest.fixture
def mock_room():
    """模拟游戏房间"""
    room = Mock()
    room.room_id = "test_room_123"
    room.name = "测试房间"
    room.host_id = "test_session_123"
    room.players = []
    room.max_players = 4
    room.status = "waiting"
    room.terrain_type = "perlin"
    return room

# 模拟API响应
@pytest.fixture
def mock_api_response():
    """模拟API响应数据"""
    return {
        "world_bank": {
            "country": "CN",
            "indicator": "NY.GDP.PCAP.CD",
            "value": 10500.0,
            "date": "2023"
        },
        "geographic": {
            "features": [
                {
                    "type": "Feature",
                    "properties": {
                        "name": "广州",
                        "population": ********
                    },
                    "geometry": {
                        "type": "Point",
                        "coordinates": [113.2644, 23.1291]
                    }
                }
            ]
        }
    }

# 模拟地形数据
@pytest.fixture
def mock_terrain_data():
    """模拟地形数据"""
    return {
        "size": (64, 64),
        "heightmap": [[0.5 for _ in range(64)] for _ in range(64)],
        "biomes": [["plains" for _ in range(64)] for _ in range(64)],
        "resources": {
            "oil": [[0.1 for _ in range(64)] for _ in range(64)],
            "minerals": [[0.2 for _ in range(64)] for _ in range(64)]
        }
    }

# 语言数据fixture
@pytest.fixture
def mock_language_data():
    """模拟语言数据"""
    return {
        "chinese": {
            "game.welcome": "欢迎来到世界大战",
            "game.room_created": "房间已创建",
            "error.connection_failed": "连接失败"
        },
        "english": {
            "game.welcome": "Welcome to World War",
            "game.room_created": "Room created",
            "error.connection_failed": "Connection failed"
        }
    }