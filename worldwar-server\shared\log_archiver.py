#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志归档管理器
Log Archiver Manager

提供日志文件的归档和清理功能
Provides log file archiving and cleanup functionality
"""

import os
import shutil
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Optional
import threading

class LogArchiver:
    """日志归档管理器"""
    
    def __init__(self, max_archive_days: int = 30, max_archive_count: int = 50):
        """
        初始化日志归档管理器
        
        Args:
            max_archive_days: 最大保留天数
            max_archive_count: 最大归档文件数量
        """
        self.max_archive_days = max_archive_days
        self.max_archive_count = max_archive_count
        self.lock = threading.Lock()
    
    def archive_latest_log(self, log_type: str = "server") -> bool:
        """
        归档latest.log文件
        
        Args:
            log_type: 日志类型
            
        Returns:
            是否成功归档
        """
        with self.lock:
            try:
                from shared.log_config import LogConfig
                log_dir = LogConfig.get_log_directory(log_type)
                latest_log = log_dir / "latest.log"
                
                if not latest_log.exists():
                    return False
                
                # 创建归档文件名
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                archive_name = f"{log_type}_{timestamp}.log"
                archive_path = log_dir / archive_name
                
                # 移动文件到归档
                shutil.move(str(latest_log), str(archive_path))
                print(f"日志已归档: {archive_path}")
                
                # 清理旧的归档文件
                self._cleanup_old_archives(log_dir)
                
                return True
                
            except Exception as e:
                print(f"归档日志失败: {e}")
                return False
    
    def cleanup_old_logs(self, log_type: str = "server") -> int:
        """
        清理旧的日志文件
        
        Args:
            log_type: 日志类型
            
        Returns:
            清理的文件数量
        """
        with self.lock:
            try:
                from shared.log_config import LogConfig
                log_dir = LogConfig.get_log_directory(log_type)
                
                if not log_dir.exists():
                    return 0
                
                return self._cleanup_old_archives(log_dir)
                
            except Exception as e:
                print(f"清理旧日志失败: {e}")
                return 0
    
    def _cleanup_old_archives(self, log_dir: Path) -> int:
        """
        清理旧的归档文件
        
        Args:
            log_dir: 日志目录
            
        Returns:
            清理的文件数量
        """
        cleaned_count = 0
        
        try:
            # 获取所有日志文件（排除latest.log）
            log_files = []
            for file_path in log_dir.glob("*.log"):
                if file_path.name != "latest.log":
                    log_files.append(file_path)
            
            # 按修改时间排序（最新的在前）
            log_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # 计算需要删除的文件
            cutoff_time = datetime.now() - timedelta(days=self.max_archive_days)
            files_to_delete = []
            
            # 按时间删除
            for file_path in log_files:
                file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_time < cutoff_time:
                    files_to_delete.append(file_path)
            
            # 按数量删除（保留最新的N个文件）
            if len(log_files) > self.max_archive_count:
                files_to_delete.extend(log_files[self.max_archive_count:])
            
            # 删除重复的文件路径
            files_to_delete = list(set(files_to_delete))
            
            # 执行删除
            for file_path in files_to_delete:
                try:
                    file_path.unlink()
                    cleaned_count += 1
                    print(f"删除旧日志: {file_path.name}")
                except Exception as e:
                    print(f"删除文件失败 {file_path}: {e}")
            
            if cleaned_count > 0:
                print(f"清理完成，删除了 {cleaned_count} 个旧日志文件")
            
        except Exception as e:
            print(f"清理归档文件时出错: {e}")
        
        return cleaned_count
    
    def get_log_statistics(self, log_type: str = "server") -> dict:
        """
        获取日志统计信息
        
        Args:
            log_type: 日志类型
            
        Returns:
            统计信息字典
        """
        try:
            from shared.log_config import LogConfig
            log_dir = LogConfig.get_log_directory(log_type)
            
            if not log_dir.exists():
                return {
                    "total_files": 0,
                    "total_size": 0,
                    "latest_log_size": 0,
                    "archive_count": 0
                }
            
            total_files = 0
            total_size = 0
            latest_log_size = 0
            archive_count = 0
            
            for file_path in log_dir.glob("*.log"):
                if file_path.is_file():
                    total_files += 1
                    file_size = file_path.stat().st_size
                    total_size += file_size
                    
                    if file_path.name == "latest.log":
                        latest_log_size = file_size
                    else:
                        archive_count += 1
            
            return {
                "total_files": total_files,
                "total_size": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "latest_log_size": latest_log_size,
                "latest_log_size_mb": round(latest_log_size / (1024 * 1024), 2),
                "archive_count": archive_count
            }
            
        except Exception as e:
            print(f"获取日志统计失败: {e}")
            return {}
    
    def compress_old_logs(self, log_type: str = "server", days_old: int = 7) -> int:
        """
        压缩旧的日志文件
        
        Args:
            log_type: 日志类型
            days_old: 多少天前的日志需要压缩
            
        Returns:
            压缩的文件数量
        """
        compressed_count = 0
        
        try:
            import gzip
            from shared.log_config import LogConfig
            
            log_dir = LogConfig.get_log_directory(log_type)
            if not log_dir.exists():
                return 0
            
            cutoff_time = datetime.now() - timedelta(days=days_old)
            
            for file_path in log_dir.glob("*.log"):
                if file_path.name == "latest.log":
                    continue
                
                file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_time < cutoff_time and not file_path.name.endswith('.gz'):
                    # 压缩文件
                    compressed_path = file_path.with_suffix('.log.gz')
                    
                    with open(file_path, 'rb') as f_in:
                        with gzip.open(compressed_path, 'wb') as f_out:
                            shutil.copyfileobj(f_in, f_out)
                    
                    # 删除原文件
                    file_path.unlink()
                    compressed_count += 1
                    print(f"压缩日志: {file_path.name} -> {compressed_path.name}")
            
            if compressed_count > 0:
                print(f"压缩完成，压缩了 {compressed_count} 个日志文件")
                
        except Exception as e:
            print(f"压缩日志文件失败: {e}")
        
        return compressed_count
