[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "worldwar-game"
version = "2.0.0"
description = "World War Strategy Game with Advanced Terrain Generation"
authors = [
    {name = "WorldWar Team"}
]
dependencies = [
    "requests>=2.25.0",
    "urllib3>=1.26.0",
    "json5>=0.9.0",
    "cryptography>=3.4.0",
    "configparser>=5.0.0",
    "numpy>=1.21.0",
    "noise>=1.2.2",
    "aiohttp>=3.8.0",
    "aiofiles>=0.8.0",
    "Pillow>=8.3.0"
]

[project.optional-dependencies]
test = [
    "pytest>=6.0.0",
    "pytest-cov>=2.10.0",
    "pytest-asyncio>=0.18.0",
    "pytest-mock>=3.6.0"
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "unit: marks tests as unit tests",
    "integration: marks tests as integration tests",
    "performance: marks tests as performance tests",
    "slow: marks tests as slow running tests"
]
addopts = [
    "-v",
    "--tb=short",
    "--strict-markers",
    "--disable-warnings",
    "--cov=worldwar-server",
    "--cov=worldwar-client", 
    "--cov=tools",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov"
]
norecursedirs = [".git", ".tox", "dist", "build", "*.egg", "htmlcov", "logs", "saves", "__pycache__"]
asyncio_mode = "auto"