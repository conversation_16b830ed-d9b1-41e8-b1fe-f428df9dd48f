#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地形生成服务 - 集成柏林噪声和真实世界地形生成
Terrain Generation Service - Integrates Perlin noise and real-world terrain generation
"""

import asyncio
import logging
import time
import threading
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass
from enum import Enum

from .terrain.perlin_generator import PerlinTerrainGenerator, NoiseConfig, TerrainConfig
from .data.realworld_generator import RealWorldGenerator
from .data.api_client import APIClient
from .data.data_cache import DataCache


class TerrainGenerationStatus(Enum):
    """地形生成状态"""
    PENDING = "pending"
    GENERATING = "generating"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class TerrainGenerationTask:
    """地形生成任务"""
    task_id: str
    room_id: str
    terrain_type: str  # "perlin" or "realworld"
    config: Dict[str, Any]
    status: TerrainGenerationStatus
    progress: float = 0.0
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    created_at: float = None
    completed_at: Optional[float] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = time.time()


class TerrainGenerationService:
    """地形生成服务"""
    
    def __init__(self, api_client: APIClient = None, cache: DataCache = None):
        """
        初始化地形生成服务
        
        Args:
            api_client: API客户端实例
            cache: 数据缓存实例
        """
        self.logger = logging.getLogger(__name__)
        self.api_client = api_client
        self.cache = cache
        
        # 任务管理
        self.tasks: Dict[str, TerrainGenerationTask] = {}
        self.task_lock = threading.RLock()
        
        # 进度回调
        self.progress_callbacks: Dict[str, Callable] = {}
        
        # 生成器实例
        self.perlin_generator = None
        self.realworld_generator = None
        
        # 工作线程池
        self.worker_threads = []
        self.max_workers = 2
        self.task_queue = asyncio.Queue()
        self.shutdown_event = threading.Event()
        
        # 启动工作线程
        self._start_workers()
        
        self.logger.info("地形生成服务初始化完成")
    
    def _start_workers(self):
        """启动工作线程"""
        for i in range(self.max_workers):
            worker = threading.Thread(
                target=self._worker_thread,
                name=f"TerrainWorker-{i}",
                daemon=True
            )
            worker.start()
            self.worker_threads.append(worker)
        
        self.logger.info(f"启动了 {self.max_workers} 个地形生成工作线程")
    
    def _worker_thread(self):
        """工作线程主循环"""
        while not self.shutdown_event.is_set():
            try:
                # 检查是否有待处理的任务
                pending_task = None
                with self.task_lock:
                    for task in self.tasks.values():
                        if task.status == TerrainGenerationStatus.PENDING:
                            task.status = TerrainGenerationStatus.GENERATING
                            pending_task = task
                            break
                
                if pending_task:
                    self._process_task(pending_task)
                else:
                    # 没有任务时等待
                    time.sleep(1)
                    
            except Exception as e:
                self.logger.error(f"地形生成工作线程错误: {e}")
                time.sleep(5)
    
    def _process_task(self, task: TerrainGenerationTask):
        """处理地形生成任务"""
        try:
            self.logger.info(f"开始处理地形生成任务: {task.task_id}, 类型: {task.terrain_type}")
            
            # 更新进度
            self._update_task_progress(task, 0.1, "开始生成地形...")
            
            if task.terrain_type == "perlin":
                result = self._generate_perlin_terrain(task)
            elif task.terrain_type == "realworld":
                result = asyncio.run(self._generate_realworld_terrain(task))
            else:
                raise ValueError(f"不支持的地形类型: {task.terrain_type}")
            
            # 完成任务
            with self.task_lock:
                task.status = TerrainGenerationStatus.COMPLETED
                task.result = result
                task.completed_at = time.time()
                task.progress = 1.0
            
            self._update_task_progress(task, 1.0, "地形生成完成")
            self.logger.info(f"地形生成任务完成: {task.task_id}")
            
        except Exception as e:
            # 任务失败
            error_msg = f"地形生成失败: {str(e)}"
            self.logger.error(f"任务 {task.task_id} 失败: {error_msg}")
            
            with self.task_lock:
                task.status = TerrainGenerationStatus.FAILED
                task.error_message = error_msg
                task.completed_at = time.time()
            
            self._update_task_progress(task, 0.0, error_msg)
    
    def _generate_perlin_terrain(self, task: TerrainGenerationTask) -> Dict[str, Any]:
        """生成柏林噪声地形"""
        config = task.config
        
        # 解析配置参数
        size_mapping = {
            "small": (256, 256),
            "medium": (512, 512),
            "large": (1024, 1024)
        }
        
        terrain_size = size_mapping.get(config.get("size", "medium"), (512, 512))
        seed = config.get("seed")
        params = config.get("params", {})
        
        # 创建噪声配置
        noise_config = NoiseConfig(
            octaves=params.get("octaves", 4),
            persistence=params.get("persistence", 0.5),
            lacunarity=params.get("lacunarity", 2.0),
            scale=params.get("scale", 100.0),
            base_frequency=params.get("base_frequency", 0.01)
        )
        
        # 创建地形配置
        terrain_config = TerrainConfig(
            size=terrain_size,
            height_scale=params.get("height_scale", 100.0),
            sea_level=params.get("sea_level", 0.3),
            mountain_threshold=params.get("mountain_threshold", 0.7),
            smoothing_iterations=params.get("smoothing_iterations", 2),
            smoothing_factor=params.get("smoothing_factor", 0.1)
        )
        
        # 创建生成器
        generator = PerlinTerrainGenerator(seed, noise_config, terrain_config)
        
        # 更新进度
        self._update_task_progress(task, 0.3, "生成高度图...")
        
        # 生成高度图
        heightmap = generator.generate_heightmap()
        
        # 更新进度
        self._update_task_progress(task, 0.6, "生成温度和降水图...")
        
        # 生成温度和降水图
        temperature_map = generator.generate_temperature_map(heightmap)
        precipitation_map = generator.generate_precipitation_map(heightmap)
        
        # 更新进度
        self._update_task_progress(task, 0.8, "计算地形统计...")
        
        # 获取地形统计
        stats = generator.get_terrain_stats(heightmap)
        
        # 更新进度
        self._update_task_progress(task, 0.9, "整理地形数据...")
        
        # 组装结果
        result = {
            "type": "perlin",
            "size": terrain_size,
            "heightmap": heightmap.tolist(),
            "temperature_map": temperature_map.tolist(),
            "precipitation_map": precipitation_map.tolist(),
            "stats": stats,
            "generation_params": {
                "seed": generator.seed,
                "noise_config": {
                    "octaves": noise_config.octaves,
                    "persistence": noise_config.persistence,
                    "lacunarity": noise_config.lacunarity,
                    "scale": noise_config.scale,
                    "base_frequency": noise_config.base_frequency
                },
                "terrain_config": {
                    "size": terrain_config.size,
                    "height_scale": terrain_config.height_scale,
                    "sea_level": terrain_config.sea_level,
                    "mountain_threshold": terrain_config.mountain_threshold,
                    "smoothing_iterations": terrain_config.smoothing_iterations,
                    "smoothing_factor": terrain_config.smoothing_factor
                }
            },
            "generated_at": time.time()
        }
        
        return result
    
    async def _generate_realworld_terrain(self, task: TerrainGenerationTask) -> Dict[str, Any]:
        """生成真实世界地形"""
        config = task.config
        region_name = config.get("region")
        
        if not region_name:
            raise ValueError("真实世界地形生成需要指定地区名称")
        
        # 创建真实世界生成器
        if not self.realworld_generator:
            self.realworld_generator = RealWorldGenerator(self.api_client, self.cache)
        
        # 更新进度
        self._update_task_progress(task, 0.2, f"获取 {region_name} 的地理数据...")
        
        # 生成地区数据
        terrain_data = await self.realworld_generator.generate_region_data(region_name)
        
        # 更新进度
        self._update_task_progress(task, 0.6, "处理经济和人口数据...")
        
        # 验证数据
        validation_result = await self.realworld_generator.validate_region_data(terrain_data)
        
        # 更新进度
        self._update_task_progress(task, 0.8, "验证数据完整性...")
        
        # 转换为游戏格式
        result = {
            "type": "realworld",
            "region_name": region_name,
            "geographic_data": {
                "name": terrain_data.region.name,
                "country_code": terrain_data.region.country_code,
                "coordinates": terrain_data.region.coordinates,
                "area_km2": terrain_data.region.area_km2,
                "population": terrain_data.region.population,
                "elevation": terrain_data.region.elevation,
                "climate_zone": terrain_data.region.climate_zone,
                "terrain_type": terrain_data.region.terrain_type
            },
            "economic_data": {
                "gdp_per_capita": terrain_data.economic_data.gdp_per_capita,
                "poverty_rate": terrain_data.economic_data.poverty_rate,
                "unemployment_rate": terrain_data.economic_data.unemployment_rate,
                "inflation_rate": terrain_data.economic_data.inflation_rate,
                "urban_population_percent": terrain_data.economic_data.urban_population_percent,
                "literacy_rate": terrain_data.economic_data.literacy_rate,
                "life_expectancy": terrain_data.economic_data.life_expectancy
            },
            "resource_distribution": {
                "oil_reserves": terrain_data.resource_distribution.oil_reserves,
                "mineral_deposits": terrain_data.resource_distribution.mineral_deposits,
                "agricultural_potential": terrain_data.resource_distribution.agricultural_potential,
                "water_resources": terrain_data.resource_distribution.water_resources,
                "renewable_energy_potential": terrain_data.resource_distribution.renewable_energy_potential,
                "industrial_capacity": terrain_data.resource_distribution.industrial_capacity
            },
            "cities": [
                {
                    "name": city.name,
                    "population": city.population,
                    "coordinates": city.coordinates,
                    "economic_importance": city.economic_importance,
                    "infrastructure_level": city.infrastructure_level,
                    "strategic_value": city.strategic_value
                }
                for city in terrain_data.cities
            ],
            "strategic_locations": terrain_data.strategic_locations,
            "game_balance_factors": terrain_data.game_balance_factors,
            "validation_result": validation_result,
            "generated_at": time.time()
        }
        
        # 更新进度
        self._update_task_progress(task, 0.9, "整理真实世界数据...")
        
        return result
    
    def _update_task_progress(self, task: TerrainGenerationTask, progress: float, message: str):
        """更新任务进度"""
        task.progress = progress
        
        # 调用进度回调
        callback = self.progress_callbacks.get(task.task_id)
        if callback:
            try:
                callback(task.task_id, progress, message)
            except Exception as e:
                self.logger.error(f"进度回调错误: {e}")
    
    def start_terrain_generation(self, room_id: str, terrain_config: Dict[str, Any],
                               progress_callback: Callable = None) -> str:
        """
        开始地形生成任务
        
        Args:
            room_id: 房间ID
            terrain_config: 地形配置
            progress_callback: 进度回调函数
            
        Returns:
            任务ID
        """
        task_id = f"terrain_{room_id}_{int(time.time())}"
        
        # 创建任务
        task = TerrainGenerationTask(
            task_id=task_id,
            room_id=room_id,
            terrain_type=terrain_config.get("type", "perlin"),
            config=terrain_config,
            status=TerrainGenerationStatus.PENDING
        )
        
        with self.task_lock:
            self.tasks[task_id] = task
        
        # 设置进度回调
        if progress_callback:
            self.progress_callbacks[task_id] = progress_callback
        
        self.logger.info(f"创建地形生成任务: {task_id}, 房间: {room_id}, 类型: {task.terrain_type}")
        
        return task_id
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        with self.task_lock:
            task = self.tasks.get(task_id)
            if not task:
                return None
            
            return {
                "task_id": task_id,
                "room_id": task.room_id,
                "terrain_type": task.terrain_type,
                "status": task.status.value,
                "progress": task.progress,
                "error_message": task.error_message,
                "created_at": task.created_at,
                "completed_at": task.completed_at,
                "has_result": task.result is not None
            }
    
    def get_task_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务结果"""
        with self.task_lock:
            task = self.tasks.get(task_id)
            if not task or task.status != TerrainGenerationStatus.COMPLETED:
                return None
            
            return task.result
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self.task_lock:
            task = self.tasks.get(task_id)
            if not task:
                return False
            
            if task.status == TerrainGenerationStatus.PENDING:
                task.status = TerrainGenerationStatus.FAILED
                task.error_message = "任务已取消"
                task.completed_at = time.time()
                return True
            
            return False
    
    def cleanup_old_tasks(self, max_age_hours: int = 24):
        """清理旧任务"""
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        with self.task_lock:
            tasks_to_remove = []
            for task_id, task in self.tasks.items():
                if (current_time - task.created_at) > max_age_seconds:
                    tasks_to_remove.append(task_id)
            
            for task_id in tasks_to_remove:
                del self.tasks[task_id]
                self.progress_callbacks.pop(task_id, None)
        
        if tasks_to_remove:
            self.logger.info(f"清理了 {len(tasks_to_remove)} 个旧的地形生成任务")
    
    def get_supported_regions(self) -> List[str]:
        """获取支持的真实世界地区列表"""
        if not self.realworld_generator:
            self.realworld_generator = RealWorldGenerator(self.api_client, self.cache)
        
        return self.realworld_generator.get_supported_regions()
    
    def shutdown(self):
        """关闭服务"""
        self.logger.info("正在关闭地形生成服务...")
        self.shutdown_event.set()
        
        # 等待工作线程结束
        for worker in self.worker_threads:
            worker.join(timeout=5)
        
        self.logger.info("地形生成服务已关闭")


# 全局服务实例
_terrain_service: Optional[TerrainGenerationService] = None


def get_terrain_service() -> TerrainGenerationService:
    """获取地形生成服务实例"""
    global _terrain_service
    if _terrain_service is None:
        # 创建API客户端和缓存
        from pathlib import Path
        cache_dir = Path("cache/terrain")
        cache_dir.mkdir(parents=True, exist_ok=True)
        
        api_client = APIClient()
        cache = DataCache(cache_dir)
        
        _terrain_service = TerrainGenerationService(api_client, cache)
    
    return _terrain_service


def shutdown_terrain_service():
    """关闭地形生成服务"""
    global _terrain_service
    if _terrain_service:
        _terrain_service.shutdown()
        _terrain_service = None