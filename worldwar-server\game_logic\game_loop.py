#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏循环管理器
Game Loop Manager
"""

import time
import threading
import random
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from enum import Enum

from game_logic.economic_system import EconomicSystem
from game_logic.policy_system import PolicyManager
from game_logic.combat_system import CombatSystem
from game_logic.victory_system import VictorySystem
from game_logic.player_manager import <PERSON><PERSON><PERSON><PERSON>, Player, PlayerActionState
from data.local_storage import LocalStorage
from shared.enhanced_logger import get_server_logger

class GamePhase(Enum):
    """游戏阶段枚举"""
    SETUP = "setup"                     # 设置阶段
    DEVELOPMENT = "development"         # 发展阶段
    EXPANSION = "expansion"             # 扩张阶段
    COMBAT = "combat"                   # 战斗阶段
    EVENTS = "events"                   # 事件阶段
    END_TURN = "end_turn"              # 回合结束

class GameState(Enum):
    """游戏状态枚举"""
    WAITING = "waiting"                 # 等待开始
    RUNNING = "running"                 # 运行中
    PAUSED = "paused"                  # 暂停
    FINISHED = "finished"              # 已结束

@dataclass
class GameTurn:
    """游戏回合数据"""
    turn_number: int
    current_phase: GamePhase
    current_player: str
    phase_start_time: float = field(default_factory=time.time)
    actions_taken: List[Dict[str, Any]] = field(default_factory=list)
    phase_time_limit: int = 60
    
    def is_phase_expired(self) -> bool:
        """检查阶段是否超时"""
        return time.time() - self.phase_start_time > self.phase_time_limit
    
    def get_remaining_time(self) -> int:
        """获取剩余时间"""
        elapsed = time.time() - self.phase_start_time
        return max(0, self.phase_time_limit - int(elapsed))

class GameLoop:
    """游戏循环管理器"""

    DEFAULT_GAME_SETTINGS = {
        "game_loop": {
            "max_turns": 100,
            "phase_time_limits": {
                "default": 60,
                "development": 90,
                "expansion": 60,
                "combat": 45,
                "events": 30,
                "end_turn": 15
            }
        },
        "random_events": {
            "probability": 0.15,
            "events": []
        },
        "victory_conditions": {} # 让 victory_system 自己处理默认值
    }
    
    def __init__(self, room_id: str, players: List[str], room_manager, terrain_data: Dict[str, Any] = None):
        """初始化游戏循环"""
        self.room_id = room_id
        self.players = players.copy()
        self.eliminated_players = []
        self.room_manager = room_manager
        self.terrain_data = terrain_data
        
        # 游戏状态
        self.game_state = GameState.WAITING
        self.current_turn = None
        self.turn_number = 0
        
        # 加载游戏设置
        self._load_game_settings()

        # 系统组件
        # 系统组件的初始化顺序很重要
        # 注意: PlayerManager 和 VictorySystem 存在循环依赖。
        # 此处通过两步初始化来解决：
        # 1. 创建 PlayerManager，将 victory_system 暂时设为 None。
        # 2. 创建 VictorySystem，传入 PlayerManager。
        # 3. 将创建好的 VictorySystem 实例注入回 PlayerManager。
        
        # 1. 创建核心管理系统
        self.player_manager = PlayerManager(victory_system=None, game_settings=self.game_settings)
        self.economic_system = EconomicSystem(player_manager=self.player_manager, game_settings=self.game_settings)
        self.victory_system = VictorySystem(self.player_manager, self.economic_system, self.game_settings)
        
        # 2. 注入循环依赖
        self.player_manager.victory_system = self.victory_system

        # 3. 创建依赖于核心系统的其他系统
        self.policy_manager = PolicyManager(self.player_manager)
        self.combat_system = CombatSystem(self.player_manager, self.economic_system, self.game_settings)

        # 4. 添加玩家
        for player_id in players:
            self.player_manager.add_player(Player(player_id=player_id, name=player_id))
        
        # 游戏循环线程
        self.loop_thread = None
        self.running = False
        
        # 导入日志
        self.logger = get_server_logger("GameLoop")
        
        self.logger.info(f"游戏循环初始化完成，房间: {room_id}, 玩家: {len(players)}")

    def _load_game_settings(self):
        """从文件加载游戏设置"""
        storage = LocalStorage()
        settings = storage.get_game_settings_data()

        if not settings:
            self.logger.warning("无法加载游戏设置，将使用默认值。")
            self.game_settings = self.DEFAULT_GAME_SETTINGS.copy()
        else:
            # 这里可以做一个深度合并，但为了简单起见，我们直接使用加载的设置
            # 实际项目中，应该用加载的设置覆盖默认设置的相应部分
            self.game_settings = settings

        # 为了向后兼容和方便访问，仍然设置单独的属性
        loop_settings = self.game_settings.get("game_loop", {})
        self.max_turns = loop_settings.get("max_turns", 50)
        self.phase_time_limits = loop_settings.get("phase_time_limits", {
            "default": 60, "development": 90, "expansion": 60,
            "combat": 45, "events": 30, "end_turn": 15
        })

        event_settings = self.game_settings.get("random_events", {})
        self.random_event_probability = event_settings.get("probability", 0.1)
        self.random_event_types = event_settings.get("events", [])
        
        self.logger.info("游戏设置加载完成。")

    def confirm_player_action(self, player_id: str):
        """确认玩家已完成其行动"""
        if self.current_turn and self.current_turn.current_player == player_id:
            self.player_manager.set_player_action_state(player_id, PlayerActionState.CONFIRMED)
            self.logger.info(f"玩家 {player_id} 已确认其在 {self.current_turn.current_phase.value} 阶段的行动。")
        else:
            self.logger.warning(f"玩家 {player_id} 试图在非其回合或无效回合中确认行动。")
    
    def start_game(self):
        """开始游戏"""
        if self.game_state != GameState.WAITING:
            return False
        
        self.game_state = GameState.RUNNING
        self.running = True
        self.turn_number = 1
        
        # 初始化玩家经济状态
        self._initialize_player_economies()
        
        # 启动游戏循环线程
        self.loop_thread = threading.Thread(target=self._game_loop, daemon=True)
        self.loop_thread.start()
        
        self.logger.info(f"游戏开始，房间: {self.room_id}")
        return True
    
    def pause_game(self):
        """暂停游戏"""
        if self.game_state == GameState.RUNNING:
            self.game_state = GameState.PAUSED
            self.logger.info("游戏已暂停")
    
    def resume_game(self):
        """恢复游戏"""
        if self.game_state == GameState.PAUSED:
            self.game_state = GameState.RUNNING
            self.logger.info("游戏已恢复")
    
    def end_game(self, reason: str = "normal"):
        """结束游戏"""
        self.game_state = GameState.FINISHED
        self.running = False
        
        # 计算最终结果
        final_results = self._calculate_final_results()
        
        self.logger.info(f"游戏结束，原因: {reason}")
        return final_results
    
    def _game_loop(self):
        """主游戏循环"""
        while self.running and self.game_state != GameState.FINISHED:
            try:
                if self.game_state == GameState.PAUSED:
                    time.sleep(1)
                    continue

                # 检查胜利条件 (通过 PlayerManager)
                winner = self.player_manager.check_victory_conditions(self.turn_number)
                if winner:
                    # victory_system 内部已经记录了日志
                    self.end_game(f"玩家 {winner.name} 获胜")
                    break
                
                # 检查最大回合数
                if self.turn_number > self.max_turns:
                    self.end_game("达到最大回合数")
                    break
                
                # 执行回合
                self._execute_turn()
                
                # 推进到下一回合
                self._advance_turn()
                
            except Exception as e:
                self.logger.error(f"游戏循环错误: {e}")
                time.sleep(1)
    
    def _execute_turn(self):
        """执行单个回合"""
        current_player_obj = self.player_manager.get_current_player()
        if not current_player_obj:
            self.logger.warning("无法获取当前玩家，跳过回合")
            return
        current_player = current_player_obj.player_id
        
        # 发展阶段
        self._execute_development_phase(current_player)
        
        # 扩张阶段
        self._execute_expansion_phase(current_player)
        
        # 战斗阶段
        self._execute_combat_phase(current_player)
        
        # 事件阶段
        self._execute_events_phase(current_player)
        
        # 回合结束
        self._execute_end_turn_phase(current_player)
    
    def _execute_player_action_phase(self, player: str, phase: GamePhase):
        """执行一个需要玩家确认行动的通用阶段"""
        phase_name = phase.value
        time_limit = self.phase_time_limits.get(phase_name, self.phase_time_limits.get("default", 60))

        self.current_turn = GameTurn(
            turn_number=self.turn_number,
            current_phase=phase,
            current_player=player,
            phase_time_limit=time_limit
        )

        self._broadcast_phase_start()
        self.player_manager.reset_all_players_action_state()

        # 等待玩家行动或超时
        phase_start_time = time.time()
        while time.time() - phase_start_time < time_limit and self.running:
            if self._is_player_action_complete(player):
                self.logger.info(f"玩家 {player} 在 {phase_name} 阶段确认了行动。")
                break
            time.sleep(0.5) # 使用更短的休眠时间以获得更快的响应
        else:
            if self.running:
                self.logger.info(f"玩家 {player} 在 {phase_name} 阶段超时。")

    def _execute_development_phase(self, player: str):
        """执行发展阶段"""
        self._execute_player_action_phase(player, GamePhase.DEVELOPMENT)

    def _execute_expansion_phase(self, player: str):
        """执行扩张阶段"""
        self._execute_player_action_phase(player, GamePhase.EXPANSION)
    
    def _execute_combat_phase(self, player: str):
        """执行战斗阶段 (重构版)"""
        # 在这个新模型中，战斗行动是通过命令异步添加的。
        # 这个阶段可以用来让玩家确认他们的战斗计划，或者就是一个简单的过渡阶段。
        self._execute_player_action_phase(player, GamePhase.COMBAT)
    
    def _execute_events_phase(self, player: str):
        """执行事件阶段"""
        self.current_turn = GameTurn(
            turn_number=self.turn_number,
            current_phase=GamePhase.EVENTS,
            current_player=player,
            phase_time_limit=self.phase_time_limits.get("events", 30)
        )
        
        self._broadcast_phase_start()
        
        # 处理随机事件
        events = self._generate_random_events()
        if events:
            self._apply_random_events(events)
            self._broadcast_events(events)
    
    def _execute_end_turn_phase(self, player: str):
        """执行回合结束阶段"""
        self.current_turn = GameTurn(
            turn_number=self.turn_number,
            current_phase=GamePhase.END_TURN,
            current_player=player,
            phase_time_limit=self.phase_time_limits.get("end_turn", 15)
        )

        # 更新所有玩家的资源（收入和政策点数）
        self._update_player_resources()

        # 处理本回合所有战斗
        combat_results = self.combat_system.process_combat_actions()
        if combat_results:
            self._broadcast_combat_results(combat_results)

        # 支付单位维护费
        self.combat_system.pay_upkeep_costs()

        # 更新政策状态并应用效果
        active_effects = self.policy_manager.update_policies(self.turn_number)
        self.economic_system.update_all_territories(active_effects)
        
        # 广播回合结束
        self._broadcast_turn_end()
    
    def _advance_turn(self):
        """推进到下一回合"""
        next_player = self.player_manager.next_player()
        
        # 如果回到了回合顺序的第一个玩家，增加回合数
        if self.player_manager.current_player_index == 0:
            self.turn_number += 1
            self.player_manager.advance_all_players_turn()
            
            # 在新回合开始时，更新所有领土的事件效果
            self.economic_system.update_all_event_modifiers()
            
            self.logger.info(f"进入第 {self.turn_number} 回合")
    
    def _initialize_player_economies(self):
        """初始化玩家经济状态"""
        # 如果有地形数据，使用地形数据初始化
        if self.terrain_data:
            self._initialize_from_terrain_data()
        else:
            # 使用默认的贫困城市数据初始化
            self._initialize_from_default_data()
    
    def _initialize_from_terrain_data(self):
        """从地形数据初始化游戏状态"""
        try:
            from game_logic.terrain_game_initializer import TerrainGameInitializer
            
            initializer = TerrainGameInitializer()
            game_init_data = initializer.initialize_game_from_terrain(
                self.terrain_data, self.players, self.game_settings
            )
            
            # 应用初始化数据到游戏系统
            self._apply_terrain_initialization(game_init_data)
            
            self.logger.info("成功从地形数据初始化游戏状态")
            
        except Exception as e:
            self.logger.error(f"从地形数据初始化失败，使用默认初始化: {e}")
            self._initialize_from_default_data()
    
    def _apply_terrain_initialization(self, game_init_data: Dict[str, Any]):
        """应用地形初始化数据到游戏系统"""
        players_data = game_init_data.get("players", {})
        
        for player_id in self.players:
            player_data = players_data.get(player_id, {})
            
            # 初始化玩家领土
            initial_territories = player_data.get("initial_territories", [])
            for territory_data in initial_territories:
                self.economic_system.add_territory(
                    territory_id=territory_data["territory_id"],
                    territory_name=territory_data["name"],
                    owner=territory_data["owner"],
                    population=territory_data["population"],
                    initial_poverty_index=0.3,  # 基于地形的初始贫困指数
                    terrain_type=territory_data.get("terrain_type"),
                    biome=territory_data.get("biome"),
                    resources=territory_data.get("resources", {})
                )
            
            # 设置玩家起始资源
            starting_resources = player_data.get("starting_resources", {})
            player = self.player_manager.get_player(player_id)
            if player and starting_resources:
                for resource, amount in starting_resources.items():
                    if resource == "gold":
                        player.gold = amount
                    elif resource == "food":
                        player.food = amount
                    elif resource == "production":
                        player.production = amount
                    elif resource == "research":
                        player.research_points = amount
            
            # 应用平衡调整
            balance_adjustments = player_data.get("balance_adjustments", {})
            if balance_adjustments:
                self._apply_balance_adjustments(player_id, balance_adjustments)
        
        # 记录地形信息
        terrain_info = game_init_data.get("terrain_info", {})
        self.logger.info(f"地形初始化完成 - 类型: {terrain_info.get('type')}, "
                        f"单元数: {terrain_info.get('cells')}, "
                        f"质量: {terrain_info.get('analysis', {}).get('terrain_quality')}")
    
    def _apply_balance_adjustments(self, player_id: str, adjustments: Dict[str, float]):
        """应用游戏平衡调整"""
        player = self.player_manager.get_player(player_id)
        if not player:
            return
        
        # 资源加成
        if "resource_bonus" in adjustments:
            bonus = adjustments["resource_bonus"]
            player.gold = int(player.gold * (1 + bonus))
            player.food = int(player.food * (1 + bonus))
            player.production = int(player.production * (1 + bonus))
            self.logger.info(f"玩家 {player_id} 获得资源加成: {bonus:.1%}")
        
        # 资源惩罚
        if "resource_penalty" in adjustments:
            penalty = adjustments["resource_penalty"]
            player.gold = int(player.gold * (1 - penalty))
            player.food = int(player.food * (1 - penalty))
            player.production = int(player.production * (1 - penalty))
            self.logger.info(f"玩家 {player_id} 受到资源惩罚: {penalty:.1%}")
    
    def _initialize_from_default_data(self):
        """使用默认数据初始化（原有逻辑）"""
        # 从数据存储加载贫困城市数据
        storage = LocalStorage()
        poverty_data = storage.get_poverty_data()
        
        if poverty_data and 'cities' in poverty_data:
            cities = poverty_data['cities']
            
            # 为每个玩家分配起始城市
            for i, player in enumerate(self.players):
                if i < len(cities):
                    city = cities[i]
                    self.economic_system.add_territory(
                        territory_id=f"{player}_start",
                        territory_name=city.get('name', f'City_{i}'),
                        owner=player,
                        population=city.get('population', 500000),
                        initial_poverty_index=city.get('poverty_index', 0.6)
                    )
    
    def _update_player_resources(self):
        """更新所有玩家的回合资源"""
        for player_id in self.players:
            player = self.player_manager.get_player(player_id)
            if not player:
                continue

            # 计算并更新经济收入
            player_territories = self.economic_system.get_player_territories(player_id)
            if not player_territories:
                continue

            economic_summary = self.economic_system.get_player_economic_summary(player_territories)
            income = economic_summary.get('total_income', 0)
            self.player_manager.add_resources(player_id, gold=income)
            self.logger.info(f"玩家 {player_id} 获得收入: {income}")

            # 计算并更新政策点数
            policy_points = self.economic_system.calculate_policy_points(player_territories)
            self.player_manager.add_resources(player_id, policy_points=policy_points)
            self.logger.info(f"玩家 {player_id} 获得政策点数: {policy_points}")
    
    def _generate_random_events(self) -> List[Dict[str, Any]]:
        """生成随机事件"""
        events = []
        
        if random.random() < self.random_event_probability and self.random_event_types:
            event_data = random.choice(self.random_event_types)
            events.append(event_data)
        
        return events
    
    def _apply_random_events(self, events: List[Dict[str, Any]]):
        """应用随机事件效果"""
        for event in events:
            self.logger.info(f"随机事件发生: {event.get('name', '未知事件')}. {event.get('description', '')}")
            
            impact = event.get("impact")
            if not impact:
                continue

            impact_type = impact.get("type")
            target = impact.get("target")

            if impact_type == "resource":
                resources = impact.get("resources", {})
                if target == "all_players":
                    for player_id in self.players:
                        self.player_manager.add_resources(player_id, **resources)
                        self.logger.info(f"向玩家 {player_id} 发放资源: {resources}")

            elif impact_type in ["economic", "development"]:
                modifiers = impact.get("modifiers")
                duration = impact.get("duration", 1)
                if not modifiers:
                    continue

                if target == "random_territory":
                    all_territory_ids = list(self.economic_system.territories.keys())
                    if all_territory_ids:
                        target_territory_id = random.choice(all_territory_ids)
                        self.economic_system.apply_event_modifier_to_territory(
                            target_territory_id, modifiers, duration
                        )
    
    def _is_player_action_complete(self, player_id: str) -> bool:
        """检查玩家是否完成行动"""
        player = self.player_manager.get_player(player_id)
        if player:
            return player.action_state == PlayerActionState.CONFIRMED
        return False
    
    def _broadcast_phase_start(self):
        """广播阶段开始"""
        message = {
            "type": "phase_start",
            "data": {
                "turn_number": self.current_turn.turn_number,
                "phase": self.current_turn.current_phase.value,
                "current_player": self.current_turn.current_player,
                "time_limit": self.current_turn.phase_time_limit,
                "remaining_time": self.current_turn.get_remaining_time()
            }
        }
        self._broadcast_to_room(message)
    
    def _broadcast_turn_end(self):
        """广播回合结束"""
        message = {
            "type": "turn_end",
            "data": {
                "turn_number": self.turn_number,
                "next_player": self.player_manager.get_current_player().player_id if self.player_manager.get_current_player() else None
            }
        }
        self._broadcast_to_room(message)
    
    def _broadcast_combat_results(self, results: Dict[str, Any]):
        """广播战斗结果"""
        message = {
            "type": "combat_results",
            "data": results
        }
        self._broadcast_to_room(message)
    
    def _broadcast_events(self, events: List[Dict[str, Any]]):
        """广播随机事件"""
        message = {
            "type": "random_events",
            "data": {"events": events}
        }
        self._broadcast_to_room(message)
    
    def _broadcast_to_room(self, message: Dict[str, Any]):
        """向房间广播消息"""
        if self.room_manager:
            room = self.room_manager.get_room(self.room_id)
            if room:
                self.room_manager._broadcast_to_room(room, message)
        else:
            self.logger.warning("RoomManager not available for broadcasting.")
    
    def _calculate_final_results(self) -> Dict[str, Any]:
        """计算最终结果"""
        results = {
            "game_id": self.room_id,
            "total_turns": self.turn_number,
            "players": [],
            "winner": None,
            "end_reason": "normal"
        }
        
        # 计算每个玩家的最终得分
        for player in self.players:
            player_territories = self.economic_system.get_player_territories(player)
            economic_summary = self.economic_system.get_player_economic_summary(player_territories)
            
            player_result = {
                "name": player,
                "final_gdp": economic_summary.get('total_gdp', 0),
                "development_index": economic_summary.get('average_development_index', 0),
                "territories": economic_summary.get('territory_count', 0),
                "eliminated": player in self.eliminated_players
            }
            
            results["players"].append(player_result)
        
        # 确定获胜者
        if not self.eliminated_players and len(self.players) > 0:
            # 平局决胜规则：当前是按游戏结束时的发展指数排序。
            # 这是一个可以根据具体游戏平衡性需求进行调整的规则。
            # 例如，可以考虑总GDP、剩余军队数量等。
            self.logger.info(f"游戏正常结束，根据发展指数决定胜者。")
            results["players"].sort(key=lambda x: x["development_index"], reverse=True)
            results["winner"] = results["players"]["name"]
        
        return results
    
    def get_game_status(self) -> Dict[str, Any]:
        """获取游戏状态"""
        return {
            "room_id": self.room_id,
            "game_state": self.game_state.value,
            "turn_number": self.turn_number,
            "max_turns": self.max_turns,
            "current_phase": self.current_turn.current_phase.value if self.current_turn else None,
            "current_player": self.current_turn.current_player if self.current_turn else None,
            "remaining_time": self.current_turn.get_remaining_time() if self.current_turn else 0,
            "players": self.players,
            "eliminated_players": self.eliminated_players
        }
