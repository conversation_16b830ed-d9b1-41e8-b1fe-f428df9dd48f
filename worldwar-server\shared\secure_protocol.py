#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全网络通信协议处理
Secure Network Communication Protocol Handler
"""

import json
import struct
import socket
import time
import threading
from typing import Dict, Any, Optional, Tuple
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from shared.security_manager import SecurityManager
from shared.protocol import Protocol
from shared.enhanced_logger import get_server_logger

class SecureProtocol:
    """安全网络通信协议处理类"""
    
    # 协议版本
    VERSION = "2.0"  # 升级到2.0支持安全通信
    
    # 消息头长度（4字节用于存储消息体长度）
    HEADER_SIZE = 4
    
    # 最大消息长度（1MB）
    MAX_MESSAGE_SIZE = 1024 * 1024
    
    # 编码格式
    ENCODING = "utf-8"
    
    def __init__(self, server_mode: bool = False):
        """
        初始化安全协议处理器
        
        Args:
            server_mode: 是否为服务器模式
        """
        self.logger = get_server_logger("SecureProtocol")
        self.security_manager = SecurityManager(server_mode)
        self.server_mode = server_mode
        
        # 握手状态管理
        self.handshake_completed = False  # 客户端模式的全局握手状态
        self.socket_handshake_status = {}  # 服务器模式：每个套接字的握手状态
        self.pending_challenges = {}  # 存储待验证的握手挑战
        self.session_tokens = {}  # 存储会话令牌
        
        # 线程安全锁
        self.lock = threading.RLock()
        
        self.logger.info(f"安全协议初始化完成 - {'服务器' if server_mode else '客户端'}模式")
    
    def initiate_handshake(self, sock: socket.socket) -> bool:
        """
        发起握手（服务器端）
        
        Args:
            sock: 客户端套接字
            
        Returns:
            握手是否成功发起
        """
        if not self.server_mode:
            self.logger.error("只有服务器模式才能发起握手")
            return False
        
        try:
            # 创建握手挑战
            challenge_msg = self.security_manager.create_handshake_challenge()
            challenge = challenge_msg["data"]["challenge"]
            
            # 存储挑战用于后续验证
            with self.lock:
                self.pending_challenges[sock] = challenge
            
            # 发送挑战
            if self._send_raw_message(sock, challenge_msg):
                self.logger.info("握手挑战已发送")
                return True
            else:
                self.logger.error("发送握手挑战失败")
                return False
                
        except Exception as e:
            self.logger.error(f"发起握手失败: {e}")
            return False
    
    def respond_to_handshake(self, sock: socket.socket, challenge_msg: Dict[str, Any]) -> bool:
        """
        响应握手（客户端）

        Args:
            sock: 服务器套接字
            challenge_msg: 握手挑战消息

        Returns:
            握手响应是否成功发送
        """
        if self.server_mode:
            self.logger.error("服务器模式不应响应握手")
            return False

        try:
            # 客户端在握手阶段需要临时使用一个默认密钥来响应挑战
            # 真正的密钥将在握手完成后从服务器获取
            if not self.security_manager.secret_key:
                # 使用临时密钥进行握手响应
                temp_key = b"temporary_handshake_key_32bytes"  # 32字节临时密钥
                self.security_manager.set_client_key(temp_key)
                self.logger.debug("设置临时握手密钥")

            # 创建握手响应
            response_msg = self.security_manager.create_handshake_response(
                challenge_msg["data"]
            )

            # 发送响应
            if self._send_raw_message(sock, response_msg):
                self.logger.info("握手响应已发送")
                return True
            else:
                self.logger.error("发送握手响应失败")
                return False

        except Exception as e:
            self.logger.error(f"响应握手失败: {e}")
            return False
    
    def complete_handshake(self, sock: socket.socket, response_msg: Dict[str, Any]) -> bool:
        """
        完成握手验证（服务器端）

        Args:
            sock: 客户端套接字
            response_msg: 握手响应消息

        Returns:
            握手是否成功完成
        """
        if not self.server_mode:
            self.logger.error("只有服务器模式才能完成握手验证")
            return False

        try:
            with self.lock:
                # 获取原始挑战
                original_challenge = self.pending_challenges.get(sock)
                if not original_challenge:
                    self.logger.error("未找到对应的握手挑战")
                    return False

                # 服务器端需要使用临时密钥来验证客户端的握手响应
                # 保存原始密钥，临时设置握手密钥，但使用线程锁确保线程安全
                temp_key = b"temporary_handshake_key_32bytes"  # 与客户端相同的临时密钥

                with self.lock:
                    # 保存原始密钥
                    original_key = self.security_manager.secret_key

                    try:
                        # 临时设置握手密钥
                        self.security_manager.secret_key = temp_key

                        # 验证握手响应
                        is_valid = self.security_manager.verify_handshake_response(
                            response_msg["data"], original_challenge
                        )
                    finally:
                        # 恢复原始密钥
                        self.security_manager.secret_key = original_key

                if is_valid:
                    # 握手成功，生成会话令牌
                    session_token = self.security_manager.generate_session_token()
                    self.session_tokens[sock] = session_token

                    # 为这个套接字标记握手完成
                    if self.server_mode:
                        self.socket_handshake_status[sock] = True
                    else:
                        self.handshake_completed = True

                    # 清理挑战
                    del self.pending_challenges[sock]

                    # 发送握手完成消息
                    server_key = self.security_manager.get_server_key()
                    self.logger.debug(f"发送给客户端的服务器密钥长度: {len(server_key)} bytes")
                    self.logger.debug(f"服务器密钥前16字节: {server_key[:16].hex()}")

                    completion_msg = {
                        "type": "handshake_complete",
                        "data": {
                            "success": True,
                            "session_token": session_token,
                            "server_key": server_key.hex()
                        }
                    }

                    if self._send_raw_message(sock, completion_msg):
                        self.logger.info("握手验证成功")
                        return True
                    else:
                        self.logger.error("发送握手完成消息失败")
                        return False
                else:
                    self.logger.warning("握手验证失败")
                    # 发送失败消息
                    failure_msg = {
                        "type": "handshake_complete",
                        "data": {
                            "success": False,
                            "error": "握手验证失败"
                        }
                    }
                    self._send_raw_message(sock, failure_msg)
                    return False

        except Exception as e:
            self.logger.error(f"完成握手验证失败: {e}")
            return False
    
    def finalize_handshake(self, completion_msg: Dict[str, Any]) -> bool:
        """
        完成握手（客户端）

        Args:
            completion_msg: 握手完成消息

        Returns:
            握手是否成功完成
        """
        if self.server_mode:
            self.logger.error("服务器模式不应完成握手")
            return False

        try:
            data = completion_msg["data"]
            if data.get("success"):
                # 保存会话令牌
                self.session_token = data.get("session_token")

                # 设置客户端密钥（用真正的服务器密钥替换临时密钥）
                server_key_hex = data.get("server_key")
                if server_key_hex:
                    server_key = bytes.fromhex(server_key_hex)
                    self.logger.debug(f"接收到的服务器密钥长度: {len(server_key)} bytes")
                    self.logger.debug(f"服务器密钥前16字节: {server_key[:16].hex()}")

                    self.security_manager.set_client_key(server_key)
                    self.handshake_completed = True
                    self.logger.info("客户端握手完成，已设置真正的服务器密钥")
                    return True
                else:
                    self.logger.error("未收到服务器密钥")
                    return False
            else:
                error = data.get("error", "未知错误")
                self.logger.error(f"握手失败: {error}")
                return False

        except Exception as e:
            self.logger.error(f"完成握手失败: {e}")
            return False
    
    def send_secure_message(self, sock: socket.socket, message: Dict[str, Any]) -> bool:
        """
        发送安全消息
        
        Args:
            sock: 套接字
            message: 要发送的消息
            
        Returns:
            发送是否成功
        """
        # 检查握手状态
        if self.server_mode:
            if not self.socket_handshake_status.get(sock, False):
                self.logger.error("握手未完成，无法发送安全消息")
                return False
        else:
            if not self.handshake_completed:
                self.logger.error("握手未完成，无法发送安全消息")
                return False
        
        try:
            # 添加协议版本到消息中（在签名之前）
            message_with_version = message.copy()
            message_with_version["protocol_version"] = self.VERSION

            # 对包含协议版本的消息进行签名
            signed_message = self.security_manager.sign_message(message_with_version)

            # 发送签名后的消息
            return self._send_raw_message(sock, signed_message)
            
        except Exception as e:
            self.logger.error(f"发送安全消息失败: {e}")
            return False
    
    def receive_secure_message(self, sock: socket.socket, timeout: float = 30.0) -> Optional[Dict[str, Any]]:
        """
        接收安全消息
        
        Args:
            sock: 套接字
            timeout: 超时时间
            
        Returns:
            接收到的消息，如果失败返回None
        """
        try:
            # 接收原始消息
            raw_message = self._receive_raw_message(sock, timeout)
            if not raw_message:
                return None
            
            # 检查握手状态，如果握手未完成，直接返回消息（用于握手过程）
            handshake_done = False
            if self.server_mode:
                handshake_done = self.socket_handshake_status.get(sock, False)
            else:
                handshake_done = self.handshake_completed

            if not handshake_done:
                return raw_message
            
            # 验证消息签名
            message_copy = raw_message.copy()
            if self.security_manager.verify_message(message_copy):
                return raw_message
            else:
                self.logger.warning("SecureProtocol: 消息签名验证失败")
                self.logger.warning(f"  消息来源套接字: {sock.getpeername() if sock else 'Unknown'}")
                self.logger.warning(f"  消息大小: {len(str(raw_message))} 字符")
                self.logger.warning(f"  握手状态: {'已完成' if self.is_handshake_completed(sock) else '未完成'}")
                return None
                
        except Exception as e:
            self.logger.error(f"接收安全消息失败: {e}")
            return None
    
    def _send_raw_message(self, sock: socket.socket, message: Dict[str, Any]) -> bool:
        """发送原始消息（内部使用）"""
        try:
            # 确保消息包含协议版本（如果没有的话添加）
            if "protocol_version" not in message:
                message["protocol_version"] = self.VERSION

            # 编码消息（使用与规范化相同的格式）
            json_str = json.dumps(message, ensure_ascii=False, sort_keys=True, separators=(',', ':'))
            message_bytes = json_str.encode(self.ENCODING)
            
            # 检查消息长度
            if len(message_bytes) > self.MAX_MESSAGE_SIZE:
                raise ValueError(f"消息过大: {len(message_bytes)} > {self.MAX_MESSAGE_SIZE}")
            
            # 创建消息头
            header = struct.pack("!I", len(message_bytes))
            
            # 发送数据
            full_data = header + message_bytes
            total_sent = 0
            while total_sent < len(full_data):
                sent = sock.send(full_data[total_sent:])
                if sent == 0:
                    return False
                total_sent += sent
            
            return True
            
        except Exception as e:
            self.logger.error(f"发送原始消息失败: {e}")
            return False
    
    def _receive_raw_message(self, sock: socket.socket, timeout: float = 30.0) -> Optional[Dict[str, Any]]:
        """接收原始消息（内部使用）"""
        try:
            # 设置超时
            sock.settimeout(timeout)
            
            # 接收消息头
            header_data = self._receive_exact(sock, self.HEADER_SIZE)
            if not header_data:
                return None
            
            # 解析消息长度
            message_length = struct.unpack("!I", header_data)[0]
            
            # 验证消息长度
            if message_length > self.MAX_MESSAGE_SIZE:
                raise ValueError(f"消息过大: {message_length} > {self.MAX_MESSAGE_SIZE}")
            
            # 接收消息体
            message_data = self._receive_exact(sock, message_length)
            if not message_data:
                return None
            
            # 解码消息
            json_str = message_data.decode(self.ENCODING)
            message = json.loads(json_str)
            
            # 验证协议版本
            if message.get("protocol_version") != self.VERSION:
                self.logger.warning(f"协议版本不匹配: {message.get('protocol_version')} != {self.VERSION}")
            
            return message
            
        except socket.timeout:
            return None
        except Exception as e:
            self.logger.error(f"接收原始消息失败: {e}")
            return None
    
    def _receive_exact(self, sock: socket.socket, length: int) -> Optional[bytes]:
        """精确接收指定长度的数据"""
        data = b""
        while len(data) < length:
            try:
                chunk = sock.recv(length - len(data))
                if not chunk:
                    return None
                data += chunk
            except socket.error:
                return None
        return data
    
    def cleanup_session(self, sock: socket.socket):
        """清理会话数据"""
        with self.lock:
            self.pending_challenges.pop(sock, None)
            self.session_tokens.pop(sock, None)
            self.socket_handshake_status.pop(sock, None)

        self.logger.debug("会话数据已清理")
    
    def is_handshake_completed(self, sock: socket.socket = None) -> bool:
        """
        检查握手是否完成

        Args:
            sock: 套接字（服务器模式需要）

        Returns:
            握手是否完成
        """
        if self.server_mode:
            if sock is None:
                return False
            return self.socket_handshake_status.get(sock, False)
        else:
            return self.handshake_completed
    
    def get_security_info(self) -> Dict[str, Any]:
        """获取安全信息"""
        return {
            "protocol_version": self.VERSION,
            "handshake_completed": self.handshake_completed,
            "security_manager": self.security_manager.get_security_info()
        }
