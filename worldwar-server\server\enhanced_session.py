#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的会话管理系统
Enhanced Session Management System

实现心跳检测、消息队列、会话持久化和超时处理
Implements heartbeat detection, message queues, session persistence and timeout handling
"""

import asyncio
import json
import pickle
import socket
import time
import threading
import uuid
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Dict, Any, List, Optional, Callable, Union
from collections import deque

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from shared.message_types import Message, MessageFactory, MessageType
from shared.enhanced_logger import get_server_logger


class SessionState(Enum):
    """会话状态枚举"""
    CONNECTING = "connecting"      # 连接中
    CONNECTED = "connected"        # 已连接
    AUTHENTICATED = "authenticated" # 已认证
    IN_ROOM = "in_room"           # 在房间中
    IN_GAME = "in_game"           # 游戏中
    DISCONNECTED = "disconnected"  # 已断开
    SUSPENDED = "suspended"        # 挂起（临时断开）
    TERMINATED = "terminated"      # 已终止


@dataclass
class SessionMetrics:
    """会话指标"""
    messages_sent: int = 0
    messages_received: int = 0
    bytes_sent: int = 0
    bytes_received: int = 0
    heartbeats_sent: int = 0
    heartbeats_received: int = 0
    reconnect_count: int = 0
    last_heartbeat_time: float = 0
    average_response_time: float = 0
    
    def update_heartbeat_received(self):
        """更新心跳接收统计"""
        self.heartbeats_received += 1
        self.last_heartbeat_time = time.time()
    
    def update_message_sent(self, size: int = 0):
        """更新消息发送统计"""
        self.messages_sent += 1
        self.bytes_sent += size
    
    def update_message_received(self, size: int = 0):
        """更新消息接收统计"""
        self.messages_received += 1
        self.bytes_received += size


class EnhancedPlayerSession:
    """增强的玩家会话类"""
    
    def __init__(self, session_id: str, socket: socket.socket, client_address: tuple):
        """
        初始化增强会话
        
        Args:
            session_id: 会话ID
            socket: 套接字
            client_address: 客户端地址
        """
        self.session_id = session_id
        self.socket = socket
        self.client_address = client_address
        
        # 基本信息
        self.player_name = ""
        self.player_id = ""
        self.language = "chinese"
        
        # 状态管理
        self.state = SessionState.CONNECTING
        self.is_authenticated = False
        self.current_room = None
        self.is_ready = False
        
        # 时间戳
        self.connection_time = time.time()
        self.last_activity = time.time()
        self.authentication_time = None
        
        # 心跳检测
        self.heartbeat_interval = 30.0  # 心跳间隔（秒）
        self.heartbeat_timeout = 90.0   # 心跳超时（秒）
        self.last_heartbeat = time.time()
        self.heartbeat_task = None
        self.missed_heartbeats = 0
        self.max_missed_heartbeats = 3
        
        # 消息队列
        self.message_queue = asyncio.Queue(maxsize=100)
        self.pending_messages = deque(maxlen=50)  # 待确认消息
        self.message_sequence = 0
        
        # 会话令牌和安全
        self.session_token = str(uuid.uuid4())
        self.handshake_completed = False
        
        # 统计指标
        self.metrics = SessionMetrics()
        
        # 事件回调
        self.event_callbacks: Dict[str, List[Callable]] = {
            'state_changed': [],
            'heartbeat_timeout': [],
            'message_received': [],
            'disconnected': []
        }
        
        # 线程安全锁
        self.lock = threading.RLock()
        
        # 日志记录器
        self.logger = get_server_logger(f"Session-{session_id[:8]}")
        
        self.logger.info(f"创建增强会话: {session_id} 来自 {client_address}")
    
    def set_state(self, new_state: SessionState) -> None:
        """
        设置会话状态
        
        Args:
            new_state: 新状态
        """
        with self.lock:
            old_state = self.state
            self.state = new_state
            self.last_activity = time.time()
            
            self.logger.info(f"会话状态变更: {old_state.value} -> {new_state.value}")
            
            # 触发状态变更事件
            self._trigger_event('state_changed', {
                'old_state': old_state,
                'new_state': new_state,
                'session_id': self.session_id
            })
    
    def authenticate(self, player_name: str) -> bool:
        """
        认证会话
        
        Args:
            player_name: 玩家名称
            
        Returns:
            是否认证成功
        """
        with self.lock:
            if self.is_authenticated:
                return False
            
            self.player_name = player_name
            self.player_id = f"player_{self.session_id}"
            self.is_authenticated = True
            self.authentication_time = time.time()
            
            self.set_state(SessionState.AUTHENTICATED)
            
            # 启动心跳检测
            self._start_heartbeat()
            
            self.logger.info(f"会话认证成功: {player_name}")
            return True
    
    def _start_heartbeat(self) -> None:
        """启动心跳检测"""
        if self.heartbeat_task:
            return
        
        async def heartbeat_loop():
            """心跳循环"""
            while self.state not in [SessionState.DISCONNECTED, SessionState.TERMINATED]:
                try:
                    await asyncio.sleep(self.heartbeat_interval)
                    
                    # 检查心跳超时
                    if time.time() - self.last_heartbeat > self.heartbeat_timeout:
                        self.missed_heartbeats += 1
                        self.logger.warning(f"心跳超时 {self.missed_heartbeats}/{self.max_missed_heartbeats}")
                        
                        if self.missed_heartbeats >= self.max_missed_heartbeats:
                            self.logger.error("心跳超时次数过多，断开连接")
                            self._trigger_event('heartbeat_timeout', {
                                'session_id': self.session_id,
                                'missed_count': self.missed_heartbeats
                            })
                            await self.disconnect(reason="heartbeat_timeout")
                            break
                    else:
                        self.missed_heartbeats = 0
                    
                    # 发送心跳
                    await self._send_heartbeat()
                    
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    self.logger.error(f"心跳循环异常: {e}")
        
        # 创建心跳任务（只在有事件循环时）
        try:
            self.heartbeat_task = asyncio.create_task(heartbeat_loop())
        except RuntimeError:
            # 没有运行的事件循环，跳过心跳任务创建
            self.logger.debug("没有运行的事件循环，跳过心跳任务创建")
    
    async def _send_heartbeat(self) -> None:
        """发送心跳消息"""
        try:
            heartbeat_msg = MessageFactory.create_heartbeat_message(
                sender="server",
                session=self.session_id
            )
            
            await self.send_message(heartbeat_msg)
            self.metrics.heartbeats_sent += 1
            
        except Exception as e:
            self.logger.error(f"发送心跳失败: {e}")
    
    def update_heartbeat(self) -> None:
        """更新心跳时间（收到客户端心跳时调用）"""
        with self.lock:
            self.last_heartbeat = time.time()
            self.last_activity = time.time()
            self.missed_heartbeats = 0
            self.metrics.update_heartbeat_received()
    
    async def send_message(self, message: Union[Message, Dict[str, Any]]) -> bool:
        """
        发送消息
        
        Args:
            message: 要发送的消息
            
        Returns:
            是否发送成功
        """
        try:
            # 转换消息格式
            if isinstance(message, dict):
                msg_obj = Message.from_dict(message)
            else:
                msg_obj = message
            
            # 设置消息序号
            self.message_sequence += 1
            msg_obj.metadata.session = self.session_id
            
            # 序列化消息
            message_data = msg_obj.to_json().encode('utf-8')
            message_size = len(message_data)
            
            # 发送消息长度头
            size_header = message_size.to_bytes(4, byteorder='big')
            
            # 发送消息
            self.socket.sendall(size_header + message_data)
            
            # 更新统计
            self.metrics.update_message_sent(message_size)
            self.last_activity = time.time()
            
            # 添加到待确认队列（如果需要确认）
            if msg_obj.type not in ["ping", "pong", "heartbeat"]:
                self.pending_messages.append({
                    'id': msg_obj.id,
                    'message': msg_obj,
                    'timestamp': time.time(),
                    'sequence': self.message_sequence
                })
            
            self.logger.debug(f"发送消息: {msg_obj.type} (ID: {msg_obj.id})")
            return True
            
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            return False
    
    async def receive_message(self, timeout: float = 30.0) -> Optional[Message]:
        """
        接收消息
        
        Args:
            timeout: 超时时间
            
        Returns:
            接收到的消息或None
        """
        try:
            # 设置套接字超时
            self.socket.settimeout(timeout)
            
            # 接收消息长度头
            size_data = self._receive_exact(4)
            if not size_data:
                return None
            
            message_size = int.from_bytes(size_data, byteorder='big')
            
            # 接收消息数据
            message_data = self._receive_exact(message_size)
            if not message_data:
                return None
            
            # 解析消息
            message_json = message_data.decode('utf-8')
            message = Message.from_json(message_json)
            
            # 更新统计
            self.metrics.update_message_received(len(message_data))
            self.last_activity = time.time()
            
            # 处理特殊消息类型
            if message.type == "pong":
                self.update_heartbeat()
            
            # 触发消息接收事件
            self._trigger_event('message_received', {
                'message': message,
                'session_id': self.session_id
            })
            
            self.logger.debug(f"接收消息: {message.type} (ID: {message.id})")
            return message
            
        except socket.timeout:
            return None
        except Exception as e:
            self.logger.error(f"接收消息失败: {e}")
            return None
    
    def _receive_exact(self, size: int) -> Optional[bytes]:
        """精确接收指定大小的数据"""
        data = b""
        while len(data) < size:
            try:
                chunk = self.socket.recv(size - len(data))
                if not chunk:
                    return None
                data += chunk
            except socket.error:
                return None
        return data
    
    async def enqueue_message(self, message: Union[Message, Dict[str, Any]]) -> bool:
        """
        将消息加入队列
        
        Args:
            message: 要加入队列的消息
            
        Returns:
            是否成功加入队列
        """
        try:
            if self.message_queue.full():
                # 队列满时，移除最旧的消息
                try:
                    self.message_queue.get_nowait()
                except asyncio.QueueEmpty:
                    pass
            
            await self.message_queue.put(message)
            return True
            
        except Exception as e:
            self.logger.error(f"消息入队失败: {e}")
            return False
    
    async def dequeue_message(self, timeout: float = 1.0) -> Optional[Union[Message, Dict[str, Any]]]:
        """
        从队列中取出消息
        
        Args:
            timeout: 超时时间
            
        Returns:
            队列中的消息或None
        """
        try:
            return await asyncio.wait_for(self.message_queue.get(), timeout=timeout)
        except asyncio.TimeoutError:
            return None
        except Exception as e:
            self.logger.error(f"消息出队失败: {e}")
            return None
    
    def get_queue_size(self) -> int:
        """获取消息队列大小"""
        return self.message_queue.qsize()
    
    async def disconnect(self, reason: str = "normal") -> None:
        """
        断开连接
        
        Args:
            reason: 断开原因
        """
        with self.lock:
            if self.state in [SessionState.DISCONNECTED, SessionState.TERMINATED]:
                return
            
            self.logger.info(f"断开会话连接: {reason}")
            
            # 取消心跳任务
            if self.heartbeat_task:
                self.heartbeat_task.cancel()
                self.heartbeat_task = None
            
            # 设置状态
            self.set_state(SessionState.DISCONNECTED)
            
            # 关闭套接字
            try:
                self.socket.close()
            except:
                pass
            
            # 清空消息队列
            while not self.message_queue.empty():
                try:
                    self.message_queue.get_nowait()
                except asyncio.QueueEmpty:
                    break
            
            # 触发断开事件
            self._trigger_event('disconnected', {
                'session_id': self.session_id,
                'reason': reason
            })
    
    def suspend(self) -> None:
        """挂起会话（临时断开，保留状态）"""
        with self.lock:
            if self.state == SessionState.DISCONNECTED:
                return
            
            self.set_state(SessionState.SUSPENDED)
            self.logger.info("会话已挂起")
    
    def resume(self, new_socket: socket.socket, new_address: tuple) -> bool:
        """
        恢复会话
        
        Args:
            new_socket: 新的套接字
            new_address: 新的地址
            
        Returns:
            是否恢复成功
        """
        with self.lock:
            if self.state != SessionState.SUSPENDED:
                return False
            
            # 更新连接信息
            self.socket = new_socket
            self.client_address = new_address
            self.metrics.reconnect_count += 1
            
            # 恢复状态
            if self.is_authenticated:
                self.set_state(SessionState.AUTHENTICATED)
            else:
                self.set_state(SessionState.CONNECTED)
            
            # 重启心跳
            if self.is_authenticated:
                self._start_heartbeat()
            
            self.logger.info(f"会话已恢复，重连次数: {self.metrics.reconnect_count}")
            return True
    
    def add_event_callback(self, event_type: str, callback: Callable) -> None:
        """
        添加事件回调
        
        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        if event_type in self.event_callbacks:
            self.event_callbacks[event_type].append(callback)
    
    def remove_event_callback(self, event_type: str, callback: Callable) -> None:
        """
        移除事件回调
        
        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        if event_type in self.event_callbacks and callback in self.event_callbacks[event_type]:
            self.event_callbacks[event_type].remove(callback)
    
    def _trigger_event(self, event_type: str, event_data: Dict[str, Any]) -> None:
        """
        触发事件
        
        Args:
            event_type: 事件类型
            event_data: 事件数据
        """
        if event_type in self.event_callbacks:
            for callback in self.event_callbacks[event_type]:
                try:
                    callback(event_data)
                except Exception as e:
                    self.logger.error(f"事件回调异常 [{event_type}]: {e}")
    
    def get_session_info(self) -> Dict[str, Any]:
        """获取会话信息"""
        return {
            "session_id": self.session_id,
            "player_name": self.player_name,
            "player_id": self.player_id,
            "client_address": f"{self.client_address[0]}:{self.client_address[1]}",
            "state": self.state.value,
            "is_authenticated": self.is_authenticated,
            "current_room": self.current_room,
            "is_ready": self.is_ready,
            "language": self.language,
            "connection_time": self.connection_time,
            "last_activity": self.last_activity,
            "authentication_time": self.authentication_time,
            "connection_duration": time.time() - self.connection_time,
            "idle_time": time.time() - self.last_activity,
            "heartbeat_status": {
                "last_heartbeat": self.last_heartbeat,
                "missed_heartbeats": self.missed_heartbeats,
                "heartbeat_interval": self.heartbeat_interval
            },
            "queue_status": {
                "message_queue_size": self.get_queue_size(),
                "pending_messages": len(self.pending_messages)
            },
            "metrics": {
                "messages_sent": self.metrics.messages_sent,
                "messages_received": self.metrics.messages_received,
                "bytes_sent": self.metrics.bytes_sent,
                "bytes_received": self.metrics.bytes_received,
                "heartbeats_sent": self.metrics.heartbeats_sent,
                "heartbeats_received": self.metrics.heartbeats_received,
                "reconnect_count": self.metrics.reconnect_count
            }
        }
    
    def is_alive(self) -> bool:
        """检查会话是否活跃"""
        return self.state not in [SessionState.DISCONNECTED, SessionState.TERMINATED]
    
    def is_idle(self, timeout_seconds: int = 300) -> bool:
        """检查是否空闲超时"""
        return time.time() - self.last_activity > timeout_seconds
    
    def get_connection_duration(self) -> float:
        """获取连接持续时间"""
        return time.time() - self.connection_time
    
    def get_idle_time(self) -> float:
        """获取空闲时间"""
        return time.time() - self.last_activity


class EnhancedSessionManager:
    """增强的会话管理器"""
    
    def __init__(self, persistence_dir: str = "sessions"):
        """
        初始化增强会话管理器
        
        Args:
            persistence_dir: 会话持久化目录
        """
        self.sessions: Dict[str, EnhancedPlayerSession] = {}
        self.player_name_to_session: Dict[str, str] = {}
        self.suspended_sessions: Dict[str, Dict[str, Any]] = {}
        
        # 持久化设置
        self.persistence_dir = Path(persistence_dir)
        self.persistence_dir.mkdir(exist_ok=True)
        self.enable_persistence = True
        
        # 线程安全锁
        self.lock = threading.RLock()
        
        # 清理任务
        self.cleanup_task = None
        self.cleanup_interval = 60  # 清理间隔（秒）
        
        # 统计信息
        self.stats = {
            "sessions_created": 0,
            "sessions_destroyed": 0,
            "sessions_suspended": 0,
            "sessions_resumed": 0,
            "cleanup_runs": 0
        }
        
        # 日志记录器
        self.logger = get_server_logger("EnhancedSessionManager")
        
        # 启动清理任务
        self._start_cleanup_task()
        
        self.logger.info("增强会话管理器初始化完成")
    
    def create_session(self, session_id: str, socket: socket.socket, 
                      client_address: tuple) -> EnhancedPlayerSession:
        """
        创建新会话
        
        Args:
            session_id: 会话ID
            socket: 套接字
            client_address: 客户端地址
            
        Returns:
            创建的会话对象
        """
        with self.lock:
            session = EnhancedPlayerSession(session_id, socket, client_address)
            
            # 添加事件回调
            session.add_event_callback('disconnected', self._on_session_disconnected)
            session.add_event_callback('heartbeat_timeout', self._on_heartbeat_timeout)
            
            self.sessions[session_id] = session
            self.stats["sessions_created"] += 1
            
            self.logger.info(f"创建增强会话: {session_id}")
            return session
    
    def get_session(self, session_id: str) -> Optional[EnhancedPlayerSession]:
        """获取会话"""
        return self.sessions.get(session_id)
    
    def get_session_by_player_name(self, player_name: str) -> Optional[EnhancedPlayerSession]:
        """根据玩家名称获取会话"""
        session_id = self.player_name_to_session.get(player_name)
        if session_id:
            return self.get_session(session_id)
        return None
    
    async def authenticate_session(self, session_id: str, player_name: str) -> Dict[str, Any]:
        """
        认证会话
        
        Args:
            session_id: 会话ID
            player_name: 玩家名称
            
        Returns:
            认证结果
        """
        with self.lock:
            session = self.get_session(session_id)
            if not session:
                return {
                    "success": False,
                    "error": "session_not_found",
                    "message": "会话不存在"
                }
            
            # 检查玩家名称是否已被使用
            if player_name in self.player_name_to_session:
                existing_session_id = self.player_name_to_session[player_name]
                existing_session = self.get_session(existing_session_id)
                
                if existing_session and existing_session.is_alive():
                    return {
                        "success": False,
                        "error": "name_taken",
                        "message": "玩家名称已被使用"
                    }
                else:
                    # 清理无效映射
                    del self.player_name_to_session[player_name]
            
            # 认证会话
            if session.authenticate(player_name):
                self.player_name_to_session[player_name] = session_id
                
                # 持久化会话状态
                if self.enable_persistence:
                    await self._persist_session(session)
                
                return {
                    "success": True,
                    "player_name": player_name,
                    "session_id": session_id
                }
            else:
                return {
                    "success": False,
                    "error": "authentication_failed",
                    "message": "认证失败"
                }
    
    async def remove_session(self, session_id: str, reason: str = "normal") -> None:
        """
        移除会话
        
        Args:
            session_id: 会话ID
            reason: 移除原因
        """
        with self.lock:
            session = self.get_session(session_id)
            if not session:
                return
            
            # 清理玩家名称映射
            if session.player_name in self.player_name_to_session:
                del self.player_name_to_session[session.player_name]
            
            # 断开会话
            await session.disconnect(reason)
            
            # 从会话列表中移除
            del self.sessions[session_id]
            self.stats["sessions_destroyed"] += 1
            
            # 清理持久化数据
            if self.enable_persistence:
                await self._remove_persisted_session(session_id)
            
            self.logger.info(f"移除会话: {session_id} (原因: {reason})")
    
    async def suspend_session(self, session_id: str) -> bool:
        """
        挂起会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            是否挂起成功
        """
        with self.lock:
            session = self.get_session(session_id)
            if not session:
                return False
            
            # 保存会话状态
            session_state = {
                'session_info': session.get_session_info(),
                'suspend_time': time.time()
            }
            
            self.suspended_sessions[session_id] = session_state
            session.suspend()
            self.stats["sessions_suspended"] += 1
            
            # 持久化挂起状态
            if self.enable_persistence:
                await self._persist_suspended_session(session_id, session_state)
            
            self.logger.info(f"挂起会话: {session_id}")
            return True
    
    async def resume_session(self, session_id: str, new_socket: socket.socket, 
                           new_address: tuple) -> bool:
        """
        恢复会话
        
        Args:
            session_id: 会话ID
            new_socket: 新套接字
            new_address: 新地址
            
        Returns:
            是否恢复成功
        """
        with self.lock:
            session = self.get_session(session_id)
            if not session:
                # 尝试从挂起会话中恢复
                if session_id in self.suspended_sessions:
                    session_state = self.suspended_sessions[session_id]
                    
                    # 重新创建会话
                    session = EnhancedPlayerSession(session_id, new_socket, new_address)
                    
                    # 恢复会话状态
                    info = session_state['session_info']
                    session.player_name = info['player_name']
                    session.player_id = info['player_id']
                    session.language = info['language']
                    session.current_room = info['current_room']
                    session.is_ready = info['is_ready']
                    session.is_authenticated = info['is_authenticated']
                    
                    # 添加到会话列表
                    self.sessions[session_id] = session
                    
                    # 恢复玩家名称映射
                    if session.player_name:
                        self.player_name_to_session[session.player_name] = session_id
                    
                    # 清理挂起状态
                    del self.suspended_sessions[session_id]
                    
                    self.stats["sessions_resumed"] += 1
                    self.logger.info(f"从挂起状态恢复会话: {session_id}")
                    return True
                
                return False
            
            # 恢复现有会话
            if session.resume(new_socket, new_address):
                self.stats["sessions_resumed"] += 1
                self.logger.info(f"恢复会话: {session_id}")
                return True
            
            return False
    
    def get_active_sessions(self) -> Dict[str, EnhancedPlayerSession]:
        """获取所有活跃会话"""
        return {
            session_id: session
            for session_id, session in self.sessions.items()
            if session.is_alive()
        }
    
    def get_authenticated_sessions(self) -> Dict[str, EnhancedPlayerSession]:
        """获取所有已认证会话"""
        return {
            session_id: session
            for session_id, session in self.sessions.items()
            if session.is_authenticated and session.is_alive()
        }
    
    def get_sessions_in_room(self, room_id: str) -> Dict[str, EnhancedPlayerSession]:
        """获取指定房间内的会话"""
        return {
            session_id: session
            for session_id, session in self.sessions.items()
            if session.current_room == room_id and session.is_alive()
        }
    
    async def cleanup_sessions(self) -> Dict[str, int]:
        """清理会话"""
        cleanup_stats = {
            "idle_sessions": 0,
            "disconnected_sessions": 0,
            "expired_suspended": 0
        }
        
        current_time = time.time()
        sessions_to_remove = []
        
        # 清理空闲和断开的会话
        for session_id, session in self.sessions.items():
            if not session.is_alive():
                sessions_to_remove.append((session_id, "disconnected"))
                cleanup_stats["disconnected_sessions"] += 1
            elif session.is_idle(300):  # 5分钟空闲
                sessions_to_remove.append((session_id, "idle"))
                cleanup_stats["idle_sessions"] += 1
        
        # 移除会话
        for session_id, reason in sessions_to_remove:
            await self.remove_session(session_id, reason)
        
        # 清理过期的挂起会话（24小时）
        expired_suspended = []
        for session_id, session_state in self.suspended_sessions.items():
            if current_time - session_state['suspend_time'] > 86400:  # 24小时
                expired_suspended.append(session_id)
        
        for session_id in expired_suspended:
            del self.suspended_sessions[session_id]
            cleanup_stats["expired_suspended"] += 1
            
            # 清理持久化数据
            if self.enable_persistence:
                await self._remove_persisted_session(session_id)
        
        self.stats["cleanup_runs"] += 1
        
        if sum(cleanup_stats.values()) > 0:
            self.logger.info(f"会话清理完成: {cleanup_stats}")
        
        return cleanup_stats
    
    def _start_cleanup_task(self) -> None:
        """启动清理任务"""
        async def cleanup_loop():
            while True:
                try:
                    await asyncio.sleep(self.cleanup_interval)
                    await self.cleanup_sessions()
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    self.logger.error(f"清理任务异常: {e}")
        
        # 创建清理任务（只在有事件循环时）
        try:
            self.cleanup_task = asyncio.create_task(cleanup_loop())
        except RuntimeError:
            # 没有运行的事件循环，跳过清理任务创建
            self.logger.debug("没有运行的事件循环，跳过清理任务创建")
    
    def _on_session_disconnected(self, event_data: Dict[str, Any]) -> None:
        """会话断开事件处理"""
        session_id = event_data['session_id']
        reason = event_data['reason']
        
        self.logger.info(f"会话断开事件: {session_id} (原因: {reason})")
        
        # 如果是心跳超时，尝试挂起而不是直接移除
        if reason == "heartbeat_timeout":
            asyncio.create_task(self.suspend_session(session_id))
    
    def _on_heartbeat_timeout(self, event_data: Dict[str, Any]) -> None:
        """心跳超时事件处理"""
        session_id = event_data['session_id']
        missed_count = event_data['missed_count']
        
        self.logger.warning(f"心跳超时: {session_id} (错过次数: {missed_count})")
    
    async def _persist_session(self, session: EnhancedPlayerSession) -> None:
        """持久化会话状态"""
        if not self.enable_persistence:
            return
        
        try:
            session_file = self.persistence_dir / f"{session.session_id}.pkl"
            session_data = {
                'session_info': session.get_session_info(),
                'persist_time': time.time()
            }
            
            with open(session_file, 'wb') as f:
                pickle.dump(session_data, f)
                
        except Exception as e:
            self.logger.error(f"持久化会话失败 {session.session_id}: {e}")
    
    async def _persist_suspended_session(self, session_id: str, session_state: Dict[str, Any]) -> None:
        """持久化挂起会话状态"""
        if not self.enable_persistence:
            return
        
        try:
            suspended_file = self.persistence_dir / f"{session_id}_suspended.pkl"
            
            with open(suspended_file, 'wb') as f:
                pickle.dump(session_state, f)
                
        except Exception as e:
            self.logger.error(f"持久化挂起会话失败 {session_id}: {e}")
    
    async def _remove_persisted_session(self, session_id: str) -> None:
        """移除持久化会话数据"""
        try:
            session_file = self.persistence_dir / f"{session_id}.pkl"
            suspended_file = self.persistence_dir / f"{session_id}_suspended.pkl"
            
            if session_file.exists():
                session_file.unlink()
            
            if suspended_file.exists():
                suspended_file.unlink()
                
        except Exception as e:
            self.logger.error(f"移除持久化会话数据失败 {session_id}: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        active_sessions = self.get_active_sessions()
        authenticated_sessions = self.get_authenticated_sessions()
        
        return {
            "total_sessions": len(self.sessions),
            "active_sessions": len(active_sessions),
            "authenticated_sessions": len(authenticated_sessions),
            "suspended_sessions": len(self.suspended_sessions),
            "sessions_created": self.stats["sessions_created"],
            "sessions_destroyed": self.stats["sessions_destroyed"],
            "sessions_suspended": self.stats["sessions_suspended"],
            "sessions_resumed": self.stats["sessions_resumed"],
            "cleanup_runs": self.stats["cleanup_runs"],
            "average_connection_time": (
                sum(s.get_connection_duration() for s in active_sessions.values()) / 
                len(active_sessions) if active_sessions else 0
            ),
            "total_messages_sent": sum(s.metrics.messages_sent for s in self.sessions.values()),
            "total_messages_received": sum(s.metrics.messages_received for s in self.sessions.values()),
            "total_heartbeats": sum(s.metrics.heartbeats_received for s in self.sessions.values())
        }
    
    async def shutdown(self) -> None:
        """关闭会话管理器"""
        self.logger.info("关闭增强会话管理器")
        
        # 取消清理任务
        if self.cleanup_task:
            self.cleanup_task.cancel()
        
        # 断开所有会话
        for session_id in list(self.sessions.keys()):
            await self.remove_session(session_id, "server_shutdown")
        
        self.logger.info("增强会话管理器已关闭")