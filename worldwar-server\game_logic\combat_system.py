#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
战斗系统模块 (单位战斗重构版)
Combat System Module (Unit-based Refactor)
"""

import random
import math
import uuid
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
from shared.enhanced_logger import get_server_logger

class CombatResultType(Enum):
    """战斗结果类型枚举"""
    VICTORY = "victory"
    DEFEAT = "defeat"
    DRAW = "draw"

@dataclass
class CombatAction:
    """战斗行动 (简化版)"""
    action_id: str
    attacker_id: str
    defender_id: str
    target_territory: str
    attacker_units: Dict[str, int]

@dataclass
class CombatResult:
    """战斗结果 (简化版)"""
    action_id: str
    winner: Optional[str]
    loser: Optional[str]
    result_type: CombatResultType
    attacker_casualties: Dict[str, int]
    defender_casualties: Dict[str, int]
    description: str = ""

class CombatSystem:
    """战斗系统管理器 (单位战斗重构版)"""

    def __init__(self, player_manager, economic_system, game_settings: Dict[str, Any]):
        """初始化战斗系统"""
        self.player_manager = player_manager
        self.economic_system = economic_system
        self.unit_definitions = game_settings.get("units", {})
        self.pending_actions: List[CombatAction] = []
        self.combat_history: List[CombatResult] = []
        
        self.logger = get_server_logger("CombatSystem")
        self.logger.info("战斗系统初始化完成 (单位战斗版)")

    def add_combat_action(self, attacker_id: str, defender_id: str, target_territory: str, attacker_units: Dict[str, int]):
        """添加一个战斗行动"""
        action = CombatAction(
            action_id=str(uuid.uuid4()),
            attacker_id=attacker_id,
            defender_id=defender_id,
            target_territory=target_territory,
            attacker_units=attacker_units
        )
        self.pending_actions.append(action)
        self.logger.info(f"添加战斗行动: {attacker_id} 攻击 {defender_id} 于 {target_territory}")

    def process_combat_actions(self) -> List[CombatResult]:
        """处理所有待定的战斗行动"""
        results = []
        for action in self.pending_actions:
            result = self._resolve_combat(action)
            results.append(result)
            self.combat_history.append(result)
        
        self.pending_actions.clear()
        return results

    def _calculate_total_force(self, units: Dict[str, int]) -> Tuple[float, float]:
        """计算一支部队的总攻击力和防御力"""
        total_attack = 0.0
        total_defense = 0.0
        for unit_type, quantity in units.items():
            unit_def = self.unit_definitions.get(unit_type)
            if unit_def:
                total_attack += unit_def.get("attack", 0) * quantity
                total_defense += unit_def.get("defense", 0) * quantity
        return total_attack, total_defense

    def _resolve_combat(self, action: CombatAction) -> CombatResult:
        """解决一场具体的单位战斗"""
        attacker = self.player_manager.get_player(action.attacker_id)
        defender = self.player_manager.get_player(action.defender_id)

        if not attacker or not defender:
            # 如果找不到玩家，则战斗无法进行
            return CombatResult(
                action_id=action.action_id, winner=None, loser=None,
                result_type=CombatResultType.DRAW,
                attacker_casualties={}, defender_casualties={},
                description="战斗取消：找不到玩家。"
            )

        # 获取防御方的所有单位作为防守部队
        defender_units = defender.military_units

        # 计算双方总战斗力
        attacker_attack, _ = self._calculate_total_force(action.attacker_units)
        _, defender_defense = self._calculate_total_force(defender_units)

        # 添加随机性
        attacker_power = attacker_attack * random.uniform(0.8, 1.2)
        defender_power = defender_defense * random.uniform(0.8, 1.2)

        power_ratio = attacker_power / defender_power if defender_power > 0 else float('inf')

        # 计算伤亡
        if power_ratio > 1: # 攻击方胜利
            winner_id, loser_id = attacker.player_id, defender.player_id
            result_type = CombatResultType.VICTORY
            # 胜利方损失较小，失败方损失较大
            attacker_loss_ratio = 0.1 + 0.2 / (power_ratio or 1)
            defender_loss_ratio = 0.3 + 0.4 * (power_ratio or 1)
        else: # 防御方胜利或平局
            winner_id, loser_id = defender.player_id, attacker.player_id
            result_type = CombatResultType.DEFEAT
            attacker_loss_ratio = 0.3 + 0.4 / (power_ratio or 1)
            defender_loss_ratio = 0.1 + 0.2 * (power_ratio or 1)

        attacker_casualties = self._calculate_casualties(action.attacker_units, attacker_loss_ratio)
        defender_casualties = self._calculate_casualties(defender_units, defender_loss_ratio)

        # 更新玩家单位
        self._apply_casualties(attacker, attacker_casualties)
        self._apply_casualties(defender, defender_casualties)
        
        # 领土变更
        if result_type == CombatResultType.VICTORY:
            territory = self.economic_system.get_territory_economy(action.target_territory)
            if territory:
                territory.owner = winner_id
            description = f"{winner_id} 成功占领了 {action.target_territory}！"
        else:
            description = f"{loser_id} 成功守住了 {action.target_territory}！"

        return CombatResult(
            action_id=action.action_id,
            winner=winner_id,
            loser=loser_id,
            result_type=result_type,
            attacker_casualties=attacker_casualties,
            defender_casualties=defender_casualties,
            description=description
        )

    def _calculate_casualties(self, units: Dict[str, int], loss_ratio: float) -> Dict[str, int]:
        """根据损失率计算伤亡单位"""
        casualties = {}
        for unit_type, quantity in units.items():
            lost_quantity = min(quantity, math.ceil(quantity * loss_ratio))
            casualties[unit_type] = lost_quantity
        return casualties

    def _apply_casualties(self, player, casualties: Dict[str, int]):
        """将伤亡应用到玩家的单位上"""
        for unit_type, lost_quantity in casualties.items():
            player.military_units[unit_type] = player.military_units.get(unit_type, 0) - lost_quantity
            if player.military_units[unit_type] < 0:
                player.military_units[unit_type] = 0
        self.logger.info(f"玩家 {player.player_id} 损失了单位: {casualties}")

    def pay_upkeep_costs(self):
        """为所有玩家处理单位维护费"""
        for player in self.player_manager.get_active_players():
            total_upkeep = {}
            for unit_type, quantity in player.military_units.items():
                unit_def = self.unit_definitions.get(unit_type)
                if unit_def and "upkeep" in unit_def:
                    for resource, amount in unit_def["upkeep"].items():
                        total_upkeep[resource] = total_upkeep.get(resource, 0) + amount * quantity
            
            # 扣除维护费
            can_afford = True
            for resource, amount in total_upkeep.items():
                if not player.consume_resource(resource, amount):
                    self.logger.warning(f"玩家 {player.player_id} 无法支付 {amount} {resource} 的维护费！")
                    # 此处可以添加惩罚逻辑，例如解散部分单位
                    can_afford = False
            
            if can_afford and total_upkeep:
                self.logger.info(f"玩家 {player.player_id} 支付了单位维护费: {total_upkeep}")
