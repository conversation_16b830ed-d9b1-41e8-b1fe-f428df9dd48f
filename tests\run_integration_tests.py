#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成测试运行器
Integration Test Runner

系统性地运行所有集成测试并生成报告
Systematically runs all integration tests and generates reports
"""

import sys
import os
import subprocess
import time
import json
from pathlib import Path
from typing import Dict, List, Any
import argparse

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

class IntegrationTestRunner:
    """集成测试运行器"""
    
    def __init__(self, verbose: bool = False, generate_report: bool = True):
        self.verbose = verbose
        self.generate_report = generate_report
        self.test_results = {}
        self.start_time = None
        self.end_time = None
        
        # 测试套件配置
        self.test_suites = {
            "basic": {
                "name": "基础集成测试",
                "file": "test_basic_integration.py",
                "description": "测试项目结构、配置和基本组件"
            },
            "room_management": {
                "name": "房间管理集成测试", 
                "file": "test_room_management_integration.py",
                "description": "测试房间创建、加入、离开等功能"
            },
            "terrain": {
                "name": "地形系统集成测试",
                "file": "test_terrain_integration.py", 
                "description": "测试地形生成和数据处理"
            },
            "realworld": {
                "name": "真实世界数据集成测试",
                "file": "test_realworld_generator.py",
                "description": "测试真实世界数据获取和处理"
            },
            "performance": {
                "name": "性能监控集成测试",
                "file": "test_performance_monitor_integration.py",
                "description": "测试性能监控和资源管理"
            },
            "system_monitoring": {
                "name": "系统监控集成测试",
                "file": "test_system_monitoring_integration.py",
                "description": "测试系统监控和日志分析"
            },
            "comprehensive": {
                "name": "综合集成测试",
                "file": "test_comprehensive_integration.py",
                "description": "测试完整系统集成和端到端场景"
            }
        }
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有集成测试"""
        print("🚀 开始运行集成测试套件...")
        print("=" * 60)
        
        self.start_time = time.time()
        
        # 检查测试环境
        if not self._check_test_environment():
            print("❌ 测试环境检查失败")
            return {"success": False, "error": "环境检查失败"}
        
        # 运行各个测试套件
        for suite_id, suite_config in self.test_suites.items():
            print(f"\n📋 运行测试套件: {suite_config['name']}")
            print(f"   描述: {suite_config['description']}")
            print("-" * 40)
            
            result = self._run_test_suite(suite_id, suite_config)
            self.test_results[suite_id] = result
            
            if result["success"]:
                print(f"✅ {suite_config['name']} - 通过")
            else:
                print(f"❌ {suite_config['name']} - 失败")
                if self.verbose and "error" in result:
                    print(f"   错误: {result['error']}")
        
        self.end_time = time.time()
        
        # 生成测试报告
        if self.generate_report:
            self._generate_test_report()
        
        # 显示总结
        self._print_test_summary()
        
        return {
            "success": self._all_tests_passed(),
            "results": self.test_results,
            "duration": self.end_time - self.start_time
        }
    
    def run_specific_test(self, suite_id: str) -> Dict[str, Any]:
        """运行特定的测试套件"""
        if suite_id not in self.test_suites:
            return {"success": False, "error": f"未知的测试套件: {suite_id}"}
        
        suite_config = self.test_suites[suite_id]
        print(f"🚀 运行测试套件: {suite_config['name']}")
        
        self.start_time = time.time()
        result = self._run_test_suite(suite_id, suite_config)
        self.end_time = time.time()
        
        self.test_results[suite_id] = result
        
        if result["success"]:
            print(f"✅ 测试通过")
        else:
            print(f"❌ 测试失败")
            if "error" in result:
                print(f"错误: {result['error']}")
        
        return result
    
    def _check_test_environment(self) -> bool:
        """检查测试环境"""
        print("🔍 检查测试环境...")
        
        # 检查必要的目录
        required_dirs = [
            "tests/integration",
            "tests/unit", 
            "tests/data",
            "worldwar-server",
            "worldwar-client"
        ]
        
        for dir_path in required_dirs:
            if not Path(dir_path).exists():
                print(f"❌ 缺少必要目录: {dir_path}")
                return False
        
        # 检查测试数据文件
        required_files = [
            "tests/data/sample_language.json",
            "tests/data/sample_terrain.json", 
            "tests/data/sample_api_response.json"
        ]
        
        for file_path in required_files:
            if not Path(file_path).exists():
                print(f"❌ 缺少测试数据文件: {file_path}")
                return False
        
        # 检查pytest是否可用
        try:
            result = subprocess.run(
                ["python", "-m", "pytest", "--version"],
                capture_output=True, text=True, timeout=10
            )
            if result.returncode != 0:
                print("❌ pytest 不可用")
                return False
        except Exception as e:
            print(f"❌ pytest 检查失败: {e}")
            return False
        
        print("✅ 测试环境检查通过")
        return True
    
    def _run_test_suite(self, suite_id: str, suite_config: Dict[str, str]) -> Dict[str, Any]:
        """运行单个测试套件"""
        test_file = Path("tests/integration") / suite_config["file"]
        
        if not test_file.exists():
            return {
                "success": False,
                "error": f"测试文件不存在: {test_file}",
                "duration": 0
            }
        
        # 构建pytest命令
        cmd = [
            "python", "-m", "pytest",
            str(test_file),
            "-v" if self.verbose else "-q",
            "--tb=short",
            "--disable-warnings",
            "-x"  # 遇到第一个失败就停止
        ]
        
        start_time = time.time()
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300,  # 5分钟超时
                cwd=Path.cwd()
            )
            
            duration = time.time() - start_time
            
            if result.returncode == 0:
                return {
                    "success": True,
                    "duration": duration,
                    "output": result.stdout
                }
            else:
                return {
                    "success": False,
                    "duration": duration,
                    "error": result.stderr,
                    "output": result.stdout
                }
                
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "duration": time.time() - start_time,
                "error": "测试超时"
            }
        except Exception as e:
            return {
                "success": False,
                "duration": time.time() - start_time,
                "error": str(e)
            }
    
    def _generate_test_report(self):
        """生成测试报告"""
        report_data = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_duration": self.end_time - self.start_time,
            "total_suites": len(self.test_suites),
            "passed_suites": sum(1 for r in self.test_results.values() if r["success"]),
            "failed_suites": sum(1 for r in self.test_results.values() if not r["success"]),
            "results": self.test_results
        }
        
        # 保存JSON报告
        report_file = Path("tests/integration_test_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        # 生成HTML报告
        self._generate_html_report(report_data)
        
        print(f"\n📊 测试报告已生成: {report_file}")
    
    def _generate_html_report(self, report_data: Dict[str, Any]):
        """生成HTML测试报告"""
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>集成测试报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background: #f5f5f5; padding: 20px; border-radius: 5px; }}
        .summary {{ display: flex; gap: 20px; margin: 20px 0; }}
        .metric {{ background: #e9ecef; padding: 15px; border-radius: 5px; text-align: center; }}
        .success {{ color: #28a745; }}
        .failure {{ color: #dc3545; }}
        .test-suite {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
        .test-suite.passed {{ border-left: 5px solid #28a745; }}
        .test-suite.failed {{ border-left: 5px solid #dc3545; }}
        pre {{ background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 集成测试报告</h1>
        <p>生成时间: {report_data['timestamp']}</p>
        <p>总耗时: {report_data['total_duration']:.2f} 秒</p>
    </div>
    
    <div class="summary">
        <div class="metric">
            <h3>总测试套件</h3>
            <div style="font-size: 2em;">{report_data['total_suites']}</div>
        </div>
        <div class="metric">
            <h3 class="success">通过</h3>
            <div style="font-size: 2em; color: #28a745;">{report_data['passed_suites']}</div>
        </div>
        <div class="metric">
            <h3 class="failure">失败</h3>
            <div style="font-size: 2em; color: #dc3545;">{report_data['failed_suites']}</div>
        </div>
    </div>
    
    <h2>📋 详细结果</h2>
"""
        
        for suite_id, result in report_data['results'].items():
            suite_config = self.test_suites[suite_id]
            status_class = "passed" if result["success"] else "failed"
            status_icon = "✅" if result["success"] else "❌"
            
            html_content += f"""
    <div class="test-suite {status_class}">
        <h3>{status_icon} {suite_config['name']}</h3>
        <p><strong>描述:</strong> {suite_config['description']}</p>
        <p><strong>耗时:</strong> {result.get('duration', 0):.2f} 秒</p>
"""
            
            if not result["success"] and "error" in result:
                html_content += f"""
        <p><strong>错误信息:</strong></p>
        <pre>{result['error']}</pre>
"""
            
            if "output" in result and result["output"]:
                html_content += f"""
        <details>
            <summary>详细输出</summary>
            <pre>{result['output']}</pre>
        </details>
"""
            
            html_content += "    </div>\n"
        
        html_content += """
</body>
</html>
"""
        
        html_file = Path("tests/integration_test_report.html")
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
    
    def _print_test_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 60)
        print("📊 测试总结")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results.values() if r["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试套件: {total_tests}")
        print(f"✅ 通过: {passed_tests}")
        print(f"❌ 失败: {failed_tests}")
        print(f"⏱️  总耗时: {self.end_time - self.start_time:.2f} 秒")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试套件:")
            for suite_id, result in self.test_results.items():
                if not result["success"]:
                    suite_name = self.test_suites[suite_id]["name"]
                    print(f"   - {suite_name}")
        
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        print(f"\n🎯 成功率: {success_rate:.1f}%")
        
        if success_rate == 100:
            print("🎉 所有测试都通过了！")
        elif success_rate >= 80:
            print("👍 大部分测试通过，还有一些需要修复")
        else:
            print("⚠️  需要修复多个测试问题")
    
    def _all_tests_passed(self) -> bool:
        """检查是否所有测试都通过"""
        return all(result["success"] for result in self.test_results.values())
    
    def list_available_tests(self):
        """列出可用的测试套件"""
        print("📋 可用的测试套件:")
        print("-" * 40)
        
        for suite_id, suite_config in self.test_suites.items():
            print(f"🔹 {suite_id}: {suite_config['name']}")
            print(f"   {suite_config['description']}")
            print()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="集成测试运行器")
    parser.add_argument(
        "--suite", "-s",
        help="运行特定的测试套件"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="详细输出"
    )
    parser.add_argument(
        "--no-report",
        action="store_true", 
        help="不生成测试报告"
    )
    parser.add_argument(
        "--list", "-l",
        action="store_true",
        help="列出可用的测试套件"
    )
    
    args = parser.parse_args()
    
    runner = IntegrationTestRunner(
        verbose=args.verbose,
        generate_report=not args.no_report
    )
    
    if args.list:
        runner.list_available_tests()
        return
    
    if args.suite:
        result = runner.run_specific_test(args.suite)
        sys.exit(0 if result["success"] else 1)
    else:
        result = runner.run_all_tests()
        sys.exit(0 if result["success"] else 1)


if __name__ == "__main__":
    main()