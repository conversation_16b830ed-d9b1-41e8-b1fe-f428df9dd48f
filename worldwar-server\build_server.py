#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务端打包脚本
Server Packaging Script
"""

import os
import sys
import shutil
from pathlib import Path

def create_directories():
    """创建必要的目录"""
    print("创建目录结构...")
    directories = [
        "dist/WorldWarServer/config",
        "dist/WorldWarServer/logs",
        "dist/WorldWarServer/logs/server",
        "dist/WorldWarServer/data",
        "dist/WorldWarServer/data/cache",
        "dist/WorldWarServer/saves",
        "dist/WorldWarServer/languages"
    ]

    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"  OK {directory}")

def copy_resources():
    """复制资源文件"""
    print("\n复制资源文件...")
    resources = [
        ("languages/chinese.json", "dist/WorldWarServer/languages/"),
        ("languages/english.json", "dist/WorldWarServer/languages/"),
        ("config/server_config.ini", "dist/WorldWarServer/config/"),
        ("README.md", "dist/WorldWarServer/")
    ]

    for src, dst in resources:
        if os.path.exists(src):
            os.makedirs(os.path.dirname(dst), exist_ok=True)
            shutil.copy2(src, dst)
            print(f"  OK {src} -> {dst}")
        else:
            print(f"  WARNING 文件不存在: {src}")

def create_readme():
    """创建使用说明"""
    print("\n创建使用说明...")
    readme_content = '''# 世界大战策略游戏服务端 / World War Strategy Game Server

## 🚀 使用方法 / Usage

### 启动服务器 / Start Server
1. 双击 WorldWarServer.exe 启动服务器
2. 首次运行会显示语言选择界面：
   - 1. 中文
   - 2. English  
   - 3. Bilingual Mode / 双语模式 (推荐)
3. 选择语言后按任意键继续
4. 服务器将在 localhost:8888 启动
5. 看到 "服务器准备就绪" 消息表示启动成功

### 停止服务器 / Stop Server
- 按 Ctrl+C 优雅停止服务器
- 或直接关闭窗口

## ⚙️ 配置文件 / Configuration Files
- `config/server_config.ini` - 服务器主配置
- `config/server_language.json` - 语言设置 (自动生成)
- `config/security.json` - 安全配置 (自动生成)

## 📝 日志文件 / Log Files
- `logs/latest.log` - 最新日志
- `logs/server/` - 服务器专用日志
- `logs/game_YYYYMMDD_HHMMSS.log` - 历史日志

## 🌐 网络设置 / Network Settings
- 默认端口: 8888
- 默认地址: localhost (本机)
- 支持命令行参数: WorldWarServer.exe --host 0.0.0.0 --port 9999

## ⚠️ 注意事项 / Important Notes
- 确保端口8888未被其他程序占用
- Windows防火墙可能需要允许程序通过
- 首次运行需要选择语言偏好
- 服务器数据保存在 data/ 和 saves/ 目录

## 🔧 故障排除 / Troubleshooting
- 如果端口被占用，尝试更改端口号
- 查看 logs/latest.log 获取详细错误信息
- 删除 config/ 目录可重置所有设置

## 📞 技术支持 / Technical Support
- 查看项目文档获取更多信息
- 检查日志文件诊断问题
'''
    
    with open("dist/WorldWarServer/README.txt", "w", encoding="utf-8") as f:
        f.write(readme_content)
    print("  OK README.txt")

def create_batch_file():
    """创建启动批处理文件"""
    print("\n🔧 创建启动脚本...")
    batch_content = '''@echo off
chcp 65001 >nul 2>&1
title World War Game Server

echo.
echo ============================================================
echo 🖥️  World War Strategy Game Server / 世界大战策略游戏服务器
echo ============================================================
echo.

WorldWarServer.exe

pause
'''
    
    with open("dist/WorldWarServer/StartServer.bat", "w", encoding="utf-8") as f:
        f.write(batch_content)
    print("  ✅ StartServer.bat")

def create_config_template():
    """创建配置文件模板"""
    print("\n⚙️ 创建配置模板...")
    
    # 服务器配置模板
    server_config = '''[server]
host = localhost
port = 8888
max_connections = 100
debug = false

[game]
max_players_per_room = 8
min_players_per_room = 3
room_timeout = 300

[logging]
level = INFO
max_log_files = 10
max_log_size_mb = 10
'''
    
    config_path = "dist/WorldWarServer/config/server_config.ini"
    if not os.path.exists(config_path):
        with open(config_path, "w", encoding="utf-8") as f:
            f.write(server_config)
        print("  ✅ server_config.ini")

def main():
    """主函数"""
    print("世界大战游戏服务端打包准备")
    print("=" * 50)
    
    try:
        create_directories()
        copy_resources()
        create_readme()
        create_batch_file()
        create_config_template()
        
        print("\n" + "=" * 50)
        print("✅ 服务端打包准备完成！")
        print("\n📋 下一步:")
        print("1. 运行: pyinstaller server.spec")
        print("2. 检查: dist/WorldWarServer/ 目录")
        print("3. 测试: 运行生成的 WorldWarServer.exe")
        
    except Exception as e:
        print(f"\n❌ 打包准备失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
