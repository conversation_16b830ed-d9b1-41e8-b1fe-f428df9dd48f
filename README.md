# 🎮 世界大战游戏 (World War Game)

**版本**: v5.1 (Stable Edition)
**类型**: 分离式客户端-服务器游戏
**语言**: Python 3.8+

---

## 📋 项目简介

世界大战游戏是一个功能丰富的多人在线策略游戏。本项目经过了全面的重构和增强，拥有一个健壮的服务器后端、现代化的客户端界面，以及创新的地形生成系统。

### 🌟 主要特性

#### 核心游戏功能
- 🔐 **安全通信**: 增强的客户端-服务器通信协议
- 🏠 **房间管理**: 完整的房间创建、加入、配置系统
- 👥 **多玩家支持**: 实时多玩家对战，支持最多8人同时游戏
- ⚔️ **深度游戏逻辑**: 经济、政策、战斗、胜利条件系统
- 🗃️ **数据驱动**: 所有游戏规则均由JSON文件配置

#### 🗺️ 创新地形系统
- 🎲 **AI生成地形**: 基于柏林噪声的程序化地形生成
- 🌍 **真实世界地形**: 基于真实地理和经济数据的地形生成
- 🎨 **地形预览**: 实时地形可视化和预览功能
- 🏔️ **多样化生物群系**: 平原、森林、山脉、沙漠等多种地形类型

#### 🌐 国际化支持
- 🇨🇳 **多语言界面**: 完整的中英文双语支持
- 🔄 **动态语言切换**: 无需重启即可切换语言
- 📝 **本地化文本**: 所有界面文本完全本地化

#### 🚀 性能与监控
- ⚡ **性能优化**: 优化的并发处理和资源管理
- 📊 **实时监控**: 系统性能和资源使用监控
- 🔍 **智能日志**: 结构化日志和问题诊断工具
- 🛠️ **调试工具**: 完整的开发和运维工具集

#### 🧪 质量保证
- ✅ **全面测试**: 单元测试、集成测试、用户验收测试
- 🔒 **稳定性**: 增强的错误处理和恢复机制
- 📈 **可扩展性**: 支持高并发和大规模部署

---

## 🆕 最新更新 (v5.1)

### ✅ 代码质量改进
- **语法错误修复**: 修复了所有语法错误和缩进问题
- **代码清理**: 清理了未使用的导入和变量
- **错误处理**: 改进了异常处理和错误恢复机制

### 🌐 国际化完善
- **硬编码字符串消除**: 移除了所有硬编码的中英文字符串
- **语言管理器增强**: 完善了客户端和服务端的语言管理系统
- **文本一致性**: 确保所有用户界面文本都通过语言管理器处理

### 🔧 启动器优化
- **稳定性提升**: 修复了启动器的各种问题
- **用户体验**: 改进了错误提示和用户引导
- **兼容性**: 确保在不同环境下的稳定运行

### 🧪 测试验证
- **功能验证**: 验证了客户端-服务端连接和通信功能
- **启动测试**: 确保所有启动方式都能正常工作
- **错误修复**: 处理了测试过程中发现的所有问题

---

## 🚀 快速开始

### 环境要求
- Python 3.8 或更高版本
- Windows/Linux/macOS

### 启动游戏 (推荐)

我们提供了一个统一的交互式启动脚本。

#### Windows 用户
双击 `start.bat` 即可启动。

#### Linux/Mac 用户
```bash
python3 start_game.py
```

启动器将会引导你安装必要的依赖，并提供菜单选项来分别或同时启动服务器和客户端。

---

## 📁 项目结构

```
worldwar/
├── start_game.py           # 🚀 统一交互式启动器
├── start.bat               # ✨ Windows 快捷方式
├── worldwar-server/        # 🖥️ 独立服务器项目
│   ├── server_main.py      # 服务器主程序
│   ├── server/             # 服务器核心网络与会话管理
│   ├── game_logic/         # 核心游戏逻辑 (经济、战斗等)
│   ├── world/              # 🗺️ 地形生成系统
│   │   ├── terrain/        # 地形数据模型和生成器
│   │   ├── data/           # 真实世界数据处理
│   │   └── resources/      # 资源分布算法
│   ├── shared/             # 🌐 共享组件 (语言管理、日志等)
│   ├── data/               # 游戏数据 (JSON 配置)
│   ├── languages/          # 🇨🇳🇺🇸 多语言文件
│   └── logs/               # 服务器日志
├── worldwar-client/        # 💻 独立客户端项目
│   ├── client_main.py      # 客户端主程序 (Textual TUI)
│   ├── client/             # 客户端核心组件
│   ├── client.css          # TUI 样式文件
│   ├── languages/          # 客户端语言文件
│   └── logs/               # 客户端日志
├── tools/                  # 🛠️ 开发和运维工具
│   ├── performance_monitor.py  # 性能监控工具
│   ├── system_monitor.py       # 系统监控工具
│   ├── log_analyzer.py         # 日志分析工具
│   ├── terrain_preview.py      # 地形预览工具
│   └── language_checker.py     # 语言文件检查工具
├── tests/                  # 🧪 测试套件
│   ├── unit/               # 单元测试
│   ├── integration/        # 集成测试
│   ├── acceptance/         # 用户验收测试
│   ├── performance/        # 性能测试
│   └── data/               # 测试数据
└── docs/                   # 📚 项目文档
```

---

## 🎮 游戏特色功能

### 🗺️ 地形生成系统

#### AI生成地形 (柏林噪声)
- **多层噪声合成**: 使用多个噪声层创建复杂地形
- **生物群系生成**: 基于高度、温度、湿度自动生成不同生物群系
- **资源分布**: 智能的资源分配算法，确保游戏平衡性
- **可配置参数**: 地图大小、种子、复杂度等完全可定制

#### 真实世界地形
- **地理数据集成**: 使用真实的地理API获取地形数据
- **经济数据融合**: 整合世界银行等经济数据源
- **城市和区域**: 基于真实城市数据生成游戏地图
- **教育价值**: 在游戏中学习真实世界地理和经济知识

### 🌐 多语言系统
- **完整本地化**: 所有界面文本、错误信息、帮助文档
- **动态切换**: 游戏运行时即时切换语言
- **扩展性**: 易于添加新语言支持
- **文本管理工具**: 自动检测缺失翻译和文本一致性

### 🚀 性能与监控
- **实时性能监控**: CPU、内存、网络使用情况
- **智能日志系统**: 结构化日志，便于问题诊断
- **资源优化**: 内存池、对象复用、并发优化
- **自动化工具**: 性能分析、日志分析、系统监控

---

## 🔧 开发与部署

### 环境设置

#### 快速安装
启动器 `start_game.py` 会在首次运行时提示安装依赖。

#### 手动安装依赖
```bash
# 安装服务器依赖
pip install -r worldwar-server/requirements.txt

# 安装客户端依赖  
pip install -r worldwar-client/requirements.txt

# 安装开发工具依赖
pip install pytest pytest-asyncio pytest-cov
```

### 运行方式

#### 使用启动器 (推荐)
```bash
python start_game.py
```

#### 独立运行
```bash
# 启动服务器
python worldwar-server/server_main.py

# 启动客户端
python worldwar-client/client_main.py
```

#### 开发模式
```bash
# 运行测试套件
python -m pytest tests/ -v

# 运行集成测试
python tests/run_integration_tests.py

# 性能监控
python tools/performance_monitor.py

# 地形预览
python tools/terrain_preview.py
```

### 🛠️ 开发工具

#### 测试工具
- **单元测试**: `pytest tests/unit/`
- **集成测试**: `python tests/run_integration_tests.py`
- **用户验收测试**: `pytest tests/acceptance/`
- **性能测试**: `pytest tests/performance/`

#### 监控工具
- **性能监控**: `python tools/performance_monitor.py`
- **系统监控**: `python tools/system_monitor.py`
- **日志分析**: `python tools/log_analyzer.py`

#### 内容工具
- **地形预览**: `python tools/terrain_preview.py`
- **语言检查**: `python tools/language_checker.py`

---

## 📚 文档 / Documentation

- **[游戏规则](docs/GAME_RULES.md)** - 详细的游戏规则和策略指南
- **[技术文档](docs/TECHNICAL.md)** - 技术架构和API文档
- **[项目完成报告](docs/PROJECT_COMPLETION_REPORT.md)** - 重构项目总结

---

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

---

## 📄 许可证

本项目采用 MIT 许可证。
