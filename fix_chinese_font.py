#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复中文字体显示问题
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import platform
import os

def fix_chinese_font():
    """修复中文字体显示"""
    print("🔧 修复中文字体显示...")
    
    # 重新构建字体缓存
    try:
        # 新版本matplotlib的方法
        if hasattr(fm.fontManager, 'addfont'):
            print("✅ 使用新版matplotlib字体管理")
        else:
            print("✅ 使用旧版matplotlib字体管理")
    except Exception as e:
        print(f"⚠️  字体管理器检查失败: {e}")
    
    # 设置中文字体
    system = platform.system()
    if system == "Windows":
        font_name = 'Microsoft YaHei'
    elif system == "Darwin":
        font_name = 'PingFang SC'
    else:
        font_name = 'WenQuanYi Micro Hei'
    
    # 强制设置字体
    plt.rcParams['font.sans-serif'] = [font_name, 'DejaVu Sans', 'Arial']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.family'] = 'sans-serif'
    
    print(f"✅ 已设置字体: {font_name}")
    
    # 测试中文显示
    import numpy as np
    fig, ax = plt.subplots(figsize=(8, 6))
    
    x = np.linspace(0, 10, 100)
    y = np.sin(x)
    
    ax.plot(x, y)
    ax.set_title('中文字体测试 - 地形高度图', fontsize=16, fontweight='bold')
    ax.set_xlabel('X 坐标', fontsize=12)
    ax.set_ylabel('Y 坐标', fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 添加中文统计信息
    stats_text = '最低: -10m\n最高: 1200m\n平均: 500m'
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('test.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print("✅ 测试图像已保存: test.png")
    print("请检查图像中的中文是否正确显示")

if __name__ == "__main__":
    fix_chinese_font()