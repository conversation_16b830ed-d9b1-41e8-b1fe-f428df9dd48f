#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
房间管理器模块
Room Manager Module
"""

import time
import threading

from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
from .room_id_generator import get_room_id_generator, RoomIdType

class RoomStatus(Enum):
    """房间状态枚举"""
    WAITING = "waiting"           # 等待玩家
    STARTING = "starting"         # 正在开始
    IN_PROGRESS = "in_progress"   # 游戏进行中
    PAUSED = "paused"            # 暂停
    FINISHED = "finished"        # 已结束
    CLOSED = "closed"            # 已关闭

@dataclass
class GameRoom:
    """游戏房间类"""
    room_id: str
    room_name: str
    host_player: str
    max_players: int
    game_mode: str
    difficulty: str = "normal"
    status: RoomStatus = RoomStatus.WAITING
    created_time: float = field(default_factory=time.time)
    last_activity: float = field(default_factory=time.time)

    # 人数限制
    min_players: int = 2  # 最少玩家数（开始游戏需要）
    is_private: bool = False  # 是否私人房间
    password: str = ""  # 房间密码
    
    # 玩家管理
    players: Dict[str, Any] = field(default_factory=dict)  # session_id -> PlayerSession
    player_ready_status: Dict[str, bool] = field(default_factory=dict)
    
    # 游戏状态
    game_started: bool = False
    current_turn: int = 0
    current_player: str = ""
    
    # 房间设置
    auto_start: bool = True
    start_countdown: int = 10
    
    def add_player(self, session) -> Dict[str, Any]:
        """
        添加玩家到房间

        Returns:
            Dict包含success状态和错误信息
        """
        # 检查房间是否已满
        if len(self.players) >= self.max_players:
            return {
                "success": False,
                "error": "room_full",
                "message": f"房间已满 ({len(self.players)}/{self.max_players})"
            }

        # 检查玩家是否已在房间中
        if session.session_id in self.players:
            return {
                "success": False,
                "error": "already_in_room",
                "message": "玩家已在房间中"
            }

        # 检查房间状态
        if self.status not in [RoomStatus.WAITING, RoomStatus.STARTING]:
            return {
                "success": False,
                "error": "room_not_available",
                "message": f"房间状态不允许加入 ({self.status.value})"
            }

        # 添加玩家
        self.players[session.session_id] = session
        self.player_ready_status[session.session_id] = False
        session.current_room = self.room_id

        self.last_activity = time.time()
        return {
            "success": True,
            "error": None,
            "message": f"成功加入房间 ({len(self.players)}/{self.max_players})"
        }
    
    def remove_player(self, session_id: str) -> bool:
        """从房间移除玩家"""
        if session_id in self.players:
            del self.players[session_id]
            if session_id in self.player_ready_status:
                del self.player_ready_status[session_id]
            
            # 如果房主离开，转移房主权限
            if self.host_player == session_id and self.players:
                self.host_player = next(iter(self.players.keys()))
            
            self.last_activity = time.time()
            return True
        return False
    
    def set_player_ready(self, session_id: str, ready: bool):
        """设置玩家准备状态"""
        if session_id in self.player_ready_status:
            self.player_ready_status[session_id] = ready
            self.last_activity = time.time()
    
    def get_player_count(self) -> int:
        """获取当前玩家数量"""
        return len(self.players)
    
    def get_ready_count(self) -> int:
        """获取准备的玩家数量"""
        return sum(1 for ready in self.player_ready_status.values() if ready)
    
    def can_start_game(self) -> Dict[str, Any]:
        """
        检查是否可以开始游戏

        Returns:
            Dict包含can_start状态和详细信息
        """
        current_players = self.get_player_count()

        # 检查人数是否足够
        if current_players < self.min_players:
            return {
                "can_start": False,
                "reason": "insufficient_players",
                "message": f"人数不足，需要至少{self.min_players}人 (当前{current_players}人)"
            }

        # 检查房间状态
        if self.status != RoomStatus.WAITING:
            return {
                "can_start": False,
                "reason": "invalid_status",
                "message": f"房间状态不允许开始游戏 ({self.status.value})"
            }

        # 检查所有玩家是否都准备好
        ready_count = self.get_ready_count()
        if ready_count < current_players:
            return {
                "can_start": False,
                "reason": "players_not_ready",
                "message": f"玩家未全部准备 ({ready_count}/{current_players})"
            }

        return {
            "can_start": True,
            "reason": None,
            "message": f"可以开始游戏 ({current_players}人已准备)"
        }
    
    def get_room_info(self) -> Dict[str, Any]:
        """获取房间信息"""
        return {
            "room_id": self.room_id,
            "room_name": self.room_name,
            "host_player": self.host_player,
            "max_players": self.max_players,
            "current_players": self.get_player_count(),
            "game_mode": self.game_mode,
            "difficulty": self.difficulty,
            "status": self.status.value,
            "game_started": self.game_started,
            "players": [
                {
                    "session_id": session_id,
                    "player_name": session.player_name,
                    "ready": self.player_ready_status.get(session_id, False),
                    "is_host": session_id == self.host_player
                }
                for session_id, session in self.players.items()
            ]
        }

class RoomManager:
    """房间管理器"""
    
    def __init__(self, protocol=None):
        """初始化房间管理器"""
        self.rooms: Dict[str, GameRoom] = {}
        self.room_counter = 0
        self.lock = threading.Lock()

        # ID生成器
        self.id_generator = get_room_id_generator()

        # 房间配置
        self.default_id_type = RoomIdType.ALPHANUMERIC
        self.min_players = 2  # 最少玩家数

        # 初始化消息管理器
        from shared.message_manager import ServerMessageManager
        self.message_manager = ServerMessageManager(protocol)
        self.max_players_limit = 8  # 最大玩家数限制

        # 导入日志
        from shared.enhanced_logger import get_server_logger
        self.logger = get_server_logger("RoomManager")

        self.logger.info("房间管理器初始化完成 / Room manager initialized")

        # 房间超时设置
        self.room_timeout_minutes = 30  # 30分钟无活动自动清理
        self.empty_room_timeout_minutes = 5  # 空房间5分钟后清理
    
    def create_room(self, room_name: str, host_player: str, max_players: int,
                   game_mode: str, difficulty: str = "normal",
                   id_type: RoomIdType = None) -> Optional[GameRoom]:
        """创建新房间"""
        with self.lock:
            # 验证参数
            validation_result = self._validate_room_parameters(
                room_name, max_players, host_player
            )
            if not validation_result["valid"]:
                self.logger.error(f"创建房间失败: {validation_result['error']}")
                return None

            # 生成唯一房间ID
            if id_type is None:
                id_type = self.default_id_type

            room_id = self.id_generator.generate_id(id_type)
            if not room_id:
                self.logger.error("生成房间ID失败")
                return None

            # 双重检查ID是否冲突
            if room_id in self.rooms:
                self.logger.error(f"房间ID冲突: {room_id}")
                self.id_generator.release_id(room_id)
                return None

            room = GameRoom(
                room_id=room_id,
                room_name=room_name,
                host_player=host_player,
                max_players=max_players,
                game_mode=game_mode,
                difficulty=difficulty
            )

            self.rooms[room_id] = room
            self.logger.info(f"创建房间: {room_name} (ID: {room_id}, 类型: {id_type.value})")

            return room

    def _validate_room_parameters(self, room_name: str, max_players: int,
                                 host_player: str) -> Dict[str, Any]:
        """验证房间创建参数"""
        # 检查房间名称
        if not room_name or not room_name.strip():
            return {"valid": False, "error": "房间名称不能为空"}

        if len(room_name.strip()) > 50:
            return {"valid": False, "error": "房间名称过长（最多50字符）"}

        # 检查玩家数量
        if max_players < self.min_players:
            return {"valid": False, "error": f"最大玩家数不能少于{self.min_players}"}

        if max_players > self.max_players_limit:
            return {"valid": False, "error": f"最大玩家数不能超过{self.max_players_limit}"}

        # 检查房主名称
        if not host_player or not host_player.strip():
            return {"valid": False, "error": "房主名称不能为空"}

        # 检查房间数量限制
        if len(self.rooms) >= 1000:  # 最多1000个房间
            return {"valid": False, "error": "服务器房间数量已达上限"}

        return {"valid": True, "error": None}

    def get_room_status_info(self, room_id: str) -> Dict[str, Any]:
        """获取房间状态详细信息"""
        room = self.get_room(room_id)
        if not room:
            return {
                "exists": False,
                "error": "房间不存在"
            }

        current_players = room.get_player_count()
        ready_players = room.get_ready_count()

        # 确定房间状态描述
        if room.status == RoomStatus.WAITING:
            if current_players == 0:
                status_desc = "空房间"
            elif current_players >= room.max_players:
                status_desc = "房间已满"
            elif current_players < room.min_players:
                status_desc = f"等待玩家 (需要{room.min_players}人)"
            else:
                status_desc = "等待准备"
        elif room.status == RoomStatus.STARTING:
            status_desc = "即将开始"
        elif room.status == RoomStatus.IN_PROGRESS:
            status_desc = "游戏中"
        elif room.status == RoomStatus.PAUSED:
            status_desc = "游戏暂停"
        elif room.status == RoomStatus.FINISHED:
            status_desc = "游戏结束"
        else:
            status_desc = "房间关闭"

        return {
            "exists": True,
            "room_id": room_id,
            "status": room.status.value,
            "status_description": status_desc,
            "current_players": current_players,
            "max_players": room.max_players,
            "min_players": room.min_players,
            "ready_players": ready_players,
            "is_full": current_players >= room.max_players,
            "can_join": (room.status == RoomStatus.WAITING and
                        current_players < room.max_players),
            "can_start": room.can_start_game()["can_start"],
            "is_private": room.is_private,
            "created_time": room.created_time,
            "game_started": room.game_started
        }

    def get_room(self, room_id: str) -> Optional[GameRoom]:
        """获取房间"""
        return self.rooms.get(room_id)
    
    def join_room(self, room_id: str, session, password: str = "") -> Dict[str, Any]:
        """
        加入房间

        Returns:
            Dict包含success状态和详细信息
        """
        with self.lock:
            room = self.get_room(room_id)
            if not room:
                return {
                    "success": False,
                    "error": "room_not_found",
                    "message": f"房间不存在: {room_id}"
                }

            # 检查房间密码
            if room.is_private and room.password != password:
                return {
                    "success": False,
                    "error": "invalid_password",
                    "message": "房间密码错误"
                }

            # 尝试添加玩家
            add_result = room.add_player(session)
            if add_result["success"]:
                self.logger.info(f"玩家 {session.player_name} 加入房间 {room_id}")

                # 广播房间更新
                self._broadcast_room_update(room)

                return {
                    "success": True,
                    "error": None,
                    "message": add_result["message"],
                    "room_info": room.get_room_info()
                }
            else:
                self.logger.warning(f"玩家 {session.player_name} 加入房间失败: {add_result['message']}")
                return add_result
    
    def leave_room(self, room_id: str, session) -> bool:
        """离开房间"""
        with self.lock:
            room = self.get_room(room_id)
            if not room:
                return False
            
            if room.remove_player(session.session_id):
                session.current_room = None
                self.logger.info(f"玩家 {session.player_name} 离开房间 {room_id}")
                
                # 如果房间空了，关闭房间
                if room.get_player_count() == 0:
                    self._close_room(room_id)
                else:
                    # 广播房间更新
                    self._broadcast_room_update(room)
                
                return True
            
            return False
    
    def set_player_ready(self, room_id: str, session_id: str, ready: bool):
        """设置玩家准备状态"""
        with self.lock:
            room = self.get_room(room_id)
            if room:
                room.set_player_ready(session_id, ready)
                self.logger.info(f"玩家 {session_id} 设置准备状态: {ready}")
                
                # 广播房间更新
                self._broadcast_room_update(room)
                
                # 检查是否可以开始游戏
                if room.can_start_game() and room.auto_start:
                    self._start_game_countdown(room)
    
    def start_game(self, room_id: str) -> Dict[str, Any]:
        """
        开始游戏

        Returns:
            Dict包含success状态和详细信息
        """
        with self.lock:
            room = self.get_room(room_id)
            if not room:
                return {
                    "success": False,
                    "error": "room_not_found",
                    "message": f"房间不存在: {room_id}"
                }

            # 检查是否可以开始游戏
            start_check = room.can_start_game()
            if not start_check["can_start"]:
                return {
                    "success": False,
                    "error": start_check["reason"],
                    "message": start_check["message"]
                }

            room.status = RoomStatus.IN_PROGRESS
            room.game_started = True
            room.current_turn = 1

            # 设置第一个玩家
            if room.players:
                room.current_player = next(iter(room.players.keys()))

            self.logger.info(f"房间 {room_id} 开始游戏 ({room.get_player_count()}人)")

            # 广播游戏开始
            self._broadcast_game_start(room)

            return {
                "success": True,
                "error": None,
                "message": f"游戏已开始 ({room.get_player_count()}人参与)"
            }
    
    def handle_game_action(self, room_id: str, session, action_data: Dict[str, Any]):
        """处理游戏动作"""
        room = self.get_room(room_id)
        if not room or room.status != RoomStatus.IN_PROGRESS:
            return
        
        # 检查是否是当前玩家的回合
        if room.current_player != session.session_id:
            self._send_error_to_player(session, "不是你的回合 / Not your turn")
            return
        
        # 处理具体的游戏动作
        self._process_game_action(room, session, action_data)
    
    def broadcast_chat_message(self, room_id: str, sender_session, message_data: Dict[str, Any]):
        """广播聊天消息"""
        room = self.get_room(room_id)
        if not room:
            return
        
        from shared.message_types import MessageType

        chat_message = {
            "type": MessageType.CHAT_MESSAGE,
            "data": {
                "sender": sender_session.player_name,
                "message": message_data.get("message", ""),
                "timestamp": time.time()
            }
        }

        # 记录聊天消息到日志
        self.logger.info(f"聊天消息 - {sender_session.player_name}: {message_data.get('message', '')}")
        
        # 发送给房间内所有玩家
        self._broadcast_to_room(room, chat_message)
    
    def get_room_list(self) -> List[Dict[str, Any]]:
        """获取房间列表"""
        with self.lock:
            return [
                {
                    "room_id": room.room_id,
                    "room_name": room.room_name,
                    "current_players": room.get_player_count(),
                    "max_players": room.max_players,
                    "status": room.status.value,
                    "game_mode": room.game_mode,
                    "host_player": room.host_player
                }
                for room in self.rooms.values()
                if room.status in [RoomStatus.WAITING, RoomStatus.STARTING]
            ]
    
    def get_active_room_count(self) -> int:
        """获取活跃房间数量"""
        return len([r for r in self.rooms.values() if r.status != RoomStatus.CLOSED])
    
    def update_rooms(self):
        """更新房间状态（定期调用）"""
        with self.lock:
            current_time = time.time()
            rooms_to_close = []
            
            for room_id, room in self.rooms.items():
                # 清理空房间
                if room.get_player_count() == 0 and room.status == RoomStatus.WAITING:
                    if current_time - room.created_time > 300:  # 5分钟无人加入则关闭
                        rooms_to_close.append(room_id)
                
                # 清理已结束的游戏
                elif room.status == RoomStatus.FINISHED:
                    if current_time - room.created_time > 3600:  # 1小时后清理
                        rooms_to_close.append(room_id)
            
            # 关闭需要清理的房间
            for room_id in rooms_to_close:
                self._close_room(room_id)

            # 清理未使用的ID
            if rooms_to_close:
                active_room_ids = set(self.rooms.keys())
                self.id_generator.clear_unused_ids(active_room_ids)
    
    def _close_room(self, room_id: str):
        """关闭房间"""
        if room_id in self.rooms:
            room = self.rooms[room_id]
            room.status = RoomStatus.CLOSED

            # 通知所有玩家房间关闭
            close_message = {
                "type": "room_closed",
                "data": {"room_id": room_id}
            }
            self._broadcast_to_room(room, close_message)

            # 清理玩家的房间引用
            for session in room.players.values():
                session.current_room = None

            # 释放房间ID
            self.id_generator.release_id(room_id)

            del self.rooms[room_id]
            self.logger.info(f"房间 {room_id} 已关闭并释放ID")
    
    def _broadcast_room_update(self, room: GameRoom):
        """广播房间更新"""
        update_message = {
            "type": "room_update",
            "data": room.get_room_info()
        }
        self._broadcast_to_room(room, update_message)
    
    def _broadcast_game_start(self, room: GameRoom):
        """广播游戏开始"""
        start_message = {
            "type": "game_start",
            "data": {
                "room_id": room.room_id,
                "current_player": room.current_player,
                "turn_number": room.current_turn
            }
        }
        self._broadcast_to_room(room, start_message)
    
    def _broadcast_to_room(self, room: GameRoom, message: Dict[str, Any]):
        """向房间内所有玩家广播消息"""
        for session in room.players.values():
            try:
                # 使用安全消息发送
                self.message_manager.send_secure_message(
                    session.socket,
                    message.get("type", "unknown"),
                    message.get("data", {})
                )
            except Exception as e:
                self.logger.error(f"发送消息到玩家 {session.player_name} 失败: {e}")
    
    def _send_error_to_player(self, session, error_message: str):
        """向玩家发送错误消息"""
        try:
            self.message_manager.send_secure_message(
                session.socket,
                "error",
                {"message": error_message}
            )
        except Exception as e:
            self.logger.error(f"发送错误消息失败: {e}")
    
    def _start_game_countdown(self, room: GameRoom):
        """开始游戏倒计时"""
        def countdown():
            for i in range(room.start_countdown, 0, -1):
                if not room.can_start_game():
                    return  # 如果条件不满足，取消倒计时
                
                countdown_message = {
                    "type": "game_starting",
                    "data": {"countdown": i}
                }
                self._broadcast_to_room(room, countdown_message)
                time.sleep(1)
            
            # 倒计时结束，开始游戏
            if room.can_start_game():
                self.start_game(room.room_id)
        
        # 在新线程中运行倒计时
        countdown_thread = threading.Thread(target=countdown, daemon=True)
        countdown_thread.start()
    
    def _process_game_action(self, room: 'GameRoom', session, action_data: Dict[str, Any]):
        """处理游戏动作（占位符，将来实现具体逻辑）"""
        action_type = action_data.get("action_type")
        
        # 这里将来会实现具体的游戏逻辑
        self.logger.info(f"处理游戏动作: {action_type} 来自玩家 {session.player_name}")
        
        # 发送动作结果
        result_message = {
            "type": "action_result",
            "data": {
                "action_type": action_type,
                "success": True,
                "message": "动作执行成功"
            }
        }
        
        try:
            from server.network_handler import NetworkHandler
            network_handler = NetworkHandler()
            network_handler.send_message(session.socket, result_message)
        except Exception as e:
            self.logger.error(f"发送动作结果失败: {e}")

    def get_room_statistics(self) -> Dict[str, Any]:
        """获取房间统计信息"""
        with self.lock:
            total_rooms = len(self.rooms)
            status_counts = {}
            player_counts = {"total": 0, "by_room": []}

            for room in self.rooms.values():
                # 统计房间状态
                status = room.status.value
                status_counts[status] = status_counts.get(status, 0) + 1

                # 统计玩家数量
                player_count = room.get_player_count()
                player_counts["total"] += player_count
                player_counts["by_room"].append({
                    "room_id": room.room_id,
                    "players": player_count,
                    "max_players": room.max_players
                })

            return {
                "total_rooms": total_rooms,
                "status_distribution": status_counts,
                "player_statistics": player_counts,
                "id_generator_stats": self.id_generator.get_id_statistics(),
                "room_limits": {
                    "min_players": self.min_players,
                    "max_players_limit": self.max_players_limit
                }
            }

    def find_available_rooms(self, max_players: int = None) -> List[Dict[str, Any]]:
        """查找可用的房间"""
        with self.lock:
            available_rooms = []

            for room in self.rooms.values():
                if room.status != RoomStatus.WAITING:
                    continue

                if room.get_player_count() >= room.max_players:
                    continue

                if max_players and room.max_players > max_players:
                    continue

                room_info = room.get_room_info()
                room_info["available_slots"] = room.max_players - room.get_player_count()
                available_rooms.append(room_info)

            # 按可用位置数量排序
            available_rooms.sort(key=lambda x: x["available_slots"], reverse=True)
            return available_rooms

    def create_room_with_custom_id(self, room_name: str, host_player: str,
                                  max_players: int, game_mode: str,
                                  custom_id: str = None, difficulty: str = "normal") -> Optional[GameRoom]:
        """使用自定义ID创建房间"""
        with self.lock:
            # 验证参数
            validation_result = self._validate_room_parameters(
                room_name, max_players, host_player
            )
            if not validation_result["valid"]:
                self.logger.error(f"创建房间失败: {validation_result['error']}")
                return None

            # 处理自定义ID
            if custom_id:
                # 验证自定义ID格式
                if len(custom_id) < 3 or len(custom_id) > 20:
                    self.logger.error("自定义ID长度必须在3-20字符之间")
                    return None

                # 检查ID是否已存在
                if custom_id in self.rooms or self.id_generator.is_id_exists(custom_id):
                    self.logger.error(f"自定义ID已存在: {custom_id}")
                    return None

                room_id = custom_id
                self.id_generator.register_id(room_id)
            else:
                # 生成自动ID
                room_id = self.id_generator.generate_id(self.default_id_type)
                if not room_id:
                    self.logger.error("生成房间ID失败")
                    return None

            room = GameRoom(
                room_id=room_id,
                room_name=room_name,
                host_player=host_player,
                max_players=max_players,
                game_mode=game_mode,
                difficulty=difficulty
            )

            self.rooms[room_id] = room
            self.logger.info(f"创建房间: {room_name} (ID: {room_id})")

            return room

    def cleanup_expired_rooms(self):
        """清理过期的房间"""
        with self.lock:
            expired_rooms = []
            current_time = time.time()

            for room_id, room in self.rooms.items():
                # 检查空房间
                if room.get_player_count() == 0 and current_time - room.created_time > self.empty_room_timeout_minutes * 60:
                    expired_rooms.append(room_id)
                    self.logger.info(f"清理空房间: {room.room_name} (ID: {room_id})")
                    continue

                # 检查长时间无活动的房间
                if current_time - room.last_activity > self.room_timeout_minutes * 60:
                    expired_rooms.append(room_id)
                    self.logger.info(f"清理无活动房间: {room.room_name} (ID: {room_id})")
                    self._notify_room_cleanup(room)

            # 移除过期房间
            for room_id in expired_rooms:
                if room_id in self.rooms:
                    self._close_room(room_id)

            if expired_rooms:
                self.logger.info(f"已清理 {len(expired_rooms)} 个过期房间")

    def _notify_room_cleanup(self, room):
        """通知房间清理"""
        cleanup_message = {
            "type": "room_cleanup",
            "data": {
                "message": "房间因长时间无活动被清理，您已被移出房间",
                "reason": "timeout"
            }
        }
        self._broadcast_to_room(room, cleanup_message)
        for session in room.players.values():
            session.current_room = None
