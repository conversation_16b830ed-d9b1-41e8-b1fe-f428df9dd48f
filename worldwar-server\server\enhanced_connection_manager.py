#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强连接管理器模块
Enhanced Connection Manager Module

基于资源管理器的优化连接处理，支持连接池、并发优化等
Optimized connection handling based on resource manager, supports connection pooling, concurrency optimization, etc.
"""

import socket
import threading
import time
import asyncio
import sys
from pathlib import Path
from typing import Dict, Any, Optional, Callable, List
from concurrent.futures import ThreadPoolExecutor, Future
import queue

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from server.player_session import PlayerSession, SessionManager
from server.network_handler import NetworkHandler
from server.resource_manager import get_resource_manager, ResourceManager
from shared.secure_protocol import SecureProtocol
from shared.message_types import MessageType
from shared.enhanced_logger import get_server_logger


class ConnectionWorker:
    """连接工作器 - 处理单个连接的所有操作"""
    
    def __init__(self, worker_id: str, connection_manager: 'EnhancedConnectionManager'):
        """初始化连接工作器"""
        self.worker_id = worker_id
        self.connection_manager = connection_manager
        self.logger = get_server_logger(f"ConnectionWorker-{worker_id}")
        self.active = True
        
        # 工作器统计
        self.connections_handled = 0
        self.messages_processed = 0
        self.errors_encountered = 0
        self.start_time = time.time()
    
    def handle_connection(self, client_socket: socket.socket, client_address: tuple) -> None:
        """处理客户端连接"""
        session_id = f"session_{self.worker_id}_{self.connections_handled}_{int(time.time())}"
        player_session = None
        
        try:
            self.connections_handled += 1
            
            # 设置套接字选项
            self._configure_socket(client_socket)
            
            # 创建玩家会话
            player_session = self.connection_manager.session_manager.create_session(
                session_id, client_socket, client_address
            )
            
            # 跟踪会话对象（用于泄漏检测）
            resource_manager = get_resource_manager()
            resource_manager.leak_detector.track_object(player_session, "PlayerSession")
            
            self.logger.info(f"工作器 {self.worker_id} 处理新连接: {client_address}")
            
            # 执行安全握手
            if not self._perform_secure_handshake(client_socket, player_session):
                self.logger.error(f"安全握手失败: {session_id}")
                return
            
            # 发送欢迎消息
            if not self._send_welcome_message(client_socket, session_id):
                self.logger.error(f"发送欢迎消息失败: {session_id}")
                return
            
            # 开始消息处理循环
            self._message_processing_loop(player_session)
            
        except Exception as e:
            self.errors_encountered += 1
            self.logger.error(f"工作器 {self.worker_id} 处理连接时发生错误: {e}")
            
        finally:
            # 清理连接
            self._cleanup_connection(player_session, session_id)
    
    def _configure_socket(self, client_socket: socket.socket):
        """配置套接字选项"""
        try:
            # 设置超时
            client_socket.settimeout(30.0)
            
            # 设置TCP选项
            client_socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
            client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
            
            # 设置缓冲区大小
            client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 65536)
            client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 65536)
            
        except Exception as e:
            self.logger.warning(f"配置套接字选项时发生错误: {e}")
    
    def _perform_secure_handshake(self, client_socket: socket.socket, session: PlayerSession) -> bool:
        """执行安全握手"""
        try:
            protocol = self.connection_manager.protocol
            
            # 发起握手
            if not protocol.initiate_handshake(client_socket):
                return False
            
            # 等待握手响应
            response_msg = protocol.receive_secure_message(client_socket, timeout=10.0)
            if not response_msg or response_msg.get("type") != "handshake_response":
                self.logger.error("未收到有效的握手响应")
                return False
            
            # 完成握手验证
            if protocol.complete_handshake(client_socket, response_msg):
                session_token = getattr(protocol, 'session_tokens', {}).get(client_socket, "")
                session.complete_handshake(session_token)
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"安全握手过程发生错误: {e}")
            return False
    
    def _send_welcome_message(self, client_socket: socket.socket, session_id: str) -> bool:
        """发送欢迎消息"""
        welcome_msg = {
            "type": MessageType.WELCOME,
            "data": {
                "server_name": "增强世界大战游戏服务器 / Enhanced World War Game Server",
                "version": "3.1",
                "session_id": session_id,
                "worker_id": self.worker_id,
                "heartbeat_interval": 30
            }
        }
        
        return self.connection_manager.protocol.send_secure_message(client_socket, welcome_msg)
    
    def _message_processing_loop(self, player_session: PlayerSession):
        """消息处理循环"""
        last_activity = time.time()
        heartbeat_timeout = 60
        message_count = 0
        
        while player_session.is_connected and self.active:
            try:
                message = self.connection_manager.protocol.receive_secure_message(
                    player_session.socket, timeout=5.0
                )
                
                if message:
                    last_activity = time.time()
                    player_session.update_activity()
                    message_count += 1
                    self.messages_processed += 1
                    
                    # 使用任务管理器异步处理消息
                    resource_manager = get_resource_manager()
                    task_id = f"msg_{player_session.session_id}_{message_count}"
                    
                    success = resource_manager.task_manager.submit_task(
                        task_id,
                        self._process_message_async,
                        player_session,
                        message
                    )
                    
                    if not success:
                        self.logger.warning(f"消息处理任务队列已满: {task_id}")
                        # 同步处理消息作为备选方案
                        self._process_message_sync(player_session, message)
                else:
                    # 检查心跳超时
                    current_time = time.time()
                    if current_time - last_activity > heartbeat_timeout:
                        self.logger.warning(f"客户端心跳超时: {player_session.session_id}")
                        break
                    continue
                    
            except socket.timeout:
                # 检查心跳超时
                current_time = time.time()
                if current_time - last_activity > heartbeat_timeout:
                    self.logger.warning(f"客户端心跳超时: {player_session.session_id}")
                    break
                continue
                
            except ConnectionResetError:
                self.logger.info(f"客户端重置连接: {player_session.session_id}")
                break
                
            except Exception as e:
                self.errors_encountered += 1
                self.logger.error(f"处理客户端消息时发生错误: {e}")
                break
    
    def _process_message_async(self, player_session: PlayerSession, message: Dict[str, Any]):
        """异步处理消息"""
        try:
            if self.connection_manager.message_processor:
                self.connection_manager.message_processor(player_session, message)
        except Exception as e:
            self.logger.error(f"异步处理消息时发生错误: {e}")
    
    def _process_message_sync(self, player_session: PlayerSession, message: Dict[str, Any]):
        """同步处理消息"""
        try:
            if self.connection_manager.message_processor:
                self.connection_manager.message_processor(player_session, message)
        except Exception as e:
            self.logger.error(f"同步处理消息时发生错误: {e}")
    
    def _cleanup_connection(self, session: Optional[PlayerSession], session_id: str):
        """清理连接"""
        try:
            if session:
                # 清理安全协议会话
                if session.socket:
                    self.connection_manager.protocol.cleanup_session(session.socket)
                
                self.logger.info(f"工作器 {self.worker_id} 清理连接: {session.client_address}")
            
            # 移除会话
            self.connection_manager.session_manager.remove_session(session_id)
            
            # 调用清理回调
            if self.connection_manager.cleanup_callback:
                self.connection_manager.cleanup_callback(session, session_id)
                
        except Exception as e:
            self.logger.error(f"清理连接时发生错误: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取工作器统计信息"""
        uptime = time.time() - self.start_time
        return {
            "worker_id": self.worker_id,
            "connections_handled": self.connections_handled,
            "messages_processed": self.messages_processed,
            "errors_encountered": self.errors_encountered,
            "uptime_seconds": uptime,
            "messages_per_second": self.messages_processed / uptime if uptime > 0 else 0,
            "error_rate": self.errors_encountered / max(1, self.connections_handled)
        }


class EnhancedConnectionManager:
    """增强连接管理器 - 支持连接池和并发优化"""
    
    def __init__(self, protocol: SecureProtocol, session_manager: SessionManager,
                 network_handler: NetworkHandler, max_workers: int = 20):
        """初始化增强连接管理器"""
        self.protocol = protocol
        self.session_manager = session_manager
        self.network_handler = network_handler
        self.max_workers = max_workers
        self.logger = get_server_logger("EnhancedConnectionManager")
        
        # 工作器管理
        self.workers: Dict[str, ConnectionWorker] = {}
        self.worker_executor = ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="ConnWorker")
        self.connection_queue: queue.Queue = queue.Queue()
        
        # 统计信息
        self.total_connections = 0
        self.active_connections = 0
        self.peak_connections = 0
        self.start_time = time.time()
        
        # 回调函数
        self.message_processor: Optional[Callable] = None
        self.cleanup_callback: Optional[Callable] = None
        
        # 资源管理器
        self.resource_manager = get_resource_manager()
        
        # 启动连接分发器
        self.dispatcher_thread = threading.Thread(target=self._connection_dispatcher, daemon=True)
        self.dispatcher_thread.start()
        
        # 创建消息对象池
        self._setup_object_pools()
        
        self.logger.info(f"增强连接管理器初始化完成，最大工作器数: {max_workers}")
    
    def _setup_object_pools(self):
        """设置对象池"""
        # 消息字典对象池
        def create_message_dict():
            return {}
        
        def cleanup_message_dict(msg_dict):
            msg_dict.clear()
        
        self.resource_manager.create_object_pool(
            "message_dict",
            create_message_dict,
            max_size=200,
            cleanup_func=cleanup_message_dict
        )
        
        # 会话信息对象池
        def create_session_info():
            return {
                "session_id": "",
                "player_name": "",
                "client_address": "",
                "connection_time": 0,
                "last_activity": 0
            }
        
        def cleanup_session_info(info):
            for key in info:
                info[key] = "" if isinstance(info[key], str) else 0
        
        self.resource_manager.create_object_pool(
            "session_info",
            create_session_info,
            max_size=100,
            cleanup_func=cleanup_session_info
        )
    
    def set_message_processor(self, processor: Callable):
        """设置消息处理器回调"""
        self.message_processor = processor
    
    def set_cleanup_callback(self, callback: Callable):
        """设置清理回调"""
        self.cleanup_callback = callback
    
    def handle_client_connection(self, client_socket: socket.socket, client_address: tuple):
        """处理客户端连接（异步分发）"""
        try:
            self.total_connections += 1
            self.active_connections += 1
            
            # 更新峰值连接数
            if self.active_connections > self.peak_connections:
                self.peak_connections = self.active_connections
            
            # 将连接放入队列等待处理
            connection_info = {
                "socket": client_socket,
                "address": client_address,
                "timestamp": time.time()
            }
            
            self.connection_queue.put(connection_info)
            
        except Exception as e:
            self.logger.error(f"处理客户端连接时发生错误: {e}")
            self.active_connections -= 1
    
    def _connection_dispatcher(self):
        """连接分发器 - 将连接分发给工作器"""
        while True:
            try:
                # 获取连接
                connection_info = self.connection_queue.get(timeout=1.0)
                
                # 选择工作器
                worker = self._select_worker()
                
                # 提交给工作器处理
                future = self.worker_executor.submit(
                    worker.handle_connection,
                    connection_info["socket"],
                    connection_info["address"]
                )
                
                # 设置完成回调
                future.add_done_callback(lambda f: self._on_connection_completed(f))
                
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"连接分发时发生错误: {e}")
    
    def _select_worker(self) -> ConnectionWorker:
        """选择工作器（负载均衡）"""
        # 简单的轮询策略
        worker_id = f"worker_{len(self.workers) % self.max_workers}"
        
        if worker_id not in self.workers:
            self.workers[worker_id] = ConnectionWorker(worker_id, self)
        
        return self.workers[worker_id]
    
    def _on_connection_completed(self, future: Future):
        """连接处理完成回调"""
        self.active_connections -= 1
        
        if future.exception():
            self.logger.error(f"连接处理时发生异常: {future.exception()}")
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        uptime = time.time() - self.start_time
        
        # 工作器统计
        worker_stats = {}
        total_messages = 0
        total_errors = 0
        
        for worker_id, worker in self.workers.items():
            stats = worker.get_stats()
            worker_stats[worker_id] = stats
            total_messages += stats["messages_processed"]
            total_errors += stats["errors_encountered"]
        
        return {
            "total_connections": self.total_connections,
            "active_connections": self.active_connections,
            "peak_connections": self.peak_connections,
            "queued_connections": self.connection_queue.qsize(),
            "active_workers": len(self.workers),
            "max_workers": self.max_workers,
            "total_messages_processed": total_messages,
            "total_errors": total_errors,
            "uptime_seconds": uptime,
            "connections_per_second": self.total_connections / uptime if uptime > 0 else 0,
            "messages_per_second": total_messages / uptime if uptime > 0 else 0,
            "error_rate": total_errors / max(1, self.total_connections),
            "worker_stats": worker_stats
        }
    
    def get_resource_usage(self) -> Dict[str, Any]:
        """获取资源使用情况"""
        return self.resource_manager.get_comprehensive_stats()
    
    def optimize_resources(self):
        """优化资源使用"""
        try:
            self.logger.info("开始连接管理器资源优化...")
            
            # 清理空闲会话
            cleaned_sessions = self.session_manager.cleanup_idle_sessions(300)  # 5分钟
            if cleaned_sessions > 0:
                self.logger.info(f"清理了 {cleaned_sessions} 个空闲会话")
            
            # 清理断开连接的会话
            cleaned_disconnected = self.session_manager.cleanup_disconnected_sessions()
            if cleaned_disconnected > 0:
                self.logger.info(f"清理了 {cleaned_disconnected} 个断开连接的会话")
            
            # 调用资源管理器优化
            self.resource_manager.optimize_resources()
            
            self.logger.info("连接管理器资源优化完成")
            
        except Exception as e:
            self.logger.error(f"资源优化时发生错误: {e}")
    
    def shutdown(self):
        """关闭连接管理器"""
        self.logger.info("正在关闭增强连接管理器...")
        
        # 停止所有工作器
        for worker in self.workers.values():
            worker.active = False
        
        # 关闭线程池
        self.worker_executor.shutdown(wait=True)
        
        # 清理所有会话
        for session_id in list(self.session_manager.sessions.keys()):
            self.session_manager.remove_session(session_id)
        
        self.logger.info("增强连接管理器已关闭")
    
    def generate_performance_report(self) -> str:
        """生成性能报告"""
        try:
            stats = self.get_connection_stats()
            resource_stats = self.get_resource_usage()
            
            report = f"""
=== 增强连接管理器性能报告 ===
运行时间: {stats['uptime_seconds']:.0f}秒
总连接数: {stats['total_connections']}
活跃连接: {stats['active_connections']}
峰值连接: {stats['peak_connections']}
排队连接: {stats['queued_connections']}

=== 工作器统计 ===
活跃工作器: {stats['active_workers']}/{stats['max_workers']}
总消息处理: {stats['total_messages_processed']}
消息处理速度: {stats['messages_per_second']:.2f} 消息/秒
错误率: {stats['error_rate']:.2%}

=== 资源使用情况 ===
系统内存: {resource_stats.get('system', {}).get('memory_mb', 0):.1f}MB
系统CPU: {resource_stats.get('system', {}).get('cpu_percent', 0):.1f}%
活跃任务: {resource_stats.get('task_manager', {}).get('active_tasks', 0)}
连接池活跃: {resource_stats.get('connection_pool', {}).get('currently_active', 0)}

=== 工作器详情 ===
"""
            
            for worker_id, worker_stat in stats['worker_stats'].items():
                report += f"""
{worker_id}:
  处理连接: {worker_stat['connections_handled']}
  处理消息: {worker_stat['messages_processed']}
  错误数: {worker_stat['errors_encountered']}
  消息速度: {worker_stat['messages_per_second']:.2f}/秒
"""
            
            report += "=" * 40
            
            return report
            
        except Exception as e:
            self.logger.error(f"生成性能报告时发生错误: {e}")
            return f"性能报告生成失败: {e}"