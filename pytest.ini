[tool:pytest]
# pytest 配置文件
# pytest configuration file

# 测试目录
testpaths = tests

# 测试文件模式
python_files = test_*.py *_test.py

# 测试类模式
python_classes = Test*

# 测试函数模式
python_functions = test_*

# 添加标记
markers =
    unit: marks tests as unit tests
    integration: marks tests as integration tests  
    performance: marks tests as performance tests
    slow: marks tests as slow running tests

# 输出选项
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=worldwar-server
    --cov=worldwar-client
    --cov=tools
    --cov-report=term-missing
    --cov-report=html:htmlcov

# 最小覆盖率要求
# --cov-fail-under=80

# 测试发现
norecursedirs = .git .tox dist build *.egg htmlcov logs saves __pycache__