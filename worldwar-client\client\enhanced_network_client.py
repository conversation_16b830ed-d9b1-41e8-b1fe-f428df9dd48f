#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强网络客户端 - 修复房间创建和连接问题
Enhanced Network Client - Fix room creation and connection issues
"""

import json
import socket
import threading
import time
import queue
from typing import Dict, Any, Optional, Callable, List
from enum import Enum
from .room_ui import RoomUI, UIState


class ConnectionState(Enum):
    """连接状态"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    AUTHENTICATED = "authenticated"
    ERROR = "error"


class MessageQueue:
    """消息队列管理器"""
    
    def __init__(self, max_size: int = 1000):
        self.queue = queue.Queue(maxsize=max_size)
        self.pending_responses: Dict[str, Any] = {}
        self.response_timeout = 30.0  # 30秒响应超时
    
    def add_message(self, message: Dict[str, Any]):
        """添加消息到队列"""
        try:
            self.queue.put_nowait(message)
        except queue.Full:
            # 队列满时丢弃最旧的消息
            try:
                self.queue.get_nowait()
                self.queue.put_nowait(message)
            except queue.Empty:
                pass
    
    def get_message(self, timeout: float = 1.0) -> Optional[Dict[str, Any]]:
        """从队列获取消息"""
        try:
            return self.queue.get(timeout=timeout)
        except queue.Empty:
            return None
    
    def wait_for_response(self, request_id: str, timeout: float = None) -> Optional[Dict[str, Any]]:
        """等待特定响应"""
        if timeout is None:
            timeout = self.response_timeout
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            if request_id in self.pending_responses:
                response = self.pending_responses.pop(request_id)
                return response
            time.sleep(0.1)
        
        return None
    
    def add_response(self, request_id: str, response: Dict[str, Any]):
        """添加响应"""
        self.pending_responses[request_id] = response


class EnhancedNetworkClient:
    """增强网络客户端"""
    
    def __init__(self, host: str = "localhost", port: int = 8888):
        """初始化增强网络客户端"""
        self.host = host
        self.port = port
        self.socket: Optional[socket.socket] = None
        self.connection_state = ConnectionState.DISCONNECTED
        
        # 用户信息
        self.username: Optional[str] = None
        self.session_id: Optional[str] = None
        
        # 消息处理
        self.message_queue = MessageQueue()
        self.message_handlers: Dict[str, Callable] = {}
        
        # 线程管理
        self.receive_thread: Optional[threading.Thread] = None
        self.message_processor_thread: Optional[threading.Thread] = None
        self.heartbeat_thread: Optional[threading.Thread] = None
        self.running = False
        
        # UI管理
        self.room_ui = RoomUI(self)
        self.ui_callbacks: List[Callable] = []
        
        # 连接监控
        self.last_heartbeat = 0
        self.heartbeat_interval = 30.0  # 30秒心跳间隔
        self.connection_timeout = 60.0  # 60秒连接超时
        
        # 重连机制
        self.auto_reconnect = True
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.reconnect_delay = 5.0  # 5秒重连延迟
        
        # 注册默认消息处理器
        self._register_default_handlers()
    
    def add_ui_callback(self, callback: Callable):
        """添加UI更新回调"""
        self.ui_callbacks.append(callback)
        self.room_ui.add_update_callback(callback)
    
    def connect(self, username: str) -> bool:
        """连接到服务器"""
        if self.connection_state != ConnectionState.DISCONNECTED:
            return False
        
        self.username = username
        self.connection_state = ConnectionState.CONNECTING
        
        try:
            # 创建套接字
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10.0)  # 10秒连接超时
            
            # 连接到服务器
            self.socket.connect((self.host, self.port))
            self.connection_state = ConnectionState.CONNECTED
            
            # 启动线程
            self.running = True
            self._start_threads()
            
            # 等待欢迎消息
            welcome_received = self._wait_for_welcome()
            if not welcome_received:
                self.disconnect()
                return False
            
            # 发送认证请求
            auth_success = self._authenticate()
            if not auth_success:
                self.disconnect()
                return False
            
            self.connection_state = ConnectionState.AUTHENTICATED
            self._trigger_ui_update()
            
            return True
            
        except Exception as e:
            print(f"连接失败: {e}")
            self.connection_state = ConnectionState.ERROR
            self.disconnect()
            return False
    
    def disconnect(self):
        """断开连接"""
        self.running = False
        self.connection_state = ConnectionState.DISCONNECTED
        
        # 关闭套接字
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
        
        # 等待线程结束
        self._stop_threads()
        
        # 隐藏UI
        self.room_ui.hide_ui()
        self._trigger_ui_update()
    
    def send_message(self, message_type: str, data: Dict[str, Any] = None) -> Optional[str]:
        """发送消息"""
        if self.connection_state not in [ConnectionState.CONNECTED, ConnectionState.AUTHENTICATED]:
            return None
        
        if data is None:
            data = {}
        
        # 生成请求ID
        request_id = f"{message_type}_{int(time.time() * 1000)}"
        
        message = {
            "type": message_type,
            "data": data,
            "request_id": request_id,
            "timestamp": time.time()
        }
        
        try:
            message_json = json.dumps(message, ensure_ascii=False)
            self.socket.sendall((message_json + '\n').encode('utf-8'))
            return request_id
        except Exception as e:
            print(f"发送消息失败: {e}")
            self._handle_connection_error()
            return None
    
    def send_message_and_wait(self, message_type: str, data: Dict[str, Any] = None, 
                             timeout: float = 10.0) -> Optional[Dict[str, Any]]:
        """发送消息并等待响应"""
        request_id = self.send_message(message_type, data)
        if not request_id:
            return None
        
        return self.message_queue.wait_for_response(request_id, timeout)
    
    def create_room(self, room_name: str, max_players: int = 4, 
                   game_mode: str = "ai_generated", difficulty: str = "normal") -> bool:
        """创建房间"""
        if self.connection_state != ConnectionState.AUTHENTICATED:
            return False
        
        data = {
            "room_name": room_name,
            "max_players": max_players,
            "game_mode": game_mode,
            "difficulty": difficulty
        }
        
        response = self.send_message_and_wait("create_room", data)
        if response and response.get("data", {}).get("success"):
            return True
        
        return False
    
    def join_room(self, room_id: str, password: str = "") -> bool:
        """加入房间"""
        if self.connection_state != ConnectionState.AUTHENTICATED:
            return False
        
        data = {
            "room_id": room_id,
            "password": password
        }
        
        response = self.send_message_and_wait("join_room", data)
        if response and response.get("data", {}).get("success"):
            return True
        
        return False
    
    def leave_room(self) -> bool:
        """离开房间"""
        if self.connection_state != ConnectionState.AUTHENTICATED:
            return False
        
        response = self.send_message_and_wait("leave_room")
        if response:
            self.room_ui.hide_ui()
            self._trigger_ui_update()
            return True
        
        return False
    
    def toggle_ready(self) -> bool:
        """切换准备状态"""
        if self.connection_state != ConnectionState.AUTHENTICATED:
            return False
        
        self.send_message("toggle_ready")
        return True
    
    def start_game(self) -> bool:
        """开始游戏"""
        if self.connection_state != ConnectionState.AUTHENTICATED:
            return False
        
        self.send_message("start_game")
        return True
    
    def request_room_list(self) -> bool:
        """请求房间列表"""
        if self.connection_state != ConnectionState.AUTHENTICATED:
            return False
        
        self.send_message("get_room_list")
        return True
    
    def handle_user_input(self, command: str) -> bool:
        """处理用户输入"""
        # 首先让房间UI处理
        if self.room_ui.handle_user_input(command):
            return True
        
        # 处理全局命令
        parts = command.strip().split()
        if not parts:
            return False
        
        cmd = parts[0].lower()
        
        if cmd == "rooms":
            self.request_room_list()
            return True
        elif cmd == "disconnect":
            self.disconnect()
            return True
        elif cmd == "status":
            self._show_status()
            return True
        
        return False
    
    def _authenticate(self) -> bool:
        """认证用户"""
        data = {"player_name": self.username}
        response = self.send_message_and_wait("join_game", data)
        
        if response and response.get("data", {}).get("success"):
            self.session_id = response["data"].get("session_id")
            return True
        
        return False
    
    def _wait_for_welcome(self) -> bool:
        """等待欢迎消息"""
        start_time = time.time()
        while time.time() - start_time < 10.0:  # 10秒超时
            message = self.message_queue.get_message(timeout=1.0)
            if message and message.get("type") == "welcome":
                return True
        
        return False
    
    def _start_threads(self):
        """启动工作线程"""
        self.receive_thread = threading.Thread(target=self._receive_worker, daemon=True)
        self.message_processor_thread = threading.Thread(target=self._message_processor_worker, daemon=True)
        self.heartbeat_thread = threading.Thread(target=self._heartbeat_worker, daemon=True)
        
        self.receive_thread.start()
        self.message_processor_thread.start()
        self.heartbeat_thread.start()
    
    def _stop_threads(self):
        """停止工作线程"""
        # 线程会因为self.running=False而自动退出
        pass
    
    def _receive_worker(self):
        """接收消息工作线程"""
        buffer = ""
        
        while self.running:
            try:
                if not self.socket:
                    break
                
                self.socket.settimeout(1.0)
                data = self.socket.recv(4096)
                
                if not data:
                    self._handle_connection_error()
                    break
                
                buffer += data.decode('utf-8')
                
                # 处理完整的消息
                while '\n' in buffer:
                    line, buffer = buffer.split('\n', 1)
                    if line.strip():
                        try:
                            message = json.loads(line.strip())
                            self.message_queue.add_message(message)
                        except json.JSONDecodeError as e:
                            print(f"JSON解析错误: {e}")
                
            except socket.timeout:
                continue
            except Exception as e:
                if self.running:
                    print(f"接收消息错误: {e}")
                    self._handle_connection_error()
                break
    
    def _message_processor_worker(self):
        """消息处理工作线程"""
        while self.running:
            message = self.message_queue.get_message()
            if message:
                self._process_message(message)
    
    def _heartbeat_worker(self):
        """心跳工作线程"""
        while self.running:
            try:
                current_time = time.time()
                
                # 发送心跳
                if current_time - self.last_heartbeat >= self.heartbeat_interval:
                    self.send_message("ping")
                    self.last_heartbeat = current_time
                
                # 检查连接超时
                if (self.connection_state == ConnectionState.AUTHENTICATED and
                    current_time - self.last_heartbeat > self.connection_timeout):
                    print("连接超时，尝试重连...")
                    self._handle_connection_error()
                
                time.sleep(5.0)  # 5秒检查间隔
                
            except Exception as e:
                print(f"心跳线程错误: {e}")
    
    def _process_message(self, message: Dict[str, Any]):
        """处理接收到的消息"""
        message_type = message.get("type")
        request_id = message.get("request_id")
        
        # 如果是响应消息，添加到响应队列
        if request_id:
            self.message_queue.add_response(request_id, message)
        
        # 调用消息处理器
        handler = self.message_handlers.get(message_type)
        if handler:
            try:
                handler(message)
            except Exception as e:
                print(f"消息处理器错误: {e}")
        else:
            print(f"未知消息类型: {message_type}")
    
    def _register_default_handlers(self):
        """注册默认消息处理器"""
        self.message_handlers.update({
            "welcome": self._handle_welcome,
            "room_list": self._handle_room_list,
            "room_created": self._handle_room_created,
            "room_joined": self._handle_room_joined,
            "room_update": self._handle_room_update,
            "game_starting": self._handle_game_starting,
            "game_started": self._handle_game_started,
            "error": self._handle_error,
            "pong": self._handle_pong
        })
    
    def _handle_welcome(self, message: Dict[str, Any]):
        """处理欢迎消息"""
        print("收到服务器欢迎消息")
    
    def _handle_room_list(self, message: Dict[str, Any]):
        """处理房间列表"""
        data = message.get("data", {})
        rooms = data.get("rooms", [])
        self.room_ui.show_room_list(rooms)
        self._trigger_ui_update()
    
    def _handle_room_created(self, message: Dict[str, Any]):
        """处理房间创建成功"""
        data = message.get("data", {})
        if data.get("success"):
            room_info = data.get("room_info", {})
            print(f"房间创建成功: {room_info.get('room_name')}")
            # 自动显示等待房间界面
            self.room_ui.show_waiting_room({"room_id": room_info.get("room_id")})
            self._trigger_ui_update()
    
    def _handle_room_joined(self, message: Dict[str, Any]):
        """处理加入房间成功"""
        data = message.get("data", {})
        room_info = data.get("room_info", {})
        self.room_ui.show_waiting_room(room_info)
        self._trigger_ui_update()
    
    def _handle_room_update(self, message: Dict[str, Any]):
        """处理房间更新"""
        data = message.get("data", {})
        self.room_ui.update_room_info(data)
        self._trigger_ui_update()
    
    def _handle_game_starting(self, message: Dict[str, Any]):
        """处理游戏开始倒计时"""
        data = message.get("data", {})
        self.room_ui.show_game_starting(data)
        self._trigger_ui_update()
    
    def _handle_game_started(self, message: Dict[str, Any]):
        """处理游戏开始"""
        print("游戏开始！")
        # TODO: 切换到游戏界面
    
    def _handle_error(self, message: Dict[str, Any]):
        """处理错误消息"""
        data = message.get("data", {})
        error_message = data.get("message", "未知错误")
        print(f"服务器错误: {error_message}")
    
    def _handle_pong(self, message: Dict[str, Any]):
        """处理心跳响应"""
        self.last_heartbeat = time.time()
    
    def _handle_connection_error(self):
        """处理连接错误"""
        if self.connection_state == ConnectionState.DISCONNECTED:
            return
        
        print("连接出现问题")
        self.connection_state = ConnectionState.ERROR
        
        if self.auto_reconnect and self.reconnect_attempts < self.max_reconnect_attempts:
            self._attempt_reconnect()
        else:
            self.disconnect()
    
    def _attempt_reconnect(self):
        """尝试重连"""
        self.reconnect_attempts += 1
        print(f"尝试重连 ({self.reconnect_attempts}/{self.max_reconnect_attempts})...")
        
        # 延迟重连
        time.sleep(self.reconnect_delay)
        
        # 保存用户名
        username = self.username
        
        # 断开当前连接
        self.disconnect()
        
        # 尝试重新连接
        if self.connect(username):
            print("重连成功")
            self.reconnect_attempts = 0
        else:
            print("重连失败")
    
    def _show_status(self):
        """显示连接状态"""
        print(f"连接状态: {self.connection_state.value}")
        print(f"用户名: {self.username}")
        print(f"会话ID: {self.session_id}")
        print(f"服务器: {self.host}:{self.port}")
    
    def _trigger_ui_update(self):
        """触发UI更新"""
        for callback in self.ui_callbacks:
            try:
                callback()
            except Exception as e:
                print(f"UI更新回调错误: {e}")
    
    def get_connection_state(self) -> ConnectionState:
        """获取连接状态"""
        return self.connection_state
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.connection_state in [ConnectionState.CONNECTED, ConnectionState.AUTHENTICATED]
    
    def get_room_ui(self) -> RoomUI:
        """获取房间UI管理器"""
        return self.room_ui