#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地形预览工具
Terrain Preview Tool

生成地形可视化图像，包括高度图、生物群系图、资源分布图和城市位置图
"""

import json
import argparse
import sys
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import ListedColormap, BoundaryNorm
import seaborn as sns
from dataclasses import dataclass
import logging

# 设置中文字体支持
import platform
import matplotlib.font_manager as fm

def setup_chinese_font():
    """强制设置中文字体"""
    system = platform.system()
    
    if system == "Windows":
        font_name = 'Microsoft YaHei'
    elif system == "Darwin":
        font_name = 'PingFang SC'  
    else:
        font_name = 'WenQuanYi Micro Hei'
    
    # 强制设置所有字体参数
    plt.rcParams.update({
        'font.sans-serif': [font_name, 'DejaVu Sans', 'Arial'],
        'font.family': 'sans-serif',
        'font.serif': [font_name, 'DejaVu Serif'],
        'font.monospace': [font_name, 'DejaVu Sans Mono'],
        'axes.unicode_minus': False,
        'figure.titlesize': 'large',
        'axes.titlesize': 'medium',
        'axes.labelsize': 'medium'
    })
    
    print(f"✅ 强制设置中文字体: {font_name}")
    return True, font_name

# 设置字体
chinese_font_available, selected_font = setup_chinese_font()

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

try:
    from worldwar_server.world.terrain.data_models import Region, TerrainCell, City, deserialize_region_from_json
except ImportError:
    # 如果导入失败，定义简化的数据结构
    @dataclass
    class TerrainCell:
        x: int
        y: int
        elevation: float
        biome: str
        temperature: float
        precipitation: float
        resources: Dict[str, float]
    
    @dataclass
    class City:
        name: str
        population: int
        coordinates: Tuple[float, float]
        gdp_per_capita: float
        poverty_rate: float
        resources: Dict[str, float]
        infrastructure_level: float
    
    @dataclass
    class Region:
        name: str
        country: str
        center: Tuple[float, float]
        terrain: List[List[TerrainCell]]
        cities: List[City]
        economic_data: Dict[str, Any]
        demographic_data: Dict[str, Any]
    
    def deserialize_region_from_json(filepath: str) -> Region:
        """简化的JSON反序列化函数"""
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 解析地形数据
        terrain = []
        for row in data.get('terrain', []):
            terrain_row = []
            for cell_data in row:
                cell = TerrainCell(
                    x=cell_data['x'],
                    y=cell_data['y'],
                    elevation=cell_data['elevation'],
                    biome=cell_data['biome'],
                    temperature=cell_data['temperature'],
                    precipitation=cell_data['precipitation'],
                    resources=cell_data.get('resources', {})
                )
                terrain_row.append(cell)
            terrain.append(terrain_row)
        
        # 解析城市数据
        cities = []
        for city_data in data.get('cities', []):
            city = City(
                name=city_data['name'],
                population=city_data['population'],
                coordinates=tuple(city_data['coordinates']),
                gdp_per_capita=city_data['gdp_per_capita'],
                poverty_rate=city_data['poverty_rate'],
                resources=city_data.get('resources', {}),
                infrastructure_level=city_data.get('infrastructure_level', 0.5)
            )
            cities.append(city)
        
        return Region(
            name=data['name'],
            country=data['country'],
            center=tuple(data['center']),
            terrain=terrain,
            cities=cities,
            economic_data=data.get('economic_data', {}),
            demographic_data=data.get('demographic_data', {})
        )


class TerrainVisualizer:
    """地形可视化器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 根据字体可用性设置标签语言
        self.use_chinese = chinese_font_available
        
        # 标签文本
        if self.use_chinese:
            self.labels = {
                'elevation_title': '地形高度图',
                'biome_title': '生物群系分布图', 
                'resource_title': '资源分布图',
                'city_title': '城市分布图',
                'comprehensive_title': '综合地形分析',
                'elevation_label': '海拔高度 (米)',
                'x_label': 'X 坐标',
                'y_label': 'Y 坐标',
                'resource_density': '资源密度',
                'biome_distribution': '生物群系分布:',
                'min_elev': '最低',
                'max_elev': '最高', 
                'avg_elev': '平均',
                'city_count': '城市数量',
                'total_pop': '总人口',
                'avg_gdp': '平均GDP',
                'total_amount': '总量',
                'max_density': '最大密度',
                'avg_density': '平均密度'
            }
        else:
            self.labels = {
                'elevation_title': 'Terrain Elevation Map',
                'biome_title': 'Biome Distribution Map',
                'resource_title': 'Resource Distribution Map', 
                'city_title': 'City Distribution Map',
                'comprehensive_title': 'Comprehensive Terrain Analysis',
                'elevation_label': 'Elevation (m)',
                'x_label': 'X Coordinate',
                'y_label': 'Y Coordinate',
                'resource_density': 'Resource Density',
                'biome_distribution': 'Biome Distribution:',
                'min_elev': 'Min',
                'max_elev': 'Max',
                'avg_elev': 'Avg',
                'city_count': 'Cities',
                'total_pop': 'Total Pop',
                'avg_gdp': 'Avg GDP',
                'total_amount': 'Total',
                'max_density': 'Max Density',
                'avg_density': 'Avg Density'
            }
        
        # 生物群系颜色映射
        self.biome_colors = {
            'ocean': '#1f77b4',      # 蓝色
            'plains': '#90EE90',     # 浅绿色
            'forest': '#228B22',     # 深绿色
            'desert': '#F4A460',     # 沙棕色
            'mountain': '#8B4513',   # 棕色
            'tundra': '#E0E0E0',     # 浅灰色
            'swamp': '#556B2F',      # 橄榄绿
            'grassland': '#9ACD32',  # 黄绿色
            'hills': '#CD853F',      # 秘鲁色
            'coastal': '#20B2AA'     # 浅海绿色
        }
        
        # 资源颜色映射
        self.resource_colors = {
            'oil': '#000000',        # 黑色
            'coal': '#2F4F4F',       # 深灰色
            'iron': '#B22222',       # 火砖红
            'gold': '#FFD700',       # 金色
            'uranium': '#9ACD32',    # 黄绿色
            'food': '#32CD32',       # 酸橙绿
            'water': '#1E90FF',      # 道奇蓝
            'wood': '#8B4513',       # 马鞍棕
            'stone': '#708090'       # 石板灰
        }
        
        # 设置图像样式
        try:
            sns.set_style("whitegrid")
            plt.style.use('seaborn-v0_8')
        except:
            # 如果seaborn样式不可用，使用默认样式
            pass
    
    def load_region_data(self, data_file: Path) -> Region:
        """加载地区数据"""
        try:
            # 尝试使用完整的数据模型
            try:
                from worldwar_server.world.terrain.data_models import deserialize_region_from_json
                return deserialize_region_from_json(str(data_file))
            except ImportError:
                # 使用简化的JSON加载
                with open(data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                return self._parse_region_data(data)
        except Exception as e:
            self.logger.error(f"加载地区数据失败: {e}")
            raise
    
    def _parse_region_data(self, data: Dict[str, Any]) -> Region:
        """解析地区数据"""
        # 解析地形数据
        terrain = []
        for row in data.get('terrain', []):
            terrain_row = []
            for cell_data in row:
                cell = TerrainCell(
                    x=cell_data['x'],
                    y=cell_data['y'],
                    elevation=cell_data['elevation'],
                    biome=cell_data['biome'],
                    temperature=cell_data['temperature'],
                    precipitation=cell_data['precipitation'],
                    resources=cell_data.get('resources', {})
                )
                terrain_row.append(cell)
            terrain.append(terrain_row)
        
        # 解析城市数据
        cities = []
        for city_data in data.get('cities', []):
            city = City(
                name=city_data['name'],
                population=city_data['population'],
                coordinates=tuple(city_data['coordinates']),
                gdp_per_capita=city_data['gdp_per_capita'],
                poverty_rate=city_data['poverty_rate'],
                resources=city_data.get('resources', {}),
                infrastructure_level=city_data.get('infrastructure_level', 0.5)
            )
            cities.append(city)
        
        return Region(
            name=data['name'],
            country=data['country'],
            center=tuple(data['center']),
            terrain=terrain,
            cities=cities,
            economic_data=data.get('economic_data', {}),
            demographic_data=data.get('demographic_data', {})
        )
    
    def generate_elevation_map(self, region: Region, output_file: Path = None) -> plt.Figure:
        """生成高度图"""
        if not region.terrain:
            raise ValueError("地形数据为空")
        
        # 提取高度数据
        height = len(region.terrain)
        width = len(region.terrain[0])
        elevation_data = np.zeros((height, width))
        
        for y in range(height):
            for x in range(width):
                elevation_data[y, x] = region.terrain[y][x].elevation
        
        # 创建图像
        fig, ax = plt.subplots(figsize=(12, 10))
        
        # 绘制高度图
        im = ax.imshow(elevation_data, cmap='terrain', aspect='equal', origin='lower')
        
        # 添加等高线
        contours = ax.contour(elevation_data, levels=10, colors='black', alpha=0.3, linewidths=0.5)
        ax.clabel(contours, inline=True, fontsize=8, fmt='%d m')
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('海拔高度 (米)', fontsize=12)
        
        # 设置标题和标签
        title = f'{region.name} - {self.labels["elevation_title"]}'
        ax.set_title(title, fontsize=16, fontweight='bold')
        ax.set_xlabel(self.labels['x_label'], fontsize=12)
        ax.set_ylabel(self.labels['y_label'], fontsize=12)
        
        # 添加网格
        ax.grid(True, alpha=0.3)
        
        # 添加统计信息
        min_elev = np.min(elevation_data)
        max_elev = np.max(elevation_data)
        mean_elev = np.mean(elevation_data)
        
        stats_text = f'最低: {min_elev:.0f}m\n最高: {max_elev:.0f}m\n平均: {mean_elev:.0f}m'
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        
        if output_file:
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            self.logger.info(f"高度图已保存到: {output_file}")
        
        return fig
    
    def generate_biome_map(self, region: Region, output_file: Path = None) -> plt.Figure:
        """生成生物群系图"""
        if not region.terrain:
            raise ValueError("地形数据为空")
        
        height = len(region.terrain)
        width = len(region.terrain[0])
        
        # 收集所有生物群系类型
        biomes = set()
        biome_data = np.empty((height, width), dtype=object)
        
        for y in range(height):
            for x in range(width):
                biome = region.terrain[y][x].biome
                biomes.add(biome)
                biome_data[y, x] = biome
        
        # 创建颜色映射
        biome_list = sorted(list(biomes))
        colors = [self.biome_colors.get(biome, '#808080') for biome in biome_list]
        
        # 创建数值映射
        biome_to_num = {biome: i for i, biome in enumerate(biome_list)}
        numeric_data = np.zeros((height, width))
        
        for y in range(height):
            for x in range(width):
                numeric_data[y, x] = biome_to_num[biome_data[y, x]]
        
        # 创建图像
        fig, ax = plt.subplots(figsize=(12, 10))
        
        # 创建自定义颜色映射
        cmap = ListedColormap(colors)
        norm = BoundaryNorm(range(len(biome_list) + 1), cmap.N)
        
        # 绘制生物群系图
        im = ax.imshow(numeric_data, cmap=cmap, norm=norm, aspect='equal', origin='lower')
        
        # 创建图例
        legend_elements = [patches.Patch(color=colors[i], label=biome_list[i].title()) 
                          for i in range(len(biome_list))]
        ax.legend(handles=legend_elements, loc='center left', bbox_to_anchor=(1, 0.5))
        
        # 设置标题和标签
        ax.set_title(f'{region.name} - 生物群系分布图', fontsize=16, fontweight='bold')
        ax.set_xlabel('X 坐标', fontsize=12)
        ax.set_ylabel('Y 坐标', fontsize=12)
        
        # 添加网格
        ax.grid(True, alpha=0.3)
        
        # 添加统计信息
        biome_counts = {}
        total_cells = height * width
        
        for biome in biome_list:
            count = np.sum(numeric_data == biome_to_num[biome])
            percentage = (count / total_cells) * 100
            biome_counts[biome] = percentage
        
        stats_text = "生物群系分布:\n" + "\n".join([f"{biome}: {pct:.1f}%" 
                                                for biome, pct in sorted(biome_counts.items())])
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        
        if output_file:
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            self.logger.info(f"生物群系图已保存到: {output_file}")
        
        return fig
    
    def generate_resource_map(self, region: Region, resource_type: str = None, output_file: Path = None) -> plt.Figure:
        """生成资源分布图"""
        if not region.terrain:
            raise ValueError("地形数据为空")
        
        height = len(region.terrain)
        width = len(region.terrain[0])
        
        # 收集所有资源类型
        all_resources = set()
        for row in region.terrain:
            for cell in row:
                all_resources.update(cell.resources.keys())
        
        if not all_resources:
            raise ValueError("没有找到资源数据")
        
        # 如果没有指定资源类型，显示所有资源的综合分布
        if resource_type is None:
            # 创建子图显示多种资源
            n_resources = len(all_resources)
            cols = min(3, n_resources)
            rows = (n_resources + cols - 1) // cols
            
            fig, axes = plt.subplots(rows, cols, figsize=(5*cols, 4*rows))
            if n_resources == 1:
                axes = [axes]
            elif rows == 1:
                axes = [axes]
            else:
                axes = axes.flatten()
            
            for i, resource in enumerate(sorted(all_resources)):
                ax = axes[i] if i < len(axes) else None
                if ax is None:
                    break
                
                # 提取资源数据
                resource_data = np.zeros((height, width))
                for y in range(height):
                    for x in range(width):
                        resource_data[y, x] = region.terrain[y][x].resources.get(resource, 0)
                
                # 绘制资源分布
                im = ax.imshow(resource_data, cmap='YlOrRd', aspect='equal', origin='lower', vmin=0, vmax=1)
                ax.set_title(f'{resource.title()} 分布', fontsize=12)
                ax.set_xlabel('X 坐标')
                ax.set_ylabel('Y 坐标')
                
                # 添加颜色条
                cbar = plt.colorbar(im, ax=ax, shrink=0.8)
                cbar.set_label('资源密度', fontsize=10)
            
            # 隐藏多余的子图
            for i in range(len(all_resources), len(axes)):
                axes[i].set_visible(False)
            
            fig.suptitle(f'{region.name} - 资源分布图', fontsize=16, fontweight='bold')
        
        else:
            # 显示单一资源分布
            if resource_type not in all_resources:
                raise ValueError(f"资源类型 '{resource_type}' 不存在。可用资源: {', '.join(all_resources)}")
            
            resource_data = np.zeros((height, width))
            for y in range(height):
                for x in range(width):
                    resource_data[y, x] = region.terrain[y][x].resources.get(resource_type, 0)
            
            fig, ax = plt.subplots(figsize=(12, 10))
            
            # 选择合适的颜色映射
            cmap = 'YlOrRd'
            if resource_type in self.resource_colors:
                # 为特定资源使用自定义颜色
                if resource_type == 'water':
                    cmap = 'Blues'
                elif resource_type == 'oil':
                    cmap = 'gray'
                elif resource_type in ['wood', 'food']:
                    cmap = 'Greens'
            
            im = ax.imshow(resource_data, cmap=cmap, aspect='equal', origin='lower', vmin=0, vmax=1)
            
            # 添加等值线
            if np.max(resource_data) > 0:
                contours = ax.contour(resource_data, levels=5, colors='black', alpha=0.3, linewidths=0.5)
                ax.clabel(contours, inline=True, fontsize=8, fmt='%.2f')
            
            # 添加颜色条
            cbar = plt.colorbar(im, ax=ax, shrink=0.8)
            cbar.set_label('资源密度 (0-1)', fontsize=12)
            
            ax.set_title(f'{region.name} - {resource_type.title()} 资源分布', fontsize=16, fontweight='bold')
            ax.set_xlabel('X 坐标', fontsize=12)
            ax.set_ylabel('Y 坐标', fontsize=12)
            ax.grid(True, alpha=0.3)
            
            # 添加统计信息
            total_resource = np.sum(resource_data)
            max_density = np.max(resource_data)
            avg_density = np.mean(resource_data)
            
            stats_text = f'总量: {total_resource:.2f}\n最大密度: {max_density:.2f}\n平均密度: {avg_density:.2f}'
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                    verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        
        if output_file:
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            self.logger.info(f"资源分布图已保存到: {output_file}")
        
        return fig
    
    def generate_city_map(self, region: Region, output_file: Path = None) -> plt.Figure:
        """生成城市位置图"""
        if not region.terrain:
            raise ValueError("地形数据为空")
        
        height = len(region.terrain)
        width = len(region.terrain[0])
        
        # 创建基础地形背景
        elevation_data = np.zeros((height, width))
        for y in range(height):
            for x in range(width):
                elevation_data[y, x] = region.terrain[y][x].elevation
        
        fig, ax = plt.subplots(figsize=(12, 10))
        
        # 绘制地形背景
        im = ax.imshow(elevation_data, cmap='terrain', alpha=0.6, aspect='equal', origin='lower')
        
        # 绘制城市
        if region.cities:
            # 将经纬度坐标转换为网格坐标（简化处理）
            center_lat, center_lon = region.center
            
            for city in region.cities:
                lat, lon = city.coordinates
                
                # 简单的坐标转换（假设地图范围为中心点±5度）
                grid_x = (lon - center_lon + 5) * width / 10
                grid_y = (lat - center_lat + 5) * height / 10
                
                # 确保坐标在有效范围内
                grid_x = max(0, min(width-1, grid_x))
                grid_y = max(0, min(height-1, grid_y))
                
                # 根据人口大小确定城市标记大小
                pop_size = city.population
                if pop_size > 1000000:
                    marker_size = 200
                    marker = 's'  # 方形
                elif pop_size > 500000:
                    marker_size = 150
                    marker = 'o'  # 圆形
                elif pop_size > 100000:
                    marker_size = 100
                    marker = '^'  # 三角形
                else:
                    marker_size = 50
                    marker = '.'  # 点
                
                # 根据经济水平确定颜色
                gdp = city.gdp_per_capita
                if gdp > 40000:
                    color = 'gold'
                elif gdp > 25000:
                    color = 'orange'
                elif gdp > 15000:
                    color = 'yellow'
                else:
                    color = 'red'
                
                # 绘制城市标记
                ax.scatter(grid_x, grid_y, s=marker_size, c=color, marker=marker, 
                          edgecolors='black', linewidth=2, alpha=0.8, zorder=5)
                
                # 添加城市名称
                ax.annotate(city.name, (grid_x, grid_y), xytext=(5, 5), 
                           textcoords='offset points', fontsize=10, fontweight='bold',
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.7))
        
        # 添加颜色条（地形）
        cbar = plt.colorbar(im, ax=ax, shrink=0.6, pad=0.1)
        cbar.set_label('海拔高度 (米)', fontsize=12)
        
        # 创建城市图例
        legend_elements = [
            plt.scatter([], [], s=200, c='gold', marker='s', edgecolors='black', 
                       label='大城市 (>100万人, 高收入)'),
            plt.scatter([], [], s=150, c='orange', marker='o', edgecolors='black', 
                       label='中等城市 (50-100万人, 中高收入)'),
            plt.scatter([], [], s=100, c='yellow', marker='^', edgecolors='black', 
                       label='小城市 (10-50万人, 中等收入)'),
            plt.scatter([], [], s=50, c='red', marker='.', edgecolors='black', 
                       label='城镇 (<10万人, 低收入)')
        ]
        
        ax.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(0, 1))
        
        # 设置标题和标签
        ax.set_title(f'{region.name} - 城市分布图', fontsize=16, fontweight='bold')
        ax.set_xlabel('X 坐标', fontsize=12)
        ax.set_ylabel('Y 坐标', fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # 添加统计信息
        if region.cities:
            total_pop = sum(city.population for city in region.cities)
            avg_gdp = sum(city.gdp_per_capita * city.population for city in region.cities) / total_pop if total_pop > 0 else 0
            
            stats_text = f'城市数量: {len(region.cities)}\n总人口: {total_pop:,}\n平均GDP: ${avg_gdp:,.0f}'
            ax.text(0.02, 0.02, stats_text, transform=ax.transAxes, 
                    verticalalignment='bottom', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        
        if output_file:
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            self.logger.info(f"城市分布图已保存到: {output_file}")
        
        return fig
    
    def generate_comprehensive_map(self, region: Region, output_file: Path = None) -> plt.Figure:
        """生成综合地形图"""
        if not region.terrain:
            raise ValueError("地形数据为空")
        
        # 创建2x2子图布局
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        height = len(region.terrain)
        width = len(region.terrain[0])
        
        # 1. 高度图
        elevation_data = np.zeros((height, width))
        for y in range(height):
            for x in range(width):
                elevation_data[y, x] = region.terrain[y][x].elevation
        
        im1 = ax1.imshow(elevation_data, cmap='terrain', aspect='equal', origin='lower')
        ax1.set_title('地形高度图', fontsize=14, fontweight='bold')
        plt.colorbar(im1, ax=ax1, shrink=0.8).set_label('海拔 (米)')
        
        # 2. 生物群系图
        biomes = set()
        biome_data = np.empty((height, width), dtype=object)
        for y in range(height):
            for x in range(width):
                biome = region.terrain[y][x].biome
                biomes.add(biome)
                biome_data[y, x] = biome
        
        biome_list = sorted(list(biomes))
        colors = [self.biome_colors.get(biome, '#808080') for biome in biome_list]
        biome_to_num = {biome: i for i, biome in enumerate(biome_list)}
        numeric_data = np.zeros((height, width))
        
        for y in range(height):
            for x in range(width):
                numeric_data[y, x] = biome_to_num[biome_data[y, x]]
        
        cmap = ListedColormap(colors)
        norm = BoundaryNorm(range(len(biome_list) + 1), cmap.N)
        im2 = ax2.imshow(numeric_data, cmap=cmap, norm=norm, aspect='equal', origin='lower')
        ax2.set_title('生物群系分布', fontsize=14, fontweight='bold')
        
        # 3. 资源综合分布
        resource_richness = np.zeros((height, width))
        for y in range(height):
            for x in range(width):
                cell = region.terrain[y][x]
                if cell.resources:
                    resource_richness[y, x] = sum(cell.resources.values()) / len(cell.resources)
        
        im3 = ax3.imshow(resource_richness, cmap='YlOrRd', aspect='equal', origin='lower', vmin=0, vmax=1)
        ax3.set_title('资源丰富度', fontsize=14, fontweight='bold')
        plt.colorbar(im3, ax=ax3, shrink=0.8).set_label('资源密度')
        
        # 4. 城市和交通
        im4 = ax4.imshow(elevation_data, cmap='terrain', alpha=0.4, aspect='equal', origin='lower')
        
        if region.cities:
            center_lat, center_lon = region.center
            for city in region.cities:
                lat, lon = city.coordinates
                grid_x = (lon - center_lon + 5) * width / 10
                grid_y = (lat - center_lat + 5) * height / 10
                grid_x = max(0, min(width-1, grid_x))
                grid_y = max(0, min(height-1, grid_y))
                
                # 城市大小基于人口
                size = min(200, max(20, city.population / 5000))
                ax4.scatter(grid_x, grid_y, s=size, c='red', alpha=0.8, edgecolors='black')
                ax4.annotate(city.name, (grid_x, grid_y), xytext=(3, 3), 
                           textcoords='offset points', fontsize=8)
        
        ax4.set_title('城市分布', fontsize=14, fontweight='bold')
        
        # 设置所有子图的标签
        for ax in [ax1, ax2, ax3, ax4]:
            ax.set_xlabel('X 坐标')
            ax.set_ylabel('Y 坐标')
            ax.grid(True, alpha=0.3)
        
        # 总标题
        fig.suptitle(f'{region.name} - 综合地形分析', fontsize=18, fontweight='bold')
        
        plt.tight_layout()
        
        if output_file:
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            self.logger.info(f"综合地形图已保存到: {output_file}")
        
        return fig
    
    def generate_all_maps(self, region: Region, output_dir: Path):
        """生成所有类型的地图"""
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        region_name = region.name.replace(' ', '_').replace('/', '_')
        
        try:
            # 生成各种地图
            self.generate_elevation_map(region, output_dir / f"{region_name}_elevation.png")
            self.generate_biome_map(region, output_dir / f"{region_name}_biomes.png")
            self.generate_resource_map(region, None, output_dir / f"{region_name}_resources.png")
            self.generate_city_map(region, output_dir / f"{region_name}_cities.png")
            self.generate_comprehensive_map(region, output_dir / f"{region_name}_comprehensive.png")
            
            self.logger.info(f"所有地图已生成到目录: {output_dir}")
            
        except Exception as e:
            self.logger.error(f"生成地图时出错: {e}")
            raise


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="地形预览工具")
    parser.add_argument("data_file", type=Path, help="地形数据文件 (JSON格式)")
    parser.add_argument("--output-dir", type=Path, default=Path("terrain_previews"), 
                       help="输出目录")
    parser.add_argument("--map-type", choices=['elevation', 'biomes', 'resources', 'cities', 'comprehensive', 'all'],
                       default='all', help="地图类型")
    parser.add_argument("--resource", type=str, help="指定资源类型 (仅用于资源地图)")
    parser.add_argument("--show", action="store_true", help="显示图像而不保存")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO if args.verbose else logging.WARNING,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    if not args.data_file.exists():
        print(f"❌ 数据文件不存在: {args.data_file}")
        return 1
    
    try:
        # 创建可视化器
        visualizer = TerrainVisualizer()
        
        # 加载数据
        print(f"📊 加载地形数据: {args.data_file}")
        region = visualizer.load_region_data(args.data_file)
        print(f"✅ 成功加载地区: {region.name}")
        
        # 生成地图
        if args.map_type == 'all':
            visualizer.generate_all_maps(region, args.output_dir)
        else:
            if not args.show:
                args.output_dir.mkdir(parents=True, exist_ok=True)
                output_file = args.output_dir / f"{region.name}_{args.map_type}.png"
            else:
                output_file = None
            
            if args.map_type == 'elevation':
                fig = visualizer.generate_elevation_map(region, output_file)
            elif args.map_type == 'biomes':
                fig = visualizer.generate_biome_map(region, output_file)
            elif args.map_type == 'resources':
                fig = visualizer.generate_resource_map(region, args.resource, output_file)
            elif args.map_type == 'cities':
                fig = visualizer.generate_city_map(region, output_file)
            elif args.map_type == 'comprehensive':
                fig = visualizer.generate_comprehensive_map(region, output_file)
            
            if args.show:
                plt.show()
        
        print("✅ 地形预览生成完成!")
        return 0
        
    except Exception as e:
        print(f"❌ 生成地形预览失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())