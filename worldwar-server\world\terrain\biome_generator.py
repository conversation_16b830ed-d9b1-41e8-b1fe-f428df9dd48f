"""
生物群系生成器
Biome Generator

根据高度、温度、湿度生成不同生物群系，实现生物群系边界平滑和过渡效果。
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import logging
from scipy.ndimage import gaussian_filter
from perlin_noise import PerlinNoise

from .terrain_types import (
    BiomeType, TerrainType, 
    get_biome_type_by_conditions, 
    get_terrain_type_by_conditions,
    BIOME_PROPERTIES, TERRAIN_PROPERTIES
)

logger = logging.getLogger(__name__)


@dataclass
class BiomeTransition:
    """生物群系过渡配置"""
    smoothing_radius: float = 2.0      # 平滑半径
    transition_strength: float = 0.3    # 过渡强度
    noise_influence: float = 0.1        # 噪声影响
    edge_detection_threshold: float = 0.1  # 边缘检测阈值


class BiomeGenerator:
    """生物群系生成器"""
    
    def __init__(self, seed: Optional[int] = None, 
                 transition_config: Optional[BiomeTransition] = None):
        """
        初始化生物群系生成器
        
        Args:
            seed: 随机种子
            transition_config: 过渡配置
        """
        self.seed = seed or 12345
        self.transition_config = transition_config or BiomeTransition()
        
        # 创建边界噪声生成器
        self.boundary_noise = PerlinNoise(octaves=3, seed=self.seed + 5000)
        
        logger.info(f"BiomeGenerator initialized with seed: {self.seed}")
    
    def generate_biome_map(self, heightmap: np.ndarray, 
                          temperature_map: np.ndarray, 
                          precipitation_map: np.ndarray) -> np.ndarray:
        """
        生成生物群系地图
        
        Args:
            heightmap: 高度图
            temperature_map: 温度图
            precipitation_map: 降水图
            
        Returns:
            np.ndarray: 生物群系地图（整数编码）
        """
        height, width = heightmap.shape
        biome_map = np.zeros((height, width), dtype=int)
        
        logger.info(f"Generating biome map of size {width}x{height}")
        
        # 为每个像素确定基础生物群系
        for y in range(height):
            for x in range(width):
                elevation = heightmap[y, x]
                temperature = temperature_map[y, x]
                precipitation = precipitation_map[y, x]
                
                # 根据环境条件确定生物群系
                biome_type = get_biome_type_by_conditions(
                    elevation, temperature, precipitation
                )
                
                # 将枚举转换为整数编码
                biome_map[y, x] = list(BiomeType).index(biome_type)
        
        # 应用生物群系过渡和平滑
        biome_map = self._apply_biome_transitions(
            biome_map, heightmap, temperature_map, precipitation_map
        )
        
        logger.info("Biome map generation completed")
        return biome_map
    
    def _apply_biome_transitions(self, biome_map: np.ndarray,
                               heightmap: np.ndarray,
                               temperature_map: np.ndarray,
                               precipitation_map: np.ndarray) -> np.ndarray:
        """
        应用生物群系过渡和平滑效果
        
        Args:
            biome_map: 原始生物群系地图
            heightmap: 高度图
            temperature_map: 温度图
            precipitation_map: 降水图
            
        Returns:
            np.ndarray: 平滑后的生物群系地图
        """
        height, width = biome_map.shape
        smoothed_biome_map = biome_map.copy().astype(float)
        
        # 检测生物群系边界
        edges = self._detect_biome_edges(biome_map)
        
        # 在边界区域应用平滑
        for y in range(height):
            for x in range(width):
                if edges[y, x]:
                    # 获取周围区域的生物群系分布
                    neighbors = self._get_neighborhood_biomes(
                        biome_map, x, y, self.transition_config.smoothing_radius
                    )
                    
                    # 计算加权平均
                    if neighbors:
                        # 添加边界噪声
                        boundary_noise_value = self.boundary_noise([
                            x * 0.02, y * 0.02
                        ]) * self.transition_config.noise_influence
                        
                        # 基于环境条件的权重
                        env_weights = self._calculate_environmental_weights(
                            heightmap[y, x], temperature_map[y, x], 
                            precipitation_map[y, x], neighbors
                        )
                        
                        # 选择最适合的生物群系
                        best_biome = self._select_best_biome(
                            neighbors, env_weights, boundary_noise_value
                        )
                        
                        # 应用过渡强度
                        original_biome = biome_map[y, x]
                        transition_factor = self.transition_config.transition_strength
                        
                        smoothed_biome_map[y, x] = (
                            original_biome * (1 - transition_factor) +
                            best_biome * transition_factor
                        )
        
        # 将浮点值转换回整数
        return np.round(smoothed_biome_map).astype(int)
    
    def _detect_biome_edges(self, biome_map: np.ndarray) -> np.ndarray:
        """
        检测生物群系边界
        
        Args:
            biome_map: 生物群系地图
            
        Returns:
            np.ndarray: 边界掩码（True表示边界）
        """
        height, width = biome_map.shape
        edges = np.zeros((height, width), dtype=bool)
        
        for y in range(1, height - 1):
            for x in range(1, width - 1):
                center_biome = biome_map[y, x]
                
                # 检查8个邻居
                neighbors = [
                    biome_map[y-1, x-1], biome_map[y-1, x], biome_map[y-1, x+1],
                    biome_map[y, x-1],                      biome_map[y, x+1],
                    biome_map[y+1, x-1], biome_map[y+1, x], biome_map[y+1, x+1]
                ]
                
                # 如果有不同的邻居，则为边界
                if any(neighbor != center_biome for neighbor in neighbors):
                    edges[y, x] = True
        
        return edges
    
    def _get_neighborhood_biomes(self, biome_map: np.ndarray, 
                               center_x: int, center_y: int, 
                               radius: float) -> List[int]:
        """
        获取指定半径内的生物群系分布
        
        Args:
            biome_map: 生物群系地图
            center_x: 中心X坐标
            center_y: 中心Y坐标
            radius: 搜索半径
            
        Returns:
            List[int]: 邻域内的生物群系列表
        """
        height, width = biome_map.shape
        neighbors = []
        
        # 计算搜索范围
        r = int(np.ceil(radius))
        
        for dy in range(-r, r + 1):
            for dx in range(-r, r + 1):
                x = center_x + dx
                y = center_y + dy
                
                # 检查边界
                if 0 <= x < width and 0 <= y < height:
                    # 检查距离
                    distance = np.sqrt(dx*dx + dy*dy)
                    if distance <= radius:
                        neighbors.append(biome_map[y, x])
        
        return neighbors
    
    def _calculate_environmental_weights(self, elevation: float, 
                                       temperature: float, 
                                       precipitation: float,
                                       neighbor_biomes: List[int]) -> Dict[int, float]:
        """
        基于环境条件计算生物群系权重
        
        Args:
            elevation: 海拔
            temperature: 温度
            precipitation: 降水
            neighbor_biomes: 邻域生物群系
            
        Returns:
            Dict[int, float]: 生物群系权重字典
        """
        weights = {}
        biome_types = list(BiomeType)
        
        for biome_idx in set(neighbor_biomes):
            if biome_idx < len(biome_types):
                biome_type = biome_types[biome_idx]
                biome_props = BIOME_PROPERTIES[biome_type]
                
                # 计算环境适应性
                temp_fit = self._calculate_range_fitness(
                    temperature, biome_props.temperature_range
                )
                precip_fit = self._calculate_range_fitness(
                    precipitation, biome_props.precipitation_range
                )
                elev_fit = self._calculate_range_fitness(
                    elevation, biome_props.elevation_range
                )
                
                # 综合适应性权重
                weights[biome_idx] = temp_fit * precip_fit * elev_fit
        
        return weights
    
    def _calculate_range_fitness(self, value: float, 
                               range_tuple: Tuple[float, float]) -> float:
        """
        计算值在指定范围内的适应性
        
        Args:
            value: 待评估的值
            range_tuple: 范围元组 (min, max)
            
        Returns:
            float: 适应性分数 (0-1)
        """
        min_val, max_val = range_tuple
        
        if min_val <= value <= max_val:
            # 在范围内，计算距离中心的适应性
            center = (min_val + max_val) / 2
            range_size = max_val - min_val
            distance_from_center = abs(value - center)
            
            if range_size > 0:
                return 1.0 - (distance_from_center / (range_size / 2))
            else:
                return 1.0
        else:
            # 在范围外，计算距离边界的惩罚
            if value < min_val:
                distance = min_val - value
            else:
                distance = value - max_val
            
            # 使用指数衰减
            return np.exp(-distance * 5)
    
    def _select_best_biome(self, neighbor_biomes: List[int],
                          env_weights: Dict[int, float],
                          noise_value: float) -> int:
        """
        选择最适合的生物群系
        
        Args:
            neighbor_biomes: 邻域生物群系
            env_weights: 环境权重
            noise_value: 噪声值
            
        Returns:
            int: 选择的生物群系索引
        """
        if not neighbor_biomes:
            return 0
        
        # 计算每个生物群系的总权重
        biome_scores = {}
        
        for biome_idx in set(neighbor_biomes):
            # 基础频率权重
            frequency_weight = neighbor_biomes.count(biome_idx) / len(neighbor_biomes)
            
            # 环境适应性权重
            env_weight = env_weights.get(biome_idx, 0.0)
            
            # 噪声影响
            noise_weight = 1.0 + noise_value
            
            # 综合权重
            total_weight = frequency_weight * 0.4 + env_weight * 0.5 + noise_weight * 0.1
            biome_scores[biome_idx] = total_weight
        
        # 选择权重最高的生物群系
        return max(biome_scores.keys(), key=lambda k: biome_scores[k])
    
    def generate_terrain_from_biomes(self, biome_map: np.ndarray,
                                   heightmap: np.ndarray,
                                   temperature_map: np.ndarray,
                                   precipitation_map: np.ndarray) -> np.ndarray:
        """
        基于生物群系生成详细地形类型
        
        Args:
            biome_map: 生物群系地图
            heightmap: 高度图
            temperature_map: 温度图
            precipitation_map: 降水图
            
        Returns:
            np.ndarray: 地形类型地图
        """
        height, width = biome_map.shape
        terrain_map = np.zeros((height, width), dtype=int)
        
        logger.info("Generating terrain types from biomes")
        
        for y in range(height):
            for x in range(width):
                elevation = heightmap[y, x]
                temperature = temperature_map[y, x]
                precipitation = precipitation_map[y, x]
                
                # 获取基础地形类型
                terrain_type = get_terrain_type_by_conditions(
                    elevation, temperature, precipitation
                )
                
                # 根据生物群系调整地形类型
                biome_idx = biome_map[y, x]
                if biome_idx < len(list(BiomeType)):
                    biome_type = list(BiomeType)[biome_idx]
                    biome_props = BIOME_PROPERTIES[biome_type]
                    
                    # 如果当前地形类型不在生物群系的主要地形中，进行调整
                    if terrain_type not in biome_props.dominant_terrain:
                        if biome_props.dominant_terrain:
                            # 选择最适合的主要地形类型
                            terrain_type = biome_props.dominant_terrain[0]
                
                # 将枚举转换为整数编码
                terrain_map[y, x] = list(TerrainType).index(terrain_type)
        
        logger.info("Terrain type generation completed")
        return terrain_map
    
    def get_biome_statistics(self, biome_map: np.ndarray) -> Dict[str, float]:
        """
        获取生物群系统计信息
        
        Args:
            biome_map: 生物群系地图
            
        Returns:
            Dict[str, float]: 统计信息
        """
        total_pixels = biome_map.size
        biome_types = list(BiomeType)
        stats = {}
        
        for i, biome_type in enumerate(biome_types):
            count = np.sum(biome_map == i)
            ratio = count / total_pixels
            stats[biome_type.value] = ratio
        
        # 添加多样性指数（Shannon多样性指数）
        diversity = 0.0
        for ratio in stats.values():
            if ratio > 0:
                diversity -= ratio * np.log(ratio)
        
        stats['diversity_index'] = diversity
        stats['total_biome_types'] = len([r for r in stats.values() if r > 0])
        
        return stats
    
    def create_biome_transition_mask(self, biome_map: np.ndarray) -> np.ndarray:
        """
        创建生物群系过渡区域掩码
        
        Args:
            biome_map: 生物群系地图
            
        Returns:
            np.ndarray: 过渡区域掩码（0-1，1表示过渡区域）
        """
        # 使用高斯滤波检测边界
        smoothed = gaussian_filter(biome_map.astype(float), sigma=1.0)
        
        # 计算梯度幅度
        grad_y, grad_x = np.gradient(smoothed)
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        
        # 归一化到0-1范围
        if gradient_magnitude.max() > 0:
            transition_mask = gradient_magnitude / gradient_magnitude.max()
        else:
            transition_mask = np.zeros_like(gradient_magnitude)
        
        return transition_mask
    
    def export_biome_data(self, biome_map: np.ndarray, 
                         heightmap: np.ndarray,
                         temperature_map: np.ndarray,
                         precipitation_map: np.ndarray,
                         filename: str):
        """
        导出生物群系数据
        
        Args:
            biome_map: 生物群系地图
            heightmap: 高度图
            temperature_map: 温度图
            precipitation_map: 降水图
            filename: 输出文件名
        """
        import json
        
        # 获取统计信息
        stats = self.get_biome_statistics(biome_map)
        
        # 创建导出数据
        export_data = {
            'metadata': {
                'size': biome_map.shape,
                'seed': self.seed,
                'generation_time': str(np.datetime64('now')),
                'biome_statistics': stats
            },
            'maps': {
                'biome_map': biome_map.tolist(),
                'heightmap': heightmap.tolist(),
                'temperature_map': temperature_map.tolist(),
                'precipitation_map': precipitation_map.tolist()
            },
            'biome_types': {
                i: biome_type.value 
                for i, biome_type in enumerate(BiomeType)
            }
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Biome data exported to {filename}")


def create_default_biome_generator(seed: Optional[int] = None) -> BiomeGenerator:
    """
    创建默认配置的生物群系生成器
    
    Args:
        seed: 随机种子
        
    Returns:
        BiomeGenerator: 生物群系生成器实例
    """
    return BiomeGenerator(seed=seed)


def create_smooth_biome_generator(seed: Optional[int] = None) -> BiomeGenerator:
    """
    创建平滑过渡的生物群系生成器
    
    Args:
        seed: 随机种子
        
    Returns:
        BiomeGenerator: 平滑生物群系生成器实例
    """
    transition_config = BiomeTransition(
        smoothing_radius=3.0,
        transition_strength=0.5,
        noise_influence=0.15,
        edge_detection_threshold=0.05
    )
    
    return BiomeGenerator(seed=seed, transition_config=transition_config)


def create_realistic_biome_generator(seed: Optional[int] = None) -> BiomeGenerator:
    """
    创建真实感生物群系生成器
    
    Args:
        seed: 随机种子
        
    Returns:
        BiomeGenerator: 真实感生物群系生成器实例
    """
    transition_config = BiomeTransition(
        smoothing_radius=4.0,
        transition_strength=0.7,
        noise_influence=0.2,
        edge_detection_threshold=0.03
    )
    
    return BiomeGenerator(seed=seed, transition_config=transition_config)