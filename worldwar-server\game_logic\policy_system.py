#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
政策系统模块
Policy System Module
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from enum import Enum
import time
import json
from data.local_storage import LocalStorage
from shared.enhanced_logger import get_server_logger

class PolicyType(Enum):
    """政策类型枚举"""
    EDUCATION = "education"           # 教育政策
    INDUSTRY = "industry"             # 产业政策
    INFRASTRUCTURE = "infrastructure" # 基础设施政策
    SOCIAL = "social"                 # 社会政策
    ENVIRONMENT = "environment"       # 环保政策
    MILITARY = "military"             # 军事政策

class PolicyStatus(Enum):
    """政策状态枚举"""
    AVAILABLE = "available"           # 可用
    ACTIVE = "active"                 # 生效中
    COMPLETED = "completed"           # 已完成
    CANCELLED = "cancelled"           # 已取消

@dataclass
class PolicyEffect:
    """政策效果"""
    effect_type: str                  # 效果类型
    target: str                       # 影响目标
    value: float                      # 效果数值
    duration: int = 0                 # 持续时间（回合数，0表示永久）
    description: str = ""             # 效果描述

@dataclass
class Policy:
    """政策类"""
    policy_id: str
    name: str
    description: str
    policy_type: PolicyType
    
    # 成本和要求
    cost_resources: Dict[str, int] = field(default_factory=dict)
    cost_policy_points: int = 0
    required_tech_level: int = 1
    required_buildings: List[str] = field(default_factory=list)
    
    # 效果
    effects: List[PolicyEffect] = field(default_factory=list)
    
    # 状态
    status: PolicyStatus = PolicyStatus.AVAILABLE
    implemented_turn: int = 0
    completion_turn: int = 0
    
    # 限制
    max_implementations: int = 1      # 最大实施次数
    current_implementations: int = 0  # 当前实施次数
    cooldown_turns: int = 0          # 冷却时间
    
    def can_implement(self, player: 'Player', current_turn: int) -> tuple[bool, str]:
        """检查是否可以实施政策 (重构版)"""
        # 检查状态
        if self.status == PolicyStatus.ACTIVE:
            return False, "政策已在实施中"
        
        # 检查实施次数
        if self.current_implementations >= self.max_implementations:
            return False, "已达到最大实施次数"
        
        # 检查冷却时间
        if self.completion_turn > 0 and current_turn - self.completion_turn < self.cooldown_turns:
            return False, f"冷却中，还需{self.cooldown_turns - (current_turn - self.completion_turn)}回合"
        
        # 检查政策点数
        if player.policy_points < self.cost_policy_points:
            return False, f"政策点数不足，需要{self.cost_policy_points}点"
        
        # 检查科技等级
        if player.tech_level < self.required_tech_level:
            return False, f"科技等级不足，需要{self.required_tech_level}级"
        
        # 检查资源
        for resource, amount in self.cost_resources.items():
            if player.get_resource(resource) < amount:
                return False, f"{resource}不足，需要{amount}"
        
        # 检查建筑 (简化：假设建筑等同于领土)
        # TODO: 未来可以扩展为真正的建筑系统
        for building in self.required_buildings:
            if building not in player.controlled_territories:
                return False, f"需要建筑/领土：{building}"
        
        return True, "可以实施"
    
    def implement(self, current_turn: int):
        """实施政策"""
        self.status = PolicyStatus.ACTIVE
        self.implemented_turn = current_turn
        self.current_implementations += 1
    
    def complete(self, current_turn: int):
        """完成政策"""
        self.status = PolicyStatus.COMPLETED
        self.completion_turn = current_turn

class PolicyManager:
    """政策管理器"""
    
    def __init__(self, player_manager):
        """初始化政策管理器"""
        self.player_manager = player_manager
        self.policies: Dict[str, Policy] = {}
        # 重构 active_policies 结构: key=player_id, value=List[Policy]
        self.active_policies: Dict[str, List[Policy]] = {}
        
        self.logger = get_server_logger("PolicyManager")
        
        # 初始化政策库
        self._initialize_policies()
        
        self.logger.info("政策管理器初始化完成 / Policy manager initialized")
    
    def _initialize_policies(self):
        """从文件初始化政策库"""
        storage = LocalStorage()
        policies_data = storage.get_policies_data()
        
        if not policies_data:
            self.logger.warning("无法加载政策数据，政策系统将为空")
            return

        for category, policy_list in policies_data.items():
            for policy_data in policy_list:
                effects = [PolicyEffect(**effect_data) for effect_data in policy_data.get("effects", [])]
                
                # 从 policy_data 中移除 'effects'，以便使用 **policy_data
                policy_data.pop("effects", None)

                policy = Policy(
                    **policy_data,
                    policy_type=PolicyType(policy_data["policy_type"]),
                    effects=effects
                )
                self.policies[policy.policy_id] = policy
        
        self.logger.info(f"从文件加载了 {len(self.policies)} 个政策")
    
    def get_available_policies(self, player_id: str, current_turn: int) -> List[Policy]:
        """获取可用政策列表 (重构版)"""
        player = self.player_manager.get_player(player_id)
        if not player:
            return []

        available = []
        for policy in self.policies.values():
            can_implement, _ = policy.can_implement(player, current_turn)
            if can_implement:
                available.append(policy)
        
        return available
    
    def implement_policy(self, policy_id: str, player_id: str,
                         current_turn: int) -> tuple[bool, str]:
        """实施政策"""
        player = self.player_manager.get_player(player_id)
        if not player:
            return False, "玩家不存在"
            
        if policy_id not in self.policies:
            return False, "政策不存在"
        
        policy = self.policies[policy_id]
        
        # 使用 Player 对象检查是否可以实施政策 (重构版)
        can_implement, reason = policy.can_implement(player, current_turn)
        
        if not can_implement:
            self.logger.warning(f"玩家 {player_id} 无法实施政策 {policy.name}: {reason}")
            return False, reason

        # 消耗资源和政策点数
        for resource, amount in policy.cost_resources.items():
            player.consume_resource(resource, amount)
        player.consume_policy_points(policy.cost_policy_points)

        # 实施政策
        policy.implement(current_turn)
        
        # 添加到新的 active_policies 结构中
        if player_id not in self.active_policies:
            self.active_policies[player_id] = []
        self.active_policies[player_id].append(policy)
        
        # 将实施的政策ID记录在玩家对象上
        player.implement_policy(policy_id)

        self.logger.info(f"玩家 {player_id} 实施政策: {policy.name}")
        
        return True, f"成功实施政策: {policy.name}"
    
    def update_policies(self, current_turn: int) -> Dict[str, List[PolicyEffect]]:
        """
        更新所有玩家的活跃政策状态 (重构版)。
        - 移除已完成的政策。
        - 返回一个字典，包含每个玩家在本回合生效的政策效果。
        """
        all_active_effects: Dict[str, List[PolicyEffect]] = {}

        # 使用 items() 的副本进行迭代，因为我们可能会在循环中修改字典
        for player_id, active_player_policies in list(self.active_policies.items()):
            
            # 用于存储仍然活跃的政策
            policies_to_keep = []
            player_effects_this_turn: List[PolicyEffect] = []

            for policy in active_player_policies:
                # 检查政策是否已过期
                # 一个政策如果其所有有持续时间的效果都已过期，则视为完成
                timed_effects = [e for e in policy.effects if e.duration > 0]
                is_permanent = not timed_effects
                
                if is_permanent:
                    # 永久性政策永远不会过期，但其效果只应用一次（或由其他系统处理）
                    # 这里我们假设它们在实施时立即生效，之后不再产生回合效果
                    policies_to_keep.append(policy)
                    continue

                # 检查所有带时限的效果是否都已结束
                expired_effects_count = sum(
                    1 for e in timed_effects
                    if (current_turn - policy.implemented_turn) >= e.duration
                )

                if expired_effects_count == len(timed_effects):
                    # 所有效果都已过期，政策完成
                    policy.complete(current_turn)
                    self.logger.info(f"玩家 {player_id} 的政策 '{policy.name}' 已完成。")
                    # 不将此政策添加到 policies_to_keep
                else:
                    # 政策仍然活跃，保留它
                    policies_to_keep.append(policy)
                    # 收集本回合仍然生效的效果
                    for effect in policy.effects:
                        is_expired = False
                        if effect.duration > 0:
                            if (current_turn - policy.implemented_turn) >= effect.duration:
                                is_expired = True
                        if not is_expired:
                            player_effects_this_turn.append(effect)
            
            # 更新该玩家的活跃政策列表
            if not policies_to_keep:
                # 如果所有政策都已完成，则从字典中移除该玩家
                del self.active_policies[player_id]
            else:
                self.active_policies[player_id] = policies_to_keep

            # 如果本回合有效果，则记录
            if player_effects_this_turn:
                all_active_effects[player_id] = player_effects_this_turn
                
        return all_active_effects
    
    def get_policy_effects(self, player_id: str) -> List[PolicyEffect]:
        """获取玩家当前所有活跃政策的效果 (重构版)"""
        effects = []
        if player_id in self.active_policies:
            for policy in self.active_policies[player_id]:
                effects.extend(policy.effects)
        return effects
    
    def get_policy_info(self, policy_id: str) -> Optional[Dict[str, Any]]:
        """获取政策信息"""
        if policy_id not in self.policies:
            return None
        
        policy = self.policies[policy_id]
        
        return {
            "policy_id": policy.policy_id,
            "name": policy.name,
            "description": policy.description,
            "type": policy.policy_type.value,
            "cost_resources": policy.cost_resources,
            "cost_policy_points": policy.cost_policy_points,
            "required_tech_level": policy.required_tech_level,
            "required_buildings": policy.required_buildings,
            "effects": [
                {
                    "type": effect.effect_type,
                    "target": effect.target,
                    "value": effect.value,
                    "duration": effect.duration,
                    "description": effect.description
                }
                for effect in policy.effects
            ],
            "status": policy.status.value,
            "max_implementations": policy.max_implementations,
            "current_implementations": policy.current_implementations,
            "cooldown_turns": policy.cooldown_turns
        }
