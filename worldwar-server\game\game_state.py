#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏状态管理器
Game State Manager

管理游戏的整体状态，包括回合、玩家状态、世界状态等
Manages overall game state including turns, player states, world state, etc.
"""

import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime

class GamePhase(Enum):
    """游戏阶段"""
    SETUP = "setup"                    # 设置阶段
    EARLY_DEVELOPMENT = "early_dev"    # 早期发展
    EXPANSION = "expansion"            # 扩张阶段
    WORLD_WAR = "world_war"           # 世界大战
    ENDGAME = "endgame"               # 终局阶段

class PlayerStatus(Enum):
    """玩家状态"""
    ACTIVE = "active"                 # 活跃
    ELIMINATED = "eliminated"         # 被淘汰
    SURRENDERED = "surrendered"       # 投降
    DISCONNECTED = "disconnected"     # 断线

@dataclass
class PlayerState:
    """玩家状态"""
    player_id: str
    player_name: str
    status: PlayerStatus
    region_type: str
    
    # 资源
    resources: Dict[str, int] = field(default_factory=dict)
    
    # 科技
    technology: Dict[str, int] = field(default_factory=dict)
    research_points: int = 0
    current_research: Optional[str] = None
    
    # 军事
    military_units: Dict[str, int] = field(default_factory=dict)
    military_power: int = 0
    
    # 经济
    population: int = 0
    infrastructure: int = 0
    economic_power: int = 0
    
    # 外交
    alliances: List[str] = field(default_factory=list)
    diplomatic_relations: Dict[str, int] = field(default_factory=dict)  # -100 to 100
    
    # 特殊特质
    special_traits: List[str] = field(default_factory=list)
    
    # 统计
    territories_controlled: int = 0
    total_score: int = 0
    
    # 时间戳
    last_action_time: float = field(default_factory=time.time)

@dataclass
class WorldEvent:
    """世界事件"""
    event_id: str
    event_type: str
    title: str
    description: str
    affected_players: List[str]
    effects: Dict[str, Any]
    timestamp: datetime
    duration: int = 0  # 持续回合数，0表示立即生效

class GameState:
    """游戏状态管理器"""
    
    def __init__(self, game_id: str, max_players: int = 8):
        """
        初始化游戏状态
        
        Args:
            game_id: 游戏ID
            max_players: 最大玩家数
        """
        self.game_id = game_id
        self.max_players = max_players
        
        # 游戏基本信息
        self.current_turn = 0
        self.current_phase = GamePhase.SETUP
        self.game_started = False
        self.game_ended = False
        self.winner = None
        
        # 玩家状态
        self.players: Dict[str, PlayerState] = {}
        self.turn_order: List[str] = []
        self.current_player_index = 0
        
        # 世界状态
        self.world_map = None
        self.world_events: List[WorldEvent] = []
        self.active_events: List[WorldEvent] = []
        
        # 时间管理
        self.turn_start_time = time.time()
        self.turn_time_limit = 300  # 5分钟每回合
        
        # 游戏设置
        self.settings = {
            "turn_time_limit": 300,
            "max_turns": 200,
            "victory_conditions": ["military", "economic", "technological", "diplomatic"],
            "random_events_enabled": True,
            "ai_difficulty": "normal"
        }
        
        # 导入日志
        from shared.enhanced_logger import get_server_logger
        self.logger = get_server_logger("GameState")
        
        self.logger.info(f"游戏状态管理器初始化完成: {game_id}")
    
    def add_player(self, player_id: str, player_name: str, starting_data: Dict[str, Any]) -> bool:
        """
        添加玩家到游戏
        
        Args:
            player_id: 玩家ID
            player_name: 玩家名称
            starting_data: 起始数据
            
        Returns:
            是否成功添加
        """
        if len(self.players) >= self.max_players:
            return False
        
        if player_id in self.players:
            return False
        
        player_state = PlayerState(
            player_id=player_id,
            player_name=player_name,
            status=PlayerStatus.ACTIVE,
            region_type=starting_data.get("region_type", "unknown"),
            resources=starting_data.get("starting_resources", {}),
            technology=starting_data.get("starting_technology", {}),
            population=starting_data.get("starting_population", 0),
            special_traits=starting_data.get("special_traits", []),
            territories_controlled=len(starting_data.get("controlled_tiles", []))
        )
        
        self.players[player_id] = player_state
        self.turn_order.append(player_id)
        
        self.logger.info(f"玩家加入游戏: {player_name} ({player_id})")
        return True
    
    def remove_player(self, player_id: str) -> bool:
        """
        移除玩家
        
        Args:
            player_id: 玩家ID
            
        Returns:
            是否成功移除
        """
        if player_id not in self.players:
            return False
        
        # 更新玩家状态为断线
        self.players[player_id].status = PlayerStatus.DISCONNECTED
        
        # 如果游戏还没开始，从回合顺序中移除
        if not self.game_started and player_id in self.turn_order:
            self.turn_order.remove(player_id)
        
        self.logger.info(f"玩家离开游戏: {player_id}")
        return True
    
    def start_game(self, world_data: Dict[str, Any]) -> bool:
        """
        开始游戏
        
        Args:
            world_data: 世界数据
            
        Returns:
            是否成功开始
        """
        if self.game_started:
            return False
        
        if len(self.players) < 2:
            return False
        
        self.world_map = world_data
        self.game_started = True
        self.current_phase = GamePhase.EARLY_DEVELOPMENT
        self.current_turn = 1
        self.turn_start_time = time.time()
        
        # 初始化外交关系
        self._initialize_diplomacy()
        
        # 生成初始事件
        self._generate_initial_events()
        
        self.logger.info(f"游戏开始: {self.game_id}, 玩家数: {len(self.players)}")
        return True
    
    def next_turn(self) -> Dict[str, Any]:
        """
        进入下一回合
        
        Returns:
            回合信息
        """
        if not self.game_started or self.game_ended:
            return {"success": False, "error": "game_not_active"}
        
        # 处理回合结束事务
        self._process_turn_end()
        
        # 增加回合数
        self.current_turn += 1
        self.turn_start_time = time.time()
        
        # 切换到下一个玩家
        self.current_player_index = (self.current_player_index + 1) % len(self.turn_order)
        
        # 检查游戏阶段
        self._update_game_phase()
        
        # 处理回合开始事务
        self._process_turn_start()
        
        # 检查胜利条件
        winner = self._check_victory_conditions()
        if winner:
            self._end_game(winner)
        
        # 检查最大回合数
        if self.current_turn >= self.settings["max_turns"]:
            self._end_game(self._determine_winner_by_score())
        
        turn_info = {
            "success": True,
            "turn": self.current_turn,
            "phase": self.current_phase.value,
            "current_player": self.get_current_player_id(),
            "time_limit": self.turn_time_limit,
            "active_events": [self._serialize_event(event) for event in self.active_events]
        }
        
        self.logger.info(f"回合 {self.current_turn} 开始，当前玩家: {self.get_current_player_id()}")
        return turn_info
    
    def get_current_player_id(self) -> Optional[str]:
        """获取当前回合玩家ID"""
        if not self.turn_order:
            return None
        return self.turn_order[self.current_player_index]
    
    def get_player_state(self, player_id: str) -> Optional[PlayerState]:
        """获取玩家状态"""
        return self.players.get(player_id)
    
    def update_player_resources(self, player_id: str, resource_changes: Dict[str, int]) -> bool:
        """
        更新玩家资源
        
        Args:
            player_id: 玩家ID
            resource_changes: 资源变化 (可以是负数)
            
        Returns:
            是否成功更新
        """
        player = self.players.get(player_id)
        if not player:
            return False
        
        # 检查资源是否足够（对于负变化）
        for resource, change in resource_changes.items():
            if change < 0:
                current_amount = player.resources.get(resource, 0)
                if current_amount + change < 0:
                    return False  # 资源不足
        
        # 应用变化
        for resource, change in resource_changes.items():
            player.resources[resource] = player.resources.get(resource, 0) + change
        
        player.last_action_time = time.time()
        return True
    
    def get_game_summary(self) -> Dict[str, Any]:
        """获取游戏摘要"""
        return {
            "game_id": self.game_id,
            "turn": self.current_turn,
            "phase": self.current_phase.value,
            "started": self.game_started,
            "ended": self.game_ended,
            "winner": self.winner,
            "players": {
                pid: {
                    "name": player.player_name,
                    "status": player.status.value,
                    "score": player.total_score,
                    "territories": player.territories_controlled
                }
                for pid, player in self.players.items()
            },
            "current_player": self.get_current_player_id(),
            "time_remaining": max(0, self.turn_time_limit - (time.time() - self.turn_start_time))
        }

    def _initialize_diplomacy(self):
        """初始化外交关系"""
        player_ids = list(self.players.keys())

        for player_id in player_ids:
            player = self.players[player_id]
            player.diplomatic_relations = {}

            for other_id in player_ids:
                if other_id != player_id:
                    # 初始关系为中性，稍微偏向负面
                    initial_relation = random.randint(-20, 10)
                    player.diplomatic_relations[other_id] = initial_relation

    def _generate_initial_events(self):
        """生成初始事件"""
        # 根据游戏阶段和玩家状态生成合适的事件
        if self.settings.get("random_events_enabled", True):
            # 早期发展阶段的事件
            early_events = [
                "natural_disaster",
                "resource_discovery",
                "population_growth",
                "technological_breakthrough",
                "trade_opportunity"
            ]

            # 随机选择1-2个初始事件
            num_events = random.randint(1, 2)
            for _ in range(num_events):
                event_type = random.choice(early_events)
                self._create_world_event(event_type)

    def _process_turn_end(self):
        """处理回合结束事务"""
        current_player_id = self.get_current_player_id()
        if current_player_id:
            player = self.players[current_player_id]

            # 更新玩家统计
            self._update_player_statistics(player)

            # 处理资源产出
            self._process_resource_production(player)

            # 处理科技研发
            self._process_research(player)

        # 处理活跃事件
        self._process_active_events()

    def _process_turn_start(self):
        """处理回合开始事务"""
        # 随机生成新事件
        if random.random() < 0.3:  # 30%概率
            self._generate_random_event()

    def _update_game_phase(self):
        """更新游戏阶段"""
        if self.current_turn <= 20:
            self.current_phase = GamePhase.EARLY_DEVELOPMENT
        elif self.current_turn <= 50:
            self.current_phase = GamePhase.EXPANSION
        elif self.current_turn <= 150:
            self.current_phase = GamePhase.WORLD_WAR
        else:
            self.current_phase = GamePhase.ENDGAME

    def _check_victory_conditions(self) -> Optional[str]:
        """检查胜利条件"""
        active_players = [p for p in self.players.values()
                         if p.status == PlayerStatus.ACTIVE]

        # 只剩一个活跃玩家
        if len(active_players) == 1:
            return active_players[0].player_id

        # 检查各种胜利条件
        for player in active_players:
            # 军事胜利：控制70%以上领土
            if player.territories_controlled >= self._get_total_territories() * 0.7:
                return player.player_id

            # 经济胜利：经济实力远超其他玩家
            if player.economic_power >= self._get_average_economic_power() * 3:
                return player.player_id

            # 科技胜利：完成终极科技
            if self._has_ultimate_technology(player):
                return player.player_id

        return None

    def _end_game(self, winner: str):
        """结束游戏"""
        self.game_ended = True
        self.winner = winner

        # 更新所有玩家的最终状态
        for player_id, player in self.players.items():
            if player_id == winner:
                player.status = PlayerStatus.ACTIVE
            elif player.status == PlayerStatus.ACTIVE:
                player.status = PlayerStatus.ELIMINATED

        self.logger.info(f"游戏结束，获胜者: {winner}")

    def _update_player_statistics(self, player: PlayerState):
        """更新玩家统计信息"""
        # 计算总分
        score = 0
        score += player.territories_controlled * 10
        score += player.population // 100
        score += player.economic_power
        score += sum(player.technology.values()) * 5
        score += len(player.alliances) * 20

        player.total_score = score

    def _process_resource_production(self, player: PlayerState):
        """处理资源产出"""
        # 基础产出
        base_production = {
            "food": player.population // 100,
            "wood": player.territories_controlled,
            "stone": player.territories_controlled // 2,
            "coal": max(1, player.territories_controlled // 3)
        }

        # 应用科技和特质加成
        for resource, amount in base_production.items():
            # 科技加成
            tech_bonus = player.technology.get("agriculture", 0) * 0.1
            if resource in ["wood", "stone", "coal"]:
                tech_bonus = player.technology.get("mining", 0) * 0.1

            # 特质加成
            trait_bonus = 0
            if "abundant_resources" in player.special_traits:
                trait_bonus = 0.2
            elif "resource_scarcity" in player.special_traits:
                trait_bonus = -0.1

            final_amount = int(amount * (1 + tech_bonus + trait_bonus))
            player.resources[resource] = player.resources.get(resource, 0) + final_amount

    def _process_research(self, player: PlayerState):
        """处理科技研发"""
        if player.current_research and player.research_points > 0:
            # 计算研发进度
            research_speed = 1

            # 科技加成
            if player.technology.get("science", 0) > 0:
                research_speed += player.technology["science"] * 0.5

            # 特质影响
            if "technological_advantage" in player.special_traits:
                research_speed *= 1.3
            elif "resource_scarcity" in player.special_traits:
                research_speed *= 0.8

            player.research_points -= research_speed

            # 研发完成
            if player.research_points <= 0:
                tech_name = player.current_research
                player.technology[tech_name] = player.technology.get(tech_name, 0) + 1
                player.current_research = None
                player.research_points = 0

                self.logger.info(f"玩家 {player.player_id} 完成科技研发: {tech_name}")

    def _create_world_event(self, event_type: str) -> WorldEvent:
        """创建世界事件"""
        import uuid

        event_id = str(uuid.uuid4())
        timestamp = datetime.now()

        # 根据事件类型创建具体事件
        if event_type == "natural_disaster":
            event = WorldEvent(
                event_id=event_id,
                event_type=event_type,
                title="自然灾害",
                description="一场自然灾害影响了部分地区",
                affected_players=random.sample(list(self.players.keys()),
                                             min(2, len(self.players))),
                effects={"population": -random.randint(50, 200),
                        "infrastructure": -random.randint(5, 15)},
                timestamp=timestamp,
                duration=1
            )
        elif event_type == "resource_discovery":
            lucky_player = random.choice(list(self.players.keys()))
            event = WorldEvent(
                event_id=event_id,
                event_type=event_type,
                title="资源发现",
                description="发现了新的资源矿藏",
                affected_players=[lucky_player],
                effects={"oil": random.randint(10, 30)},
                timestamp=timestamp,
                duration=0
            )
        else:
            # 默认事件
            event = WorldEvent(
                event_id=event_id,
                event_type=event_type,
                title="世界事件",
                description="发生了一个世界事件",
                affected_players=[],
                effects={},
                timestamp=timestamp,
                duration=0
            )

        self.world_events.append(event)
        if event.duration > 0:
            self.active_events.append(event)
        else:
            self._apply_event_effects(event)

        return event

    def _apply_event_effects(self, event: WorldEvent):
        """应用事件效果"""
        for player_id in event.affected_players:
            player = self.players.get(player_id)
            if player:
                for effect_type, effect_value in event.effects.items():
                    if effect_type in player.resources:
                        player.resources[effect_type] = max(0,
                            player.resources.get(effect_type, 0) + effect_value)
                    elif effect_type == "population":
                        player.population = max(0, player.population + effect_value)
                    elif effect_type == "infrastructure":
                        player.infrastructure = max(0,
                            min(100, player.infrastructure + effect_value))

    def _process_active_events(self):
        """处理活跃事件"""
        events_to_remove = []

        for event in self.active_events:
            event.duration -= 1

            if event.duration <= 0:
                self._apply_event_effects(event)
                events_to_remove.append(event)

        for event in events_to_remove:
            self.active_events.remove(event)

    def _generate_random_event(self):
        """生成随机事件"""
        event_types = [
            "economic_boom", "recession", "technological_breakthrough",
            "diplomatic_crisis", "resource_shortage", "population_migration",
            "military_innovation", "natural_disaster", "trade_disruption"
        ]

        event_type = random.choice(event_types)
        self._create_world_event(event_type)

    def _get_total_territories(self) -> int:
        """获取总领土数量"""
        if not self.world_map:
            return 100  # 默认值

        # 计算地图上的陆地格子数
        total = 0
        world_data = self.world_map.get("map", [])
        for row in world_data:
            for tile in row:
                if tile.get("terrain") != "ocean":
                    total += 1

        return total

    def _get_average_economic_power(self) -> float:
        """获取平均经济实力"""
        active_players = [p for p in self.players.values()
                         if p.status == PlayerStatus.ACTIVE]

        if not active_players:
            return 0

        total_power = sum(p.economic_power for p in active_players)
        return total_power / len(active_players)

    def _has_ultimate_technology(self, player: PlayerState) -> bool:
        """检查是否拥有终极科技"""
        # 定义终极科技条件
        ultimate_techs = ["nuclear_weapons", "space_technology", "artificial_intelligence"]

        for tech in ultimate_techs:
            if player.technology.get(tech, 0) >= 1:
                return True

        return False

    def _determine_winner_by_score(self) -> str:
        """根据分数确定获胜者"""
        active_players = [p for p in self.players.values()
                         if p.status == PlayerStatus.ACTIVE]

        if not active_players:
            return list(self.players.keys())[0]  # 返回第一个玩家

        winner = max(active_players, key=lambda p: p.total_score)
        return winner.player_id

    def _serialize_event(self, event: WorldEvent) -> Dict[str, Any]:
        """序列化事件数据"""
        return {
            "event_id": event.event_id,
            "event_type": event.event_type,
            "title": event.title,
            "description": event.description,
            "affected_players": event.affected_players,
            "effects": event.effects,
            "timestamp": event.timestamp.isoformat(),
            "duration": event.duration
        }

# 导入random模块
import random
