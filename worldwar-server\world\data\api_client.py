"""
API客户端模块 - 统一处理各种外部数据源API
支持世界银行、地理数据等多个数据源的集成
"""

import asyncio
import aiohttp
import time
import json
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from pathlib import Path


@dataclass
class RateLimit:
    """API限流配置"""
    requests_per_minute: int
    requests_per_hour: int
    last_request_time: float = 0
    request_count_minute: int = 0
    request_count_hour: int = 0
    minute_start: float = 0
    hour_start: float = 0


class APIError(Exception):
    """API相关错误"""
    def __init__(self, message: str, status_code: int = None, response_data: dict = None):
        self.message = message
        self.status_code = status_code
        self.response_data = response_data or {}
        super().__init__(message)


class APIClient:
    """统一API客户端 - 处理多个数据源的API调用"""
    
    def __init__(self, timeout: int = 30):
        self.session = None
        self.timeout = aiohttp.ClientTimeout(total=timeout)
        self.logger = logging.getLogger(__name__)
        
        # API端点配置
        self.endpoints = {
            "world_bank": "https://api.worldbank.org/v2",
            "openstreetmap": "https://nominatim.openstreetmap.org",
            "geonames": "http://api.geonames.org",
            "restcountries": "https://restcountries.com/v3.1",
            "openweather": "https://api.openweathermap.org/data/2.5"
        }
        
        # API限流配置
        self.rate_limits = {
            "world_bank": RateLimit(requests_per_minute=60, requests_per_hour=1000),
            "openstreetmap": RateLimit(requests_per_minute=60, requests_per_hour=1000),
            "geonames": RateLimit(requests_per_minute=60, requests_per_hour=2000),
            "restcountries": RateLimit(requests_per_minute=60, requests_per_hour=1000),
            "openweather": RateLimit(requests_per_minute=60, requests_per_hour=1000)
        }
        
        # API密钥配置（从环境变量或配置文件读取）
        self.api_keys = {
            "geonames": None,  # 需要注册获取
            "openweather": None  # 需要注册获取
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(timeout=self.timeout)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def _check_rate_limit(self, api_name: str) -> bool:
        """检查API限流"""
        if api_name not in self.rate_limits:
            return True
        
        rate_limit = self.rate_limits[api_name]
        current_time = time.time()
        
        # 重置分钟计数器
        if current_time - rate_limit.minute_start >= 60:
            rate_limit.minute_start = current_time
            rate_limit.request_count_minute = 0
        
        # 重置小时计数器
        if current_time - rate_limit.hour_start >= 3600:
            rate_limit.hour_start = current_time
            rate_limit.request_count_hour = 0
        
        # 检查限流
        if rate_limit.request_count_minute >= rate_limit.requests_per_minute:
            self.logger.warning(f"API {api_name} 达到分钟限流限制")
            return False
        
        if rate_limit.request_count_hour >= rate_limit.requests_per_hour:
            self.logger.warning(f"API {api_name} 达到小时限流限制")
            return False
        
        return True
    
    async def _update_rate_limit(self, api_name: str):
        """更新API限流计数"""
        if api_name in self.rate_limits:
            rate_limit = self.rate_limits[api_name]
            rate_limit.last_request_time = time.time()
            rate_limit.request_count_minute += 1
            rate_limit.request_count_hour += 1
    
    async def _make_request(self, api_name: str, url: str, params: dict = None, 
                          headers: dict = None) -> dict:
        """发起API请求"""
        if not self.session:
            raise APIError("API客户端未初始化，请使用async with语句")
        
        # 检查限流
        if not await self._check_rate_limit(api_name):
            raise APIError(f"API {api_name} 请求频率超限")
        
        try:
            self.logger.debug(f"请求 {api_name} API: {url}")
            async with self.session.get(url, params=params, headers=headers) as response:
                await self._update_rate_limit(api_name)
                
                if response.status == 200:
                    data = await response.json()
                    self.logger.debug(f"API {api_name} 请求成功")
                    return data
                else:
                    error_text = await response.text()
                    self.logger.error(f"API {api_name} 请求失败: {response.status} - {error_text}")
                    raise APIError(
                        f"API请求失败: {response.status}",
                        status_code=response.status,
                        response_data={"error": error_text}
                    )
        
        except aiohttp.ClientError as e:
            self.logger.error(f"API {api_name} 网络错误: {str(e)}")
            raise APIError(f"网络请求错误: {str(e)}")
        except json.JSONDecodeError as e:
            self.logger.error(f"API {api_name} JSON解析错误: {str(e)}")
            raise APIError(f"响应数据格式错误: {str(e)}")
    
    async def fetch_world_bank_data(self, indicator: str, country: str, 
                                  date_range: str = "2020:2023") -> dict:
        """
        获取世界银行数据
        
        Args:
            indicator: 指标代码 (如 'NY.GDP.PCAP.CD' 为人均GDP)
            country: 国家代码 (如 'CN', 'US')
            date_range: 日期范围 (如 '2020:2023')
        
        Returns:
            包含经济数据的字典
        """
        url = f"{self.endpoints['world_bank']}/country/{country}/indicator/{indicator}"
        params = {
            "date": date_range,
            "format": "json",
            "per_page": 100
        }
        
        try:
            data = await self._make_request("world_bank", url, params)
            
            # 世界银行API返回格式: [metadata, data]
            if isinstance(data, list) and len(data) >= 2:
                return {
                    "indicator": indicator,
                    "country": country,
                    "data": data[1] if data[1] else [],
                    "metadata": data[0] if len(data) > 0 else {}
                }
            else:
                return {"indicator": indicator, "country": country, "data": [], "metadata": {}}
        
        except APIError:
            self.logger.warning(f"获取世界银行数据失败: {indicator} for {country}")
            return {"indicator": indicator, "country": country, "data": [], "metadata": {}}
    
    async def fetch_geographic_features(self, location: str, country: str = None) -> dict:
        """
        获取地理特征数据
        
        Args:
            location: 地点名称
            country: 国家代码（可选，用于精确搜索）
        
        Returns:
            包含地理数据的字典
        """
        url = f"{self.endpoints['openstreetmap']}/search"
        params = {
            "q": location,
            "format": "json",
            "limit": 5,
            "addressdetails": 1,
            "extratags": 1
        }
        
        if country:
            params["countrycodes"] = country.lower()
        
        try:
            data = await self._make_request("openstreetmap", url, params)
            
            if data and isinstance(data, list):
                return {
                    "location": location,
                    "results": data,
                    "count": len(data)
                }
            else:
                return {"location": location, "results": [], "count": 0}
        
        except APIError:
            self.logger.warning(f"获取地理数据失败: {location}")
            return {"location": location, "results": [], "count": 0}
    
    async def fetch_city_data(self, city_name: str, country: str) -> dict:
        """
        获取城市数据
        
        Args:
            city_name: 城市名称
            country: 国家代码
        
        Returns:
            包含城市信息的字典
        """
        # 首先获取地理位置信息
        geo_data = await self.fetch_geographic_features(f"{city_name}, {country}")
        
        city_info = {
            "name": city_name,
            "country": country,
            "coordinates": None,
            "population": None,
            "area": None,
            "elevation": None,
            "timezone": None
        }
        
        if geo_data["results"]:
            result = geo_data["results"][0]  # 取第一个结果
            city_info.update({
                "coordinates": (float(result.get("lat", 0)), float(result.get("lon", 0))),
                "display_name": result.get("display_name", ""),
                "osm_type": result.get("osm_type", ""),
                "place_id": result.get("place_id", "")
            })
            
            # 提取额外信息
            if "extratags" in result:
                tags = result["extratags"]
                if "population" in tags:
                    try:
                        city_info["population"] = int(tags["population"])
                    except (ValueError, TypeError):
                        pass
                
                if "ele" in tags:
                    try:
                        city_info["elevation"] = float(tags["ele"])
                    except (ValueError, TypeError):
                        pass
        
        return city_info
    
    async def fetch_country_data(self, country_code: str) -> dict:
        """
        获取国家基本信息
        
        Args:
            country_code: 国家代码 (如 'CN', 'US')
        
        Returns:
            包含国家信息的字典
        """
        url = f"{self.endpoints['restcountries']}/alpha/{country_code}"
        
        try:
            data = await self._make_request("restcountries", url)
            
            if data:
                return {
                    "code": country_code,
                    "name": data.get("name", {}).get("common", ""),
                    "official_name": data.get("name", {}).get("official", ""),
                    "capital": data.get("capital", []),
                    "population": data.get("population", 0),
                    "area": data.get("area", 0),
                    "region": data.get("region", ""),
                    "subregion": data.get("subregion", ""),
                    "languages": data.get("languages", {}),
                    "currencies": data.get("currencies", {}),
                    "coordinates": data.get("latlng", [])
                }
            else:
                return {"code": country_code, "error": "未找到国家数据"}
        
        except APIError:
            self.logger.warning(f"获取国家数据失败: {country_code}")
            return {"code": country_code, "error": "API请求失败"}
    
    async def fetch_multiple_indicators(self, indicators: List[str], country: str) -> dict:
        """
        批量获取多个经济指标
        
        Args:
            indicators: 指标代码列表
            country: 国家代码
        
        Returns:
            包含所有指标数据的字典
        """
        results = {}
        
        # 并发请求多个指标
        tasks = []
        for indicator in indicators:
            task = self.fetch_world_bank_data(indicator, country)
            tasks.append(task)
        
        # 等待所有请求完成
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        for i, response in enumerate(responses):
            indicator = indicators[i]
            if isinstance(response, Exception):
                self.logger.error(f"获取指标 {indicator} 失败: {str(response)}")
                results[indicator] = {"error": str(response)}
            else:
                results[indicator] = response
        
        return results
    
    def get_rate_limit_status(self) -> dict:
        """获取所有API的限流状态"""
        status = {}
        current_time = time.time()
        
        for api_name, rate_limit in self.rate_limits.items():
            status[api_name] = {
                "requests_per_minute_limit": rate_limit.requests_per_minute,
                "requests_per_hour_limit": rate_limit.requests_per_hour,
                "current_minute_count": rate_limit.request_count_minute,
                "current_hour_count": rate_limit.request_count_hour,
                "last_request": rate_limit.last_request_time,
                "time_since_last_request": current_time - rate_limit.last_request_time
            }
        
        return status


# 常用的世界银行指标代码
WORLD_BANK_INDICATORS = {
    "gdp_per_capita": "NY.GDP.PCAP.CD",  # 人均GDP (美元)
    "population": "SP.POP.TOTL",  # 总人口
    "poverty_rate": "SI.POV.NAHC",  # 贫困率
    "unemployment": "SL.UEM.TOTL.ZS",  # 失业率
    "inflation": "FP.CPI.TOTL.ZG",  # 通胀率
    "life_expectancy": "SP.DYN.LE00.IN",  # 预期寿命
    "literacy_rate": "SE.ADT.LITR.ZS",  # 识字率
    "urban_population": "SP.URB.TOTL.IN.ZS",  # 城市人口比例
    "co2_emissions": "EN.ATM.CO2E.PC",  # 人均CO2排放
    "energy_use": "EG.USE.PCAP.KG.OE"  # 人均能源使用
}