#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
编码工具模块
Encoding Utilities Module
"""

import os
import sys
import subprocess
from typing import Dict, Any


class EncodingManager:
    """编码管理器 - 统一处理控制台编码和Unicode符号"""
    
    def __init__(self):
        """初始化编码管理器"""
        self.encoding_safe = self._setup_console_encoding()
        self.symbols = self._setup_safe_symbols()
    
    def _setup_console_encoding(self) -> bool:
        """设置控制台编码支持"""
        # 在Windows上设置控制台编码为UTF-8
        if os.name == 'nt':
            try:
                # 尝试设置控制台编码为UTF-8
                subprocess.run(['chcp', '65001'], shell=True, capture_output=True)
                
                # 重新配置stdout和stderr的编码
                if hasattr(sys.stdout, 'reconfigure'):
                    sys.stdout.reconfigure(encoding='utf-8', errors='replace')
                    sys.stderr.reconfigure(encoding='utf-8', errors='replace')
                
                return True
            except Exception:
                # 如果设置失败，使用安全模式
                return False
        else:
            # 非Windows系统通常支持UTF-8
            return True
    
    def _setup_safe_symbols(self) -> Dict[str, str]:
        """设置编码安全的符号"""
        if self.encoding_safe:
            # 使用Unicode符号
            return {
                'success': '✓',
                'error': '✗',
                'warning': '⚠',
                'info': 'ℹ',
                'ready': '✓',
                'waiting': '○',
                'bullet': '•',
                'arrow': '→',
                'game': '🎮',
                'chart': '📊',
                'rocket': '🚀',
                'party': '🎉',
                'cross': '❌',
                'check': '✅',
                'package': '📦',
                'server': '🖥️',
                'stop': '⏹️',
                'boom': '💥',
                'wave': '👋',
                'python': '🐍'
            }
        else:
            # 使用ASCII安全符号
            return {
                'success': '[OK]',
                'error': '[X]',
                'warning': '[!]',
                'info': '[i]',
                'ready': '[OK]',
                'waiting': '[ ]',
                'bullet': '*',
                'arrow': '->',
                'game': '[GAME]',
                'chart': '[CHART]',
                'rocket': '[START]',
                'party': '[SUCCESS]',
                'cross': '[FAIL]',
                'check': '[OK]',
                'package': '[INFO]',
                'server': '[SERVER]',
                'stop': '[STOP]',
                'boom': '[ERROR]',
                'wave': '[BYE]',
                'python': '[PYTHON]'
            }
    
    def get_symbol(self, symbol_name: str) -> str:
        """获取符号"""
        return self.symbols.get(symbol_name, symbol_name)
    
    def is_encoding_safe(self) -> bool:
        """检查编码是否安全"""
        return self.encoding_safe


# 全局编码管理器实例
_encoding_manager = None


def get_encoding_manager() -> EncodingManager:
    """获取全局编码管理器实例"""
    global _encoding_manager
    if _encoding_manager is None:
        _encoding_manager = EncodingManager()
    return _encoding_manager


def get_symbol(symbol_name: str) -> str:
    """获取符号的便捷函数"""
    return get_encoding_manager().get_symbol(symbol_name)


def is_encoding_safe() -> bool:
    """检查编码是否安全的便捷函数"""
    return get_encoding_manager().is_encoding_safe()


def setup_console_encoding():
    """设置控制台编码的便捷函数"""
    get_encoding_manager()  # 这会触发编码设置


# 为了向后兼容，提供符号字典
def get_symbols() -> Dict[str, str]:
    """获取符号字典"""
    return get_encoding_manager().symbols


# 常用符号的便捷访问
class Symbols:
    """符号常量类"""
    
    @staticmethod
    def success() -> str:
        return get_symbol('success')
    
    @staticmethod
    def error() -> str:
        return get_symbol('error')
    
    @staticmethod
    def warning() -> str:
        return get_symbol('warning')
    
    @staticmethod
    def info() -> str:
        return get_symbol('info')
    
    @staticmethod
    def game() -> str:
        return get_symbol('game')
    
    @staticmethod
    def chart() -> str:
        return get_symbol('chart')
    
    @staticmethod
    def rocket() -> str:
        return get_symbol('rocket')
    
    @staticmethod
    def party() -> str:
        return get_symbol('party')


# 创建符号实例
symbols = Symbols()
