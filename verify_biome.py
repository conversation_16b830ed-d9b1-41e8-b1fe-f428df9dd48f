#!/usr/bin/env python3
import sys
sys.path.append('worldwar-server')

from world.terrain.perlin_generator import <PERSON><PERSON><PERSON><PERSON>rainGenerator, TerrainConfig
from world.terrain.biome_generator import BiomeGenerator
from world.terrain.terrain_types import BiomeType, TerrainType

# Create terrain generator
terrain_config = TerrainConfig(size=(64, 64))
terrain_gen = PerlinTerrainGenerator(seed=12345, terrain_config=terrain_config)

print("Generating base maps...")
heightmap = terrain_gen.generate_heightmap()
temperature_map = terrain_gen.generate_temperature_map(heightmap)
precipitation_map = terrain_gen.generate_precipitation_map(heightmap)

print(f"Heightmap range: {heightmap.min():.3f} - {heightmap.max():.3f}")
print(f"Temperature range: {temperature_map.min():.3f} - {temperature_map.max():.3f}")
print(f"Precipitation range: {precipitation_map.min():.3f} - {precipitation_map.max():.3f}")

# Create biome generator
biome_gen = BiomeGenerator(seed=12345)

print("Generating biome map...")
biome_map = biome_gen.generate_biome_map(heightmap, temperature_map, precipitation_map)
print(f"Biome map shape: {biome_map.shape}")
print(f"Biome map range: {biome_map.min()} - {biome_map.max()}")

print("Generating terrain from biomes...")
terrain_map = biome_gen.generate_terrain_from_biomes(
    biome_map, heightmap, temperature_map, precipitation_map
)
print(f"Terrain map shape: {terrain_map.shape}")
print(f"Terrain map range: {terrain_map.min()} - {terrain_map.max()}")

print("Getting biome statistics...")
stats = biome_gen.get_biome_statistics(biome_map)
print(f"Diversity index: {stats['diversity_index']:.3f}")
print(f"Total biome types: {stats['total_biome_types']}")

# Show biome distribution
biome_types = list(BiomeType)
print("\nBiome distribution:")
for i, biome_type in enumerate(biome_types):
    if biome_type.value in stats and stats[biome_type.value] > 0:
        print(f"  {biome_type.value}: {stats[biome_type.value]:.3f}")

print("Creating transition mask...")
transition_mask = biome_gen.create_biome_transition_mask(biome_map)
print(f"Transition mask range: {transition_mask.min():.3f} - {transition_mask.max():.3f}")

print("✓ Biome generator working correctly!")