# 🔧 技术文档 / Technical Documentation

## 📋 项目架构 / Project Architecture

### 🏗️ 整体架构 / Overall Architecture
```
世界大战游戏 / World War Game
├── worldwar-server/     # 服务器端项目
│   ├── server/         # 服务器核心模块
│   ├── shared/         # 共享组件
│   ├── utils/          # 工具模块
│   ├── config/         # 配置文件
│   └── logs/           # 服务器日志
├── worldwar-client/     # 客户端项目
│   ├── client/         # 客户端核心模块
│   ├── shared/         # 共享组件
│   ├── config/         # 配置文件
│   └── logs/           # 客户端日志
└── docs/               # 项目文档
```

### 🔄 通信架构 / Communication Architecture
```
客户端 (Client)  ←→  网络层 (Network)  ←→  服务器 (Server)
     ↓                    ↓                    ↓
用户界面 (UI)         安全协议 (Protocol)    游戏逻辑 (Logic)
     ↓                    ↓                    ↓
本地状态 (State)      消息管理 (Messages)    数据存储 (Storage)
```

## 🖥️ 服务器端 / Server Side

### 核心模块 / Core Modules

#### 🎮 游戏服务器 (GameServer)
- **文件**: `server/game_server.py`
- **功能**: 主服务器类，管理所有游戏逻辑
- **职责**: 
  - 客户端连接管理
  - 游戏状态维护
  - 消息路由和处理

#### 👥 会话管理 (SessionManager)
- **文件**: `server/player_session.py`
- **功能**: 管理玩家会话和认证
- **特性**:
  - 线程安全的会话管理
  - 用户名验证和冲突检查
  - 随机用户名生成
  - 会话超时处理

#### 🏠 房间管理 (RoomManager)
- **文件**: `server/room_manager.py`
- **功能**: 游戏房间创建和管理
- **特性**:
  - 房间生命周期管理
  - 玩家加入/离开处理
  - 房间状态同步

#### 🔒 安全协议 (SecureProtocol)
- **文件**: `shared/secure_protocol.py`
- **功能**: 加密通信协议
- **特性**:
  - 消息加密/解密
  - 数字签名验证
  - 防重放攻击

### 🛠️ 工具模块 / Utility Modules

#### 📝 增强日志 (EnhancedLogger)
- **文件**: `shared/enhanced_logger.py`
- **功能**: 分离式日志系统
- **特性**:
  - 服务器/客户端分离日志
  - 自动日志归档
  - 多级别日志记录

#### 🔐 多开防护 (MultiInstanceGuard)
- **文件**: `utils/multi_instance_guard.py`
- **功能**: 防止多个实例同时运行
- **特性**:
  - 进程锁机制
  - 跨平台支持
  - 强制终止功能

#### 🎉 欢迎管理 (WelcomeManager)
- **文件**: `server/welcome_manager.py`
- **功能**: 欢迎消息和启动信息
- **特性**:
  - 多语言欢迎消息
  - 服务器横幅生成
  - 启动提示管理

## 💻 客户端 / Client Side

### 核心模块 / Core Modules

#### 🎮 客户端核心 (ClientCore)
- **文件**: `client/core/client_core.py`
- **功能**: 客户端主要逻辑
- **职责**:
  - 服务器连接管理
  - 用户界面控制
  - 本地状态维护

#### 🌐 网络服务 (NetworkService)
- **文件**: `client/services/network_service.py`
- **功能**: 网络通信服务
- **特性**:
  - 异步消息处理
  - 连接状态管理
  - 自动重连机制

#### 🎨 用户界面 (UIManager)
- **文件**: `client/ui/ui_manager.py`
- **功能**: 用户界面管理
- **特性**:
  - 命令行界面
  - 多语言支持
  - 交互式菜单

### 🔧 辅助模块 / Helper Modules

#### 👤 用户名助手 (UsernameHelper)
- **文件**: `client/core/username_helper.py`
- **功能**: 客户端用户名管理
- **特性**:
  - 本地格式验证
  - 随机用户名生成
  - 建议用户名提供

#### 🎉 欢迎助手 (WelcomeHelper)
- **文件**: `client/core/welcome_helper.py`
- **功能**: 客户端欢迎信息
- **特性**:
  - 客户端横幅显示
  - 服务器欢迎消息解析
  - 快速帮助信息

## 🔗 共享组件 / Shared Components

### 📨 消息系统 / Message System
- **协议版本**: v1.0
- **编码格式**: UTF-8
- **消息格式**: JSON
- **最大消息长度**: 1MB

#### 消息类型 / Message Types
```json
{
  "type": "message_type",
  "data": {
    "key": "value"
  },
  "timestamp": "2025-01-01T00:00:00Z",
  "protocol_version": "1.0"
}
```

### 🌍 语言管理 / Language Management
- **支持语言**: 中文 (Chinese), 英文 (English)
- **配置文件**: `languages/*.json`
- **动态切换**: 运行时语言切换
- **回退机制**: 缺失翻译时的回退策略

### 🔒 安全机制 / Security Mechanisms

#### 加密通信 / Encrypted Communication
- **算法**: AES-256-GCM
- **密钥交换**: ECDH (Elliptic Curve Diffie-Hellman)
- **数字签名**: ECDSA
- **哈希算法**: SHA-256

#### 防护措施 / Protection Measures
- **防重放攻击**: 时间戳和随机数验证
- **消息完整性**: HMAC验证
- **连接限制**: 单IP连接数限制
- **速率限制**: 消息发送频率限制

## 🗄️ 数据存储 / Data Storage

### 📁 文件结构 / File Structure
```
数据存储 / Data Storage
├── config/             # 配置文件
│   ├── server.ini     # 服务器配置
│   ├── client.ini     # 客户端配置
│   └── security.key   # 安全密钥
├── logs/              # 日志文件
│   ├── server/        # 服务器日志
│   └── client/        # 客户端日志
└── data/              # 游戏数据
    ├── worlds/        # 世界数据
    ├── players/       # 玩家数据
    └── cache/         # 缓存数据
```

### 💾 数据格式 / Data Formats
- **配置文件**: INI格式
- **游戏数据**: JSON格式
- **日志文件**: 纯文本格式
- **缓存数据**: 二进制格式

## 🚀 部署指南 / Deployment Guide

### 📦 依赖管理 / Dependency Management
```bash
# 服务器依赖
cd worldwar-server
pip install -r requirements.txt

# 客户端依赖
cd worldwar-client
pip install -r requirements.txt
```

### 🔧 配置选项 / Configuration Options

#### 服务器配置 / Server Configuration
```ini
[server]
host = 0.0.0.0
port = 8888
max_players = 8
debug = false

[security]
encryption_enabled = true
signature_verification = true
connection_timeout = 30

[logging]
level = INFO
log_to_file = true
log_to_console = true
```

#### 客户端配置 / Client Configuration
```ini
[client]
default_server = localhost:8888
auto_reconnect = true
language = chinese

[ui]
show_timestamps = true
color_enabled = true
animation_enabled = false
```

### 🐳 Docker部署 / Docker Deployment
```dockerfile
# 服务器Docker镜像
FROM python:3.9-slim
WORKDIR /app
COPY worldwar-server/ .
RUN pip install -r requirements.txt
EXPOSE 8888
CMD ["python", "server_main.py"]
```

## 🔍 调试和监控 / Debugging and Monitoring

### 🐛 调试选项 / Debug Options
```bash
# 服务器调试
python server_main.py --debug --verbose --log-level DEBUG

# 客户端调试
python client_main.py --debug

# 特定模块调试
python server_main.py --debug-modules network user_actions
```

### 📊 监控指标 / Monitoring Metrics
- **连接数**: 当前活跃连接数
- **消息量**: 每秒处理的消息数
- **错误率**: 错误消息占总消息的比例
- **响应时间**: 平均消息处理时间
- **内存使用**: 服务器内存占用情况

### 📝 日志分析 / Log Analysis
```bash
# 查看服务器日志
tail -f worldwar-server/logs/server/latest.log

# 查看客户端日志
tail -f worldwar-client/logs/client/latest.log

# 错误日志过滤
grep "ERROR" worldwar-server/logs/server/latest.log
```

## 🔧 开发工具 / Development Tools

### 🧪 测试工具 / Testing Tools
- **单元测试**: pytest框架
- **集成测试**: 多客户端模拟
- **压力测试**: 并发连接测试
- **安全测试**: 渗透测试工具

### 📈 性能分析 / Performance Analysis
- **性能监控**: cProfile分析
- **内存分析**: memory_profiler
- **网络分析**: Wireshark抓包
- **并发测试**: 多线程压力测试

---

**🔧 技术架构确保游戏的稳定性和可扩展性！**
**Technical architecture ensures game stability and scalability!**
