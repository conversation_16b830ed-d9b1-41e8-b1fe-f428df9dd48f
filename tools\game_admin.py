#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏管理工具
Game Administration Tool

提供游戏房间和玩家状态的管理界面
Provides management interface for game rooms and player status
"""

import json
import socket
import threading
import time
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import argparse

@dataclass
class GameRoom:
    """游戏房间信息"""
    room_id: str
    room_name: str
    status: str
    host_player: str
    players: List[str]
    max_players: int
    created_at: datetime
    started_at: Optional[datetime]
    terrain_type: Optional[str]
    game_settings: Dict[str, Any]

@dataclass
class GamePlayer:
    """游戏玩家信息"""
    player_id: str
    username: str
    session_id: str
    status: str
    current_room: Optional[str]
    connected_at: datetime
    last_activity: datetime
    ip_address: str
    total_games: int
    wins: int

class GameAdminInterface:
    """游戏管理界面"""
    
    def __init__(self, server_host: str = "localhost", server_port: int = 8888):
        """
        初始化游戏管理界面
        
        Args:
            server_host: 服务器主机
            server_port: 服务器端口
        """
        self.server_host = server_host
        self.server_port = server_port
        
        # 模拟数据存储（在实际实现中应该从服务器获取）
        self.rooms = {}
        self.players = {}
        self.game_statistics = {
            'total_games_played': 0,
            'total_players_registered': 0,
            'peak_concurrent_players': 0,
            'average_game_duration_minutes': 0,
            'most_popular_terrain_type': 'perlin'
        }
        
        # 管理操作历史
        self.admin_actions = []
        
        self.lock = threading.Lock()
    
    def connect_to_server(self) -> bool:
        """连接到游戏服务器"""
        try:
            # 在实际实现中，这里会建立与游戏服务器的管理连接
            # 目前只是检查服务器是否可达
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(5)
                result = sock.connect_ex((self.server_host, self.server_port))
                return result == 0
        except Exception as e:
            print(f"连接服务器失败: {e}")
            return False
    
    def refresh_data(self):
        """刷新数据"""
        # 在实际实现中，这里会从服务器获取最新的房间和玩家数据
        # 目前使用模拟数据
        self._generate_mock_data()
    
    def _generate_mock_data(self):
        """生成模拟数据用于演示"""
        current_time = datetime.now()
        
        # 模拟房间数据
        mock_rooms = {
            "room_001": GameRoom(
                room_id="room_001",
                room_name="新手房间",
                status="waiting",
                host_player="player_001",
                players=["player_001", "player_002"],
                max_players=4,
                created_at=current_time - timedelta(minutes=5),
                started_at=None,
                terrain_type="perlin",
                game_settings={"difficulty": "easy", "map_size": "medium"}
            ),
            "room_002": GameRoom(
                room_id="room_002",
                room_name="高手对决",
                status="playing",
                host_player="player_003",
                players=["player_003", "player_004", "player_005", "player_006"],
                max_players=4,
                created_at=current_time - timedelta(minutes=15),
                started_at=current_time - timedelta(minutes=10),
                terrain_type="realworld",
                game_settings={"difficulty": "hard", "map_size": "large", "region": "california"}
            ),
            "room_003": GameRoom(
                room_id="room_003",
                room_name="休闲娱乐",
                status="finished",
                host_player="player_007",
                players=["player_007", "player_008"],
                max_players=6,
                created_at=current_time - timedelta(hours=1),
                started_at=current_time - timedelta(minutes=50),
                terrain_type="perlin",
                game_settings={"difficulty": "normal", "map_size": "small"}
            )
        }
        
        # 模拟玩家数据
        mock_players = {
            "player_001": GamePlayer(
                player_id="player_001",
                username="新手小白",
                session_id="session_001",
                status="in_room",
                current_room="room_001",
                connected_at=current_time - timedelta(minutes=10),
                last_activity=current_time - timedelta(seconds=30),
                ip_address="*************",
                total_games=5,
                wins=2
            ),
            "player_002": GamePlayer(
                player_id="player_002",
                username="游戏达人",
                session_id="session_002",
                status="in_room",
                current_room="room_001",
                connected_at=current_time - timedelta(minutes=8),
                last_activity=current_time - timedelta(seconds=15),
                ip_address="*************",
                total_games=25,
                wins=18
            ),
            "player_003": GamePlayer(
                player_id="player_003",
                username="战略大师",
                session_id="session_003",
                status="playing",
                current_room="room_002",
                connected_at=current_time - timedelta(minutes=20),
                last_activity=current_time - timedelta(seconds=5),
                ip_address="*************",
                total_games=50,
                wins=35
            )
        }
        
        with self.lock:
            self.rooms = mock_rooms
            self.players = mock_players
    
    def list_rooms(self, status_filter: str = None) -> List[GameRoom]:
        """
        列出游戏房间
        
        Args:
            status_filter: 状态过滤器 (waiting, playing, finished)
            
        Returns:
            房间列表
        """
        with self.lock:
            rooms = list(self.rooms.values())
            
            if status_filter:
                rooms = [r for r in rooms if r.status == status_filter]
            
            return sorted(rooms, key=lambda x: x.created_at, reverse=True)
    
    def list_players(self, status_filter: str = None) -> List[GamePlayer]:
        """
        列出游戏玩家
        
        Args:
            status_filter: 状态过滤器 (connected, in_room, playing, disconnected)
            
        Returns:
            玩家列表
        """
        with self.lock:
            players = list(self.players.values())
            
            if status_filter:
                players = [p for p in players if p.status == status_filter]
            
            return sorted(players, key=lambda x: x.connected_at, reverse=True)
    
    def get_room_details(self, room_id: str) -> Optional[GameRoom]:
        """获取房间详情"""
        with self.lock:
            return self.rooms.get(room_id)
    
    def get_player_details(self, player_id: str) -> Optional[GamePlayer]:
        """获取玩家详情"""
        with self.lock:
            return self.players.get(player_id)
    
    def close_room(self, room_id: str, reason: str = "管理员关闭") -> bool:
        """
        关闭房间
        
        Args:
            room_id: 房间ID
            reason: 关闭原因
            
        Returns:
            是否成功
        """
        with self.lock:
            if room_id not in self.rooms:
                return False
            
            room = self.rooms[room_id]
            
            # 记录管理操作
            action = {
                'timestamp': datetime.now(),
                'action': 'close_room',
                'target': room_id,
                'reason': reason,
                'details': {
                    'room_name': room.room_name,
                    'players_affected': len(room.players),
                    'room_status': room.status
                }
            }
            self.admin_actions.append(action)
            
            # 在实际实现中，这里会向服务器发送关闭房间的命令
            print(f"房间 {room.room_name} ({room_id}) 已关闭，原因: {reason}")
            
            # 更新房间状态
            room.status = "closed"
            
            # 更新受影响玩家的状态
            for player_id in room.players:
                if player_id in self.players:
                    self.players[player_id].status = "connected"
                    self.players[player_id].current_room = None
            
            return True
    
    def kick_player(self, player_id: str, reason: str = "管理员踢出") -> bool:
        """
        踢出玩家
        
        Args:
            player_id: 玩家ID
            reason: 踢出原因
            
        Returns:
            是否成功
        """
        with self.lock:
            if player_id not in self.players:
                return False
            
            player = self.players[player_id]
            
            # 记录管理操作
            action = {
                'timestamp': datetime.now(),
                'action': 'kick_player',
                'target': player_id,
                'reason': reason,
                'details': {
                    'username': player.username,
                    'current_room': player.current_room,
                    'player_status': player.status
                }
            }
            self.admin_actions.append(action)
            
            # 在实际实现中，这里会向服务器发送踢出玩家的命令
            print(f"玩家 {player.username} ({player_id}) 已被踢出，原因: {reason}")
            
            # 更新玩家状态
            player.status = "disconnected"
            
            # 如果玩家在房间中，从房间中移除
            if player.current_room and player.current_room in self.rooms:
                room = self.rooms[player.current_room]
                if player_id in room.players:
                    room.players.remove(player_id)
                
                player.current_room = None
            
            return True
    
    def send_message_to_room(self, room_id: str, message: str) -> bool:
        """
        向房间发送消息
        
        Args:
            room_id: 房间ID
            message: 消息内容
            
        Returns:
            是否成功
        """
        with self.lock:
            if room_id not in self.rooms:
                return False
            
            room = self.rooms[room_id]
            
            # 记录管理操作
            action = {
                'timestamp': datetime.now(),
                'action': 'send_room_message',
                'target': room_id,
                'details': {
                    'room_name': room.room_name,
                    'message': message,
                    'recipients': len(room.players)
                }
            }
            self.admin_actions.append(action)
            
            # 在实际实现中，这里会向服务器发送消息
            print(f"向房间 {room.room_name} 发送消息: {message}")
            
            return True
    
    def send_message_to_player(self, player_id: str, message: str) -> bool:
        """
        向玩家发送消息
        
        Args:
            player_id: 玩家ID
            message: 消息内容
            
        Returns:
            是否成功
        """
        with self.lock:
            if player_id not in self.players:
                return False
            
            player = self.players[player_id]
            
            # 记录管理操作
            action = {
                'timestamp': datetime.now(),
                'action': 'send_player_message',
                'target': player_id,
                'details': {
                    'username': player.username,
                    'message': message
                }
            }
            self.admin_actions.append(action)
            
            # 在实际实现中，这里会向服务器发送消息
            print(f"向玩家 {player.username} 发送消息: {message}")
            
            return True
    
    def get_game_statistics(self) -> Dict[str, Any]:
        """获取游戏统计信息"""
        with self.lock:
            current_time = datetime.now()
            
            # 计算实时统计
            total_rooms = len(self.rooms)
            active_rooms = len([r for r in self.rooms.values() if r.status in ['waiting', 'playing']])
            total_players = len(self.players)
            online_players = len([p for p in self.players.values() if p.status != 'disconnected'])
            
            # 计算平均游戏时长
            finished_rooms = [r for r in self.rooms.values() if r.status == 'finished' and r.started_at]
            if finished_rooms:
                durations = []
                for room in finished_rooms:
                    if room.started_at:
                        # 假设游戏持续了30分钟（实际应该有结束时间）
                        duration = 30  # 分钟
                        durations.append(duration)
                avg_duration = sum(durations) / len(durations) if durations else 0
            else:
                avg_duration = 0
            
            # 统计地形类型使用情况
            terrain_usage = {}
            for room in self.rooms.values():
                if room.terrain_type:
                    terrain_usage[room.terrain_type] = terrain_usage.get(room.terrain_type, 0) + 1
            
            most_popular_terrain = max(terrain_usage.items(), key=lambda x: x[1])[0] if terrain_usage else "unknown"
            
            return {
                'current_stats': {
                    'total_rooms': total_rooms,
                    'active_rooms': active_rooms,
                    'total_players': total_players,
                    'online_players': online_players,
                    'rooms_waiting': len([r for r in self.rooms.values() if r.status == 'waiting']),
                    'rooms_playing': len([r for r in self.rooms.values() if r.status == 'playing']),
                    'rooms_finished': len([r for r in self.rooms.values() if r.status == 'finished'])
                },
                'historical_stats': {
                    'total_games_played': self.game_statistics['total_games_played'] + len(finished_rooms),
                    'average_game_duration_minutes': avg_duration,
                    'most_popular_terrain_type': most_popular_terrain,
                    'terrain_usage': terrain_usage
                },
                'player_stats': {
                    'total_registered': total_players,
                    'currently_online': online_players,
                    'in_rooms': len([p for p in self.players.values() if p.current_room]),
                    'playing': len([p for p in self.players.values() if p.status == 'playing'])
                }
            }
    
    def get_admin_actions(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取管理操作历史"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        with self.lock:
            return [action for action in self.admin_actions 
                   if action['timestamp'] >= cutoff_time]
    
    def generate_admin_report(self) -> str:
        """生成管理报告"""
        stats = self.get_game_statistics()
        recent_actions = self.get_admin_actions(hours=24)
        
        report_lines = [
            "=" * 60,
            "游戏管理报告",
            "=" * 60,
            "",
            f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"服务器地址: {self.server_host}:{self.server_port}",
            "",
            "当前状态:",
            "-" * 20,
            f"总房间数: {stats['current_stats']['total_rooms']}",
            f"活跃房间: {stats['current_stats']['active_rooms']}",
            f"  - 等待中: {stats['current_stats']['rooms_waiting']}",
            f"  - 游戏中: {stats['current_stats']['rooms_playing']}",
            f"  - 已结束: {stats['current_stats']['rooms_finished']}",
            "",
            f"总玩家数: {stats['current_stats']['total_players']}",
            f"在线玩家: {stats['current_stats']['online_players']}",
            f"房间中玩家: {stats['player_stats']['in_rooms']}",
            f"游戏中玩家: {stats['player_stats']['playing']}",
            "",
            "历史统计:",
            "-" * 20,
            f"总游戏场次: {stats['historical_stats']['total_games_played']}",
            f"平均游戏时长: {stats['historical_stats']['average_game_duration_minutes']:.1f} 分钟",
            f"最受欢迎地形: {stats['historical_stats']['most_popular_terrain_type']}",
        ]
        
        # 添加地形使用统计
        if stats['historical_stats']['terrain_usage']:
            report_lines.extend([
                "",
                "地形类型使用统计:",
                "-" * 20
            ])
            for terrain, count in stats['historical_stats']['terrain_usage'].items():
                report_lines.append(f"  {terrain}: {count} 次")
        
        # 添加最近管理操作
        if recent_actions:
            report_lines.extend([
                "",
                "最近管理操作 (24小时内):",
                "-" * 20
            ])
            
            for action in recent_actions[-10:]:  # 显示最近10个操作
                timestamp = action['timestamp'].strftime('%H:%M:%S')
                action_type = action['action']
                target = action['target']
                reason = action.get('reason', '')
                
                report_lines.append(f"  [{timestamp}] {action_type} - {target}")
                if reason:
                    report_lines.append(f"    原因: {reason}")
        
        report_lines.extend([
            "",
            "=" * 60,
            "报告结束",
            "=" * 60
        ])
        
        return "\n".join(report_lines)
    
    def export_data(self, output_file: Path):
        """导出管理数据"""
        with self.lock:
            data = {
                'export_time': datetime.now().isoformat(),
                'server_info': {
                    'host': self.server_host,
                    'port': self.server_port
                },
                'rooms': {room_id: {
                    'room_id': room.room_id,
                    'room_name': room.room_name,
                    'status': room.status,
                    'host_player': room.host_player,
                    'players': room.players,
                    'max_players': room.max_players,
                    'created_at': room.created_at.isoformat(),
                    'started_at': room.started_at.isoformat() if room.started_at else None,
                    'terrain_type': room.terrain_type,
                    'game_settings': room.game_settings
                } for room_id, room in self.rooms.items()},
                'players': {player_id: {
                    'player_id': player.player_id,
                    'username': player.username,
                    'session_id': player.session_id,
                    'status': player.status,
                    'current_room': player.current_room,
                    'connected_at': player.connected_at.isoformat(),
                    'last_activity': player.last_activity.isoformat(),
                    'ip_address': player.ip_address,
                    'total_games': player.total_games,
                    'wins': player.wins
                } for player_id, player in self.players.items()},
                'statistics': self.get_game_statistics(),
                'admin_actions': [{
                    'timestamp': action['timestamp'].isoformat(),
                    'action': action['action'],
                    'target': action['target'],
                    'reason': action.get('reason', ''),
                    'details': action.get('details', {})
                } for action in self.admin_actions]
            }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"管理数据已导出到: {output_file}")

def interactive_mode(admin: GameAdminInterface):
    """交互模式"""
    print("进入交互模式，输入 'help' 查看可用命令")
    
    while True:
        try:
            command = input("\n> ").strip().lower()
            
            if command == 'help':
                print("""
可用命令:
  rooms [status]     - 列出房间 (可选状态过滤: waiting, playing, finished)
  players [status]   - 列出玩家 (可选状态过滤: connected, in_room, playing)
  room <room_id>     - 查看房间详情
  player <player_id> - 查看玩家详情
  close <room_id>    - 关闭房间
  kick <player_id>   - 踢出玩家
  msg_room <room_id> <message> - 向房间发送消息
  msg_player <player_id> <message> - 向玩家发送消息
  stats              - 显示游戏统计
  report             - 生成管理报告
  refresh            - 刷新数据
  export <file>      - 导出数据
  quit               - 退出
                """)
            
            elif command == 'quit':
                break
            
            elif command == 'refresh':
                admin.refresh_data()
                print("数据已刷新")
            
            elif command.startswith('rooms'):
                parts = command.split()
                status_filter = parts[1] if len(parts) > 1 else None
                rooms = admin.list_rooms(status_filter)
                
                print(f"\n房间列表 ({len(rooms)} 个):")
                print("-" * 80)
                print(f"{'房间ID':<12} {'房间名':<20} {'状态':<10} {'玩家':<8} {'地形':<10} {'创建时间':<20}")
                print("-" * 80)
                
                for room in rooms:
                    created_time = room.created_at.strftime('%m-%d %H:%M')
                    print(f"{room.room_id:<12} {room.room_name:<20} {room.status:<10} "
                          f"{len(room.players)}/{room.max_players:<8} {room.terrain_type or 'N/A':<10} {created_time:<20}")
            
            elif command.startswith('players'):
                parts = command.split()
                status_filter = parts[1] if len(parts) > 1 else None
                players = admin.list_players(status_filter)
                
                print(f"\n玩家列表 ({len(players)} 个):")
                print("-" * 80)
                print(f"{'玩家ID':<12} {'用户名':<15} {'状态':<10} {'房间':<12} {'连接时间':<20}")
                print("-" * 80)
                
                for player in players:
                    connected_time = player.connected_at.strftime('%m-%d %H:%M')
                    print(f"{player.player_id:<12} {player.username:<15} {player.status:<10} "
                          f"{player.current_room or 'N/A':<12} {connected_time:<20}")
            
            elif command.startswith('room '):
                room_id = command.split()[1]
                room = admin.get_room_details(room_id)
                
                if room:
                    print(f"\n房间详情: {room.room_name}")
                    print("-" * 40)
                    print(f"房间ID: {room.room_id}")
                    print(f"状态: {room.status}")
                    print(f"房主: {room.host_player}")
                    print(f"玩家: {', '.join(room.players)} ({len(room.players)}/{room.max_players})")
                    print(f"地形类型: {room.terrain_type}")
                    print(f"创建时间: {room.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
                    if room.started_at:
                        print(f"开始时间: {room.started_at.strftime('%Y-%m-%d %H:%M:%S')}")
                    print(f"游戏设置: {room.game_settings}")
                else:
                    print(f"房间 {room_id} 不存在")
            
            elif command.startswith('player '):
                player_id = command.split()[1]
                player = admin.get_player_details(player_id)
                
                if player:
                    print(f"\n玩家详情: {player.username}")
                    print("-" * 40)
                    print(f"玩家ID: {player.player_id}")
                    print(f"用户名: {player.username}")
                    print(f"状态: {player.status}")
                    print(f"当前房间: {player.current_room or '无'}")
                    print(f"连接时间: {player.connected_at.strftime('%Y-%m-%d %H:%M:%S')}")
                    print(f"最后活动: {player.last_activity.strftime('%Y-%m-%d %H:%M:%S')}")
                    print(f"IP地址: {player.ip_address}")
                    print(f"总游戏数: {player.total_games}")
                    print(f"胜利数: {player.wins}")
                    print(f"胜率: {player.wins/player.total_games*100:.1f}%" if player.total_games > 0 else "胜率: N/A")
                else:
                    print(f"玩家 {player_id} 不存在")
            
            elif command.startswith('close '):
                room_id = command.split()[1]
                if admin.close_room(room_id):
                    print(f"房间 {room_id} 已关闭")
                else:
                    print(f"关闭房间 {room_id} 失败")
            
            elif command.startswith('kick '):
                player_id = command.split()[1]
                if admin.kick_player(player_id):
                    print(f"玩家 {player_id} 已被踢出")
                else:
                    print(f"踢出玩家 {player_id} 失败")
            
            elif command == 'stats':
                stats = admin.get_game_statistics()
                print("\n游戏统计:")
                print("-" * 40)
                print(f"总房间数: {stats['current_stats']['total_rooms']}")
                print(f"活跃房间: {stats['current_stats']['active_rooms']}")
                print(f"总玩家数: {stats['current_stats']['total_players']}")
                print(f"在线玩家: {stats['current_stats']['online_players']}")
                print(f"总游戏场次: {stats['historical_stats']['total_games_played']}")
                print(f"平均游戏时长: {stats['historical_stats']['average_game_duration_minutes']:.1f} 分钟")
            
            elif command == 'report':
                report = admin.generate_admin_report()
                print(report)
            
            elif command.startswith('export '):
                filename = command.split()[1]
                admin.export_data(Path(filename))
            
            else:
                print("未知命令，输入 'help' 查看可用命令")
        
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"命令执行出错: {e}")
    
    print("退出交互模式")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='游戏管理工具')
    parser.add_argument('--host', default='localhost', help='服务器主机地址')
    parser.add_argument('--port', type=int, default=8888, help='服务器端口')
    parser.add_argument('--interactive', '-i', action='store_true', help='进入交互模式')
    parser.add_argument('--export', type=Path, help='导出数据文件路径')
    parser.add_argument('--report', action='store_true', help='生成管理报告')
    
    args = parser.parse_args()
    
    # 创建游戏管理界面
    admin = GameAdminInterface(args.host, args.port)
    
    # 连接服务器并刷新数据
    if admin.connect_to_server():
        print(f"已连接到服务器 {args.host}:{args.port}")
    else:
        print(f"无法连接到服务器 {args.host}:{args.port}，使用模拟数据")
    
    admin.refresh_data()
    
    if args.interactive:
        # 交互模式
        interactive_mode(admin)
    else:
        # 命令行模式
        if args.report:
            report = admin.generate_admin_report()
            print(report)
        
        if args.export:
            admin.export_data(args.export)
        
        if not args.report and not args.export:
            # 显示简要状态
            stats = admin.get_game_statistics()
            print("游戏服务器状态:")
            print(f"  房间: {stats['current_stats']['active_rooms']}/{stats['current_stats']['total_rooms']} 活跃")
            print(f"  玩家: {stats['current_stats']['online_players']}/{stats['current_stats']['total_players']} 在线")
            print("\n使用 --interactive 进入交互模式，或 --help 查看更多选项")

if __name__ == '__main__':
    main()