2025-07-04 15:47:37 - client_ClientMain - INFO - [enhanced_logger.py:174] - 客户端启动
2025-07-04 15:47:37 - client_ClientMain - INFO - [enhanced_logger.py:174] - 🎮 世界大战游戏客户端启动
2025-07-04 15:47:37 - client_ClientMain - INFO - [enhanced_logger.py:174] - 初始化客户端...
2025-07-04 15:47:37 - client_NewClientCore - INFO - [enhanced_logger.py:174] - 客户端核心初始化
2025-07-04 15:47:37 - client_SettingsManager - INFO - [enhanced_logger.py:174] - 设置文件加载成功
2025-07-04 15:47:37 - client_SettingsManager - INFO - [enhanced_logger.py:174] - 设置管理器初始化完成
2025-07-04 15:47:37 - client_PlayerManager - INFO - [enhanced_logger.py:174] - 玩家管理器初始化完成
2025-07-04 15:47:37 - client_NewClientCore - INFO - [enhanced_logger.py:174] - 初始化房间管理器...
2025-07-04 15:47:37 - client_RoomManager - INFO - [enhanced_logger.py:174] - 房间管理器初始化完成
2025-07-04 15:47:37 - client_NewClientCore - INFO - [enhanced_logger.py:174] - 房间管理器初始化完成
2025-07-04 15:47:37 - client_NewClientCore - INFO - [enhanced_logger.py:174] - 初始化界面管理器...
2025-07-04 15:47:37 - client_InterfaceManager - INFO - [enhanced_logger.py:174] - 界面管理器初始化完成
2025-07-04 15:47:37 - client_NewClientCore - INFO - [enhanced_logger.py:174] - 界面管理器初始化完成
2025-07-04 15:47:37 - client_NewClientCore - INFO - [enhanced_logger.py:174] - 初始化网络服务...
2025-07-04 15:47:37 - client_SecurityManager - INFO - [enhanced_logger.py:174] - 安全管理器初始化完成 - 客户端模式
2025-07-04 15:47:37 - client_SecureProtocol - INFO - [enhanced_logger.py:174] - 安全协议初始化完成 - 客户端模式
2025-07-04 15:47:37 - client_SecureNetworkClient - INFO - [enhanced_logger.py:174] - 安全网络客户端初始化完成
2025-07-04 15:47:37 - client_MessageManager - INFO - [enhanced_logger.py:174] - 消息管理器初始化完成 - 客户端模式
2025-07-04 15:47:37 - client_NetworkService - INFO - [enhanced_logger.py:174] - 网络服务初始化完成
2025-07-04 15:47:37 - client_NewClientCore - INFO - [enhanced_logger.py:174] - 网络服务初始化完成
2025-07-04 15:47:37 - client_NewClientCore - INFO - [enhanced_logger.py:174] - 设置连接回调...
2025-07-04 15:47:37 - client_NewClientCore - INFO - [enhanced_logger.py:174] - 连接回调设置完成
2025-07-04 15:47:37 - client_NewClientCore - INFO - [enhanced_logger.py:174] - 新客户端核心初始化完成 / New client core initialized
2025-07-04 15:47:37 - client_ClientMain - INFO - [enhanced_logger.py:174] - 客户端初始化完成，开始运行...
2025-07-04 15:47:37 - client_NewClientCore - INFO - [enhanced_logger.py:174] - 开始启动新客户端 / Starting new client
2025-07-04 15:47:37 - client_NewClientCore - INFO - [enhanced_logger.py:174] - 客户端版本: 3.0 / Client version: 3.0
2025-07-04 15:47:37 - client_NewClientCore - INFO - [enhanced_logger.py:174] - Python版本: 3.11.5 / Python version: 3.11.5
2025-07-04 15:47:37 - client_NewClientCore - INFO - [enhanced_logger.py:174] - 操作系统: Windows 10 / OS: Windows 10
2025-07-04 15:47:37 - client_NewClientCore - INFO - [enhanced_logger.py:174] - 新客户端启动完成，耗时: 0.03秒 / New client startup complete, time taken: 0.03s
2025-07-04 15:47:37 - client_NewClientCore - INFO - [enhanced_logger.py:174] - 主循环开始运行 / Main loop started
2025-07-04 15:47:41 - client_NetworkService - INFO - [enhanced_logger.py:174] - 开始连接到服务器 localhost:8888
2025-07-04 15:47:41 - client_SecureNetworkClient - INFO - [enhanced_logger.py:174] - 开始连接到服务器 localhost:8888
2025-07-04 15:47:41 - client_SecureNetworkClient - INFO - [enhanced_logger.py:174] - TCP连接建立成功，开始安全握手...
2025-07-04 15:47:41 - client_SecureNetworkClient - INFO - [enhanced_logger.py:174] - 等待服务器握手挑战...
2025-07-04 15:47:41 - client_SecurityManager - INFO - [enhanced_logger.py:174] - 客户端密钥设置完成
2025-07-04 15:47:41 - client_SecureProtocol - INFO - [enhanced_logger.py:174] - 握手响应已发送
2025-07-04 15:47:41 - client_SecureNetworkClient - INFO - [enhanced_logger.py:174] - 等待握手完成确认...
2025-07-04 15:47:41 - client_SecurityManager - INFO - [enhanced_logger.py:174] - 客户端密钥设置完成
2025-07-04 15:47:41 - client_SecureProtocol - INFO - [enhanced_logger.py:174] - 客户端握手完成，已设置真正的服务器密钥
2025-07-04 15:47:41 - client_SecureNetworkClient - INFO - [enhanced_logger.py:174] - 安全握手完成，会话ID: 7K8XkSwfBSvWkPWM5LT7ioRPPvsMufCM1pKNcGVq7Is
2025-07-04 15:47:41 - client_ConnectionManager - INFO - [enhanced_logger.py:174] - 连接已建立: localhost:8888
2025-07-04 15:47:41 - client_NewClientCore - INFO - [enhanced_logger.py:174] - 连接成功回调: localhost:8888
2025-07-04 15:47:41 - client_SecureNetworkClient - INFO - [enhanced_logger.py:174] - 接收循环开始
2025-07-04 15:47:41 - client_MessageHandler - INFO - [enhanced_logger.py:174] - 收到服务器欢迎消息: 安全世界大战游戏服务器 / Secure World War Game Server v3.0
2025-07-04 15:47:41 - client_SecureNetworkClient - INFO - [enhanced_logger.py:174] - 成功连接到服务器 localhost:8888
2025-07-04 15:47:41 - client_NetworkService - INFO - [enhanced_logger.py:174] - 服务器连接成功 - 耗时: 0.04秒
2025-07-04 15:47:41 - client_SettingsManager - INFO - [enhanced_logger.py:174] - 设置已保存到文件
2025-07-04 15:47:41 - client_SettingsManager - INFO - [enhanced_logger.py:174] - 设置已更新: server_address = localhost
2025-07-04 15:47:41 - client_SettingsManager - INFO - [enhanced_logger.py:174] - 设置已保存到文件
2025-07-04 15:47:41 - client_SettingsManager - INFO - [enhanced_logger.py:174] - 设置已更新: server_port = 8888
2025-07-04 15:47:41 - client_SettingsManager - INFO - [enhanced_logger.py:174] - 设置已保存到文件
2025-07-04 15:47:42 - client_NewClientCore - INFO - [enhanced_logger.py:174] - 用户尝试加入游戏: sweatent
2025-07-04 15:47:42 - client_NetworkService - INFO - [enhanced_logger.py:174] - 开始加入游戏，玩家名称: sweatent
2025-07-04 15:47:42 - client_NetworkService - INFO - [enhanced_logger.py:174] - 玩家 sweatent 成功加入游戏 - 耗时: 0.10秒
2025-07-04 15:47:42 - client_NewClientCore - INFO - [enhanced_logger.py:174] - 用户成功加入游戏: sweatent
2025-07-04 15:47:42 - client_PlayerManager - INFO - [enhanced_logger.py:174] - 玩家信息已设置: sweatent
2025-07-04 15:47:42 - client_PlayerManager - INFO - [enhanced_logger.py:174] - 玩家认证成功: sweatent
2025-07-04 15:47:42 - client_SettingsManager - INFO - [enhanced_logger.py:174] - 设置已保存到文件
2025-07-04 15:47:42 - client_SettingsManager - INFO - [enhanced_logger.py:174] - 设置已更新: player_name = sweatent
2025-07-04 15:47:42 - client_SettingsManager - INFO - [enhanced_logger.py:174] - 设置已保存到文件
2025-07-04 15:47:45 - client_NetworkService - INFO - [enhanced_logger.py:174] - 开始创建房间: 我的房间
2025-07-04 15:47:45 - client_NetworkService - INFO - [enhanced_logger.py:174] - 房间创建成功: 我的房间 - 耗时: 0.10秒
2025-07-04 15:47:45 - client_RoomManager - INFO - [enhanced_logger.py:174] - 创建房间成功:  (ID: )
2025-07-04 15:51:26 - client_SecureProtocol - ERROR - [enhanced_logger.py:204] - 发送原始消息失败: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-07-04 15:51:26 - client_SecureNetworkClient - ERROR - [enhanced_logger.py:204] - 发送消息失败
2025-07-04 15:51:26 - client_SecureNetworkClient - WARNING - [enhanced_logger.py:194] - 发送心跳失败
