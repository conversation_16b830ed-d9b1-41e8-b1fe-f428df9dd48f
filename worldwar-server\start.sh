#!/bin/bash
# 世界大战游戏服务器启动脚本
# World War Game Server Start Script

# 设置UTF-8编码
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 显示启动信息
echo
echo "============================================================"
echo -e "${BLUE}🎮 世界大战游戏服务器 / World War Game Server${NC}"
echo "============================================================"
echo -e "${CYAN}📡 版本: v3.0${NC}"
echo -e "${CYAN}🌐 默认地址: localhost:8888${NC}"
echo -e "${CYAN}📝 日志目录: logs/server/${NC}"
echo -e "${YELLOW}🛑 按 Ctrl+C 停止服务器${NC}"
echo "============================================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo -e "${RED}❌ 错误: 未找到Python，请先安装Python 3.8+${NC}"
    echo -e "${RED}❌ Error: Python not found, please install Python 3.8+${NC}"
    exit 1
fi

# 确定Python命令
PYTHON_CMD="python3"
if ! command -v python3 &> /dev/null; then
    PYTHON_CMD="python"
fi

# 检查Python版本
PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | grep -oP '\d+\.\d+')
MAJOR_VERSION=$(echo $PYTHON_VERSION | cut -d. -f1)
MINOR_VERSION=$(echo $PYTHON_VERSION | cut -d. -f2)

if [ "$MAJOR_VERSION" -lt 3 ] || ([ "$MAJOR_VERSION" -eq 3 ] && [ "$MINOR_VERSION" -lt 8 ]); then
    echo -e "${RED}❌ 错误: Python版本过低 ($PYTHON_VERSION)，需要Python 3.8+${NC}"
    echo -e "${RED}❌ Error: Python version too old ($PYTHON_VERSION), requires Python 3.8+${NC}"
    exit 1
fi

# 检查依赖是否安装
if [ -f "requirements.txt" ]; then
    echo -e "${CYAN}📦 检查依赖包...${NC}"
    if ! $PYTHON_CMD -c "import requests" &> /dev/null; then
        echo -e "${YELLOW}📦 正在安装依赖包...${NC}"
        $PYTHON_CMD -m pip install -r requirements.txt
        if [ $? -ne 0 ]; then
            echo -e "${RED}❌ 依赖安装失败，请手动运行: $PYTHON_CMD -m pip install -r requirements.txt${NC}"
            exit 1
        fi
    fi
else
    echo -e "${YELLOW}⚠️  警告: 未找到requirements.txt文件${NC}"
    echo -e "${YELLOW}⚠️  Warning: requirements.txt not found${NC}"
fi

# 创建日志目录
mkdir -p logs/server

echo -e "${GREEN}🚀 启动服务器...${NC}"
echo

# 启动服务器
$PYTHON_CMD server_main.py "$@"

# 如果服务器异常退出，显示错误信息
EXIT_CODE=$?
if [ $EXIT_CODE -ne 0 ]; then
    echo
    echo -e "${RED}❌ 服务器异常退出，错误代码: $EXIT_CODE${NC}"
    echo -e "${CYAN}📝 请查看日志文件: logs/server/${NC}"
    echo
fi

exit $EXIT_CODE
