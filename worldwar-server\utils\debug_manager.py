#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试管理器
Debug Manager

提供调试选项和日志级别管理
Provides debug options and log level management
"""

import os
import sys
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
from datetime import datetime

class DebugManager:
    """调试管理器"""
    
    def __init__(self):
        """初始化调试管理器"""
        self.debug_enabled = False
        self.verbose_enabled = False
        self.log_level = "INFO"
        self.debug_modules = set()
        
        # 调试选项
        self.debug_options = {
            "network": False,      # 网络调试
            "database": False,     # 数据库调试
            "game_logic": False,   # 游戏逻辑调试
            "user_actions": False, # 用户操作调试
            "performance": False,  # 性能调试
            "security": False,     # 安全调试
            "ai": False,          # AI调试
            "world_gen": False    # 世界生成调试
        }
        
        # 日志级别映射
        self.log_levels = {
            "DEBUG": logging.DEBUG,
            "INFO": logging.INFO,
            "WARNING": logging.WARNING,
            "ERROR": logging.ERROR,
            "CRITICAL": logging.CRITICAL
        }
        
        # 性能监控
        self.performance_stats = {
            "start_time": datetime.now(),
            "request_count": 0,
            "error_count": 0,
            "warning_count": 0,
            "memory_usage": []
        }
        
        # 导入日志
        from shared.enhanced_logger import get_server_logger
        self.logger = get_server_logger("DebugManager")
        
        self.logger.info("调试管理器初始化完成")
    
    def enable_debug(self, modules: List[str] = None):
        """
        启用调试模式
        
        Args:
            modules: 要启用调试的模块列表
        """
        self.debug_enabled = True
        self.log_level = "DEBUG"
        
        if modules:
            for module in modules:
                if module in self.debug_options:
                    self.debug_options[module] = True
                    self.debug_modules.add(module)
        else:
            # 启用所有调试选项
            for module in self.debug_options:
                self.debug_options[module] = True
                self.debug_modules.add(module)
        
        self.logger.info(f"调试模式已启用，模块: {list(self.debug_modules)}")
    
    def disable_debug(self):
        """禁用调试模式"""
        self.debug_enabled = False
        self.log_level = "INFO"
        
        for module in self.debug_options:
            self.debug_options[module] = False
        
        self.debug_modules.clear()
        self.logger.info("调试模式已禁用")
    
    def enable_verbose(self):
        """启用详细模式"""
        self.verbose_enabled = True
        self.logger.info("详细模式已启用")
    
    def disable_verbose(self):
        """禁用详细模式"""
        self.verbose_enabled = False
        self.logger.info("详细模式已禁用")
    
    def set_log_level(self, level: str):
        """
        设置日志级别
        
        Args:
            level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        if level.upper() in self.log_levels:
            self.log_level = level.upper()
            self.logger.info(f"日志级别设置为: {self.log_level}")
        else:
            self.logger.warning(f"无效的日志级别: {level}")
    
    def is_debug_enabled(self, module: str = None) -> bool:
        """
        检查调试是否启用
        
        Args:
            module: 模块名称
            
        Returns:
            是否启用调试
        """
        if not self.debug_enabled:
            return False
        
        if module:
            return module in self.debug_modules
        
        return True
    
    def debug_log(self, module: str, message: str, level: str = "DEBUG"):
        """
        记录调试日志
        
        Args:
            module: 模块名称
            message: 日志消息
            level: 日志级别
        """
        if self.is_debug_enabled(module):
            log_message = f"[{module.upper()}] {message}"
            
            if level == "DEBUG":
                self.logger.debug(log_message)
            elif level == "INFO":
                self.logger.info(log_message)
            elif level == "WARNING":
                self.logger.warning(log_message)
                self.performance_stats["warning_count"] += 1
            elif level == "ERROR":
                self.logger.error(log_message)
                self.performance_stats["error_count"] += 1
            elif level == "CRITICAL":
                self.logger.critical(log_message)
                self.performance_stats["error_count"] += 1
    
    def log_performance(self, operation: str, duration: float, details: Dict[str, Any] = None):
        """
        记录性能信息
        
        Args:
            operation: 操作名称
            duration: 持续时间（秒）
            details: 详细信息
        """
        if self.is_debug_enabled("performance"):
            message = f"性能监控 - {operation}: {duration:.4f}秒"
            if details:
                message += f", 详情: {details}"
            
            self.debug_log("performance", message, "INFO")
    
    def log_user_action(self, user: str, action: str, details: Dict[str, Any] = None):
        """
        记录用户操作
        
        Args:
            user: 用户名
            action: 操作名称
            details: 详细信息
        """
        if self.is_debug_enabled("user_actions"):
            message = f"用户操作 - {user}: {action}"
            if details:
                message += f", 详情: {details}"
            
            self.debug_log("user_actions", message, "INFO")
    
    def log_network_activity(self, activity_type: str, details: Dict[str, Any]):
        """
        记录网络活动
        
        Args:
            activity_type: 活动类型
            details: 详细信息
        """
        if self.is_debug_enabled("network"):
            message = f"网络活动 - {activity_type}: {details}"
            self.debug_log("network", message, "DEBUG")
    
    def get_debug_status(self) -> Dict[str, Any]:
        """获取调试状态"""
        return {
            "debug_enabled": self.debug_enabled,
            "verbose_enabled": self.verbose_enabled,
            "log_level": self.log_level,
            "debug_modules": list(self.debug_modules),
            "debug_options": self.debug_options.copy(),
            "performance_stats": self.get_performance_stats()
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        uptime = datetime.now() - self.performance_stats["start_time"]
        
        return {
            "uptime_seconds": uptime.total_seconds(),
            "uptime_formatted": str(uptime),
            "request_count": self.performance_stats["request_count"],
            "error_count": self.performance_stats["error_count"],
            "warning_count": self.performance_stats["warning_count"],
            "error_rate": (
                self.performance_stats["error_count"] / max(1, self.performance_stats["request_count"])
            ) * 100
        }
    
    def increment_request_count(self):
        """增加请求计数"""
        self.performance_stats["request_count"] += 1
    
    def generate_debug_report(self) -> str:
        """生成调试报告"""
        status = self.get_debug_status()
        
        report_lines = [
            "=" * 60,
            "🔧 调试状态报告 / Debug Status Report",
            "=" * 60,
            f"调试模式 / Debug Mode: {'启用' if status['debug_enabled'] else '禁用'}",
            f"详细模式 / Verbose Mode: {'启用' if status['verbose_enabled'] else '禁用'}",
            f"日志级别 / Log Level: {status['log_level']}",
            "",
            "📊 性能统计 / Performance Stats:",
            f"  运行时间 / Uptime: {status['performance_stats']['uptime_formatted']}",
            f"  请求总数 / Total Requests: {status['performance_stats']['request_count']}",
            f"  错误总数 / Total Errors: {status['performance_stats']['error_count']}",
            f"  警告总数 / Total Warnings: {status['performance_stats']['warning_count']}",
            f"  错误率 / Error Rate: {status['performance_stats']['error_rate']:.2f}%",
            "",
            "🔍 调试模块 / Debug Modules:",
        ]
        
        for module, enabled in status['debug_options'].items():
            status_text = "启用" if enabled else "禁用"
            report_lines.append(f"  {module}: {status_text}")
        
        report_lines.append("=" * 60)
        
        return "\n".join(report_lines)
    
    def export_debug_info(self, file_path: str = None) -> str:
        """
        导出调试信息到文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            导出的文件路径
        """
        if not file_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_path = f"logs/debug_report_{timestamp}.txt"
        
        # 确保目录存在
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)
        
        report = self.generate_debug_report()
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        self.logger.info(f"调试报告已导出到: {file_path}")
        return file_path
