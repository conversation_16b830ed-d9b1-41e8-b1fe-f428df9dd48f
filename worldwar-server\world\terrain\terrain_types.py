"""
地形类型定义
Terrain Types Definition

定义各种地形类型、生物群系和相关属性。
"""

from enum import Enum
from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional
import json


class TerrainType(Enum):
    """基础地形类型"""
    WATER = "water"           # 水域
    BEACH = "beach"           # 海滩
    GRASSLAND = "grassland"   # 草原
    FOREST = "forest"         # 森林
    HILLS = "hills"           # 丘陵
    MOUNTAINS = "mountains"   # 山地
    DESERT = "desert"         # 沙漠
    TUNDRA = "tundra"         # 苔原
    SWAMP = "swamp"           # 沼泽
    SNOW = "snow"             # 雪地


class BiomeType(Enum):
    """生物群系类型"""
    OCEAN = "ocean"                    # 海洋
    COASTAL = "coastal"                # 海岸
    TROPICAL_RAINFOREST = "tropical_rainforest"  # 热带雨林
    TEMPERATE_FOREST = "temperate_forest"        # 温带森林
    BOREAL_FOREST = "boreal_forest"              # 北方针叶林
    GRASSLAND = "grassland"                      # 草原
    SAVANNA = "savanna"                          # 热带草原
    DESERT = "desert"                            # 沙漠
    TUNDRA = "tundra"                           # 苔原
    ALPINE = "alpine"                           # 高山
    WETLAND = "wetland"                         # 湿地


@dataclass
class TerrainProperties:
    """地形属性"""
    name: str                          # 地形名称
    movement_cost: float               # 移动成本
    defense_bonus: float               # 防御加成
    resource_multiplier: Dict[str, float]  # 资源产出倍数
    buildable: bool                    # 是否可建造
    color: Tuple[int, int, int]        # 显示颜色 (RGB)
    description: str                   # 描述


@dataclass
class BiomeProperties:
    """生物群系属性"""
    name: str                          # 生物群系名称
    temperature_range: Tuple[float, float]  # 温度范围 (0-1)
    precipitation_range: Tuple[float, float]  # 降水范围 (0-1)
    elevation_range: Tuple[float, float]     # 海拔范围 (0-1)
    dominant_terrain: List[TerrainType]      # 主要地形类型
    resource_abundance: Dict[str, float]     # 资源丰富度
    population_capacity: float               # 人口承载力
    color: Tuple[int, int, int]             # 显示颜色 (RGB)
    description: str                        # 描述


# 地形类型属性定义
TERRAIN_PROPERTIES: Dict[TerrainType, TerrainProperties] = {
    TerrainType.WATER: TerrainProperties(
        name="水域",
        movement_cost=2.0,
        defense_bonus=0.0,
        resource_multiplier={"fish": 2.0, "oil": 0.5},
        buildable=False,
        color=(64, 164, 223),
        description="海洋、湖泊和河流"
    ),
    
    TerrainType.BEACH: TerrainProperties(
        name="海滩",
        movement_cost=1.2,
        defense_bonus=0.1,
        resource_multiplier={"fish": 1.5, "tourism": 2.0},
        buildable=True,
        color=(238, 214, 175),
        description="沙滩和海岸线"
    ),
    
    TerrainType.GRASSLAND: TerrainProperties(
        name="草原",
        movement_cost=1.0,
        defense_bonus=0.0,
        resource_multiplier={"agriculture": 1.5, "livestock": 2.0},
        buildable=True,
        color=(124, 252, 0),
        description="开阔的草地和牧场"
    ),
    
    TerrainType.FOREST: TerrainProperties(
        name="森林",
        movement_cost=1.5,
        defense_bonus=0.3,
        resource_multiplier={"wood": 3.0, "wildlife": 2.0, "oxygen": 2.0},
        buildable=True,
        color=(34, 139, 34),
        description="茂密的森林和林地"
    ),
    
    TerrainType.HILLS: TerrainProperties(
        name="丘陵",
        movement_cost=1.3,
        defense_bonus=0.2,
        resource_multiplier={"stone": 2.0, "minerals": 1.5},
        buildable=True,
        color=(107, 142, 35),
        description="起伏的丘陵地带"
    ),
    
    TerrainType.MOUNTAINS: TerrainProperties(
        name="山地",
        movement_cost=2.0,
        defense_bonus=0.5,
        resource_multiplier={"minerals": 3.0, "stone": 2.5, "rare_metals": 2.0},
        buildable=False,
        color=(139, 69, 19),
        description="高山和山脉"
    ),
    
    TerrainType.DESERT: TerrainProperties(
        name="沙漠",
        movement_cost=1.8,
        defense_bonus=0.1,
        resource_multiplier={"oil": 2.0, "solar": 3.0, "rare_minerals": 1.5},
        buildable=True,
        color=(238, 203, 173),
        description="干旱的沙漠地区"
    ),
    
    TerrainType.TUNDRA: TerrainProperties(
        name="苔原",
        movement_cost=1.6,
        defense_bonus=0.2,
        resource_multiplier={"oil": 1.5, "gas": 2.0, "wildlife": 1.2},
        buildable=True,
        color=(176, 196, 222),
        description="寒冷的苔原地带"
    ),
    
    TerrainType.SWAMP: TerrainProperties(
        name="沼泽",
        movement_cost=2.5,
        defense_bonus=0.4,
        resource_multiplier={"gas": 1.8, "wildlife": 1.5, "water": 2.0},
        buildable=False,
        color=(107, 142, 35),
        description="湿润的沼泽地"
    ),
    
    TerrainType.SNOW: TerrainProperties(
        name="雪地",
        movement_cost=2.2,
        defense_bonus=0.3,
        resource_multiplier={"water": 2.0, "hydroelectric": 1.5},
        buildable=True,
        color=(255, 250, 250),
        description="常年积雪的地区"
    )
}


# 生物群系属性定义
BIOME_PROPERTIES: Dict[BiomeType, BiomeProperties] = {
    BiomeType.OCEAN: BiomeProperties(
        name="海洋",
        temperature_range=(0.3, 0.8),
        precipitation_range=(0.8, 1.0),
        elevation_range=(0.0, 0.3),
        dominant_terrain=[TerrainType.WATER],
        resource_abundance={
            "fish": 3.0,
            "oil": 1.5,
            "salt": 2.0,
            "shipping": 2.5
        },
        population_capacity=0.1,
        color=(25, 25, 112),
        description="深海和浅海区域"
    ),
    
    BiomeType.COASTAL: BiomeProperties(
        name="海岸",
        temperature_range=(0.4, 0.7),
        precipitation_range=(0.5, 0.9),
        elevation_range=(0.25, 0.4),
        dominant_terrain=[TerrainType.BEACH, TerrainType.GRASSLAND],
        resource_abundance={
            "fish": 2.5,
            "tourism": 3.0,
            "trade": 2.0,
            "agriculture": 1.2
        },
        population_capacity=1.5,
        color=(135, 206, 235),
        description="海岸线和近海区域"
    ),
    
    BiomeType.TROPICAL_RAINFOREST: BiomeProperties(
        name="热带雨林",
        temperature_range=(0.7, 1.0),
        precipitation_range=(0.8, 1.0),
        elevation_range=(0.3, 0.6),
        dominant_terrain=[TerrainType.FOREST],
        resource_abundance={
            "wood": 4.0,
            "biodiversity": 5.0,
            "medicine": 3.0,
            "oxygen": 4.0,
            "carbon_storage": 4.0
        },
        population_capacity=0.8,
        color=(0, 100, 0),
        description="茂密的热带雨林"
    ),
    
    BiomeType.TEMPERATE_FOREST: BiomeProperties(
        name="温带森林",
        temperature_range=(0.4, 0.7),
        precipitation_range=(0.5, 0.8),
        elevation_range=(0.3, 0.7),
        dominant_terrain=[TerrainType.FOREST, TerrainType.HILLS],
        resource_abundance={
            "wood": 3.0,
            "wildlife": 2.5,
            "recreation": 2.0,
            "water": 1.8,
            "oxygen": 2.5
        },
        population_capacity=1.2,
        color=(34, 139, 34),
        description="温带落叶林和混交林"
    ),
    
    BiomeType.BOREAL_FOREST: BiomeProperties(
        name="北方针叶林",
        temperature_range=(0.1, 0.5),
        precipitation_range=(0.4, 0.7),
        elevation_range=(0.3, 0.6),
        dominant_terrain=[TerrainType.FOREST, TerrainType.TUNDRA],
        resource_abundance={
            "wood": 3.5,
            "paper": 3.0,
            "wildlife": 2.0,
            "carbon_storage": 3.0
        },
        population_capacity=0.6,
        color=(0, 128, 0),
        description="北方针叶林带"
    ),
    
    BiomeType.GRASSLAND: BiomeProperties(
        name="草原",
        temperature_range=(0.3, 0.8),
        precipitation_range=(0.3, 0.7),
        elevation_range=(0.3, 0.5),
        dominant_terrain=[TerrainType.GRASSLAND],
        resource_abundance={
            "agriculture": 3.0,
            "livestock": 3.5,
            "wind": 2.5,
            "biofuel": 2.0
        },
        population_capacity=2.0,
        color=(154, 205, 50),
        description="温带草原和大草原"
    ),
    
    BiomeType.SAVANNA: BiomeProperties(
        name="热带草原",
        temperature_range=(0.6, 0.9),
        precipitation_range=(0.2, 0.6),
        elevation_range=(0.3, 0.5),
        dominant_terrain=[TerrainType.GRASSLAND, TerrainType.FOREST],
        resource_abundance={
            "livestock": 2.5,
            "wildlife": 3.0,
            "solar": 2.5,
            "tourism": 2.0
        },
        population_capacity=1.0,
        color=(255, 215, 0),
        description="热带稀树草原"
    ),
    
    BiomeType.DESERT: BiomeProperties(
        name="沙漠",
        temperature_range=(0.5, 1.0),
        precipitation_range=(0.0, 0.3),
        elevation_range=(0.3, 0.8),
        dominant_terrain=[TerrainType.DESERT],
        resource_abundance={
            "oil": 2.5,
            "solar": 4.0,
            "minerals": 2.0,
            "rare_minerals": 2.5
        },
        population_capacity=0.3,
        color=(238, 203, 173),
        description="干旱沙漠地区"
    ),
    
    BiomeType.TUNDRA: BiomeProperties(
        name="苔原",
        temperature_range=(0.0, 0.3),
        precipitation_range=(0.2, 0.5),
        elevation_range=(0.3, 0.6),
        dominant_terrain=[TerrainType.TUNDRA, TerrainType.SNOW],
        resource_abundance={
            "oil": 2.0,
            "gas": 3.0,
            "wildlife": 1.5,
            "wind": 2.0
        },
        population_capacity=0.2,
        color=(176, 196, 222),
        description="北极苔原"
    ),
    
    BiomeType.ALPINE: BiomeProperties(
        name="高山",
        temperature_range=(0.0, 0.4),
        precipitation_range=(0.4, 0.9),
        elevation_range=(0.7, 1.0),
        dominant_terrain=[TerrainType.MOUNTAINS, TerrainType.SNOW],
        resource_abundance={
            "minerals": 4.0,
            "rare_metals": 3.0,
            "hydroelectric": 3.0,
            "tourism": 2.5,
            "water": 2.0
        },
        population_capacity=0.4,
        color=(105, 105, 105),
        description="高山地区"
    ),
    
    BiomeType.WETLAND: BiomeProperties(
        name="湿地",
        temperature_range=(0.3, 0.8),
        precipitation_range=(0.7, 1.0),
        elevation_range=(0.25, 0.4),
        dominant_terrain=[TerrainType.SWAMP, TerrainType.WATER],
        resource_abundance={
            "water": 3.0,
            "biodiversity": 3.5,
            "fish": 2.0,
            "carbon_storage": 2.5,
            "flood_control": 3.0
        },
        population_capacity=0.5,
        color=(72, 61, 139),
        description="湿地和沼泽"
    )
}


def get_terrain_type_by_conditions(elevation: float, temperature: float, 
                                 precipitation: float) -> TerrainType:
    """
    根据环境条件确定地形类型
    
    Args:
        elevation: 海拔高度 (0-1)
        temperature: 温度 (0-1)
        precipitation: 降水量 (0-1)
        
    Returns:
        TerrainType: 对应的地形类型
    """
    # 水域
    if elevation < 0.3:
        return TerrainType.WATER
    
    # 海滩
    if elevation < 0.35:
        return TerrainType.BEACH
    
    # 高山
    if elevation > 0.8:
        if temperature < 0.3:
            return TerrainType.SNOW
        else:
            return TerrainType.MOUNTAINS
    
    # 根据温度和降水确定其他地形
    if temperature < 0.2:
        return TerrainType.TUNDRA
    elif temperature > 0.8 and precipitation < 0.3:
        return TerrainType.DESERT
    elif precipitation > 0.8 and temperature > 0.6:
        return TerrainType.SWAMP
    elif precipitation > 0.6:
        return TerrainType.FOREST
    elif elevation > 0.6:
        return TerrainType.HILLS
    else:
        return TerrainType.GRASSLAND


def get_biome_type_by_conditions(elevation: float, temperature: float, 
                               precipitation: float) -> BiomeType:
    """
    根据环境条件确定生物群系类型
    
    Args:
        elevation: 海拔高度 (0-1)
        temperature: 温度 (0-1)
        precipitation: 降水量 (0-1)
        
    Returns:
        BiomeType: 对应的生物群系类型
    """
    # 水域相关
    if elevation < 0.25:
        return BiomeType.OCEAN
    elif elevation < 0.35:
        return BiomeType.COASTAL
    
    # 高山
    if elevation > 0.7:
        return BiomeType.ALPINE
    
    # 湿地
    if precipitation > 0.8 and elevation < 0.4:
        return BiomeType.WETLAND
    
    # 根据温度和降水确定生物群系
    if temperature < 0.3:
        return BiomeType.TUNDRA
    elif temperature > 0.7:
        if precipitation > 0.8:
            return BiomeType.TROPICAL_RAINFOREST
        elif precipitation < 0.3:
            return BiomeType.DESERT
        else:
            return BiomeType.SAVANNA
    else:  # 温带
        if precipitation > 0.6:
            if temperature < 0.5:
                return BiomeType.BOREAL_FOREST
            else:
                return BiomeType.TEMPERATE_FOREST
        elif precipitation < 0.3:
            return BiomeType.DESERT
        else:
            return BiomeType.GRASSLAND


def get_terrain_color(terrain_type: TerrainType) -> Tuple[int, int, int]:
    """获取地形类型的显示颜色"""
    return TERRAIN_PROPERTIES[terrain_type].color


def get_biome_color(biome_type: BiomeType) -> Tuple[int, int, int]:
    """获取生物群系的显示颜色"""
    return BIOME_PROPERTIES[biome_type].color


def export_terrain_data_to_json(filename: str):
    """
    将地形数据导出为JSON文件
    
    Args:
        filename: 输出文件名
    """
    data = {
        "terrain_types": {
            terrain.value: {
                "name": props.name,
                "movement_cost": props.movement_cost,
                "defense_bonus": props.defense_bonus,
                "resource_multiplier": props.resource_multiplier,
                "buildable": props.buildable,
                "color": props.color,
                "description": props.description
            }
            for terrain, props in TERRAIN_PROPERTIES.items()
        },
        "biome_types": {
            biome.value: {
                "name": props.name,
                "temperature_range": props.temperature_range,
                "precipitation_range": props.precipitation_range,
                "elevation_range": props.elevation_range,
                "dominant_terrain": [t.value for t in props.dominant_terrain],
                "resource_abundance": props.resource_abundance,
                "population_capacity": props.population_capacity,
                "color": props.color,
                "description": props.description
            }
            for biome, props in BIOME_PROPERTIES.items()
        }
    }
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)


if __name__ == "__main__":
    # 导出地形数据
    export_terrain_data_to_json("terrain_data.json")
    print("地形数据已导出到 terrain_data.json")