#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统验证脚本 - 验证世界大战游戏改进功能
System Verification Script - Verify World War Game Improvements
"""

import sys
import os
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "worldwar-server"))
sys.path.insert(0, str(project_root / "worldwar-client"))

def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f"🔍 {title}")
    print("="*60)

def print_section(title):
    """打印章节"""
    print(f"\n📋 {title}")
    print("-" * 40)

def verify_server_components():
    """验证服务器组件"""
    print_section("验证服务器组件")
    
    components = [
        ("增强房间管理器", "server.enhanced_room_manager", "EnhancedRoomManager"),
        ("玩家会话管理", "server.player_session", "SessionManager"),
        ("消息处理器", "server.message_processor", "MessageProcessor"),
        ("安全协议", "shared.secure_protocol", "SecureProtocol"),
        ("消息类型", "shared.message_types", "MessageType"),
    ]
    
    results = []
    
    for name, module_path, class_name in components:
        try:
            module = __import__(module_path, fromlist=[class_name])
            cls = getattr(module, class_name)
            print(f"✅ {name}: 导入成功")
            results.append(True)
        except Exception as e:
            print(f"❌ {name}: 导入失败 - {e}")
            results.append(False)
    
    return all(results)

def verify_client_components():
    """验证客户端组件"""
    print_section("验证客户端组件")
    
    components = [
        ("增强网络客户端", "client.enhanced_network_client", "EnhancedNetworkClient"),
        ("房间UI管理器", "client.room_ui", "RoomUI"),
    ]
    
    results = []
    
    for name, module_path, class_name in components:
        try:
            module = __import__(module_path, fromlist=[class_name])
            cls = getattr(module, class_name)
            print(f"✅ {name}: 导入成功")
            results.append(True)
        except Exception as e:
            print(f"❌ {name}: 导入失败 - {e}")
            results.append(False)
    
    return all(results)

def verify_room_management():
    """验证房间管理功能"""
    print_section("验证房间管理功能")
    
    try:
        from server.enhanced_room_manager import EnhancedRoomManager
        from server.player_session import SessionManager
        
        # 创建管理器
        room_manager = EnhancedRoomManager()
        session_manager = SessionManager()
        
        print("✅ 管理器创建成功")
        
        # 创建模拟会话
        class MockSocket:
            def close(self): pass
            def send(self, data): pass
        
        session = session_manager.create_session(
            "verify_session", MockSocket(), ("127.0.0.1", 12345)
        )
        
        # 认证会话
        auth_result = session_manager.authenticate_session(session.session_id, "勇士999")
        if auth_result["success"]:
            print("✅ 会话认证成功")
        else:
            print(f"❌ 会话认证失败: {auth_result['message']}")
            return False
        
        # 创建房间
        room = room_manager.create_room_safe(
            "验证房间", session.session_id, 4, "ai_generated"
        )
        
        if room:
            print("✅ 房间创建成功")
        else:
            print("❌ 房间创建失败")
            return False
        
        # 加入房间
        join_result = room_manager.join_room_safe(room.room_id, session)
        if join_result["success"]:
            print("✅ 房间加入成功")
        else:
            print(f"❌ 房间加入失败: {join_result['message']}")
            return False
        
        # 获取房间信息
        room_info = room_manager.get_room_waiting_info(room.room_id)
        if room_info:
            print("✅ 房间信息获取成功")
        else:
            print("❌ 房间信息获取失败")
            return False
        
        print("🎉 房间管理功能验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 房间管理功能验证失败: {e}")
        return False

def verify_ui_components():
    """验证UI组件"""
    print_section("验证UI组件")
    
    try:
        from client.room_ui import RoomUI, UIState
        
        # 创建房间UI
        room_ui = RoomUI()
        print("✅ 房间UI创建成功")
        
        # 测试房间列表显示
        test_rooms = [
            {
                "room_id": "test_room",
                "room_name": "验证房间",
                "status": "waiting",
                "max_players": 4,
                "current_players": 1,
                "game_mode": "ai_generated",
                "difficulty": "normal",
                "is_private": False
            }
        ]
        
        room_ui.show_room_list(test_rooms)
        if room_ui.get_current_state() == UIState.ROOM_LIST:
            print("✅ 房间列表显示成功")
        else:
            print("❌ 房间列表显示失败")
            return False
        
        # 测试UI渲染
        rendered = room_ui.render_current_ui()
        if "验证房间" in rendered:
            print("✅ UI渲染成功")
        else:
            print("❌ UI渲染失败")
            return False
        
        print("🎉 UI组件验证通过")
        return True
        
    except Exception as e:
        print(f"❌ UI组件验证失败: {e}")
        return False

def verify_network_resilience():
    """验证网络弹性"""
    print_section("验证网络弹性")
    
    try:
        from client.enhanced_network_client import EnhancedNetworkClient, ConnectionState
        
        # 创建网络客户端
        client = EnhancedNetworkClient("localhost", 8888)
        print("✅ 网络客户端创建成功")
        
        # 验证初始状态
        if client.get_connection_state() == ConnectionState.DISCONNECTED:
            print("✅ 初始状态正确")
        else:
            print("❌ 初始状态错误")
            return False
        
        # 验证消息队列
        queue = client.message_queue
        test_msg = {"type": "test", "data": {}}
        queue.add_message(test_msg)
        
        received = queue.get_message(timeout=0.1)
        if received == test_msg:
            print("✅ 消息队列功能正常")
        else:
            print("❌ 消息队列功能异常")
            return False
        
        # 验证错误处理配置
        if hasattr(client, 'auto_reconnect') and hasattr(client, 'max_reconnect_attempts'):
            print("✅ 错误处理配置正常")
        else:
            print("❌ 错误处理配置缺失")
            return False
        
        print("🎉 网络弹性验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 网络弹性验证失败: {e}")
        return False

def verify_message_system():
    """验证消息系统"""
    print_section("验证消息系统")
    
    try:
        from shared.message_types import MessageFactory, MessageType, MessageValidator
        
        # 创建测试消息
        message = MessageFactory.create_message(
            MessageType.CREATE_ROOM,
            {"room_name": "验证房间", "max_players": 4}
        )
        
        print("✅ 消息创建成功")
        
        # 验证消息
        is_valid, error = MessageValidator.validate_message(message)
        if is_valid:
            print("✅ 消息验证通过")
        else:
            print(f"❌ 消息验证失败: {error}")
            return False
        
        # 测试消息序列化
        json_str = message.to_json()
        if json_str and "create_room" in json_str:
            print("✅ 消息序列化成功")
        else:
            print("❌ 消息序列化失败")
            return False
        
        print("🎉 消息系统验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 消息系统验证失败: {e}")
        return False

def verify_file_structure():
    """验证文件结构"""
    print_section("验证文件结构")
    
    required_files = [
        "worldwar-server/server/enhanced_room_manager.py",
        "worldwar-client/client/room_ui.py",
        "worldwar-client/client/enhanced_network_client.py",
        "worldwar-client/enhanced_client_main.py",
        "tests/test_room_management_simple.py",
        "tests/test_network_resilience.py",
        "tools/verify_setup.py"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - 文件不存在")
            missing_files.append(file_path)
    
    if not missing_files:
        print("🎉 文件结构验证通过")
        return True
    else:
        print(f"❌ 缺少 {len(missing_files)} 个文件")
        return False

def main():
    """主验证函数"""
    print_header("世界大战游戏改进功能验证")
    
    print("🚀 开始系统验证...")
    print(f"📁 项目根目录: {project_root}")
    
    # 执行各项验证
    verifications = [
        ("文件结构", verify_file_structure),
        ("服务器组件", verify_server_components),
        ("客户端组件", verify_client_components),
        ("房间管理功能", verify_room_management),
        ("UI组件", verify_ui_components),
        ("网络弹性", verify_network_resilience),
        ("消息系统", verify_message_system),
    ]
    
    results = []
    
    for name, verify_func in verifications:
        try:
            result = verify_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name}验证发生异常: {e}")
            results.append((name, False))
    
    # 汇总结果
    print_header("验证结果汇总")
    
    passed_count = 0
    total_count = len(results)
    
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {name:<15}: {status}")
        if result:
            passed_count += 1
    
    print(f"\n📊 验证统计: {passed_count}/{total_count} 项通过")
    
    if passed_count == total_count:
        print("\n🎉 所有验证通过！")
        print("🔧 世界大战游戏改进功能已成功实现")
        print("\n✨ 主要改进包括:")
        print("   • 修复了房间创建和管理功能")
        print("   • 实现了客户端UI增强")
        print("   • 添加了网络重连和错误处理")
        print("   • 提供了完整的房间等待界面")
        print("   • 增强了消息处理和验证")
        print("   • 改进了连接状态监控")
        
        print("\n🚀 使用方法:")
        print("   服务器: python worldwar-server/server_main.py")
        print("   客户端: python worldwar-client/enhanced_client_main.py")
        
        return True
    else:
        print(f"\n❌ {total_count - passed_count} 项验证失败")
        print("🔧 请检查失败的组件并进行修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)