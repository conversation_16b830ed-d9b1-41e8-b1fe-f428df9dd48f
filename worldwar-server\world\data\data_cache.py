"""
数据缓存系统 - 支持内存和磁盘缓存，提供过期管理和性能监控
"""

import json
import time
import hashlib
import asyncio
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    data: Any
    created_at: float
    expires_at: float
    access_count: int = 0
    last_accessed: float = 0
    size_bytes: int = 0


@dataclass
class CacheStats:
    """缓存统计信息"""
    total_entries: int = 0
    memory_entries: int = 0
    disk_entries: int = 0
    total_size_bytes: int = 0
    hit_count: int = 0
    miss_count: int = 0
    eviction_count: int = 0
    cleanup_count: int = 0
    
    @property
    def hit_rate(self) -> float:
        """缓存命中率"""
        total = self.hit_count + self.miss_count
        return self.hit_count / total if total > 0 else 0.0


class DataCache:
    """数据缓存系统 - 支持内存和磁盘双层缓存"""
    
    def __init__(self, cache_dir: Path = None, default_ttl: int = 86400, 
                 max_memory_entries: int = 1000, max_disk_size_mb: int = 100):
        """
        初始化缓存系统
        
        Args:
            cache_dir: 磁盘缓存目录
            default_ttl: 默认缓存有效期（秒）
            max_memory_entries: 内存缓存最大条目数
            max_disk_size_mb: 磁盘缓存最大大小（MB）
        """
        self.cache_dir = cache_dir or Path("cache")
        self.default_ttl = default_ttl
        self.max_memory_entries = max_memory_entries
        self.max_disk_size_bytes = max_disk_size_mb * 1024 * 1024
        
        # 内存缓存
        self.memory_cache: Dict[str, CacheEntry] = {}
        
        # 缓存统计
        self.stats = CacheStats()
        
        # 日志
        self.logger = logging.getLogger(__name__)
        
        # 确保缓存目录存在
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 启动清理任务
        self._cleanup_task = None
        self._start_cleanup_task()
    
    def _start_cleanup_task(self):
        """启动定期清理任务"""
        async def cleanup_loop():
            while True:
                try:
                    await asyncio.sleep(3600)  # 每小时清理一次
                    await self.cleanup_expired()
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    self.logger.error(f"缓存清理任务错误: {str(e)}")
        
        self._cleanup_task = asyncio.create_task(cleanup_loop())
    
    def _generate_cache_key(self, key: str) -> str:
        """生成缓存键的哈希值"""
        return hashlib.md5(key.encode('utf-8')).hexdigest()
    
    def _get_disk_path(self, cache_key: str) -> Path:
        """获取磁盘缓存文件路径"""
        return self.cache_dir / f"{cache_key}.json"
    
    def _calculate_size(self, data: Any) -> int:
        """计算数据大小（字节）"""
        try:
            return len(json.dumps(data, ensure_ascii=False).encode('utf-8'))
        except (TypeError, ValueError):
            return len(str(data).encode('utf-8'))
    
    async def get(self, key: str) -> Optional[Any]:
        """
        获取缓存数据
        
        Args:
            key: 缓存键
        
        Returns:
            缓存的数据，如果不存在或已过期则返回None
        """
        cache_key = self._generate_cache_key(key)
        current_time = time.time()
        
        # 首先检查内存缓存
        if cache_key in self.memory_cache:
            entry = self.memory_cache[cache_key]
            
            # 检查是否过期
            if current_time <= entry.expires_at:
                # 更新访问统计
                entry.access_count += 1
                entry.last_accessed = current_time
                self.stats.hit_count += 1
                
                self.logger.debug(f"内存缓存命中: {key}")
                return entry.data
            else:
                # 过期，从内存中删除
                del self.memory_cache[cache_key]
                self.logger.debug(f"内存缓存过期: {key}")
        
        # 检查磁盘缓存
        disk_path = self._get_disk_path(cache_key)
        if disk_path.exists():
            try:
                with open(disk_path, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                
                # 检查是否过期
                if current_time <= cache_data['expires_at']:
                    data = cache_data['data']
                    
                    # 将数据加载到内存缓存（如果有空间）
                    if len(self.memory_cache) < self.max_memory_entries:
                        entry = CacheEntry(
                            key=key,
                            data=data,
                            created_at=cache_data['created_at'],
                            expires_at=cache_data['expires_at'],
                            access_count=cache_data.get('access_count', 0) + 1,
                            last_accessed=current_time,
                            size_bytes=self._calculate_size(data)
                        )
                        self.memory_cache[cache_key] = entry
                    
                    self.stats.hit_count += 1
                    self.logger.debug(f"磁盘缓存命中: {key}")
                    return data
                else:
                    # 过期，删除磁盘文件
                    disk_path.unlink()
                    self.logger.debug(f"磁盘缓存过期: {key}")
            
            except (json.JSONDecodeError, KeyError, IOError) as e:
                self.logger.warning(f"读取磁盘缓存失败: {key} - {str(e)}")
                # 删除损坏的缓存文件
                try:
                    disk_path.unlink()
                except:
                    pass
        
        # 缓存未命中
        self.stats.miss_count += 1
        self.logger.debug(f"缓存未命中: {key}")
        return None
    
    async def set(self, key: str, data: Any, ttl: int = None) -> bool:
        """
        设置缓存数据
        
        Args:
            key: 缓存键
            data: 要缓存的数据
            ttl: 缓存有效期（秒），None使用默认值
        
        Returns:
            是否设置成功
        """
        if ttl is None:
            ttl = self.default_ttl
        
        cache_key = self._generate_cache_key(key)
        current_time = time.time()
        expires_at = current_time + ttl
        data_size = self._calculate_size(data)
        
        # 创建缓存条目
        entry = CacheEntry(
            key=key,
            data=data,
            created_at=current_time,
            expires_at=expires_at,
            access_count=0,
            last_accessed=current_time,
            size_bytes=data_size
        )
        
        try:
            # 设置内存缓存
            if len(self.memory_cache) >= self.max_memory_entries:
                # 内存缓存已满，使用LRU策略清理
                await self._evict_lru_memory()
            
            self.memory_cache[cache_key] = entry
            
            # 设置磁盘缓存
            await self._save_to_disk(cache_key, entry)
            
            self.logger.debug(f"缓存设置成功: {key} (TTL: {ttl}s)")
            return True
        
        except Exception as e:
            self.logger.error(f"设置缓存失败: {key} - {str(e)}")
            return False
    
    async def _save_to_disk(self, cache_key: str, entry: CacheEntry):
        """保存数据到磁盘缓存"""
        disk_path = self._get_disk_path(cache_key)
        
        # 检查磁盘空间
        await self._check_disk_space()
        
        cache_data = {
            'key': entry.key,
            'data': entry.data,
            'created_at': entry.created_at,
            'expires_at': entry.expires_at,
            'access_count': entry.access_count,
            'last_accessed': entry.last_accessed,
            'size_bytes': entry.size_bytes
        }
        
        try:
            with open(disk_path, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存磁盘缓存失败: {entry.key} - {str(e)}")
    
    async def _check_disk_space(self):
        """检查并管理磁盘缓存空间"""
        total_size = 0
        cache_files = []
        
        # 统计所有缓存文件
        for cache_file in self.cache_dir.glob("*.json"):
            try:
                stat = cache_file.stat()
                total_size += stat.st_size
                cache_files.append((cache_file, stat.st_mtime, stat.st_size))
            except OSError:
                continue
        
        # 如果超过限制，删除最旧的文件
        if total_size > self.max_disk_size_bytes:
            # 按修改时间排序
            cache_files.sort(key=lambda x: x[1])
            
            for cache_file, _, file_size in cache_files:
                try:
                    cache_file.unlink()
                    total_size -= file_size
                    self.stats.eviction_count += 1
                    
                    if total_size <= self.max_disk_size_bytes * 0.8:  # 清理到80%
                        break
                except OSError:
                    continue
    
    async def _evict_lru_memory(self):
        """使用LRU策略清理内存缓存"""
        if not self.memory_cache:
            return
        
        # 找到最少使用的条目
        lru_key = min(self.memory_cache.keys(), 
                     key=lambda k: self.memory_cache[k].last_accessed)
        
        del self.memory_cache[lru_key]
        self.stats.eviction_count += 1
        self.logger.debug(f"LRU清理内存缓存: {lru_key}")
    
    async def delete(self, key: str) -> bool:
        """
        删除缓存数据
        
        Args:
            key: 缓存键
        
        Returns:
            是否删除成功
        """
        cache_key = self._generate_cache_key(key)
        deleted = False
        
        # 删除内存缓存
        if cache_key in self.memory_cache:
            del self.memory_cache[cache_key]
            deleted = True
        
        # 删除磁盘缓存
        disk_path = self._get_disk_path(cache_key)
        if disk_path.exists():
            try:
                disk_path.unlink()
                deleted = True
            except OSError as e:
                self.logger.error(f"删除磁盘缓存失败: {key} - {str(e)}")
        
        if deleted:
            self.logger.debug(f"缓存删除成功: {key}")
        
        return deleted
    
    async def clear(self):
        """清空所有缓存"""
        # 清空内存缓存
        self.memory_cache.clear()
        
        # 清空磁盘缓存
        for cache_file in self.cache_dir.glob("*.json"):
            try:
                cache_file.unlink()
            except OSError:
                continue
        
        # 重置统计
        self.stats = CacheStats()
        self.logger.info("所有缓存已清空")
    
    async def cleanup_expired(self):
        """清理过期的缓存条目"""
        current_time = time.time()
        cleanup_count = 0
        
        # 清理内存缓存
        expired_keys = []
        for cache_key, entry in self.memory_cache.items():
            if current_time > entry.expires_at:
                expired_keys.append(cache_key)
        
        for cache_key in expired_keys:
            del self.memory_cache[cache_key]
            cleanup_count += 1
        
        # 清理磁盘缓存
        for cache_file in self.cache_dir.glob("*.json"):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                
                if current_time > cache_data.get('expires_at', 0):
                    cache_file.unlink()
                    cleanup_count += 1
            
            except (json.JSONDecodeError, KeyError, IOError):
                # 删除损坏的文件
                try:
                    cache_file.unlink()
                    cleanup_count += 1
                except:
                    pass
        
        self.stats.cleanup_count += cleanup_count
        if cleanup_count > 0:
            self.logger.info(f"清理了 {cleanup_count} 个过期缓存条目")
    
    def is_expired(self, timestamp: float) -> bool:
        """检查时间戳是否过期"""
        return time.time() > timestamp
    
    def get_stats(self) -> CacheStats:
        """获取缓存统计信息"""
        # 更新统计信息
        self.stats.total_entries = len(self.memory_cache)
        self.stats.memory_entries = len(self.memory_cache)
        
        # 统计磁盘缓存
        disk_count = 0
        total_size = 0
        
        for cache_file in self.cache_dir.glob("*.json"):
            try:
                stat = cache_file.stat()
                disk_count += 1
                total_size += stat.st_size
            except OSError:
                continue
        
        self.stats.disk_entries = disk_count
        self.stats.total_size_bytes = total_size
        
        return self.stats
    
    def get_cache_info(self, key: str) -> Optional[Dict[str, Any]]:
        """获取特定缓存条目的信息"""
        cache_key = self._generate_cache_key(key)
        
        # 检查内存缓存
        if cache_key in self.memory_cache:
            entry = self.memory_cache[cache_key]
            return {
                "key": entry.key,
                "location": "memory",
                "created_at": datetime.fromtimestamp(entry.created_at).isoformat(),
                "expires_at": datetime.fromtimestamp(entry.expires_at).isoformat(),
                "access_count": entry.access_count,
                "last_accessed": datetime.fromtimestamp(entry.last_accessed).isoformat(),
                "size_bytes": entry.size_bytes,
                "is_expired": self.is_expired(entry.expires_at)
            }
        
        # 检查磁盘缓存
        disk_path = self._get_disk_path(cache_key)
        if disk_path.exists():
            try:
                with open(disk_path, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                
                return {
                    "key": cache_data.get('key', key),
                    "location": "disk",
                    "created_at": datetime.fromtimestamp(cache_data['created_at']).isoformat(),
                    "expires_at": datetime.fromtimestamp(cache_data['expires_at']).isoformat(),
                    "access_count": cache_data.get('access_count', 0),
                    "last_accessed": datetime.fromtimestamp(cache_data.get('last_accessed', 0)).isoformat(),
                    "size_bytes": cache_data.get('size_bytes', 0),
                    "is_expired": self.is_expired(cache_data['expires_at'])
                }
            except (json.JSONDecodeError, KeyError, IOError):
                return None
        
        return None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass