#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整打包脚本 - 打包服务端和客户端
Complete Packaging Script - Package both server and client
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import time

def run_command(command, cwd=None):
    """运行命令并返回结果"""
    try:
        print(f"运行命令: {command}")
        if cwd:
            print(f"工作目录: {cwd}")

        # 在Windows中文环境下使用gbk编码
        encoding = 'gbk' if os.name == 'nt' else 'utf-8'

        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            encoding=encoding,
            errors='ignore'  # 忽略编码错误
        )
        
        if result.returncode == 0:
            print("OK 命令执行成功")
            if result.stdout:
                print(f"输出: {result.stdout}")
            return True
        else:
            print(f"ERROR 命令执行失败 (返回码: {result.returncode})")
            if result.stderr:
                print(f"错误: {result.stderr}")
            return False

    except Exception as e:
        print(f"ERROR 命令执行异常: {e}")
        return False

def check_pyinstaller():
    """检查PyInstaller是否安装"""
    print("检查PyInstaller...")
    try:
        result = subprocess.run(['pyinstaller', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"OK PyInstaller版本: {result.stdout.strip()}")
            return True
        else:
            print("ERROR PyInstaller未安装")
            return False
    except FileNotFoundError:
        print("ERROR PyInstaller未找到")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("📦 安装PyInstaller...")
    return run_command("pip install pyinstaller")

def build_server():
    """打包服务端"""
    print("\n" + "=" * 60)
    print("🖥️ 开始打包服务端...")
    print("=" * 60)
    
    server_dir = Path("worldwar-server")
    if not server_dir.exists():
        print("❌ 服务端目录不存在")
        return False
    
    # 运行打包准备脚本
    print("\n📋 运行打包准备...")
    if not run_command("python build_server.py", cwd=str(server_dir)):
        print("❌ 服务端打包准备失败")
        return False
    
    # 运行PyInstaller
    print("\n🔨 运行PyInstaller...")
    if not run_command("pyinstaller server.spec", cwd=str(server_dir)):
        print("❌ 服务端PyInstaller打包失败")
        return False
    
    # 检查输出文件
    exe_path = server_dir / "dist" / "WorldWarServer.exe"
    if exe_path.exists():
        size_mb = exe_path.stat().st_size / (1024 * 1024)
        print(f"✅ 服务端打包成功! 文件大小: {size_mb:.1f}MB")
        return True
    else:
        print("❌ 服务端exe文件未生成")
        return False

def build_client():
    """打包客户端"""
    print("\n" + "=" * 60)
    print("🎮 开始打包客户端...")
    print("=" * 60)
    
    client_dir = Path("worldwar-client")
    if not client_dir.exists():
        print("❌ 客户端目录不存在")
        return False
    
    # 运行打包准备脚本
    print("\n📋 运行打包准备...")
    if not run_command("python build_client.py", cwd=str(client_dir)):
        print("❌ 客户端打包准备失败")
        return False
    
    # 运行PyInstaller
    print("\n🔨 运行PyInstaller...")
    if not run_command("pyinstaller client.spec", cwd=str(client_dir)):
        print("❌ 客户端PyInstaller打包失败")
        return False
    
    # 检查输出文件
    exe_path = client_dir / "dist" / "WorldWarClient.exe"
    if exe_path.exists():
        size_mb = exe_path.stat().st_size / (1024 * 1024)
        print(f"✅ 客户端打包成功! 文件大小: {size_mb:.1f}MB")
        return True
    else:
        print("❌ 客户端exe文件未生成")
        return False

def create_release_package():
    """创建发布包"""
    print("\n" + "=" * 60)
    print("📦 创建发布包...")
    print("=" * 60)
    
    # 创建发布目录
    release_dir = Path("WorldWarGame_Release")
    if release_dir.exists():
        print("🗑️ 删除旧的发布目录...")
        shutil.rmtree(release_dir)
    
    release_dir.mkdir()
    
    # 复制服务端
    server_src = Path("worldwar-server/dist/WorldWarServer")
    server_dst = release_dir / "Server"
    if server_src.exists():
        shutil.copytree(server_src, server_dst)
        print("✅ 复制服务端文件")
    else:
        print("❌ 服务端文件不存在")
        return False
    
    # 复制客户端
    client_src = Path("worldwar-client/dist/WorldWarClient")
    client_dst = release_dir / "Client"
    if client_src.exists():
        shutil.copytree(client_src, client_dst)
        print("✅ 复制客户端文件")
    else:
        print("❌ 客户端文件不存在")
        return False
    
    # 创建主安装说明
    create_main_readme(release_dir)
    
    # 计算总大小
    total_size = sum(f.stat().st_size for f in release_dir.rglob('*') if f.is_file())
    total_size_mb = total_size / (1024 * 1024)
    
    print(f"✅ 发布包创建完成! 总大小: {total_size_mb:.1f}MB")
    print(f"📁 发布目录: {release_dir.absolute()}")
    
    return True

def create_main_readme(release_dir):
    """创建主README文件"""
    readme_content = '''# 🎮 世界大战策略游戏 / World War Strategy Game

## 📦 安装说明 / Installation Guide

### 🚀 快速开始 / Quick Start

1. **启动服务器 / Start Server**
   - 进入 `Server/` 目录
   - 双击 `WorldWarServer.exe` 或 `StartServer.bat`
   - 首次运行选择语言 (推荐选择双语模式)
   - 等待看到 "服务器准备就绪" 消息

2. **启动客户端 / Start Client**
   - 进入 `Client/` 目录  
   - 双击 `WorldWarClient.exe` 或 `StartClient.bat`
   - 首次运行选择语言 (推荐选择双语模式)
   - 连接到服务器 (默认: localhost:8888)
   - 输入玩家名称开始游戏

### 🌍 语言支持 / Language Support
- 中文 (Chinese)
- English
- 双语模式 (Bilingual Mode) - 推荐

### 📁 目录结构 / Directory Structure
```
WorldWarGame_Release/
├── Server/                    # 服务端
│   ├── WorldWarServer.exe     # 服务端程序
│   ├── StartServer.bat        # 启动脚本
│   ├── config/                # 配置文件
│   ├── languages/             # 语言包
│   └── README.txt             # 服务端说明
├── Client/                    # 客户端
│   ├── WorldWarClient.exe     # 客户端程序
│   ├── StartClient.bat        # 启动脚本
│   ├── config/                # 配置文件
│   ├── client/languages/      # 语言包
│   └── README.txt             # 客户端说明
└── README.txt                 # 本文件
```

### ⚠️ 系统要求 / System Requirements
- Windows 7/8/10/11 (64位)
- 至少 100MB 可用磁盘空间
- 网络连接 (用于多人游戏)

### 🔧 故障排除 / Troubleshooting
- 如果程序无法启动，请以管理员身份运行
- 如果连接失败，检查防火墙设置
- 查看各自目录下的 logs/ 文件夹获取详细日志

### 📞 技术支持 / Technical Support
- 查看 Server/README.txt 和 Client/README.txt 获取详细说明
- 检查日志文件诊断问题

---
🎉 享受游戏! / Enjoy the game!
'''
    
    with open(release_dir / "README.txt", "w", encoding="utf-8") as f:
        f.write(readme_content)

def main():
    """主函数"""
    print("🚀 世界大战策略游戏完整打包脚本")
    print("=" * 60)
    print("📅 开始时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    print()
    
    # 检查PyInstaller
    if not check_pyinstaller():
        print("📦 尝试安装PyInstaller...")
        if not install_pyinstaller():
            print("❌ PyInstaller安装失败，请手动安装: pip install pyinstaller")
            return False
    
    success_count = 0
    total_tasks = 3
    
    # 打包服务端
    if build_server():
        success_count += 1
    
    # 打包客户端
    if build_client():
        success_count += 1
    
    # 创建发布包
    if success_count == 2:
        if create_release_package():
            success_count += 1
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 打包结果总结")
    print("=" * 60)
    print(f"✅ 成功任务: {success_count}/{total_tasks}")
    print(f"📅 完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    if success_count == total_tasks:
        print("\n🎉 所有打包任务完成!")
        print("📁 发布包位置: WorldWarGame_Release/")
        print("\n📋 下一步:")
        print("1. 测试 WorldWarGame_Release/Server/WorldWarServer.exe")
        print("2. 测试 WorldWarGame_Release/Client/WorldWarClient.exe")
        print("3. 压缩 WorldWarGame_Release/ 目录进行分发")
        return True
    else:
        print("\n❌ 部分打包任务失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    
    print("\n按任意键退出...")
    try:
        input()
    except KeyboardInterrupt:
        pass
    
    sys.exit(0 if success else 1)
