#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络通信协议
Network Communication Protocol
"""

import json
import struct
import socket
from typing import Dict, Any, Optional

class Protocol:
    """网络通信协议处理类"""
    
    # 协议版本
    VERSION = "1.0"
    
    # 消息头长度（4字节用于存储消息体长度）
    HEADER_SIZE = 4
    
    # 最大消息长度（1MB）
    MAX_MESSAGE_SIZE = 1024 * 1024
    
    # 编码格式
    ENCODING = "utf-8"
    
    @staticmethod
    def encode_message(message: Dict[str, Any]) -> bytes:
        """将消息编码为字节流"""
        try:
            # 添加协议版本信息
            message["protocol_version"] = Protocol.VERSION
            
            # 将消息转换为JSON字符串
            json_str = json.dumps(message, ensure_ascii=False)
            
            # 编码为字节
            message_bytes = json_str.encode(Protocol.ENCODING)
            
            # 检查消息长度
            if len(message_bytes) > Protocol.MAX_MESSAGE_SIZE:
                raise ValueError(f"消息过大: {len(message_bytes)} > {Protocol.MAX_MESSAGE_SIZE}")
            
            # 创建消息头（消息长度）
            header = struct.pack("!I", len(message_bytes))
            
            # 返回头部 + 消息体
            return header + message_bytes
            
        except Exception as e:
            raise ValueError(f"消息编码失败: {e}")
    
    @staticmethod
    def decode_message(data: bytes) -> Optional[Dict[str, Any]]:
        """将字节流解码为消息"""
        try:
            # 解码JSON字符串
            json_str = data.decode(Protocol.ENCODING)
            
            # 解析JSON
            message = json.loads(json_str)
            
            # 验证协议版本
            if message.get("protocol_version") != Protocol.VERSION:
                raise ValueError(f"协议版本不匹配: {message.get('protocol_version')} != {Protocol.VERSION}")
            
            return message
            
        except Exception as e:
            raise ValueError(f"消息解码失败: {e}")
    
    @staticmethod
    def send_message(sock: socket.socket, message: Dict[str, Any]) -> bool:
        """发送消息到套接字"""
        try:
            # 编码消息
            encoded_data = Protocol.encode_message(message)
            
            # 发送所有数据
            total_sent = 0
            while total_sent < len(encoded_data):
                sent = sock.send(encoded_data[total_sent:])
                if sent == 0:
                    return False  # 连接断开
                total_sent += sent
            
            return True
            
        except Exception as e:
            print(f"发送消息失败: {e}")
            return False
    
    @staticmethod
    def receive_message(sock: socket.socket, timeout: float = 30.0) -> Optional[Dict[str, Any]]:
        """从套接字接收消息"""
        try:
            # 设置超时
            sock.settimeout(timeout)
            
            # 首先接收消息头（4字节）
            header_data = Protocol._receive_exact(sock, Protocol.HEADER_SIZE)
            if not header_data:
                return None
            
            # 解析消息长度
            message_length = struct.unpack("!I", header_data)[0]
            
            # 验证消息长度
            if message_length > Protocol.MAX_MESSAGE_SIZE:
                raise ValueError(f"消息过大: {message_length} > {Protocol.MAX_MESSAGE_SIZE}")
            
            # 接收消息体
            message_data = Protocol._receive_exact(sock, message_length)
            if not message_data:
                return None
            
            # 解码消息
            return Protocol.decode_message(message_data)
            
        except socket.timeout:
            return None  # 超时
        except Exception as e:
            print(f"接收消息失败: {e}")
            return None
    
    @staticmethod
    def _receive_exact(sock: socket.socket, length: int) -> Optional[bytes]:
        """精确接收指定长度的数据"""
        data = b""
        while len(data) < length:
            try:
                chunk = sock.recv(length - len(data))
                if not chunk:
                    return None  # 连接断开
                data += chunk
            except socket.error:
                return None
        
        return data
    
    @staticmethod
    def create_message(msg_type: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """创建标准格式的消息"""
        import time
        
        message = {
            "type": msg_type,
            "timestamp": time.time(),
            "data": data or {}
        }
        
        return message
    
    @staticmethod
    def create_error_message(error_code: int, error_message: str) -> Dict[str, Any]:
        """创建错误消息"""
        return Protocol.create_message("error", {
            "error_code": error_code,
            "error_message": error_message
        })
    
    @staticmethod
    def create_success_message(data: Dict[str, Any] = None) -> Dict[str, Any]:
        """创建成功消息"""
        return Protocol.create_message("success", data or {})
    
    @staticmethod
    def validate_message(message: Dict[str, Any]) -> bool:
        """验证消息格式"""
        try:
            # 检查必需字段
            required_fields = ["type", "timestamp", "data"]
            for field in required_fields:
                if field not in message:
                    return False
            
            # 检查字段类型
            if not isinstance(message["type"], str):
                return False
            
            if not isinstance(message["timestamp"], (int, float)):
                return False
            
            if not isinstance(message["data"], dict):
                return False
            
            return True
            
        except Exception:
            return False
    
    @staticmethod
    def compress_message(message: Dict[str, Any]) -> bytes:
        """压缩消息（用于大型数据传输）"""
        import gzip
        
        try:
            # 编码消息
            encoded_data = Protocol.encode_message(message)
            
            # 压缩数据
            compressed_data = gzip.compress(encoded_data)
            
            return compressed_data
            
        except Exception as e:
            raise ValueError(f"消息压缩失败: {e}")
    
    @staticmethod
    def decompress_message(compressed_data: bytes) -> Dict[str, Any]:
        """解压缩消息"""
        import gzip
        
        try:
            # 解压缩数据
            decompressed_data = gzip.decompress(compressed_data)
            
            # 解码消息（跳过头部）
            message_data = decompressed_data[Protocol.HEADER_SIZE:]
            
            return Protocol.decode_message(message_data)
            
        except Exception as e:
            raise ValueError(f"消息解压缩失败: {e}")

class MessageBuilder:
    """消息构建器辅助类"""
    
    @staticmethod
    def welcome_message(server_name: str, version: str, session_id: str) -> Dict[str, Any]:
        """构建欢迎消息"""
        return Protocol.create_message("welcome", {
            "server_name": server_name,
            "version": version,
            "session_id": session_id
        })
    
    @staticmethod
    def join_game_message(player_name: str) -> Dict[str, Any]:
        """构建加入游戏消息"""
        return Protocol.create_message("join_game", {
            "player_name": player_name
        })
    
    @staticmethod
    def create_room_message(room_name: str, max_players: int, game_mode: str) -> Dict[str, Any]:
        """构建创建房间消息"""
        return Protocol.create_message("create_room", {
            "room_name": room_name,
            "max_players": max_players,
            "game_mode": game_mode
        })
    
    @staticmethod
    def join_room_message(room_id: str) -> Dict[str, Any]:
        """构建加入房间消息"""
        return Protocol.create_message("join_room", {
            "room_id": room_id
        })
    
    @staticmethod
    def game_action_message(action_type: str, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """构建游戏动作消息"""
        return Protocol.create_message("game_action", {
            "action_type": action_type,
            "action_data": action_data
        })
    
    @staticmethod
    def chat_message(message: str, target: str = "all") -> Dict[str, Any]:
        """构建聊天消息"""
        return Protocol.create_message("chat_message", {
            "message": message,
            "target": target
        })
    
    @staticmethod
    def ping_message() -> Dict[str, Any]:
        """构建心跳消息"""
        return Protocol.create_message("ping", {})
    
    @staticmethod
    def room_update_message(room_info: Dict[str, Any]) -> Dict[str, Any]:
        """构建房间更新消息"""
        return Protocol.create_message("room_update", room_info)
    
    @staticmethod
    def game_state_message(game_state: Dict[str, Any]) -> Dict[str, Any]:
        """构建游戏状态消息"""
        return Protocol.create_message("game_state", game_state)
    
    @staticmethod
    def turn_start_message(player_name: str, turn_number: int) -> Dict[str, Any]:
        """构建回合开始消息"""
        return Protocol.create_message("turn_start", {
            "current_player": player_name,
            "turn_number": turn_number
        })
    
    @staticmethod
    def natural_disaster_message(disaster_type: str, affected_territories: list, severity: int) -> Dict[str, Any]:
        """构建自然灾害消息"""
        return Protocol.create_message("natural_disaster", {
            "disaster_type": disaster_type,
            "affected_territories": affected_territories,
            "severity": severity
        })
