#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新地形预览工具的字体设置
"""

def update_terrain_preview_font():
    """更新terrain_preview.py的字体设置"""
    
    # 读取原文件
    with open('tools/terrain_preview.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换字体设置部分
    old_font_setup = """# 设置中文字体支持
import platform
import matplotlib.font_manager as fm

def setup_chinese_font():
    \"\"\"设置中文字体\"\"\"
    system = platform.system()
    
    if system == "Windows":
        # Windows系统字体
        fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi']
    elif system == "Darwin":  # macOS
        fonts = ['PingFang SC', 'Hiragino Sans GB', 'STHeiti', 'Arial Unicode MS']
    else:  # Linux
        fonts = ['WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC', 'DejaVu Sans']
    
    # 尝试找到可用的中文字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    for font in fonts:
        if font in available_fonts:
            # 强制设置字体参数
            plt.rcParams['font.sans-serif'] = [font]
            plt.rcParams['font.family'] = 'sans-serif'
            plt.rcParams['axes.unicode_minus'] = False
            
            # 设置所有可能的字体参数
            plt.rcParams['font.serif'] = [font]
            plt.rcParams['font.monospace'] = [font]
            
            print(f"✅ 使用中文字体: {font}")
            return True, font
    
    # 如果没有找到中文字体，使用英文标题
    print("⚠️  未找到中文字体，将使用英文标题")
    return False, None

# 设置字体
chinese_font_available, selected_font = setup_chinese_font()"""

    new_font_setup = """# 设置中文字体支持
import platform
import matplotlib.font_manager as fm

def setup_chinese_font():
    \"\"\"强制设置中文字体\"\"\"
    system = platform.system()
    
    if system == "Windows":
        font_name = 'Microsoft YaHei'
    elif system == "Darwin":
        font_name = 'PingFang SC'  
    else:
        font_name = 'WenQuanYi Micro Hei'
    
    # 强制设置所有字体参数
    plt.rcParams.update({
        'font.sans-serif': [font_name, 'DejaVu Sans', 'Arial'],
        'font.family': 'sans-serif',
        'font.serif': [font_name, 'DejaVu Serif'],
        'font.monospace': [font_name, 'DejaVu Sans Mono'],
        'axes.unicode_minus': False,
        'figure.titlesize': 'large',
        'axes.titlesize': 'medium',
        'axes.labelsize': 'medium'
    })
    
    print(f"✅ 强制设置中文字体: {font_name}")
    return True, font_name

# 设置字体
chinese_font_available, selected_font = setup_chinese_font()"""
    
    # 替换内容
    if old_font_setup in content:
        content = content.replace(old_font_setup, new_font_setup)
        print("✅ 已更新字体设置")
    else:
        print("⚠️  未找到原字体设置代码")
        return False
    
    # 写回文件
    with open('tools/terrain_preview.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 已更新 tools/terrain_preview.py")
    return True

if __name__ == "__main__":
    update_terrain_preview_font()