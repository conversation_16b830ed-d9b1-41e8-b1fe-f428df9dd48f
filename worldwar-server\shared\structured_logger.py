#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结构化日志系统
Structured Logging System

实现结构化日志记录，支持网络通信和游戏状态的详细日志
Implements structured logging with detailed network communication and game state logging
"""

import json
import logging
import threading
import time
from datetime import datetime
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum

class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class LogCategory(Enum):
    """日志分类枚举"""
    NETWORK = "network"
    GAME_STATE = "game_state"
    PLAYER_ACTION = "player_action"
    ROOM_MANAGEMENT = "room_management"
    TERRAIN_GENERATION = "terrain_generation"
    PERFORMANCE = "performance"
    SECURITY = "security"
    SYSTEM = "system"
    ERROR = "error"

@dataclass
class LogEntry:
    """结构化日志条目"""
    timestamp: str
    level: str
    category: str
    component: str
    message: str
    session_id: Optional[str] = None
    player_id: Optional[str] = None
    room_id: Optional[str] = None
    data: Optional[Dict[str, Any]] = None
    trace_id: Optional[str] = None
    duration_ms: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, default=str)

class StructuredLogger:
    """结构化日志记录器"""
    
    def __init__(self, component: str, log_type: str = "server"):
        """
        初始化结构化日志记录器
        
        Args:
            component: 组件名称
            log_type: 日志类型
        """
        self.component = component
        self.log_type = log_type
        self.session_context = threading.local()
        
        # 创建基础日志记录器
        from shared.enhanced_logger import EnhancedLogger
        self.base_logger = EnhancedLogger.get_logger(
            name=f"structured_{component}",
            log_type=log_type,
            level="DEBUG",
            log_to_file=True,
            log_to_console=True
        )
        
        # 创建结构化日志文件处理器
        self._setup_structured_handler()
        
        # 性能监控
        self.performance_data = {}
        self.lock = threading.Lock()
    
    def _setup_structured_handler(self):
        """设置结构化日志文件处理器"""
        try:
            log_dir = Path("logs") / self.log_type
            log_dir.mkdir(parents=True, exist_ok=True)
            
            # 结构化日志文件
            structured_log_file = log_dir / f"structured_{self.component}.jsonl"
            
            # 创建文件处理器
            handler = logging.FileHandler(structured_log_file, encoding='utf-8')
            handler.setLevel(logging.DEBUG)
            
            # 使用简单格式，因为我们会输出JSON
            formatter = logging.Formatter('%(message)s')
            handler.setFormatter(formatter)
            
            # 创建专门的结构化日志记录器
            self.structured_logger = logging.getLogger(f"structured_{self.component}_json")
            self.structured_logger.setLevel(logging.DEBUG)
            self.structured_logger.addHandler(handler)
            self.structured_logger.propagate = False
            
        except Exception as e:
            self.base_logger.error(f"设置结构化日志处理器失败: {e}")
    
    def set_session_context(self, session_id: str, player_id: str = None, room_id: str = None):
        """设置会话上下文"""
        self.session_context.session_id = session_id
        self.session_context.player_id = player_id
        self.session_context.room_id = room_id
    
    def clear_session_context(self):
        """清除会话上下文"""
        if hasattr(self.session_context, 'session_id'):
            delattr(self.session_context, 'session_id')
        if hasattr(self.session_context, 'player_id'):
            delattr(self.session_context, 'player_id')
        if hasattr(self.session_context, 'room_id'):
            delattr(self.session_context, 'room_id')
    
    def _get_context(self) -> Dict[str, Optional[str]]:
        """获取当前上下文"""
        return {
            'session_id': getattr(self.session_context, 'session_id', None),
            'player_id': getattr(self.session_context, 'player_id', None),
            'room_id': getattr(self.session_context, 'room_id', None)
        }
    
    def log(self, level: LogLevel, category: LogCategory, message: str,
            data: Optional[Dict[str, Any]] = None, trace_id: Optional[str] = None,
            duration_ms: Optional[float] = None, **kwargs):
        """
        记录结构化日志
        
        Args:
            level: 日志级别
            category: 日志分类
            message: 日志消息
            data: 附加数据
            trace_id: 跟踪ID
            duration_ms: 持续时间（毫秒）
            **kwargs: 其他上下文信息
        """
        context = self._get_context()
        context.update(kwargs)
        
        # 创建日志条目
        entry = LogEntry(
            timestamp=datetime.now().isoformat(),
            level=level.value,
            category=category.value,
            component=self.component,
            message=message,
            session_id=context.get('session_id'),
            player_id=context.get('player_id'),
            room_id=context.get('room_id'),
            data=data,
            trace_id=trace_id,
            duration_ms=duration_ms
        )
        
        # 记录到基础日志
        log_method = getattr(self.base_logger, level.value.lower())
        formatted_message = self._format_message(entry)
        log_method(formatted_message)
        
        # 记录到结构化日志
        if hasattr(self, 'structured_logger'):
            self.structured_logger.info(entry.to_json())
    
    def _format_message(self, entry: LogEntry) -> str:
        """格式化日志消息"""
        parts = [f"[{entry.category}]", entry.message]
        
        if entry.session_id:
            parts.append(f"session={entry.session_id}")
        if entry.player_id:
            parts.append(f"player={entry.player_id}")
        if entry.room_id:
            parts.append(f"room={entry.room_id}")
        if entry.duration_ms is not None:
            parts.append(f"duration={entry.duration_ms:.2f}ms")
        if entry.trace_id:
            parts.append(f"trace={entry.trace_id}")
        
        return " | ".join(parts)
    
    # 便捷方法
    def debug(self, category: LogCategory, message: str, **kwargs):
        """记录调试日志"""
        self.log(LogLevel.DEBUG, category, message, **kwargs)
    
    def info(self, category: LogCategory, message: str, **kwargs):
        """记录信息日志"""
        self.log(LogLevel.INFO, category, message, **kwargs)
    
    def warning(self, category: LogCategory, message: str, **kwargs):
        """记录警告日志"""
        self.log(LogLevel.WARNING, category, message, **kwargs)
    
    def error(self, category: LogCategory, message: str, **kwargs):
        """记录错误日志"""
        self.log(LogLevel.ERROR, category, message, **kwargs)
    
    def critical(self, category: LogCategory, message: str, **kwargs):
        """记录严重错误日志"""
        self.log(LogLevel.CRITICAL, category, message, **kwargs)
    
    # 网络通信专用方法
    def log_network_event(self, event_type: str, direction: str, message_type: str = None,
                         message_data: Dict[str, Any] = None, peer_address: str = None,
                         success: bool = True, error: str = None, duration_ms: float = None):
        """
        记录网络通信事件
        
        Args:
            event_type: 事件类型 (connect, disconnect, send, receive, error)
            direction: 方向 (inbound, outbound)
            message_type: 消息类型
            message_data: 消息数据
            peer_address: 对端地址
            success: 是否成功
            error: 错误信息
            duration_ms: 持续时间
        """
        data = {
            'event_type': event_type,
            'direction': direction,
            'success': success
        }
        
        if message_type:
            data['message_type'] = message_type
        if message_data:
            data['message_data'] = message_data
        if peer_address:
            data['peer_address'] = peer_address
        if error:
            data['error'] = error
        
        level = LogLevel.ERROR if not success else LogLevel.INFO
        message = f"网络{event_type}: {direction}"
        if message_type:
            message += f" - {message_type}"
        if not success and error:
            message += f" - {error}"
        
        self.log(level, LogCategory.NETWORK, message, data=data, duration_ms=duration_ms)
    
    def log_game_state_change(self, state_type: str, old_state: Any = None,
                             new_state: Any = None, player_id: str = None,
                             room_id: str = None, details: Dict[str, Any] = None):
        """
        记录游戏状态变化
        
        Args:
            state_type: 状态类型
            old_state: 旧状态
            new_state: 新状态
            player_id: 玩家ID
            room_id: 房间ID
            details: 详细信息
        """
        data = {
            'state_type': state_type,
            'old_state': old_state,
            'new_state': new_state
        }
        
        if details:
            data.update(details)
        
        message = f"游戏状态变化: {state_type}"
        if old_state and new_state:
            message += f" ({old_state} -> {new_state})"
        
        self.log(LogLevel.INFO, LogCategory.GAME_STATE, message, 
                data=data, player_id=player_id, room_id=room_id)
    
    def log_player_action(self, action: str, player_id: str, room_id: str = None,
                         action_data: Dict[str, Any] = None, success: bool = True,
                         error: str = None, duration_ms: float = None):
        """
        记录玩家行为
        
        Args:
            action: 行为类型
            player_id: 玩家ID
            room_id: 房间ID
            action_data: 行为数据
            success: 是否成功
            error: 错误信息
            duration_ms: 持续时间
        """
        data = {
            'action': action,
            'success': success
        }
        
        if action_data:
            data['action_data'] = action_data
        if error:
            data['error'] = error
        
        level = LogLevel.ERROR if not success else LogLevel.INFO
        message = f"玩家行为: {action}"
        if not success and error:
            message += f" - {error}"
        
        self.log(level, LogCategory.PLAYER_ACTION, message,
                data=data, player_id=player_id, room_id=room_id, duration_ms=duration_ms)
    
    def log_performance_metric(self, metric_name: str, value: Union[int, float],
                              unit: str = None, tags: Dict[str, str] = None):
        """
        记录性能指标
        
        Args:
            metric_name: 指标名称
            value: 指标值
            unit: 单位
            tags: 标签
        """
        data = {
            'metric_name': metric_name,
            'value': value
        }
        
        if unit:
            data['unit'] = unit
        if tags:
            data['tags'] = tags
        
        # 存储性能数据用于分析
        with self.lock:
            if metric_name not in self.performance_data:
                self.performance_data[metric_name] = []
            self.performance_data[metric_name].append({
                'timestamp': time.time(),
                'value': value,
                'tags': tags or {}
            })
            
            # 保持最近1000个数据点
            if len(self.performance_data[metric_name]) > 1000:
                self.performance_data[metric_name] = self.performance_data[metric_name][-1000:]
        
        message = f"性能指标: {metric_name}={value}"
        if unit:
            message += f" {unit}"
        
        self.log(LogLevel.DEBUG, LogCategory.PERFORMANCE, message, data=data)
    
    def get_performance_summary(self, metric_name: str = None) -> Dict[str, Any]:
        """获取性能摘要"""
        with self.lock:
            if metric_name:
                if metric_name in self.performance_data:
                    values = [d['value'] for d in self.performance_data[metric_name]]
                    return {
                        'metric': metric_name,
                        'count': len(values),
                        'min': min(values) if values else 0,
                        'max': max(values) if values else 0,
                        'avg': sum(values) / len(values) if values else 0,
                        'latest': values[-1] if values else 0
                    }
                else:
                    return {'metric': metric_name, 'count': 0}
            else:
                summary = {}
                for name, data_points in self.performance_data.items():
                    values = [d['value'] for d in data_points]
                    summary[name] = {
                        'count': len(values),
                        'min': min(values) if values else 0,
                        'max': max(values) if values else 0,
                        'avg': sum(values) / len(values) if values else 0,
                        'latest': values[-1] if values else 0
                    }
                return summary

class LogContext:
    """日志上下文管理器"""
    
    def __init__(self, logger: StructuredLogger, **context):
        self.logger = logger
        self.context = context
        self.old_context = {}
    
    def __enter__(self):
        # 保存旧上下文
        self.old_context = self.logger._get_context()
        
        # 设置新上下文
        for key, value in self.context.items():
            if hasattr(self.logger.session_context, key):
                setattr(self.logger.session_context, key, value)
        
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # 恢复旧上下文
        for key, value in self.old_context.items():
            if value is not None:
                setattr(self.logger.session_context, key, value)
            elif hasattr(self.logger.session_context, key):
                delattr(self.logger.session_context, key)

# 全局日志记录器实例
_loggers: Dict[str, StructuredLogger] = {}
_lock = threading.Lock()

def get_structured_logger(component: str, log_type: str = "server") -> StructuredLogger:
    """获取结构化日志记录器实例"""
    with _lock:
        key = f"{log_type}_{component}"
        if key not in _loggers:
            _loggers[key] = StructuredLogger(component, log_type)
        return _loggers[key]