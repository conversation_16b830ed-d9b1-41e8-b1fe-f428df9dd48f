#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据获取器模块
Data Fetcher Module
"""

import requests
import json
import time
import subprocess
from typing import Dict, List, Any, Optional
from pathlib import Path

class DataFetcher:
    """真实世界数据获取器"""
    
    def __init__(self):
        """初始化数据获取器"""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'WorldWarGame/1.0 (Educational Purpose)'
        })
        
        # API端点
        self.world_bank_api = "https://api.worldbank.org/v2"
        # 注意：只使用世界银行API获取贫困城市数据，其他数据使用AI生成
        
        # 缓存目录
        self.cache_dir = Path("data/cache")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
    
    def fetch_poverty_cities(self) -> Dict[str, Any]:
        """获取贫困城市数据"""
        print("正在获取世界银行贫困数据... / Fetching World Bank poverty data...")
        
        try:
            # 尝试从世界银行API获取贫困数据
            poverty_data = self._fetch_world_bank_poverty_data()
            
            if not poverty_data:
                print("API数据获取失败，使用预设数据... / API fetch failed, using preset data...")
                poverty_data = self._get_preset_poverty_cities()
            
            return poverty_data
            
        except Exception as e:
            print(f"获取贫困数据失败: {e} / Failed to fetch poverty data: {e}")
            return self._get_preset_poverty_cities()
    
    def fetch_terrain_data(self) -> Dict[str, Any]:
        """获取地形数据（使用AI生成）"""
        print("正在生成地形数据... / Generating terrain data...")

        try:
            # 使用AI生成地形数据
            from world.ai_generator import AIDataGenerator
            generator = AIDataGenerator()
            terrain_data = generator.generate_terrain_data()

            print("地形数据生成完成 / Terrain data generation complete")
            return terrain_data

        except Exception as e:
            print(f"生成地形数据失败: {e} / Failed to generate terrain data: {e}")
            return self._get_preset_terrain_data()

    def fetch_climate_data(self) -> Dict[str, Any]:
        """获取气候数据（使用AI生成）"""
        print("正在生成气候数据... / Generating climate data...")

        try:
            # 使用AI生成气候数据
            from world.ai_generator import AIDataGenerator
            generator = AIDataGenerator()
            climate_data = generator.generate_climate_data()

            print("气候数据生成完成 / Climate data generation complete")
            return climate_data

        except Exception as e:
            print(f"生成气候数据失败: {e} / Failed to generate climate data: {e}")
            return self._get_preset_climate_data()
    
    def fetch_resource_data(self) -> Dict[str, Any]:
        """获取资源数据"""
        print("正在获取资源数据... / Fetching resource data...")
        
        try:
            # 使用预设资源数据（真实的地质资源数据API较难获取）
            return self._get_preset_resource_data()
            
        except Exception as e:
            print(f"获取资源数据失败: {e} / Failed to fetch resource data: {e}")
            return self._get_preset_resource_data()
    
    def fetch_disaster_data(self) -> Dict[str, Any]:
        """获取灾害数据"""
        print("正在获取灾害数据... / Fetching disaster data...")
        
        try:
            # 使用预设灾害数据
            return self._get_preset_disaster_data()
            
        except Exception as e:
            print(f"获取灾害数据失败: {e} / Failed to fetch disaster data: {e}")
            return self._get_preset_disaster_data()
    
    def fetch_cities_data(self) -> Dict[str, Any]:
        """获取城市数据"""
        print("正在获取城市数据... / Fetching cities data...")
        
        try:
            # 使用预设城市数据
            return self._get_preset_cities_data()
            
        except Exception as e:
            print(f"获取城市数据失败: {e} / Failed to fetch cities data: {e}")
            return self._get_preset_cities_data()
    
    def _fetch_world_bank_poverty_data(self) -> Optional[Dict[str, Any]]:
        """从世界银行API获取贫困数据"""
        try:
            # 世界银行贫困数据API调用
            url = f"{self.world_bank_api}/country/all/indicator/SI.POV.DDAY"
            params = {
                'format': 'json',
                'per_page': 100,
                'date': '2020:2023'  # 最近几年的数据
            }
            
            response = self.session.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if len(data) > 1 and data[1]:  # 世界银行API返回格式特殊
                    return self._process_world_bank_data(data[1])
            
            return None
            
        except Exception as e:
            print(f"世界银行API调用失败: {e}")
            return None
    
    def _fetch_nasa_terrain_data(self) -> Optional[Dict[str, Any]]:
        """从NASA API获取地形数据"""
        try:
            # 这里应该调用NASA的地形API，但由于复杂性，我们使用预设数据
            return None
            
        except Exception as e:
            print(f"NASA API调用失败: {e}")
            return None
    
    def _fetch_climate_api_data(self) -> Optional[Dict[str, Any]]:
        """获取气候API数据"""
        try:
            # 这里应该调用气候数据API
            return None
            
        except Exception as e:
            print(f"气候API调用失败: {e}")
            return None
    
    def _process_world_bank_data(self, raw_data: List[Dict]) -> Dict[str, Any]:
        """处理世界银行原始数据"""
        cities = []
        
        for item in raw_data:
            if item.get('value') and item.get('country'):
                city = {
                    "name": f"{item['country']['value']}主要城市",
                    "country": item['country']['value'],
                    "poverty_index": min(1.0, item['value'] / 100.0),  # 转换为0-1范围
                    "population": 500000,  # 默认人口
                    "year": item.get('date', '2020')
                }
                cities.append(city)
        
        return {"cities": cities[:20]}  # 限制数量
    
    def _get_preset_poverty_cities(self) -> Dict[str, Any]:
        """获取预设的贫困城市数据"""
        cities = [
            {"name": "达卡", "country": "孟加拉国", "poverty_index": 0.85, "population": 9000000},
            {"name": "拉各斯", "country": "尼日利亚", "poverty_index": 0.78, "population": 15000000},
            {"name": "金沙萨", "country": "刚果民主共和国", "poverty_index": 0.92, "population": 12000000},
            {"name": "加尔各答", "country": "印度", "poverty_index": 0.73, "population": 14000000},
            {"name": "马尼拉", "country": "菲律宾", "poverty_index": 0.68, "population": 13000000},
            {"name": "开罗", "country": "埃及", "poverty_index": 0.65, "population": 20000000},
            {"name": "雅加达", "country": "印度尼西亚", "poverty_index": 0.62, "population": 10000000},
            {"name": "卡拉奇", "country": "巴基斯坦", "poverty_index": 0.81, "population": 15000000},
            {"name": "利马", "country": "秘鲁", "poverty_index": 0.58, "population": 10000000},
            {"name": "内罗毕", "country": "肯尼亚", "poverty_index": 0.75, "population": 4000000},
            {"name": "阿克拉", "country": "加纳", "poverty_index": 0.71, "population": 2000000},
            {"name": "阿迪斯阿贝巴", "country": "埃塞俄比亚", "poverty_index": 0.88, "population": 5000000},
            {"name": "巴格达", "country": "伊拉克", "poverty_index": 0.69, "population": 7000000},
            {"name": "喀布尔", "country": "阿富汗", "poverty_index": 0.95, "population": 4000000},
            {"name": "太子港", "country": "海地", "poverty_index": 0.91, "population": 3000000},
            {"name": "达累斯萨拉姆", "country": "坦桑尼亚", "poverty_index": 0.79, "population": 6000000},
            {"name": "瓜亚基尔", "country": "厄瓜多尔", "poverty_index": 0.64, "population": 3000000},
            {"name": "马普托", "country": "莫桑比克", "poverty_index": 0.86, "population": 1000000},
            {"name": "科纳克里", "country": "几内亚", "poverty_index": 0.83, "population": 2000000},
            {"name": "弗里敦", "country": "塞拉利昂", "poverty_index": 0.89, "population": 1000000}
        ]
        
        return {"cities": cities}
    
    def _get_preset_terrain_data(self) -> Dict[str, Any]:
        """获取预设地形数据"""
        return {
            "terrain_types": ["mountain", "plain", "desert", "forest", "coastal", "swamp", "tundra"],
            "terrain_effects": {
                "mountain": {"defense_bonus": 0.4, "movement_penalty": 0.3},
                "plain": {"defense_bonus": 0.0, "movement_penalty": 0.0},
                "desert": {"defense_bonus": 0.1, "movement_penalty": 0.4},
                "forest": {"defense_bonus": 0.3, "movement_penalty": 0.2},
                "coastal": {"defense_bonus": 0.1, "movement_penalty": 0.0},
                "swamp": {"defense_bonus": 0.2, "movement_penalty": 0.5},
                "tundra": {"defense_bonus": 0.1, "movement_penalty": 0.3}
            },
            "terrain_distribution": {
                "plain": 0.25,
                "forest": 0.20,
                "mountain": 0.15,
                "desert": 0.12,
                "coastal": 0.10,
                "swamp": 0.08,
                "tundra": 0.10
            }
        }
    
    def _get_preset_climate_data(self) -> Dict[str, Any]:
        """获取预设气候数据"""
        return {
            "climate_types": ["tropical", "temperate", "arid", "polar", "mediterranean", "continental"],
            "climate_effects": {
                "tropical": {"temperature_range": (20, 35), "rainfall": "high"},
                "temperate": {"temperature_range": (5, 25), "rainfall": "medium"},
                "arid": {"temperature_range": (10, 40), "rainfall": "low"},
                "polar": {"temperature_range": (-20, 5), "rainfall": "low"},
                "mediterranean": {"temperature_range": (10, 30), "rainfall": "medium"},
                "continental": {"temperature_range": (-10, 30), "rainfall": "medium"}
            }
        }
    
    def _get_preset_resource_data(self) -> Dict[str, Any]:
        """获取预设资源数据"""
        return {
            "resource_types": ["iron", "coal", "oil", "gold", "copper", "uranium", "diamonds", "food", "water"],
            "resource_properties": {
                "iron": {"rarity": 0.3, "value": 10},
                "coal": {"rarity": 0.4, "value": 8},
                "oil": {"rarity": 0.2, "value": 15},
                "gold": {"rarity": 0.1, "value": 50},
                "copper": {"rarity": 0.35, "value": 12},
                "uranium": {"rarity": 0.05, "value": 100},
                "diamonds": {"rarity": 0.03, "value": 200},
                "food": {"rarity": 0.8, "value": 5},
                "water": {"rarity": 0.6, "value": 3}
            }
        }
    
    def _get_preset_disaster_data(self) -> Dict[str, Any]:
        """获取预设灾害数据"""
        return {
            "disaster_types": ["earthquake", "flood", "drought", "hurricane", "wildfire", "volcanic_eruption"],
            "disaster_frequencies": {
                "earthquake": 0.05,
                "flood": 0.08,
                "drought": 0.06,
                "hurricane": 0.04,
                "wildfire": 0.07,
                "volcanic_eruption": 0.02
            }
        }
    
    def _get_preset_cities_data(self) -> Dict[str, Any]:
        """获取预设城市数据"""
        return {
            "total_territories": 100,
            "major_cities": 20,
            "description": "基于真实世界地理分布的预设数据"
        }
    
    def download_file_with_curl(self, url: str, output_path: Path) -> bool:
        """使用curl下载文件（Windows环境）"""
        try:
            # 在Windows上使用PowerShell调用curl
            cmd = [
                "powershell", "-Command",
                f"curl -o '{output_path}' '{url}'"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print(f"文件下载成功: {output_path}")
                return True
            else:
                print(f"curl下载失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("下载超时")
            return False
        except Exception as e:
            print(f"下载过程中发生错误: {e}")
            return False
