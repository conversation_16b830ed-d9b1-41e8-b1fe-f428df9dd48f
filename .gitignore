# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs/*.log
logs/server/*.log
logs/client/*.log
worldwar-server/logs/*.log
worldwar-client/logs/*.log

# Config files with sensitive data
config/api_keys.json
config/server_key.dat
worldwar-server/config/server_key.dat

# Cache
data/cache/
*.cache

# Saves
saves/
*.save

# OS
.DS_Store
Thumbs.db
desktop.ini

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Test files
test_*.py
*_test.py
