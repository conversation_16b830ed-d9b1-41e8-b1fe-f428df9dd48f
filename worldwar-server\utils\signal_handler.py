#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
信号处理管理器 - 处理Ctrl+C等退出信号
Signal Handler Manager - Handle Ctrl+C and other exit signals
"""

import signal
import sys
import threading
import time
from typing import Callable, List, Optional
from shared.enhanced_logger import get_server_logger


class SignalHandler:
    """信号处理器 - 优雅处理程序退出"""
    
    def __init__(self, app_name: str = "Application"):
        """
        初始化信号处理器
        
        Args:
            app_name: 应用程序名称
        """
        self.app_name = app_name
        self.logger = get_server_logger("SignalHandler")
        self.cleanup_callbacks: List[Callable] = []
        self.is_shutting_down = False
        self.shutdown_event = threading.Event()
        
        # 注册信号处理器
        self._register_signal_handlers()
    
    def _register_signal_handlers(self):
        """注册信号处理器"""
        try:
            # 注册SIGINT (Ctrl+C)
            signal.signal(signal.SIGINT, self._signal_handler)
            
            # 注册SIGTERM (终止信号)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            # Windows下的特殊处理
            if sys.platform == "win32":
                signal.signal(signal.SIGBREAK, self._signal_handler)
            
            self.logger.info(f"信号处理器已注册 - {self.app_name}")
            
        except Exception as e:
            self.logger.error(f"注册信号处理器失败: {e}")
    
    def _signal_handler(self, signum: int, frame):
        """信号处理函数"""
        signal_name = signal.Signals(signum).name
        
        if self.is_shutting_down:
            self.logger.warning(f"收到信号 {signal_name}，但程序已在关闭中...")
            return
        
        self.logger.info(f"收到退出信号: {signal_name}")
        print(f"\n🛑 收到退出信号 ({signal_name})，正在优雅关闭 {self.app_name}...")
        
        self.is_shutting_down = True
        self.shutdown_event.set()
        
        # 执行清理回调
        self._execute_cleanup_callbacks()
        
        # 强制退出
        self._force_exit()
    
    def _execute_cleanup_callbacks(self):
        """执行清理回调"""
        if not self.cleanup_callbacks:
            return
        
        print("🧹 正在执行清理操作...")
        self.logger.info("开始执行清理回调")
        
        for i, callback in enumerate(self.cleanup_callbacks):
            try:
                print(f"  📋 执行清理操作 {i+1}/{len(self.cleanup_callbacks)}...")
                callback()
                self.logger.info(f"清理回调 {i+1} 执行成功")
            except Exception as e:
                self.logger.error(f"清理回调 {i+1} 执行失败: {e}")
                print(f"  ❌ 清理操作 {i+1} 失败: {e}")
        
        print("✅ 清理操作完成")
        self.logger.info("所有清理回调执行完成")
    
    def _force_exit(self):
        """强制退出程序"""
        print(f"👋 {self.app_name} 已安全退出")
        self.logger.info(f"{self.app_name} 程序退出")
        
        # 给一点时间让日志写入
        time.sleep(0.1)
        
        # 强制退出
        sys.exit(0)
    
    def add_cleanup_callback(self, callback: Callable):
        """
        添加清理回调函数
        
        Args:
            callback: 清理回调函数
        """
        if callback not in self.cleanup_callbacks:
            self.cleanup_callbacks.append(callback)
            self.logger.info(f"添加清理回调: {callback.__name__}")
    
    def remove_cleanup_callback(self, callback: Callable):
        """
        移除清理回调函数
        
        Args:
            callback: 清理回调函数
        """
        if callback in self.cleanup_callbacks:
            self.cleanup_callbacks.remove(callback)
            self.logger.info(f"移除清理回调: {callback.__name__}")
    
    def wait_for_shutdown(self, timeout: Optional[float] = None) -> bool:
        """
        等待关闭信号
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            是否收到关闭信号
        """
        return self.shutdown_event.wait(timeout)
    
    def is_shutdown_requested(self) -> bool:
        """检查是否请求关闭"""
        return self.is_shutting_down
    
    def trigger_shutdown(self):
        """手动触发关闭"""
        self.logger.info("手动触发程序关闭")
        self._signal_handler(signal.SIGTERM, None)


def setup_signal_handler(app_name: str, cleanup_func: Optional[Callable] = None) -> SignalHandler:
    """
    设置信号处理器

    Args:
        app_name: 应用程序名称
        cleanup_func: 可选的清理函数

    Returns:
        信号处理器实例
    """
    handler = SignalHandler(app_name)

    if cleanup_func:
        handler.add_cleanup_callback(cleanup_func)

    return handler


def setup_graceful_shutdown(app_name: str, cleanup_func: Optional[Callable] = None) -> SignalHandler:
    """
    设置优雅关闭（向后兼容的别名）

    Args:
        app_name: 应用程序名称
        cleanup_func: 清理函数

    Returns:
        信号处理器实例
    """
    # 这是 setup_signal_handler 的别名，保持向后兼容
    return setup_signal_handler(app_name, cleanup_func)
