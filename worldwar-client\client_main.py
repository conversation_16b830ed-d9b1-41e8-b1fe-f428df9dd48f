#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
世界大战游戏客户端主程序 - 纯命令行 (CLI) 重构版
World War Game Client Main - Pure Command-Line (CLI) Refactor
"""

import argparse
import json
import socket
import sys
import threading
import time
from pathlib import Path

# --- 修复模块导入路径 ---
project_root = Path(__file__).parent.parent
server_dir = project_root / "worldwar-server"
client_dir = project_root / "worldwar-client"
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))
if str(server_dir) not in sys.path:
    sys.path.insert(0, str(server_dir))
if str(client_dir) not in sys.path:
    sys.path.insert(0, str(client_dir))

from shared.enhanced_language_manager import get_enhanced_language_manager
from client.core.username_helper import ClientUsernameHelper

class CommandLineUI:
    """处理命令行界面的显示和输入"""

    def __init__(self, network_client):
        self.network_client = network_client
        self.running = True
        self.input_thread = threading.Thread(target=self.handle_input, daemon=True)

    def start(self):
        self.input_thread.start()

    def stop(self):
        self.running = False

    def handle_input(self):
        while self.running:
            try:
                command_text = input(">>> ")
                if command_text:
                    self.network_client.handle_command(command_text)
            except (KeyboardInterrupt, EOFError):
                self.network_client.stop()
                break

    def display_message(self, message, end="\n"):
        # 清除当前行并打印消息，以避免与输入提示符冲突
        sys.stdout.write('\r' + ' ' * 80 + '\r')
        sys.stdout.flush()
        print(message, end=end)
        sys.stdout.write('>>> ')
        sys.stdout.flush()

class NetworkClient:
    """处理与服务器的网络通信"""

    def __init__(self, host, port, username):
        self.host = host
        self.port = port
        self.username = username
        self.client_socket = None
        self.connected = False
        self.receive_thread = None
        self.ui = CommandLineUI(self)
        self.language_manager = get_enhanced_language_manager("client")

    def start(self):
        if self.connect():
            self.ui.start()
            self.receive_thread.join() # 等待接收线程结束

    def stop(self):
        lang_manager = get_enhanced_language_manager("client")
        self.ui.display_message(lang_manager.get_text("client.disconnecting"))
        self.ui.stop()
        self.disconnect()

    def connect(self):
        try:
            self.client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.client_socket.connect((self.host, self.port))
            self.connected = True
            
            self.receive_thread = threading.Thread(target=self.receive_messages, daemon=True)
            self.receive_thread.start()
            
            # 不再需要sleep，握手由消息驱动
            return True
        except Exception as e:
            self.ui.display_message(f"[错误] 连接服务器失败: {e}")
            return False

    def disconnect(self):
        if self.connected:
            self.connected = False
            if self.client_socket:
                try:
                    self.client_socket.shutdown(socket.SHUT_RDWR)
                    self.client_socket.close()
                except OSError:
                    pass
        self.ui.display_message("已与服务器断开连接。")

    def receive_messages(self):
        while self.connected:
            try:
                data = self.client_socket.recv(4096)
                if not data:
                    self.stop()
                    break
                
                for message_str in data.decode('utf-8').strip().split('\n'):
                    if message_str:
                        try:
                            message = json.loads(message_str)
                            self.handle_server_message(message)
                        except json.JSONDecodeError:
                            self.ui.display_message(f"[警告] 收到非JSON消息: {message_str}")
            except (ConnectionResetError, BrokenPipeError):
                self.stop()
                break
            except Exception as e:
                self.ui.display_message(f"[错误] 接收消息时出错: {e}")
                self.stop()
                break

    def send_message(self, message):
        if self.connected and self.client_socket:
            try:
                self.client_socket.sendall((json.dumps(message) + '\n').encode('utf-8'))
            except Exception as e:
                self.ui.display_message(f"[错误] 发送消息失败: {e}")
                self.stop()

    def handle_server_message(self, message: dict):
        msg_type = message.get("type")
        data = message.get("data", {})

        if msg_type == "welcome":
            self.ui.display_message(f"服务器: {data.get('message', '欢迎!')}")
            # 收到欢迎消息后，发送握手确认
            handshake_ack_message = {"type": "handshake_ack", "data": {}}
            self.send_message(handshake_ack_message)
            # 然后发送加入游戏请求
            auth_message = {
                "type": "join_game",
                "data": {"player_name": self.username}
            }
            self.send_message(auth_message)
        elif msg_type == "join_game_response":
            if data.get("success"):
                self.ui.display_message("[成功] 成功加入游戏!")
            else:
                error = data.get("message", "未知错误")
                self.ui.display_message(f"[错误] 加入游戏失败: {error}")
                self.stop()
        else:
            pretty_message = json.dumps(message, indent=2, ensure_ascii=False)
            self.ui.display_message(pretty_message)

    def handle_command(self, command: str):
        parts = command.strip().split()
        cmd = parts[0].lower()
        args = parts[1:]
        
        message = {"type": cmd, "data": {}}

        if cmd == "quit":
            self.stop()
            return
        elif cmd == "rooms":
            message["type"] = "get_room_list"
        elif cmd == "create" and args:
            message["type"] = "create_room"
            message["data"] = {"room_name": args[0]}
        # ... 可以添加更多命令 ...
        else:
            self.ui.display_message(f"未知命令: {cmd}")
            return
            
        self.send_message(message)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="世界大战游戏客户端 (CLI版)")
    parser.add_argument("--host", default="localhost", help="服务器地址")
    parser.add_argument("--port", type=int, default=8888, help="服务器端口")
    parser.add_argument("--username", help="玩家名称")
    parser.add_argument("--debug", action="store_true", help="启用调试模式")
    args = parser.parse_args()

    if not args.username:
        username_helper = ClientUsernameHelper(get_enhanced_language_manager("client"))
        args.username = username_helper.generate_random_username()
        lang_manager = get_enhanced_language_manager("client")
        print(lang_manager.get_text("client.username_generated", username=args.username))

    client = NetworkClient(args.host, args.port, args.username)
    client.start()

if __name__ == "__main__":
    main()
