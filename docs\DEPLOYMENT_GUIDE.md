# 🚀 部署指南 (Deployment Guide)

本文档提供了世界大战游戏的完整部署指南，包括开发环境、测试环境和生产环境的部署方法。

---

## 📋 目录

- [系统要求](#系统要求)
- [开发环境部署](#开发环境部署)
- [测试环境部署](#测试环境部署)
- [生产环境部署](#生产环境部署)
- [配置管理](#配置管理)
- [监控和维护](#监控和维护)
- [故障排除](#故障排除)

---

## 🖥️ 系统要求

### 最低要求
- **操作系统**: Windows 10, macOS 10.14, Ubuntu 18.04 或更高版本
- **Python**: 3.8 或更高版本
- **内存**: 2GB RAM
- **存储**: 1GB 可用空间
- **网络**: 稳定的互联网连接（用于真实世界数据获取）

### 推荐配置
- **操作系统**: Windows 11, macOS 12+, Ubuntu 20.04+
- **Python**: 3.10 或更高版本
- **内存**: 4GB RAM 或更多
- **存储**: 2GB 可用空间
- **CPU**: 多核处理器（用于并发处理）
- **网络**: 高速互联网连接

---

## 🛠️ 开发环境部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd worldwar-game
```

### 2. 创建虚拟环境
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate
```

### 3. 安装依赖
```bash
# 使用启动器自动安装（推荐）
python start_game.py

# 或手动安装
pip install -r worldwar-server/requirements.txt
pip install -r worldwar-client/requirements.txt

# 安装开发工具
pip install pytest pytest-asyncio pytest-cov black flake8
```

### 4. 配置开发环境
```bash
# 创建配置文件
cp worldwar-server/config/config.example.json worldwar-server/config/config.json

# 编辑配置文件
# 设置开发模式参数
```

### 5. 运行测试
```bash
# 运行所有测试
python -m pytest tests/ -v

# 运行集成测试
python tests/run_integration_tests.py

# 运行用户验收测试
python -m pytest tests/acceptance/ -v
```

### 6. 启动开发服务器
```bash
# 使用启动器
python start_game.py

# 或分别启动
python worldwar-server/server_main.py
python worldwar-client/client_main.py
```

---

## 🧪 测试环境部署

### 1. 环境准备
```bash
# 创建测试环境目录
mkdir worldwar-test
cd worldwar-test

# 克隆代码
git clone <repository-url> .
```

### 2. 配置测试环境
```bash
# 创建测试配置
cp worldwar-server/config/config.example.json worldwar-server/config/config.test.json

# 编辑测试配置
# 设置测试数据库、日志级别等
```

### 3. 安装和配置
```bash
# 安装依赖
pip install -r worldwar-server/requirements.txt
pip install -r worldwar-client/requirements.txt

# 设置环境变量
export WORLDWAR_ENV=test
export WORLDWAR_CONFIG=config.test.json
```

### 4. 运行自动化测试
```bash
# 运行完整测试套件
python tests/run_integration_tests.py --all

# 运行性能测试
python -m pytest tests/performance/ -v

# 生成测试报告
python tests/run_integration_tests.py --generate-report
```

### 5. 部署验证
```bash
# 启动测试服务器
python worldwar-server/server_main.py --config config.test.json

# 运行健康检查
python tools/system_monitor.py --health-check

# 验证所有功能
python tests/acceptance/test_user_acceptance.py
```

---

## 🌐 生产环境部署

### 1. 服务器准备

#### 系统配置
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要软件
sudo apt install python3 python3-pip python3-venv nginx supervisor

# 创建应用用户
sudo useradd -m -s /bin/bash worldwar
sudo su - worldwar
```

#### 应用部署
```bash
# 克隆应用
git clone <repository-url> /home/<USER>/worldwar-game
cd /home/<USER>/worldwar-game

# 创建生产环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r worldwar-server/requirements.txt
pip install gunicorn
```

### 2. 配置文件设置

#### 服务器配置
```json
{
  "server": {
    "host": "0.0.0.0",
    "port": 8888,
    "max_connections": 100,
    "timeout": 30
  },
  "logging": {
    "level": "INFO",
    "file": "/var/log/worldwar/server.log",
    "max_size": "100MB",
    "backup_count": 5
  },
  "database": {
    "type": "sqlite",
    "path": "/home/<USER>/data/worldwar.db"
  },
  "cache": {
    "enabled": true,
    "max_size": "256MB",
    "ttl": 3600
  }
}
```

#### Nginx 配置
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8888;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static/ {
        alias /home/<USER>/worldwar-game/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

#### Supervisor 配置
```ini
[program:worldwar-server]
command=/home/<USER>/worldwar-game/venv/bin/python server_main.py
directory=/home/<USER>/worldwar-game/worldwar-server
user=worldwar
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/worldwar/server.log
environment=WORLDWAR_ENV=production
```

### 3. 启动服务
```bash
# 启动 Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# 启动 Supervisor
sudo systemctl start supervisor
sudo systemctl enable supervisor

# 重新加载配置
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start worldwar-server
```

### 4. SSL 配置 (可选)
```bash
# 安装 Certbot
sudo apt install certbot python3-certbot-nginx

# 获取 SSL 证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

---

## ⚙️ 配置管理

### 环境变量
```bash
# 服务器配置
export WORLDWAR_ENV=production
export WORLDWAR_CONFIG=/path/to/config.json
export WORLDWAR_LOG_LEVEL=INFO

# 数据库配置
export WORLDWAR_DB_PATH=/path/to/database.db

# API 配置
export WORLDWAR_API_TIMEOUT=30
export WORLDWAR_API_RETRY=3

# 缓存配置
export WORLDWAR_CACHE_SIZE=256MB
export WORLDWAR_CACHE_TTL=3600
```

### 配置文件模板
```json
{
  "server": {
    "host": "${WORLDWAR_HOST:0.0.0.0}",
    "port": "${WORLDWAR_PORT:8888}",
    "max_connections": "${WORLDWAR_MAX_CONN:50}",
    "timeout": "${WORLDWAR_TIMEOUT:30}"
  },
  "game": {
    "max_players": "${WORLDWAR_MAX_PLAYERS:8}",
    "default_language": "${WORLDWAR_DEFAULT_LANG:chinese}",
    "terrain_cache_size": "${WORLDWAR_TERRAIN_CACHE:100}"
  },
  "api": {
    "world_bank_key": "${WORLD_BANK_API_KEY}",
    "geographic_key": "${GEOGRAPHIC_API_KEY}",
    "timeout": "${API_TIMEOUT:30}",
    "retry_count": "${API_RETRY:3}"
  }
}
```

---

## 📊 监控和维护

### 1. 系统监控
```bash
# 启动系统监控
python tools/system_monitor.py --daemon

# 性能监控
python tools/performance_monitor.py --continuous

# 日志监控
python tools/log_analyzer.py --watch /var/log/worldwar/
```

### 2. 健康检查脚本
```bash
#!/bin/bash
# health_check.sh

# 检查服务器进程
if ! pgrep -f "server_main.py" > /dev/null; then
    echo "ERROR: Server process not running"
    exit 1
fi

# 检查端口
if ! nc -z localhost 8888; then
    echo "ERROR: Server port not accessible"
    exit 1
fi

# 检查日志错误
if grep -q "ERROR" /var/log/worldwar/server.log; then
    echo "WARNING: Errors found in log"
fi

echo "OK: All checks passed"
```

### 3. 备份脚本
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backup/worldwar"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
cp /home/<USER>/data/worldwar.db $BACKUP_DIR/worldwar_$DATE.db

# 备份配置文件
tar -czf $BACKUP_DIR/config_$DATE.tar.gz /home/<USER>/worldwar-game/worldwar-server/config/

# 备份日志
tar -czf $BACKUP_DIR/logs_$DATE.tar.gz /var/log/worldwar/

# 清理旧备份（保留7天）
find $BACKUP_DIR -name "*.db" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
```

### 4. 自动更新脚本
```bash
#!/bin/bash
# update.sh

cd /home/<USER>/worldwar-game

# 拉取最新代码
git pull origin main

# 更新依赖
source venv/bin/activate
pip install -r worldwar-server/requirements.txt

# 运行测试
python -m pytest tests/unit/ -q

# 重启服务
sudo supervisorctl restart worldwar-server

echo "Update completed"
```

---

## 🔧 故障排除

### 常见问题

#### 1. 服务器无法启动
```bash
# 检查端口占用
netstat -tulpn | grep 8888

# 检查配置文件
python -c "import json; json.load(open('config.json'))"

# 检查权限
ls -la /var/log/worldwar/

# 查看详细错误
python worldwar-server/server_main.py --debug
```

#### 2. 客户端连接失败
```bash
# 检查网络连接
telnet server-ip 8888

# 检查防火墙
sudo ufw status

# 检查服务器日志
tail -f /var/log/worldwar/server.log
```

#### 3. 性能问题
```bash
# 监控系统资源
python tools/system_monitor.py --report

# 分析性能瓶颈
python tools/performance_monitor.py --profile

# 检查内存泄漏
python tools/performance_monitor.py --memory-check
```

#### 4. 地形生成失败
```bash
# 检查API连接
python tools/api_test.py

# 验证缓存
python -c "from worldwar_server.world.data.data_cache import DataCache; cache = DataCache(); print(cache.get_stats())"

# 测试地形生成
python tools/terrain_preview.py --test
```

### 日志分析

#### 错误级别
- **ERROR**: 严重错误，需要立即处理
- **WARNING**: 警告信息，需要关注
- **INFO**: 一般信息，正常运行状态
- **DEBUG**: 调试信息，开发时使用

#### 常见错误模式
```bash
# 连接错误
grep "Connection" /var/log/worldwar/server.log

# API错误
grep "API" /var/log/worldwar/server.log

# 内存错误
grep "Memory" /var/log/worldwar/server.log

# 地形生成错误
grep "Terrain" /var/log/worldwar/server.log
```

### 性能优化

#### 服务器优化
```python
# 配置文件优化
{
  "server": {
    "worker_processes": 4,
    "max_connections": 100,
    "keep_alive_timeout": 30,
    "buffer_size": "64KB"
  },
  "cache": {
    "enabled": true,
    "max_size": "512MB",
    "compression": true
  }
}
```

#### 数据库优化
```bash
# SQLite 优化
PRAGMA journal_mode = WAL;
PRAGMA synchronous = NORMAL;
PRAGMA cache_size = 10000;
PRAGMA temp_store = memory;
```

---

## 📞 支持和联系

如果在部署过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查项目的 Issues 页面
3. 运行诊断工具：`python tools/system_monitor.py --diagnose`
4. 提交详细的错误报告

---

**最后更新**: 2024年1月
**版本**: v5.0