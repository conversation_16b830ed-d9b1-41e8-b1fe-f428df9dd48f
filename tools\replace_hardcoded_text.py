#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
硬编码文本替换工具
Hardcoded Text Replacement Tool

扫描并替换源代码中的硬编码中英文文本为语言管理器调用
"""

import os
import re
import json
from pathlib import Path
from typing import Dict, List, Tuple, Set
import argparse


class HardcodedTextReplacer:
    """硬编码文本替换器"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.language_files = {}
        self.replacements = []
        self.new_keys = {}
        
        # 加载现有语言文件
        self._load_language_files()
        
        # 定义替换映射
        self._define_replacements()
    
    def _load_language_files(self):
        """加载语言文件"""
        languages_dir = self.project_root / "worldwar-server" / "languages"
        
        for lang_file in ["chinese.json", "english.json"]:
            file_path = languages_dir / lang_file
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    lang_name = lang_file.replace('.json', '')
                    self.language_files[lang_name] = json.load(f)
    
    def _define_replacements(self):
        """定义文本替换映射"""
        # 启动器相关替换
        launcher_replacements = [
            # 标题和菜单
            (r'"🎮 WorldWar 世界大战策略游戏启动器"', 'lang_manager.get_text("launcher.title")'),
            (r'"🎯 World War Strategy Game Launcher"', 'lang_manager.get_text("launcher.subtitle")'),
            (r'"请选择启动模式 / Please select launch mode:"', 'lang_manager.get_text("launcher.select_mode")'),
            (r'"1\. 🖥️  启动服务器 / Start Server"', 'lang_manager.get_text("launcher.start_server")'),
            (r'"2\. 🎮 启动客户端 / Start Client"', 'lang_manager.get_text("launcher.start_client")'),
            (r'"3\. 🧪 运行测试 / Run Tests"', 'lang_manager.get_text("launcher.run_tests")'),
            (r'"4\. 📚 查看帮助 / Show Help"', 'lang_manager.get_text("launcher.show_help")'),
            (r'"5\. 🚀 快速开始 / Quick Start"', 'lang_manager.get_text("launcher.quick_start")'),
            (r'"0\. ❌ 退出 / Exit"', 'lang_manager.get_text("launcher.exit")'),
            
            # 状态消息
            (r'f"❌ 服务器脚本未找到: {([^}]+)}"', r'lang_manager.get_text("launcher.server_script_not_found", script_path=\1)'),
            (r'f"❌ 客户端脚本未找到: {([^}]+)}"', r'lang_manager.get_text("launcher.client_script_not_found", script_path=\1)'),
            (r'"🖥️ 启动服务器\.\.\."', 'lang_manager.get_text("launcher.starting_server")'),
            (r'"🎮 启动客户端\.\.\."', 'lang_manager.get_text("launcher.starting_client")'),
            (r'"🛑 客户端已退出"', 'lang_manager.get_text("launcher.client_exited")'),
            (r'f"❌ 启动客户端失败: {([^}]+)}"', r'lang_manager.get_text("launcher.client_start_failed", error=\1)'),
            
            # 测试相关
            (r'"🧪 运行功能测试\.\.\."', 'lang_manager.get_text("launcher.running_tests")'),
            (r'"✅ 测试服务器启动脚本\.\.\."', 'lang_manager.get_text("launcher.test_server_script")'),
            (r'"✅ 测试客户端启动脚本\.\.\."', 'lang_manager.get_text("launcher.test_client_script")'),
            (r'"❌ 服务器启动脚本不存在!"', 'lang_manager.get_text("launcher.server_script_missing")'),
            (r'"❌ 客户端启动脚本不存在!"', 'lang_manager.get_text("launcher.client_script_missing")'),
            (r'"✅ 所有启动脚本检查通过!"', 'lang_manager.get_text("launcher.all_scripts_ok")'),
            
            # 帮助相关
            (r'"📚 WorldWar游戏帮助"', 'lang_manager.get_text("launcher.help_title")'),
            (r'"🎯 游戏简介:"', 'lang_manager.get_text("launcher.game_intro")'),
            (r'"WorldWar是一个多人在线策略游戏，支持中英文双语界面。"', 'lang_manager.get_text("launcher.game_description")'),
            (r'"玩家可以连接到服务器，与其他玩家进行实时互动。"', 'lang_manager.get_text("launcher.game_interaction")'),
            (r'"🚀 快速开始:"', 'lang_manager.get_text("launcher.quick_start_title")'),
            (r'"1\. 选择 \'1\' 启动服务器"', 'lang_manager.get_text("launcher.quick_start_step1")'),
            (r'"2\. 在另一个终端选择 \'2\' 启动客户端"', 'lang_manager.get_text("launcher.quick_start_step2")'),
            (r'"3\. 在客户端选择 \'1\. 连接服务器\'"', 'lang_manager.get_text("launcher.quick_start_step3")'),
            (r'"4\. 开始游戏!"', 'lang_manager.get_text("launcher.quick_start_step4")'),
        ]
        
        # 客户端相关替换
        client_replacements = [
            # 初始化消息
            (r'f"🎮 世界大战游戏客户端已初始化"', 'lang_manager.get_text("client.initialized")'),
            (r'f"📡 服务器: {([^}]+)}:{([^}]+)}"', r'lang_manager.get_text("client.server_info", host=\1, port=\2)'),
            (r'f"👤 用户名: {([^}]+)}"', r'lang_manager.get_text("client.username_info", username=\1)'),
            
            # 启动和连接
            (r'"🚀 正在启动客户端\.\.\."', 'lang_manager.get_text("client.starting")'),
            (r'"❌ 连接服务器失败，程序退出"', 'lang_manager.get_text("client.connection_failed")'),
            (r'"✅ 成功连接到服务器"', 'lang_manager.get_text("client.connected_success")'),
            (r'"📋 正在获取房间列表\.\.\."', 'lang_manager.get_text("client.getting_rooms")'),
            (r'f"🔄 连接尝试 {([^}]+)}/{([^}]+)}\.\.\."', r'lang_manager.get_text("client.connection_attempt", attempt=\1, max_attempts=\2)'),
            (r'"⏳ 等待3秒后重试\.\.\."', 'lang_manager.get_text("client.retry_wait")'),
            
            # 主界面
            (r'"🎮 世界大战游戏客户端"', 'lang_manager.get_text("client.title")'),
            (r'"📝 输入 \'help\' 查看可用命令"', 'lang_manager.get_text("client.help_command")'),
            (r'"🛑 输入 \'quit\' 退出游戏"', 'lang_manager.get_text("client.quit_command")'),
            (r'f"❓ 未知命令: {([^}]+)}"', r'lang_manager.get_text("client.unknown_command", command=\1)'),
            (r'"💡 输入 \'help\' 查看可用命令"', 'lang_manager.get_text("client.help_tip")'),
            
            # 错误和退出
            (r'"🛑 收到中断信号"', 'lang_manager.get_text("client.interrupt_signal")'),
            (r'"🛑 输入结束"', 'lang_manager.get_text("client.input_ended")'),
            (r'f"❌ 处理输入时发生错误: {([^}]+)}"', r'lang_manager.get_text("client.input_error", error=\1)'),
            (r'"👋 正在退出游戏\.\.\."', 'lang_manager.get_text("client.exiting")'),
            (r'"📤 正在离开房间\.\.\."', 'lang_manager.get_text("client.leaving_room")'),
            (r'"✅ 已安全退出"', 'lang_manager.get_text("client.safely_exited")'),
            
            # 帮助信息
            (r'"📖 帮助信息"', 'lang_manager.get_text("client.help_info")'),
            (r'"🔧 通用命令:"', 'lang_manager.get_text("client.general_commands")'),
            (r'"help, h          - 显示此帮助信息"', 'lang_manager.get_text("client.help_desc")'),
            (r'"clear, cls       - 清屏"', 'lang_manager.get_text("client.clear_desc")'),
            (r'"quit, exit, q    - 退出游戏"', 'lang_manager.get_text("client.quit_desc")'),
            (r'"status           - 显示连接状态"', 'lang_manager.get_text("client.status_desc")'),
            (r'"rooms            - 刷新房间列表"', 'lang_manager.get_text("client.rooms_desc")'),
            
            # 房间命令
            (r'"🏠 房间列表命令:"', 'lang_manager.get_text("client.room_list_commands")'),
            (r'"join <序号>      - 加入指定房间"', 'lang_manager.get_text("client.join_desc")'),
            (r'"create <房间名>  - 创建新房间"', 'lang_manager.get_text("client.create_desc")'),
            (r'"refresh          - 刷新房间列表"', 'lang_manager.get_text("client.refresh_desc")'),
            (r'"⏳ 等待房间命令:"', 'lang_manager.get_text("client.waiting_room_commands")'),
            (r'"ready            - 切换准备状态"', 'lang_manager.get_text("client.ready_desc")'),
            (r'"start            - 开始游戏 \(仅房主\)"', 'lang_manager.get_text("client.start_desc")'),
            (r'"leave            - 离开房间"', 'lang_manager.get_text("client.leave_desc")'),
            
            # 程序状态
            (r'"🛑 程序被用户中断"', 'lang_manager.get_text("client.program_interrupted")'),
            (r'f"❌ 程序发生错误: {([^}]+)}"', r'lang_manager.get_text("client.program_error", error=\1)'),
            (r'"👋 程序已退出"', 'lang_manager.get_text("client.program_exited")'),
            (r'f"未提供用户名，已为您生成随机用户名: {([^}]+)}"', r'lang_manager.get_text("client.username_generated", username=\1)'),
            
            # 网络错误
            (r'f"连接失败: {([^}]+)}"', r'lang_manager.get_text("client.connection_failed_error", error=\1)'),
            (r'f"发送消息失败: {([^}]+)}"', r'lang_manager.get_text("client.send_message_failed", error=\1)'),
            (r'f"JSON解析错误: {([^}]+)}"', r'lang_manager.get_text("client.json_parse_error", error=\1)'),
            (r'f"UI更新回调错误: {([^}]+)}"', r'lang_manager.get_text("client.ui_update_callback_error", error=\1)'),
            (r'f"UI更新工作线程错误: {([^}]+)}"', r'lang_manager.get_text("client.ui_update_thread_error", error=\1)'),
        ]
        
        self.replacements = launcher_replacements + client_replacements
    
    def scan_files(self, file_patterns: List[str]) -> List[Path]:
        """扫描匹配的文件"""
        files = []
        for pattern in file_patterns:
            files.extend(self.project_root.glob(pattern))
        return files
    
    def process_file(self, file_path: Path) -> bool:
        """处理单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            changes_made = False
            
            # 应用替换
            for pattern, replacement in self.replacements:
                new_content = re.sub(pattern, replacement, content)
                if new_content != content:
                    content = new_content
                    changes_made = True
                    print(f"  替换: {pattern} -> {replacement}")
            
            # 如果有变更，写回文件
            if changes_made:
                # 添加语言管理器导入（如果需要）
                content = self._add_language_manager_import(content, file_path)
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✅ 已更新: {file_path}")
                return True
            else:
                print(f"⏭️  无需更新: {file_path}")
                return False
                
        except Exception as e:
            print(f"❌ 处理文件失败 {file_path}: {e}")
            return False
    
    def _add_language_manager_import(self, content: str, file_path: Path) -> str:
        """添加语言管理器导入"""
        # 检查是否已经有导入
        if 'enhanced_language_manager' in content or 'language_manager' in content:
            return content
        
        # 确定导入路径
        if 'worldwar-server' in str(file_path):
            import_line = "from shared.enhanced_language_manager import get_enhanced_language_manager\n"
            manager_init = 'lang_manager = get_enhanced_language_manager("server")\n'
        elif 'worldwar-client' in str(file_path):
            import_line = "from shared.enhanced_language_manager import get_enhanced_language_manager\n"
            manager_init = 'lang_manager = get_enhanced_language_manager("client")\n'
        else:
            import_line = "from worldwar-server.shared.enhanced_language_manager import get_enhanced_language_manager\n"
            manager_init = 'lang_manager = get_enhanced_language_manager("server")\n'
        
        # 查找合适的位置插入导入
        lines = content.split('\n')
        import_inserted = False
        manager_inserted = False
        
        for i, line in enumerate(lines):
            # 在其他导入后插入
            if line.startswith('import ') or line.startswith('from '):
                continue
            elif not import_inserted and (line.strip() == '' or not line.startswith('#')):
                lines.insert(i, import_line.rstrip())
                import_inserted = True
                break
        
        # 如果没找到合适位置，在文件开头插入
        if not import_inserted:
            lines.insert(0, import_line.rstrip())
        
        # 在类或函数定义前插入管理器初始化
        for i, line in enumerate(lines):
            if line.startswith('class ') or line.startswith('def ') and not manager_inserted:
                lines.insert(i, manager_init.rstrip())
                manager_inserted = True
                break
        
        return '\n'.join(lines)
    
    def run(self, file_patterns: List[str] = None):
        """运行替换过程"""
        if file_patterns is None:
            file_patterns = [
                "start_game.py",
                "worldwar-client/**/*.py",
                "worldwar-server/**/*.py"
            ]
        
        print("🔍 扫描硬编码文本...")
        files = self.scan_files(file_patterns)
        
        print(f"📁 找到 {len(files)} 个文件")
        
        updated_count = 0
        for file_path in files:
            # 跳过测试文件和语言文件
            if 'test' in str(file_path).lower() or 'language' in str(file_path).lower():
                continue
                
            print(f"\n🔧 处理: {file_path}")
            if self.process_file(file_path):
                updated_count += 1
        
        print(f"\n✅ 完成! 更新了 {updated_count} 个文件")


def main():
    parser = argparse.ArgumentParser(description="替换硬编码文本为语言管理器调用")
    parser.add_argument("--root", type=Path, default=Path.cwd(), help="项目根目录")
    parser.add_argument("--patterns", nargs="+", help="文件匹配模式")
    parser.add_argument("--dry-run", action="store_true", help="仅显示将要进行的更改，不实际修改文件")
    
    args = parser.parse_args()
    
    replacer = HardcodedTextReplacer(args.root)
    replacer.run(args.patterns)


if __name__ == "__main__":
    main()