#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WorldWar游戏启动器 (简化版)
WorldWar Game Launcher (Simplified)
"""

import sys
import subprocess
import time
from pathlib import Path

class GameLauncher:
    """游戏启动器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.server_dir = self.project_root / "worldwar-server"
        self.client_dir = self.project_root / "worldwar-client"
    
    def show_menu(self):
        """显示启动菜单"""
        print("=" * 60)
        print("🎮 WorldWar 世界大战策略游戏启动器")
        print("🎯 World War Strategy Game Launcher")
        print("=" * 60)
        print()
        print("请选择启动模式 / Please select launch mode:")
        print()
        print("1. 🖥️  启动服务器 / Start Server")
        print("2. 🎮 启动客户端 / Start Client")
        print("3. 🧪 运行测试 / Run Tests")
        print("4. 📚 查看帮助 / Show Help")
        print("5. 🚀 快速开始 / Quick Start")
        print("0. ❌ 退出 / Exit")
        print()
    
    def start_server(self, in_new_window=True):
        """启动服务器"""
        print("🖥️ 启动服务器...")
        server_script = self.server_dir / "server_main.py"
        if not server_script.exists():
            print(f"❌ 服务器脚本未找到: {server_script}")
            return None

        try:
            if in_new_window:
                if sys.platform == "win32":
                    return subprocess.Popen([sys.executable, str(server_script)], creationflags=subprocess.CREATE_NEW_CONSOLE)
                else:
                    # For macOS and Linux
                    return subprocess.Popen([sys.executable, str(server_script)], start_new_session=True)
            else:
                return subprocess.Popen([sys.executable, str(server_script)])
        except Exception as e:
            print(f"❌ 启动服务器失败: {e}")
            return None
    
    def start_client(self):
        """启动客户端"""
        print("🎮 启动客户端...")
        client_script = self.client_dir / "client_main.py"
        if not client_script.exists():
            print(f"❌ 客户端脚本未找到: {client_script}")
            return None
        
        try:
            # 在当前窗口运行客户端
            process = subprocess.Popen([sys.executable, str(client_script)])
            process.wait()
        except KeyboardInterrupt:
            print("🛑 客户端已退出")
        except Exception as e:
            print(f"❌ 启动客户端失败: {e}")
    
    def run_tests(self):
        """运行测试"""
        print("🧪 运行功能测试...")
        print("=" * 40)

        # 检查服务器和客户端是否可以正常启动
        print("✅ 测试服务器启动脚本...")
        server_script = self.project_root / "worldwar-server" / "server_main.py"
        if not server_script.exists():
            print("❌ 服务器启动脚本不存在!")
            return False

        print("✅ 测试客户端启动脚本...")
        client_script = self.project_root / "worldwar-client" / "client_main.py"
        if not client_script.exists():
            print("❌ 客户端启动脚本不存在!")
            return False

        print("✅ 所有启动脚本检查通过!")
        print("💡 提示: 使用 'python worldwar-server/server_main.py' 启动服务器")
        print("💡 提示: 使用 'python worldwar-client/client_main.py' 启动客户端")
        return True
    
    def show_help(self):
        """显示帮助"""
        print("\n📚 WorldWar游戏帮助")
        print("=" * 40)
        print()
        print("🎯 游戏简介:")
        print("   WorldWar是一个多人在线策略游戏，支持中英文双语界面。")
        print("   玩家可以连接到服务器，与其他玩家进行实时互动。")
        print()
        print("🚀 快速开始:")
        print("   1. 选择 '1' 启动服务器")
        print("   2. 在另一个终端选择 '2' 启动客户端")
        print("   3. 在客户端选择 '1. 连接服务器'")
        print("   4. 开始游戏!")
        print()
        print("🔧 高级用法:")
        print("   • 服务器支持自定义地址和端口")
        print("   • 客户端支持指定默认服务器")
        print("   • 支持多个客户端同时连接")
        print()
        print("🌐 语言支持:")
        print("   • 中文界面")
        print("   • English interface")
        print("   • 双语模式 (中文 / English)")
        print()
        print("📞 技术支持:")
        print("   • 查看 README.md 了解详细信息")
        print("   • 运行测试检查系统状态")
        print()
    
    def quick_start(self):
        """快速开始"""
        print("\n🚀 快速开始模式...")
        server_process = self.start_server()
        if not server_process:
            return

        print("\n⏳ 等待服务器启动...")
        time.sleep(3)

        self.start_client()

        # 清理服务器进程
        print("\n🛑 正在关闭服务器...")
        server_process.terminate()
        try:
            server_process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            server_process.kill()
    
    def run(self):
        """运行启动器"""
        while True:
            try:
                self.show_menu()
                choice = input("请选择 / Please select (0-5): ").strip()
                
                if choice == "1":
                    self.start_server()
                elif choice == "2":
                    self.start_client()
                elif choice == "3":
                    self.run_tests()
                elif choice == "4":
                    self.show_help()
                elif choice == "5":
                    self.quick_start()
                elif choice == "0":
                    print("\n👋 再见! / Goodbye!")
                    break
                else:
                    print("\n❌ 无效选择，请重试 / Invalid choice, please try again")
                
                if choice != "0":
                    input("\n按回车键返回主菜单 / Press Enter to return to main menu...")
                
            except KeyboardInterrupt:
                print("\n\n👋 再见! / Goodbye!")
                break
            except Exception as e:
                print(f"\n❌ 错误: {e}")
                input("按回车键继续 / Press Enter to continue...")

def main():
    """主函数"""
    launcher = GameLauncher()
    launcher.run()

if __name__ == "__main__":
    main()
