#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语言文件管理工具
Language File Management Tool

检查语言文件完整性、生成模板、同步翻译等功能
"""

import json
import argparse
import sys
from pathlib import Path
from typing import Dict, Set, List, Optional, Any
from dataclasses import dataclass, asdict
import re
from collections import defaultdict


@dataclass
class LanguageStats:
    """语言统计信息"""
    total_keys: int
    missing_keys: int
    extra_keys: int
    empty_values: int
    completion_rate: float


@dataclass
class ValidationReport:
    """验证报告"""
    language: str
    is_valid: bool
    stats: LanguageStats
    missing_keys: List[str]
    extra_keys: List[str]
    empty_keys: List[str]
    errors: List[str]
    warnings: List[str]


class LanguageFileManager:
    """语言文件管理器"""
    
    def __init__(self, languages_dir: Path):
        self.languages_dir = Path(languages_dir)
        self.languages = {}
        self.supported_languages = ["chinese", "english"]
        
        # 确保语言目录存在
        self.languages_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载现有语言文件
        self._load_languages()
    
    def _load_languages(self):
        """加载所有语言文件"""
        self.languages.clear()
        
        for lang in self.supported_languages:
            lang_file = self.languages_dir / f"{lang}.json"
            if lang_file.exists():
                try:
                    with open(lang_file, 'r', encoding='utf-8') as f:
                        self.languages[lang] = json.load(f)
                except Exception as e:
                    print(f"❌ 加载语言文件失败 {lang_file}: {e}")
                    self.languages[lang] = {}
            else:
                print(f"⚠️  语言文件不存在: {lang_file}")
                self.languages[lang] = {}
    
    def _get_all_keys(self, data: Dict, prefix: str = "") -> Set[str]:
        """递归获取所有键"""
        keys = set()
        
        if isinstance(data, dict):
            for key, value in data.items():
                full_key = f"{prefix}.{key}" if prefix else key
                if isinstance(value, dict):
                    keys.update(self._get_all_keys(value, full_key))
                else:
                    keys.add(full_key)
        
        return keys
    
    def _get_value_by_key(self, data: Dict, key: str) -> Any:
        """根据键路径获取值"""
        keys = key.split('.')
        current = data
        
        try:
            for k in keys:
                if isinstance(current, dict) and k in current:
                    current = current[k]
                else:
                    return None
            return current
        except Exception:
            return None
    
    def _set_value_by_key(self, data: Dict, key: str, value: Any):
        """根据键路径设置值"""
        keys = key.split('.')
        current = data
        
        # 创建嵌套字典结构
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            elif not isinstance(current[k], dict):
                current[k] = {}
            current = current[k]
        
        # 设置最终值
        current[keys[-1]] = value
    
    def _find_empty_keys(self, data: Dict, prefix: str = "") -> Set[str]:
        """查找空值的键"""
        empty_keys = set()
        
        if isinstance(data, dict):
            for key, value in data.items():
                full_key = f"{prefix}.{key}" if prefix else key
                if isinstance(value, dict):
                    empty_keys.update(self._find_empty_keys(value, full_key))
                elif not value or (isinstance(value, str) and not value.strip()):
                    empty_keys.add(full_key)
        
        return empty_keys
    
    def check_completeness(self) -> Dict[str, ValidationReport]:
        """检查语言文件完整性"""
        print("🔍 检查语言文件完整性...")
        
        # 获取所有语言的所有键
        all_keys = set()
        for lang_data in self.languages.values():
            all_keys.update(self._get_all_keys(lang_data))
        
        reports = {}
        
        for lang in self.supported_languages:
            lang_data = self.languages.get(lang, {})
            lang_keys = self._get_all_keys(lang_data)
            
            # 计算缺失和额外的键
            missing_keys = list(all_keys - lang_keys)
            extra_keys = list(lang_keys - all_keys)
            empty_keys = list(self._find_empty_keys(lang_data))
            
            # 计算统计信息
            total_keys = len(all_keys)
            missing_count = len(missing_keys)
            extra_count = len(extra_keys)
            empty_count = len(empty_keys)
            completion_rate = ((total_keys - missing_count) / total_keys * 100) if total_keys > 0 else 0
            
            stats = LanguageStats(
                total_keys=total_keys,
                missing_keys=missing_count,
                extra_keys=extra_count,
                empty_values=empty_count,
                completion_rate=completion_rate
            )
            
            # 生成错误和警告
            errors = []
            warnings = []
            
            # 检查关键键是否缺失
            critical_keys = {
                "launcher.title", "launcher.start_server", "launcher.start_client",
                "client.title", "client.starting", "client.connection_failed",
                "server.welcome", "server.starting", "server.started"
            }
            
            missing_critical = set(missing_keys) & critical_keys
            if missing_critical:
                errors.extend([f"缺失关键键: {key}" for key in missing_critical])
            
            if missing_count > 0:
                warnings.append(f"缺失 {missing_count} 个翻译键")
            
            if empty_count > 0:
                warnings.append(f"有 {empty_count} 个空值")
            
            if extra_count > 0:
                warnings.append(f"有 {extra_count} 个额外键（可能已过时）")
            
            is_valid = len(errors) == 0 and missing_count == 0
            
            reports[lang] = ValidationReport(
                language=lang,
                is_valid=is_valid,
                stats=stats,
                missing_keys=missing_keys,
                extra_keys=extra_keys,
                empty_keys=empty_keys,
                errors=errors,
                warnings=warnings
            )
        
        return reports
    
    def generate_template(self, target_language: str, reference_language: str = "chinese") -> Dict[str, str]:
        """生成语言模板"""
        print(f"📝 为 {target_language} 生成模板（参考语言: {reference_language}）...")
        
        if reference_language not in self.languages:
            raise ValueError(f"参考语言 {reference_language} 不存在")
        
        reference_data = self.languages[reference_language]
        target_data = self.languages.get(target_language, {})
        
        reference_keys = self._get_all_keys(reference_data)
        target_keys = self._get_all_keys(target_data)
        
        missing_keys = reference_keys - target_keys
        template = {}
        
        for key in sorted(missing_keys):
            reference_value = self._get_value_by_key(reference_data, key)
            if reference_value:
                template[key] = f"[TRANSLATE: {reference_value}]"
            else:
                template[key] = f"[TRANSLATE: {key}]"
        
        return template
    
    def sync_languages(self, dry_run: bool = False) -> Dict[str, int]:
        """同步语言文件"""
        print("🔄 同步语言文件...")
        
        # 获取所有键的并集
        all_keys = set()
        for lang_data in self.languages.values():
            all_keys.update(self._get_all_keys(lang_data))
        
        sync_stats = {}
        
        for lang in self.supported_languages:
            lang_data = self.languages[lang]
            lang_keys = self._get_all_keys(lang_data)
            missing_keys = all_keys - lang_keys
            
            added_count = 0
            
            for key in missing_keys:
                if not dry_run:
                    # 尝试从其他语言获取参考值
                    reference_value = None
                    for other_lang, other_data in self.languages.items():
                        if other_lang != lang:
                            ref_val = self._get_value_by_key(other_data, key)
                            if ref_val:
                                reference_value = ref_val
                                break
                    
                    # 设置占位符值
                    placeholder = f"[TRANSLATE: {reference_value}]" if reference_value else f"[TRANSLATE: {key}]"
                    self._set_value_by_key(lang_data, key, placeholder)
                    added_count += 1
                else:
                    added_count += 1
            
            sync_stats[lang] = added_count
            
            if not dry_run and added_count > 0:
                self._save_language_file(lang)
        
        return sync_stats
    
    def _save_language_file(self, language: str):
        """保存语言文件"""
        lang_file = self.languages_dir / f"{language}.json"
        
        try:
            with open(lang_file, 'w', encoding='utf-8') as f:
                json.dump(self.languages[language], f, ensure_ascii=False, indent=2, sort_keys=True)
            print(f"✅ 已保存: {lang_file}")
        except Exception as e:
            print(f"❌ 保存失败 {lang_file}: {e}")
    
    def export_template_file(self, target_language: str, output_file: Path, reference_language: str = "chinese"):
        """导出模板文件"""
        template = self.generate_template(target_language, reference_language)
        
        if not template:
            print(f"✅ {target_language} 语言文件已完整，无需生成模板")
            return
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(template, f, ensure_ascii=False, indent=2, sort_keys=True)
            print(f"📄 模板已导出到: {output_file}")
            print(f"📊 包含 {len(template)} 个待翻译键")
        except Exception as e:
            print(f"❌ 导出模板失败: {e}")
    
    def import_translations(self, language: str, translation_file: Path):
        """导入翻译"""
        print(f"📥 导入 {language} 翻译...")
        
        try:
            with open(translation_file, 'r', encoding='utf-8') as f:
                translations = json.load(f)
            
            if language not in self.languages:
                self.languages[language] = {}
            
            imported_count = 0
            for key, value in translations.items():
                if not value.startswith("[TRANSLATE:"):  # 跳过未翻译的占位符
                    self._set_value_by_key(self.languages[language], key, value)
                    imported_count += 1
            
            self._save_language_file(language)
            print(f"✅ 已导入 {imported_count} 个翻译")
            
        except Exception as e:
            print(f"❌ 导入翻译失败: {e}")
    
    def find_unused_keys(self, source_dirs: List[Path]) -> Dict[str, Set[str]]:
        """查找未使用的键"""
        print("🔍 查找未使用的翻译键...")
        
        # 获取所有键
        all_keys = set()
        for lang_data in self.languages.values():
            all_keys.update(self._get_all_keys(lang_data))
        
        # 扫描源代码文件
        used_keys = set()
        
        for source_dir in source_dirs:
            if source_dir.exists():
                for py_file in source_dir.rglob("*.py"):
                    try:
                        with open(py_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        # 查找 get_text 调用
                        pattern = r'get_text\(["\']([^"\']+)["\']'
                        matches = re.findall(pattern, content)
                        used_keys.update(matches)
                        
                    except Exception as e:
                        print(f"⚠️  读取文件失败 {py_file}: {e}")
        
        # 计算未使用的键
        unused_keys = {}
        for lang in self.supported_languages:
            lang_keys = self._get_all_keys(self.languages[lang])
            unused_keys[lang] = lang_keys - used_keys
        
        return unused_keys
    
    def generate_report(self, output_file: Path = None):
        """生成完整报告"""
        print("📊 生成语言文件报告...")
        
        reports = self.check_completeness()
        
        report_lines = []
        report_lines.append("# 语言文件完整性报告")
        report_lines.append("# Language File Completeness Report")
        report_lines.append("")
        report_lines.append(f"生成时间: {Path(__file__).stat().st_mtime}")
        report_lines.append("")
        
        # 总体统计
        report_lines.append("## 总体统计 / Overall Statistics")
        report_lines.append("")
        
        for lang, report in reports.items():
            status = "✅ 完整" if report.is_valid else "❌ 不完整"
            report_lines.append(f"### {lang.title()} ({status})")
            report_lines.append("")
            report_lines.append(f"- 总键数: {report.stats.total_keys}")
            report_lines.append(f"- 缺失键数: {report.stats.missing_keys}")
            report_lines.append(f"- 额外键数: {report.stats.extra_keys}")
            report_lines.append(f"- 空值数: {report.stats.empty_values}")
            report_lines.append(f"- 完成率: {report.stats.completion_rate:.1f}%")
            report_lines.append("")
            
            if report.errors:
                report_lines.append("**错误:**")
                for error in report.errors:
                    report_lines.append(f"- {error}")
                report_lines.append("")
            
            if report.warnings:
                report_lines.append("**警告:**")
                for warning in report.warnings:
                    report_lines.append(f"- {warning}")
                report_lines.append("")
            
            if report.missing_keys:
                report_lines.append("**缺失的键:**")
                for key in sorted(report.missing_keys)[:10]:  # 只显示前10个
                    report_lines.append(f"- {key}")
                if len(report.missing_keys) > 10:
                    report_lines.append(f"- ... 还有 {len(report.missing_keys) - 10} 个")
                report_lines.append("")
        
        report_content = "\n".join(report_lines)
        
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(report_content)
                print(f"📄 报告已保存到: {output_file}")
            except Exception as e:
                print(f"❌ 保存报告失败: {e}")
        else:
            print(report_content)


def main():
    parser = argparse.ArgumentParser(description="语言文件管理工具")
    parser.add_argument("--languages-dir", type=Path, 
                       default=Path.cwd() / "worldwar-server" / "languages",
                       help="语言文件目录")
    
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 检查命令
    check_parser = subparsers.add_parser("check", help="检查语言文件完整性")
    check_parser.add_argument("--report", type=Path, help="保存报告到文件")
    
    # 同步命令
    sync_parser = subparsers.add_parser("sync", help="同步语言文件")
    sync_parser.add_argument("--dry-run", action="store_true", help="仅显示将要进行的更改")
    
    # 模板命令
    template_parser = subparsers.add_parser("template", help="生成翻译模板")
    template_parser.add_argument("--target", required=True, help="目标语言")
    template_parser.add_argument("--reference", default="chinese", help="参考语言")
    template_parser.add_argument("--output", type=Path, help="输出文件")
    
    # 导入命令
    import_parser = subparsers.add_parser("import", help="导入翻译")
    import_parser.add_argument("--language", required=True, help="目标语言")
    import_parser.add_argument("--file", type=Path, required=True, help="翻译文件")
    
    # 查找未使用键命令
    unused_parser = subparsers.add_parser("unused", help="查找未使用的键")
    unused_parser.add_argument("--source-dirs", nargs="+", type=Path,
                              default=[Path.cwd() / "worldwar-server", Path.cwd() / "worldwar-client"],
                              help="源代码目录")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    manager = LanguageFileManager(args.languages_dir)
    
    if args.command == "check":
        reports = manager.check_completeness()
        
        print("\n📊 检查结果:")
        for lang, report in reports.items():
            status = "✅ 完整" if report.is_valid else "❌ 不完整"
            print(f"\n{lang.title()}: {status}")
            print(f"  完成率: {report.stats.completion_rate:.1f}%")
            print(f"  缺失键: {report.stats.missing_keys}")
            print(f"  空值: {report.stats.empty_values}")
            
            if report.errors:
                print("  错误:")
                for error in report.errors:
                    print(f"    - {error}")
            
            if report.warnings:
                print("  警告:")
                for warning in report.warnings:
                    print(f"    - {warning}")
        
        if args.report:
            manager.generate_report(args.report)
    
    elif args.command == "sync":
        stats = manager.sync_languages(args.dry_run)
        
        print("\n🔄 同步结果:")
        for lang, count in stats.items():
            action = "将添加" if args.dry_run else "已添加"
            print(f"  {lang}: {action} {count} 个键")
    
    elif args.command == "template":
        template = manager.generate_template(args.target, args.reference)
        
        if args.output:
            manager.export_template_file(args.target, args.output, args.reference)
        else:
            print(f"\n📝 {args.target} 翻译模板:")
            for key, value in sorted(template.items()):
                print(f"  {key}: {value}")
    
    elif args.command == "import":
        manager.import_translations(args.language, args.file)
    
    elif args.command == "unused":
        unused_keys = manager.find_unused_keys(args.source_dirs)
        
        print("\n🔍 未使用的键:")
        for lang, keys in unused_keys.items():
            if keys:
                print(f"\n{lang.title()}:")
                for key in sorted(keys):
                    print(f"  - {key}")
            else:
                print(f"\n{lang.title()}: 所有键都在使用中 ✅")


if __name__ == "__main__":
    main()