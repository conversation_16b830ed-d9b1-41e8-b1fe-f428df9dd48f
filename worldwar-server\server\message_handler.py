#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的消息处理器
Enhanced Message Handler

实现统一的消息处理器，支持中间件模式、消息路由和异步处理
Implements unified message handler with middleware support, message routing and async processing
"""

import asyncio
import time
import traceback
from typing import Dict, Any, Optional, Callable, List, Union
from dataclasses import dataclass
from enum import Enum

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from shared.message_types import Message, MessageType, MessageValidator, MessageFactory, ErrorCode
from shared.enhanced_logger import get_server_logger


class MiddlewareType(Enum):
    """中间件类型"""
    PRE_PROCESS = "pre_process"    # 预处理中间件
    POST_PROCESS = "post_process"  # 后处理中间件
    ERROR_HANDLER = "error_handler" # 错误处理中间件


@dataclass
class HandlerContext:
    """处理器上下文"""
    session: Any  # PlayerSession对象
    message: Message
    response: Optional[Message] = None
    error: Optional[Exception] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class MessageHandler:
    """统一消息处理器"""
    
    def __init__(self, protocol_version: str = "2.0"):
        """
        初始化消息处理器
        
        Args:
            protocol_version: 协议版本
        """
        self.protocol_version = protocol_version
        self.logger = get_server_logger("MessageHandler")
        
        # 消息处理器注册表
        self.handlers: Dict[str, Callable] = {}
        
        # 中间件列表
        self.middleware: Dict[MiddlewareType, List[Callable]] = {
            MiddlewareType.PRE_PROCESS: [],
            MiddlewareType.POST_PROCESS: [],
            MiddlewareType.ERROR_HANDLER: []
        }
        
        # 统计信息
        self.stats = {
            "messages_processed": 0,
            "messages_failed": 0,
            "handlers_registered": 0,
            "middleware_registered": 0
        }
        
        self.logger.info(f"消息处理器初始化完成 - 协议版本: {protocol_version}")
    
    def register_handler(self, message_type: Union[MessageType, str], 
                        handler: Callable[[HandlerContext], Union[Message, None]]) -> None:
        """
        注册消息处理器
        
        Args:
            message_type: 消息类型
            handler: 处理器函数，接收HandlerContext，返回Message或None
        """
        # 处理消息类型
        if isinstance(message_type, MessageType):
            msg_type = message_type.value
        else:
            msg_type = str(message_type)
        
        self.handlers[msg_type] = handler
        self.stats["handlers_registered"] += 1
        
        self.logger.debug(f"注册消息处理器: {msg_type}")
    
    def add_middleware(self, middleware_type: MiddlewareType, 
                      middleware: Callable[[HandlerContext], None]) -> None:
        """
        添加中间件
        
        Args:
            middleware_type: 中间件类型
            middleware: 中间件函数，接收HandlerContext
        """
        self.middleware[middleware_type].append(middleware)
        self.stats["middleware_registered"] += 1
        
        self.logger.debug(f"添加中间件: {middleware_type.value}")
    
    async def process_message(self, session: Any, message: Union[Message, Dict[str, Any]]) -> Optional[Message]:
        """
        处理消息
        
        Args:
            session: 会话对象
            message: 消息对象或字典
            
        Returns:
            响应消息或None
        """
        start_time = time.time()
        context = None
        
        try:
            # 转换消息格式
            if isinstance(message, dict):
                msg_obj = Message.from_dict(message)
            elif isinstance(message, Message):
                msg_obj = message
            else:
                raise ValueError(f"不支持的消息类型: {type(message)}")
            
            # 验证消息
            is_valid, error_msg = MessageValidator.validate_message(msg_obj)
            if not is_valid:
                self.logger.warning(f"消息验证失败: {error_msg}")
                self.stats["messages_failed"] += 1
                return MessageFactory.create_error_message(
                    ErrorCode.INVALID_ACTION,
                    f"消息格式错误: {error_msg}"
                )
            
            # 创建处理上下文
            context = HandlerContext(
                session=session,
                message=msg_obj,
                metadata={
                    "start_time": start_time,
                    "handler_name": self.__class__.__name__
                }
            )
            
            # 执行预处理中间件
            await self._execute_middleware(MiddlewareType.PRE_PROCESS, context)
            
            # 如果预处理中间件设置了错误，直接返回错误响应
            if context.error:
                raise context.error
            
            # 查找并执行消息处理器
            handler = self.handlers.get(msg_obj.type)
            if handler:
                self.logger.debug(f"处理消息: {msg_obj.type} (ID: {msg_obj.id})")
                
                # 执行处理器
                if asyncio.iscoroutinefunction(handler):
                    response = await handler(context)
                else:
                    response = handler(context)
                
                context.response = response
            else:
                self.logger.warning(f"未找到消息处理器: {msg_obj.type}")
                context.response = MessageFactory.create_error_message(
                    ErrorCode.INVALID_ACTION,
                    f"未知消息类型: {msg_obj.type}"
                )
            
            # 执行后处理中间件
            await self._execute_middleware(MiddlewareType.POST_PROCESS, context)
            
            # 更新统计信息
            self.stats["messages_processed"] += 1
            processing_time = time.time() - start_time
            context.metadata["processing_time"] = processing_time
            
            self.logger.debug(f"消息处理完成: {msg_obj.type} (耗时: {processing_time:.3f}s)")
            
            return context.response
            
        except Exception as e:
            self.logger.error(f"消息处理异常: {str(e)}\n{traceback.format_exc()}")
            self.stats["messages_failed"] += 1
            
            # 如果有上下文，设置错误并执行错误处理中间件
            if context:
                context.error = e
                try:
                    await self._execute_middleware(MiddlewareType.ERROR_HANDLER, context)
                    if context.response:
                        return context.response
                except Exception as middleware_error:
                    self.logger.error(f"错误处理中间件异常: {str(middleware_error)}")
            
            # 返回通用错误响应
            return MessageFactory.create_error_message(
                ErrorCode.INTERNAL_ERROR,
                "服务器内部错误"
            )
    
    async def _execute_middleware(self, middleware_type: MiddlewareType, context: HandlerContext) -> None:
        """
        执行中间件
        
        Args:
            middleware_type: 中间件类型
            context: 处理上下文
        """
        for middleware in self.middleware[middleware_type]:
            try:
                if asyncio.iscoroutinefunction(middleware):
                    await middleware(context)
                else:
                    middleware(context)
                    
                # 如果中间件设置了错误，停止执行后续中间件
                if context.error and middleware_type != MiddlewareType.ERROR_HANDLER:
                    break
                    
            except Exception as e:
                self.logger.error(f"中间件执行异常 [{middleware_type.value}]: {str(e)}")
                if middleware_type != MiddlewareType.ERROR_HANDLER:
                    context.error = e
                    break
    
    def get_registered_handlers(self) -> List[str]:
        """获取已注册的处理器列表"""
        return list(self.handlers.keys())
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.stats.copy()
    
    def clear_handlers(self) -> None:
        """清空所有处理器"""
        self.handlers.clear()
        self.stats["handlers_registered"] = 0
        self.logger.info("已清空所有消息处理器")
    
    def clear_middleware(self, middleware_type: Optional[MiddlewareType] = None) -> None:
        """
        清空中间件
        
        Args:
            middleware_type: 要清空的中间件类型，None表示清空所有
        """
        if middleware_type:
            self.middleware[middleware_type].clear()
            self.logger.info(f"已清空 {middleware_type.value} 中间件")
        else:
            for mw_type in self.middleware:
                self.middleware[mw_type].clear()
            self.stats["middleware_registered"] = 0
            self.logger.info("已清空所有中间件")


class DefaultMiddleware:
    """默认中间件集合"""
    
    @staticmethod
    def logging_middleware(context: HandlerContext) -> None:
        """日志记录中间件"""
        logger = get_server_logger("LoggingMiddleware")
        
        if context.error:
            logger.error(f"消息处理错误 - 类型: {context.message.type}, "
                        f"会话: {getattr(context.session, 'session_id', 'unknown')}, "
                        f"错误: {str(context.error)}")
        else:
            logger.info(f"处理消息 - 类型: {context.message.type}, "
                       f"会话: {getattr(context.session, 'session_id', 'unknown')}")
    
    @staticmethod
    def authentication_middleware(context: HandlerContext) -> None:
        """认证中间件"""
        # 不需要认证的消息类型
        public_messages = {
            MessageType.WELCOME.value,
            MessageType.JOIN_GAME.value,
            MessageType.PING.value,
            MessageType.PONG.value
        }
        
        if context.message.type not in public_messages:
            if not hasattr(context.session, 'is_authenticated') or not context.session.is_authenticated:
                context.error = ValueError("用户未认证")
    
    @staticmethod
    def rate_limiting_middleware(context: HandlerContext) -> None:
        """限流中间件"""
        # 简单的限流实现
        current_time = time.time()
        session_id = getattr(context.session, 'session_id', 'unknown')
        
        # 在会话对象上存储最后请求时间
        if not hasattr(context.session, '_last_request_time'):
            context.session._last_request_time = {}
        
        last_time = context.session._last_request_time.get(context.message.type, 0)
        
        # 某些消息类型的最小间隔（秒）
        min_intervals = {
            MessageType.CREATE_ROOM.value: 1.0,
            MessageType.JOIN_ROOM.value: 0.5,
            MessageType.GAME_ACTION.value: 0.1
        }
        
        min_interval = min_intervals.get(context.message.type, 0.0)
        
        if current_time - last_time < min_interval:
            context.error = ValueError(f"请求过于频繁，请等待 {min_interval} 秒")
        else:
            context.session._last_request_time[context.message.type] = current_time
    
    @staticmethod
    def validation_middleware(context: HandlerContext) -> None:
        """数据验证中间件"""
        # 验证消息数据的基本格式
        if not isinstance(context.message.data, dict):
            context.error = ValueError("消息数据必须是字典格式")
            return
        
        # 特定消息类型的数据验证
        msg_type = context.message.type
        data = context.message.data
        
        if msg_type == MessageType.CREATE_ROOM.value:
            if not data.get("room_name") or not isinstance(data.get("room_name"), str):
                context.error = ValueError("房间名称必须是非空字符串")
            elif not isinstance(data.get("max_players", 4), int) or data.get("max_players", 4) < 2:
                context.error = ValueError("最大玩家数必须是大于等于2的整数")
        
        elif msg_type == MessageType.JOIN_GAME.value:
            if not data.get("player_name") or not isinstance(data.get("player_name"), str):
                context.error = ValueError("玩家名称必须是非空字符串")
    
    @staticmethod
    def error_response_middleware(context: HandlerContext) -> None:
        """错误响应中间件"""
        if context.error and not context.response:
            # 根据错误类型创建适当的错误响应
            if isinstance(context.error, ValueError):
                error_code = ErrorCode.INVALID_ACTION
            elif isinstance(context.error, PermissionError):
                error_code = ErrorCode.INSUFFICIENT_PERMISSIONS
            else:
                error_code = ErrorCode.INTERNAL_ERROR
            
            context.response = MessageFactory.create_error_message(
                error_code,
                str(context.error),
                sender="server",
                session=getattr(context.session, 'session_id', None)
            )


class GameMessageHandler(MessageHandler):
    """游戏专用消息处理器"""
    
    def __init__(self, room_manager=None, session_manager=None, game_manager=None):
        """
        初始化游戏消息处理器
        
        Args:
            room_manager: 房间管理器
            session_manager: 会话管理器
            game_manager: 游戏管理器
        """
        super().__init__()
        
        self.room_manager = room_manager
        self.session_manager = session_manager
        self.game_manager = game_manager
        
        # 注册默认中间件
        self._register_default_middleware()
        
        # 注册默认处理器
        self._register_default_handlers()
        
        self.logger.info("游戏消息处理器初始化完成")
    
    def _register_default_middleware(self) -> None:
        """注册默认中间件"""
        # 预处理中间件
        self.add_middleware(MiddlewareType.PRE_PROCESS, DefaultMiddleware.logging_middleware)
        self.add_middleware(MiddlewareType.PRE_PROCESS, DefaultMiddleware.authentication_middleware)
        self.add_middleware(MiddlewareType.PRE_PROCESS, DefaultMiddleware.rate_limiting_middleware)
        self.add_middleware(MiddlewareType.PRE_PROCESS, DefaultMiddleware.validation_middleware)
        
        # 错误处理中间件
        self.add_middleware(MiddlewareType.ERROR_HANDLER, DefaultMiddleware.error_response_middleware)
    
    def _register_default_handlers(self) -> None:
        """注册默认处理器"""
        # 基础消息处理器
        self.register_handler(MessageType.PING, self._handle_ping)
        self.register_handler(MessageType.JOIN_GAME, self._handle_join_game)
        
        # 房间管理处理器
        self.register_handler(MessageType.CREATE_ROOM, self._handle_create_room)
        self.register_handler(MessageType.JOIN_ROOM, self._handle_join_room)
        self.register_handler(MessageType.LEAVE_ROOM, self._handle_leave_room)
        self.register_handler(MessageType.GET_ROOM_LIST, self._handle_get_room_list)
        
        # 游戏处理器
        self.register_handler(MessageType.GAME_ACTION, self._handle_game_action)
        self.register_handler(MessageType.CHAT_MESSAGE, self._handle_chat_message)
    
    def _handle_ping(self, context: HandlerContext) -> Message:
        """处理心跳消息"""
        return MessageFactory.create_message(
            MessageType.PONG,
            {"timestamp": time.time()},
            sender="server",
            session=context.session.session_id if hasattr(context.session, 'session_id') else None
        )
    
    def _handle_join_game(self, context: HandlerContext) -> Message:
        """处理加入游戏请求"""
        if not self.session_manager:
            return MessageFactory.create_error_message(
                ErrorCode.INTERNAL_ERROR,
                "会话管理器未初始化"
            )
        
        player_name = context.message.data.get("player_name")
        session_id = getattr(context.session, 'session_id', None)
        
        # 使用会话管理器进行认证
        auth_result = self.session_manager.authenticate_session(session_id, player_name)
        
        if auth_result.get("success"):
            return MessageFactory.create_success_message(
                {
                    "player_name": player_name,
                    "session_id": session_id,
                    "available_rooms": self.room_manager.get_room_list() if self.room_manager else []
                },
                sender="server",
                session=session_id
            )
        else:
            return MessageFactory.create_error_message(
                ErrorCode.INVALID_CREDENTIALS,
                auth_result.get("message", "认证失败"),
                sender="server",
                session=session_id
            )
    
    def _handle_create_room(self, context: HandlerContext) -> Message:
        """处理创建房间请求"""
        if not self.room_manager:
            return MessageFactory.create_error_message(
                ErrorCode.INTERNAL_ERROR,
                "房间管理器未初始化"
            )
        
        data = context.message.data
        room_name = data.get("room_name")
        max_players = data.get("max_players", 4)
        game_mode = data.get("game_mode", "ai_generated")
        
        session_id = getattr(context.session, 'session_id', None)
        
        # 创建房间
        room = self.room_manager.create_room(
            room_name, session_id, max_players, game_mode
        )
        
        if room:
            # 房主自动加入房间
            join_result = room.add_player(context.session)
            
            if join_result.get("success"):
                context.session.current_room = room.room_id
                
                return MessageFactory.create_message(
                    MessageType.ROOM_CREATED,
                    {
                        "room_id": room.room_id,
                        "room_name": room_name,
                        "is_host": True,
                        "max_players": max_players,
                        "game_mode": game_mode,
                        "status": "waiting"
                    },
                    sender="server",
                    session=session_id,
                    room_id=room.room_id
                )
            else:
                return MessageFactory.create_error_message(
                    ErrorCode.ROOM_FULL,
                    join_result.get("message", "加入房间失败")
                )
        else:
            return MessageFactory.create_error_message(
                ErrorCode.INTERNAL_ERROR,
                "创建房间失败"
            )
    
    def _handle_join_room(self, context: HandlerContext) -> Message:
        """处理加入房间请求"""
        if not self.room_manager:
            return MessageFactory.create_error_message(
                ErrorCode.INTERNAL_ERROR,
                "房间管理器未初始化"
            )
        
        room_id = context.message.data.get("room_id")
        password = context.message.data.get("password", "")
        
        join_result = self.room_manager.join_room(room_id, context.session, password)
        
        if join_result.get("success"):
            context.session.current_room = room_id
            
            return MessageFactory.create_message(
                MessageType.ROOM_JOINED,
                {
                    "room_id": room_id,
                    "is_host": False,
                    "message": join_result.get("message"),
                    "room_info": join_result.get("room_info")
                },
                sender="server",
                session=getattr(context.session, 'session_id', None),
                room_id=room_id
            )
        else:
            return MessageFactory.create_error_message(
                ErrorCode.ROOM_NOT_FOUND,
                join_result.get("message", "加入房间失败")
            )
    
    def _handle_leave_room(self, context: HandlerContext) -> Message:
        """处理离开房间请求"""
        if not self.room_manager or not hasattr(context.session, 'current_room'):
            return MessageFactory.create_error_message(
                ErrorCode.NOT_IN_ROOM,
                "您不在任何房间中"
            )
        
        room_id = context.session.current_room
        self.room_manager.leave_room(room_id, context.session)
        context.session.current_room = None
        
        return MessageFactory.create_message(
            MessageType.ROOM_LEFT,
            {"message": "已离开房间"},
            sender="server",
            session=getattr(context.session, 'session_id', None)
        )
    
    def _handle_get_room_list(self, context: HandlerContext) -> Message:
        """处理获取房间列表请求"""
        if not self.room_manager:
            return MessageFactory.create_error_message(
                ErrorCode.INTERNAL_ERROR,
                "房间管理器未初始化"
            )
        
        room_list = self.room_manager.get_room_list()
        
        return MessageFactory.create_message(
            MessageType.ROOM_LIST,
            {"rooms": room_list},
            sender="server",
            session=getattr(context.session, 'session_id', None)
        )
    
    def _handle_game_action(self, context: HandlerContext) -> Message:
        """处理游戏动作"""
        if not hasattr(context.session, 'current_room') or not context.session.current_room:
            return MessageFactory.create_error_message(
                ErrorCode.NOT_IN_ROOM,
                "您不在任何房间中"
            )
        
        if not self.room_manager:
            return MessageFactory.create_error_message(
                ErrorCode.INTERNAL_ERROR,
                "房间管理器未初始化"
            )
        
        # 委托给房间管理器处理游戏动作
        result = self.room_manager.handle_game_action(
            context.session.current_room,
            context.session,
            context.message.data
        )
        
        if result.get("success"):
            return MessageFactory.create_message(
                MessageType.ACTION_RESULT,
                result,
                sender="server",
                session=getattr(context.session, 'session_id', None),
                room_id=context.session.current_room
            )
        else:
            return MessageFactory.create_error_message(
                ErrorCode.INVALID_ACTION,
                result.get("message", "游戏动作失败")
            )
    
    def _handle_chat_message(self, context: HandlerContext) -> Message:
        """处理聊天消息"""
        if not hasattr(context.session, 'current_room') or not context.session.current_room:
            return MessageFactory.create_error_message(
                ErrorCode.NOT_IN_ROOM,
                "您不在任何房间中"
            )
        
        if not self.room_manager:
            return MessageFactory.create_error_message(
                ErrorCode.INTERNAL_ERROR,
                "房间管理器未初始化"
            )
        
        # 委托给房间管理器处理聊天消息
        self.room_manager.broadcast_chat_message(
            context.session.current_room,
            context.session,
            context.message.data
        )
        
        return MessageFactory.create_success_message(
            {"message": "聊天消息已发送"},
            sender="server",
            session=getattr(context.session, 'session_id', None)
        )