#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
欢迎语管理器
Welcome Message Manager

提供丰富的欢迎语和启动信息管理
Provides rich welcome messages and startup information management
"""

import random
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

class WelcomeManager:
    """欢迎语管理器"""
    
    def __init__(self, language_manager=None):
        """初始化欢迎语管理器"""
        self.language_manager = language_manager
        
        # 欢迎语模板
        self.welcome_templates = {
            "chinese": [
                "🎮 欢迎来到世界大战策略游戏！",
                "🌍 准备好征服世界了吗？",
                "⚔️ 战争即将开始，指挥官！",
                "🏰 建立你的帝国，统治世界！",
                "🚀 踏上征服之路，成为传奇！"
            ],
            "english": [
                "🎮 Welcome to World War Strategy Game!",
                "🌍 Ready to conquer the world?",
                "⚔️ War is coming, Commander!",
                "🏰 Build your empire, rule the world!",
                "🚀 Embark on conquest, become legendary!"
            ]
        }
        
        # 启动提示
        self.startup_tips = {
            "chinese": [
                "💡 提示：合理分配资源是胜利的关键",
                "💡 提示：外交联盟可以帮助你度过困难时期",
                "💡 提示：科技发展决定军事实力",
                "💡 提示：地形优势在战斗中很重要",
                "💡 提示：经济基础决定战争潜力"
            ],
            "english": [
                "💡 Tip: Resource allocation is key to victory",
                "💡 Tip: Diplomatic alliances help in difficult times",
                "💡 Tip: Technology determines military strength",
                "💡 Tip: Terrain advantage is important in battles",
                "💡 Tip: Economic foundation determines war potential"
            ]
        }
        
        # 服务器状态信息
        self.server_info = {
            "version": "v3.0",
            "max_players": 8,
            "supported_languages": ["中文", "English", "双语模式"],
            "features": [
                "多人在线对战",
                "实时策略游戏",
                "世界征服模式",
                "AI智能对手",
                "动态世界事件"
            ]
        }
        
        # 导入日志
        from shared.enhanced_logger import get_server_logger
        self.logger = get_server_logger("WelcomeManager")
        
        self.logger.info("欢迎语管理器初始化完成")
    
    def get_welcome_message(self, language: str = "chinese") -> str:
        """
        获取欢迎消息
        
        Args:
            language: 语言偏好
            
        Returns:
            欢迎消息
        """
        if language not in self.welcome_templates:
            language = "chinese"
        
        welcome = random.choice(self.welcome_templates[language])
        return welcome
    
    def get_startup_tip(self, language: str = "chinese") -> str:
        """
        获取启动提示
        
        Args:
            language: 语言偏好
            
        Returns:
            启动提示
        """
        if language not in self.startup_tips:
            language = "chinese"
        
        tip = random.choice(self.startup_tips[language])
        return tip
    
    def generate_server_banner(self, language: str = "chinese") -> str:
        """
        生成服务器横幅
        
        Args:
            language: 语言偏好
            
        Returns:
            服务器横幅文本
        """
        banner_lines = []
        
        # 顶部分隔线
        banner_lines.append("=" * 80)
        
        # 标题
        if language == "english":
            banner_lines.append("🎮 WORLD WAR STRATEGY GAME SERVER")
            banner_lines.append("🌍 Conquer the World, Command Your Destiny")
        else:
            banner_lines.append("🎮 世界大战策略游戏服务器")
            banner_lines.append("🌍 征服世界，掌控命运")
        
        # 版本和时间信息
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        banner_lines.append(f"📡 版本 / Version: {self.server_info['version']}")
        banner_lines.append(f"⏰ 启动时间 / Start Time: {current_time}")
        
        # 分隔线
        banner_lines.append("-" * 80)
        
        # 欢迎消息
        welcome_msg = self.get_welcome_message(language)
        banner_lines.append(f"   {welcome_msg}")
        
        # 启动提示
        tip = self.get_startup_tip(language)
        banner_lines.append(f"   {tip}")
        
        # 分隔线
        banner_lines.append("-" * 80)
        
        # 服务器信息
        if language == "english":
            banner_lines.append("🔧 Server Features:")
            for feature in self.server_info["features"]:
                # 简单的英文翻译
                eng_feature = feature.replace("多人在线对战", "Multiplayer Online Battle")
                eng_feature = eng_feature.replace("实时策略游戏", "Real-time Strategy Game")
                eng_feature = eng_feature.replace("世界征服模式", "World Conquest Mode")
                eng_feature = eng_feature.replace("AI智能对手", "AI Intelligent Opponents")
                eng_feature = eng_feature.replace("动态世界事件", "Dynamic World Events")
                banner_lines.append(f"   • {eng_feature}")
        else:
            banner_lines.append("🔧 服务器特性:")
            for feature in self.server_info["features"]:
                banner_lines.append(f"   • {feature}")
        
        # 底部分隔线
        banner_lines.append("=" * 80)
        
        return "\n".join(banner_lines)
    
    def generate_client_welcome(self, player_name: str = "", language: str = "chinese") -> Dict[str, Any]:
        """
        生成客户端欢迎信息
        
        Args:
            player_name: 玩家名称
            language: 语言偏好
            
        Returns:
            欢迎信息字典
        """
        welcome_data = {
            "type": "welcome",
            "timestamp": datetime.now().isoformat(),
            "server_info": self.server_info.copy()
        }
        
        if language == "english":
            welcome_data.update({
                "title": "🎮 World War Strategy Game",
                "subtitle": "🌍 Welcome to the Ultimate Strategy Experience",
                "message": self.get_welcome_message("english"),
                "tip": self.get_startup_tip("english"),
                "player_greeting": f"Welcome, Commander {player_name}!" if player_name else "Welcome, Commander!",
                "instructions": [
                    "📋 Use the menu to navigate game options",
                    "🎯 Join or create rooms to start playing",
                    "💬 Use chat to communicate with other players",
                    "❓ Type 'help' for available commands"
                ]
            })
        else:
            welcome_data.update({
                "title": "🎮 世界大战策略游戏",
                "subtitle": "🌍 欢迎来到终极策略体验",
                "message": self.get_welcome_message("chinese"),
                "tip": self.get_startup_tip("chinese"),
                "player_greeting": f"欢迎，指挥官 {player_name}！" if player_name else "欢迎，指挥官！",
                "instructions": [
                    "📋 使用菜单导航游戏选项",
                    "🎯 加入或创建房间开始游戏",
                    "💬 使用聊天与其他玩家交流",
                    "❓ 输入 'help' 查看可用命令"
                ]
            })
        
        return welcome_data
    
    def get_startup_ascii_art(self) -> str:
        """获取启动ASCII艺术字"""
        ascii_art = """
    ██╗    ██╗ ██████╗ ██████╗ ██╗     ██╗██████╗     ██╗    ██╗ █████╗ ██████╗ 
    ██║    ██║██╔═══██╗██╔══██╗██║     ██║██╔══██╗    ██║    ██║██╔══██╗██╔══██╗
    ██║ █╗ ██║██║   ██║██████╔╝██║     ██║██║  ██║    ██║ █╗ ██║███████║██████╔╝
    ██║███╗██║██║   ██║██╔══██╗██║     ██║██║  ██║    ██║███╗██║██╔══██║██╔══██╗
    ╚███╔███╔╝╚██████╔╝██║  ██║███████╗██║██████╔╝    ╚███╔███╔╝██║  ██║██║  ██║
     ╚══╝╚══╝  ╚═════╝ ╚═╝  ╚═╝╚══════╝╚═╝╚═════╝      ╚══╝╚══╝ ╚═╝  ╚═╝╚═╝  ╚═╝
        """
        return ascii_art
    
    def get_debug_info(self) -> Dict[str, Any]:
        """获取调试信息"""
        return {
            "welcome_templates_count": {
                lang: len(templates) for lang, templates in self.welcome_templates.items()
            },
            "startup_tips_count": {
                lang: len(tips) for lang, tips in self.startup_tips.items()
            },
            "server_info": self.server_info,
            "supported_languages": list(self.welcome_templates.keys())
        }
