#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务器设置管理器
Server Settings Manager
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
from shared.enhanced_logger import EnhancedLogger


class ServerSettingsManager:
    """服务器设置管理器"""
    
    def __init__(self, config_file: str = "config/server_settings.json"):
        """
        初始化设置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.settings = self._load_default_settings()
        self._load_settings()
    
    def _load_default_settings(self) -> Dict[str, Any]:
        """加载默认设置"""
        return {
            "language": "bilingual",
            "max_log_lines": 500,
            "clear_terminal": True,
            "log_level": "INFO",
            "auto_archive_logs": True,
            "server_host": "localhost",
            "server_port": 8888,
            "max_connections": 100,
            "debug_mode": False
        }
    
    def _load_settings(self):
        """从文件加载设置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                    self.settings.update(saved_settings)
        except Exception as e:
            print(f"加载设置失败: {e}")
    
    def save_settings(self):
        """保存设置到文件"""
        try:
            # 确保配置目录存在
            config_dir = os.path.dirname(self.config_file)
            if config_dir and not os.path.exists(config_dir):
                os.makedirs(config_dir, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存设置失败: {e}")
    
    def get_setting(self, key: str, default=None):
        """获取设置值"""
        return self.settings.get(key, default)
    
    def set_setting(self, key: str, value: Any):
        """设置值"""
        self.settings[key] = value
        self.save_settings()
        
        # 应用特殊设置
        if key == "max_log_lines":
            EnhancedLogger.set_max_log_lines(value)
    
    def show_settings_menu(self, language_manager=None):
        """显示设置菜单"""
        while True:
            if self.get_setting("clear_terminal"):
                os.system('cls' if os.name == 'nt' else 'clear')
            
            print("\n" + "=" * 60)
            print("服务器设置 / Server Settings")
            print("=" * 60)
            
            print("当前设置 / Current Settings:")
            print(f"1. 语言设置 / Language: {self.get_setting('language')}")
            print(f"2. 最大日志行数 / Max Log Lines: {self.get_setting('max_log_lines')}")
            print(f"3. 清空终端 / Clear Terminal: {self.get_setting('clear_terminal')}")
            print(f"4. 日志级别 / Log Level: {self.get_setting('log_level')}")
            print(f"5. 自动归档日志 / Auto Archive Logs: {self.get_setting('auto_archive_logs')}")
            print(f"6. 服务器地址 / Server Host: {self.get_setting('server_host')}")
            print(f"7. 服务器端口 / Server Port: {self.get_setting('server_port')}")
            print(f"8. 最大连接数 / Max Connections: {self.get_setting('max_connections')}")
            print(f"9. 调试模式 / Debug Mode: {self.get_setting('debug_mode')}")
            print("10. 重置所有设置 / Reset All Settings")
            print("0. 返回 / Back")
            print()
            
            choice = input("请选择 / Enter choice (0-10): ").strip()
            
            if choice == "1":
                self._change_language_setting(language_manager)
            elif choice == "2":
                self._change_max_log_lines()
            elif choice == "3":
                self._toggle_clear_terminal()
            elif choice == "4":
                self._change_log_level()
            elif choice == "5":
                self._toggle_auto_archive()
            elif choice == "6":
                self._change_server_host()
            elif choice == "7":
                self._change_server_port()
            elif choice == "8":
                self._change_max_connections()
            elif choice == "9":
                self._toggle_debug_mode()
            elif choice == "10":
                self._reset_settings()
            elif choice == "0":
                break
            else:
                print("无效选择 / Invalid choice")
                input("按回车键继续 / Press Enter to continue...")
    
    def _change_language_setting(self, language_manager=None):
        """更改语言设置"""
        print("\n语言选择 / Language Selection:")
        print("1. 中文")
        print("2. English")
        print("3. 双语模式 / Bilingual Mode")
        
        choice = input("请选择 / Enter choice (1-3): ").strip()
        
        if choice == "1":
            self.set_setting("language", "chinese")
            if language_manager:
                language_manager.set_language("chinese")
            print("语言已设置为中文")
        elif choice == "2":
            self.set_setting("language", "english")
            if language_manager:
                language_manager.set_language("english")
            print("Language set to English")
        elif choice == "3":
            self.set_setting("language", "bilingual")
            if language_manager:
                language_manager.set_language("bilingual")
            print("语言已设置为双语模式 / Language set to bilingual mode")
        else:
            print("无效选择 / Invalid choice")
        
        input("按回车键继续 / Press Enter to continue...")
    
    def _change_max_log_lines(self):
        """更改最大日志行数"""
        try:
            current = self.get_setting("max_log_lines")
            print(f"\n当前最大日志行数 / Current max log lines: {current}")
            new_value = input("输入新的最大日志行数 / Enter new max log lines (100-10000): ").strip()
            
            if new_value:
                lines = int(new_value)
                if 100 <= lines <= 10000:
                    self.set_setting("max_log_lines", lines)
                    print(f"最大日志行数已设置为 / Max log lines set to: {lines}")
                else:
                    print("值必须在100-10000之间 / Value must be between 100-10000")
        except ValueError:
            print("无效数字 / Invalid number")
        
        input("按回车键继续 / Press Enter to continue...")
    
    def _toggle_clear_terminal(self):
        """切换清空终端设置"""
        current = self.get_setting("clear_terminal")
        new_value = not current
        self.set_setting("clear_terminal", new_value)
        
        status = "启用 / Enabled" if new_value else "禁用 / Disabled"
        print(f"\n清空终端已{status}")
        input("按回车键继续 / Press Enter to continue...")
    
    def _change_log_level(self):
        """更改日志级别"""
        print("\n日志级别 / Log Level:")
        print("1. DEBUG")
        print("2. INFO")
        print("3. WARNING")
        print("4. ERROR")
        
        choice = input("请选择 / Enter choice (1-4): ").strip()
        
        levels = {"1": "DEBUG", "2": "INFO", "3": "WARNING", "4": "ERROR"}
        if choice in levels:
            level = levels[choice]
            self.set_setting("log_level", level)
            print(f"日志级别已设置为 / Log level set to: {level}")
        else:
            print("无效选择 / Invalid choice")
        
        input("按回车键继续 / Press Enter to continue...")
    
    def _toggle_auto_archive(self):
        """切换自动归档设置"""
        current = self.get_setting("auto_archive_logs")
        new_value = not current
        self.set_setting("auto_archive_logs", new_value)
        
        status = "启用 / Enabled" if new_value else "禁用 / Disabled"
        print(f"\n自动归档日志已{status}")
        input("按回车键继续 / Press Enter to continue...")
    
    def _change_server_host(self):
        """更改服务器地址"""
        current = self.get_setting("server_host")
        print(f"\n当前服务器地址 / Current server host: {current}")
        new_value = input("输入新的服务器地址 / Enter new server host: ").strip()
        
        if new_value:
            self.set_setting("server_host", new_value)
            print(f"服务器地址已设置为 / Server host set to: {new_value}")
        
        input("按回车键继续 / Press Enter to continue...")
    
    def _change_server_port(self):
        """更改服务器端口"""
        try:
            current = self.get_setting("server_port")
            print(f"\n当前服务器端口 / Current server port: {current}")
            new_value = input("输入新的服务器端口 / Enter new server port (1024-65535): ").strip()
            
            if new_value:
                port = int(new_value)
                if 1024 <= port <= 65535:
                    self.set_setting("server_port", port)
                    print(f"服务器端口已设置为 / Server port set to: {port}")
                else:
                    print("端口必须在1024-65535之间 / Port must be between 1024-65535")
        except ValueError:
            print("无效端口号 / Invalid port number")
        
        input("按回车键继续 / Press Enter to continue...")
    
    def _change_max_connections(self):
        """更改最大连接数"""
        try:
            current = self.get_setting("max_connections")
            print(f"\n当前最大连接数 / Current max connections: {current}")
            new_value = input("输入新的最大连接数 / Enter new max connections (1-1000): ").strip()
            
            if new_value:
                connections = int(new_value)
                if 1 <= connections <= 1000:
                    self.set_setting("max_connections", connections)
                    print(f"最大连接数已设置为 / Max connections set to: {connections}")
                else:
                    print("连接数必须在1-1000之间 / Connections must be between 1-1000")
        except ValueError:
            print("无效数字 / Invalid number")
        
        input("按回车键继续 / Press Enter to continue...")
    
    def _toggle_debug_mode(self):
        """切换调试模式"""
        current = self.get_setting("debug_mode")
        new_value = not current
        self.set_setting("debug_mode", new_value)
        
        status = "启用 / Enabled" if new_value else "禁用 / Disabled"
        print(f"\n调试模式已{status}")
        input("按回车键继续 / Press Enter to continue...")
    
    def _reset_settings(self):
        """重置所有设置"""
        confirm = input("\n确定要重置所有设置吗？/ Are you sure to reset all settings? (y/N): ").strip().lower()
        
        if confirm in ['y', 'yes', '是']:
            self.settings = self._load_default_settings()
            self.save_settings()
            print("所有设置已重置为默认值 / All settings reset to default")
        else:
            print("取消重置 / Reset cancelled")
        
        input("按回车键继续 / Press Enter to continue...")
