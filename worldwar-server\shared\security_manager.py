#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全管理器 - 处理客户端服务端安全通信
Security Manager - Handles client-server secure communication
"""

import hashlib
import hmac
import secrets
import time
import json
from typing import Dict, Any, Optional, Tuple
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from shared.enhanced_logger import get_server_logger

class SecurityManager:
    """安全管理器 - 负责消息签名验证和密钥管理"""
    
    def __init__(self, server_mode: bool = False):
        """
        初始化安全管理器
        
        Args:
            server_mode: 是否为服务器模式
        """
        self.logger = get_server_logger("SecurityManager")
        self.server_mode = server_mode
        
        # 密钥配置
        self.secret_key: Optional[bytes] = None
        self.key_file_path = project_root / "config" / "server_key.dat"
        
        # 消息验证配置
        self.signature_algorithm = "sha256"
        self.max_timestamp_diff = 300  # 5分钟时间戳容差
        
        # 初始化密钥
        self._initialize_security()
        
        self.logger.info(f"安全管理器初始化完成 - {'服务器' if server_mode else '客户端'}模式")
    
    def _initialize_security(self):
        """初始化安全配置"""
        if self.server_mode:
            self._load_or_generate_server_key()
        else:
            # 客户端模式下，密钥将在连接时从服务器获取
            pass
    
    def _load_or_generate_server_key(self):
        """加载或生成服务器密钥"""
        try:
            # 确保配置目录存在
            self.key_file_path.parent.mkdir(parents=True, exist_ok=True)
            
            if self.key_file_path.exists():
                # 加载现有密钥
                with open(self.key_file_path, 'rb') as f:
                    self.secret_key = f.read()
                self.logger.info("已加载现有服务器密钥")
            else:
                # 生成新密钥
                self.secret_key = secrets.token_bytes(32)  # 256位密钥
                with open(self.key_file_path, 'wb') as f:
                    f.write(self.secret_key)
                self.logger.info("已生成新的服务器密钥")
                
        except Exception as e:
            self.logger.error(f"密钥初始化失败: {e}")
            raise
    
    def set_secret_key(self, key: bytes):
        """设置密钥（通用方法，用于测试和调试）"""
        self.secret_key = key
        self.logger.info(f"密钥设置完成，长度: {len(key)} bytes")

    def set_client_key(self, key: bytes):
        """设置客户端密钥（从服务器接收）"""
        if not self.server_mode:
            self.secret_key = key
            self.logger.info("客户端密钥设置完成")
        else:
            self.logger.warning("服务器模式下不应调用set_client_key")
    
    def get_server_key(self) -> Optional[bytes]:
        """获取服务器密钥（仅服务器模式）"""
        if self.server_mode:
            return self.secret_key
        return None
    
    def sign_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        对消息进行签名
        
        Args:
            message: 要签名的消息
            
        Returns:
            包含签名的消息
        """
        if not self.secret_key:
            raise ValueError("密钥未设置，无法签名消息")
        
        try:
            # 添加时间戳
            message["timestamp"] = time.time()
            
            # 创建消息的规范化表示用于签名
            canonical_message = self._canonicalize_message(message)
            
            # 生成HMAC签名
            signature = hmac.new(
                self.secret_key,
                canonical_message.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            # 调试信息
            self.logger.debug(f"签名消息 - 密钥前16字节: {self.secret_key[:16].hex()}")
            self.logger.debug(f"签名消息 - 规范化消息: {canonical_message}")
            self.logger.debug(f"签名消息 - 生成的签名: {signature[:16]}...{signature[-16:]}")

            # 添加签名到消息
            message["signature"] = signature
            message["signature_algorithm"] = self.signature_algorithm
            
            return message
            
        except Exception as e:
            self.logger.error(f"消息签名失败: {e}")
            raise
    
    def verify_message(self, message: Dict[str, Any]) -> bool:
        """
        验证消息签名
        
        Args:
            message: 要验证的消息
            
        Returns:
            验证是否成功
        """
        if not self.secret_key:
            self.logger.error("密钥未设置，无法验证消息")
            self.logger.error("  可能原因: 握手未完成或密钥同步失败")
            return False
        
        try:
            # 检查必要字段
            if "signature" not in message or "timestamp" not in message:
                missing_fields = []
                if "signature" not in message:
                    missing_fields.append("signature")
                if "timestamp" not in message:
                    missing_fields.append("timestamp")

                self.logger.warning(f"消息缺少必要字段: {', '.join(missing_fields)}")
                self.logger.warning(f"  消息包含的字段: {list(message.keys())}")
                self.logger.warning(f"  消息类型: {message.get('type', 'N/A')}")
                return False
            
            # 验证时间戳
            if not self._verify_timestamp(message["timestamp"]):
                current_time = time.time()
                msg_timestamp = message["timestamp"]
                time_diff = abs(current_time - msg_timestamp)

                self.logger.warning("消息时间戳验证失败")
                self.logger.warning(f"  消息时间戳: {msg_timestamp}")
                self.logger.warning(f"  当前时间:   {current_time}")
                self.logger.warning(f"  时间差:     {time_diff:.2f} 秒")
                self.logger.warning(f"  允许偏差:   {self.timestamp_tolerance} 秒")
                return False
            
            # 提取签名
            received_signature = message.pop("signature")
            signature_algorithm = message.pop("signature_algorithm", "sha256")
            
            # 验证签名算法
            if signature_algorithm != self.signature_algorithm:
                self.logger.warning(f"签名算法不匹配")
                self.logger.warning(f"  接收到的算法: {signature_algorithm}")
                self.logger.warning(f"  期望的算法:   {self.signature_algorithm}")
                self.logger.warning(f"  消息类型:     {message.get('type', 'N/A')}")
                return False
            
            # 创建消息的规范化表示
            canonical_message = self._canonicalize_message(message)
            
            # 计算期望的签名
            expected_signature = hmac.new(
                self.secret_key,
                canonical_message.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            # 调试信息
            self.logger.debug(f"验证消息 - 密钥前16字节: {self.secret_key[:16].hex()}")
            self.logger.debug(f"验证消息 - 规范化消息: {canonical_message}")
            self.logger.debug(f"验证消息 - 期望的签名: {expected_signature[:16]}...{expected_signature[-16:]}")

            # 比较签名
            is_valid = hmac.compare_digest(received_signature, expected_signature)

            if not is_valid:
                # 详细的签名验证失败信息
                self.logger.warning("消息签名验证失败 - 详细信息:")
                self.logger.warning(f"  接收到的签名: {received_signature[:16]}...{received_signature[-16:]}")
                self.logger.warning(f"  期望的签名:   {expected_signature[:16]}...{expected_signature[-16:]}")
                self.logger.warning(f"  签名算法:     {signature_algorithm}")
                self.logger.warning(f"  消息时间戳:   {message.get('timestamp', 'N/A')}")
                self.logger.warning(f"  消息类型:     {message.get('type', 'N/A')}")
                self.logger.warning(f"  密钥长度:     {len(self.secret_key) if self.secret_key else 0} bytes")
                self.logger.warning(f"  规范化消息长度: {len(canonical_message)} chars")

                # 输出更多调试信息
                self.logger.debug(f"  完整接收签名: {received_signature}")
                self.logger.debug(f"  完整期望签名: {expected_signature}")
                self.logger.debug(f"  规范化消息:   {canonical_message[:100]}...")

                # 使用调试方法获取更详细的信息
                try:
                    original_message = message.copy()
                    original_message["signature"] = received_signature
                    original_message["signature_algorithm"] = signature_algorithm
                    debug_info = self.debug_message_signature(original_message)
                    self.logger.debug(f"  调试信息: {debug_info}")
                except Exception as debug_error:
                    self.logger.debug(f"  调试信息获取失败: {debug_error}")

            return is_valid
            
        except Exception as e:
            self.logger.error(f"消息验证异常: {e}")
            self.logger.error(f"  异常类型: {type(e).__name__}")
            self.logger.error(f"  消息类型: {message.get('type', 'N/A') if isinstance(message, dict) else 'Invalid'}")
            import traceback
            self.logger.debug(f"  异常堆栈: {traceback.format_exc()}")
            return False

    def debug_message_signature(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        调试消息签名问题，返回详细的分析信息

        Args:
            message: 要分析的消息

        Returns:
            包含调试信息的字典
        """
        debug_info = {
            "message_valid": False,
            "has_signature": "signature" in message,
            "has_timestamp": "timestamp" in message,
            "timestamp_valid": False,
            "algorithm_match": False,
            "signature_match": False,
            "error_details": []
        }

        try:
            # 检查基本字段
            if not debug_info["has_signature"]:
                debug_info["error_details"].append("缺少signature字段")
            if not debug_info["has_timestamp"]:
                debug_info["error_details"].append("缺少timestamp字段")

            if debug_info["has_signature"] and debug_info["has_timestamp"]:
                # 验证时间戳
                debug_info["timestamp_valid"] = self._verify_timestamp(message["timestamp"])
                if not debug_info["timestamp_valid"]:
                    current_time = time.time()
                    time_diff = abs(current_time - message["timestamp"])
                    debug_info["error_details"].append(f"时间戳验证失败，时间差: {time_diff:.2f}秒")

                # 检查签名算法
                signature_algorithm = message.get("signature_algorithm", "sha256")
                debug_info["algorithm_match"] = (signature_algorithm == self.signature_algorithm)
                if not debug_info["algorithm_match"]:
                    debug_info["error_details"].append(f"签名算法不匹配: {signature_algorithm} vs {self.signature_algorithm}")

                # 检查签名匹配
                if debug_info["timestamp_valid"] and debug_info["algorithm_match"]:
                    message_copy = message.copy()
                    received_signature = message_copy.pop("signature")
                    message_copy.pop("signature_algorithm", None)

                    canonical_message = self._canonicalize_message(message_copy)
                    expected_signature = hmac.new(
                        self.secret_key,
                        canonical_message.encode('utf-8'),
                        hashlib.sha256
                    ).hexdigest()

                    debug_info["signature_match"] = hmac.compare_digest(received_signature, expected_signature)
                    debug_info["received_signature"] = received_signature
                    debug_info["expected_signature"] = expected_signature
                    debug_info["canonical_message_length"] = len(canonical_message)

                    if not debug_info["signature_match"]:
                        debug_info["error_details"].append("签名不匹配")

            debug_info["message_valid"] = (
                debug_info["has_signature"] and
                debug_info["has_timestamp"] and
                debug_info["timestamp_valid"] and
                debug_info["algorithm_match"] and
                debug_info["signature_match"]
            )

        except Exception as e:
            debug_info["error_details"].append(f"调试过程异常: {e}")

        return debug_info

    def _canonicalize_message(self, message: Dict[str, Any]) -> str:
        """
        创建消息的规范化表示用于签名
        
        Args:
            message: 消息字典
            
        Returns:
            规范化的消息字符串
        """
        # 创建消息副本，排除签名相关字段
        msg_copy = {k: v for k, v in message.items() 
                   if k not in ["signature", "signature_algorithm"]}
        
        # 按键排序并转换为JSON字符串
        return json.dumps(msg_copy, sort_keys=True, separators=(',', ':'))
    
    def _verify_timestamp(self, timestamp: float) -> bool:
        """
        验证消息时间戳
        
        Args:
            timestamp: 消息时间戳
            
        Returns:
            时间戳是否有效
        """
        current_time = time.time()
        time_diff = abs(current_time - timestamp)
        
        return time_diff <= self.max_timestamp_diff
    
    def generate_session_token(self) -> str:
        """生成会话令牌"""
        return secrets.token_urlsafe(32)
    
    def create_handshake_challenge(self) -> Dict[str, Any]:
        """创建握手挑战（服务器端）"""
        if not self.server_mode:
            raise ValueError("只有服务器模式才能创建握手挑战")
        
        challenge = secrets.token_urlsafe(32)
        timestamp = time.time()
        
        return {
            "type": "handshake_challenge",
            "data": {
                "challenge": challenge,
                "timestamp": timestamp,
                "server_version": "1.0"
            }
        }
    
    def create_handshake_response(self, challenge_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建握手响应（客户端）"""
        if self.server_mode:
            raise ValueError("服务器模式不应创建握手响应")
        
        if not self.secret_key:
            raise ValueError("客户端密钥未设置")
        
        challenge = challenge_data.get("challenge", "")
        timestamp = time.time()
        
        # 创建响应哈希
        response_data = f"{challenge}:{timestamp}"
        response_hash = hmac.new(
            self.secret_key,
            response_data.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return {
            "type": "handshake_response",
            "data": {
                "challenge": challenge,
                "response": response_hash,
                "timestamp": timestamp,
                "client_version": "1.0"
            }
        }
    
    def verify_handshake_response(self, response_data: Dict[str, Any], 
                                 original_challenge: str) -> bool:
        """验证握手响应（服务器端）"""
        if not self.server_mode:
            raise ValueError("只有服务器模式才能验证握手响应")
        
        try:
            challenge = response_data.get("challenge", "")
            response_hash = response_data.get("response", "")
            timestamp = response_data.get("timestamp", 0)
            
            # 验证挑战是否匹配
            if challenge != original_challenge:
                self.logger.warning("握手挑战不匹配")
                return False
            
            # 验证时间戳
            if not self._verify_timestamp(timestamp):
                self.logger.warning("握手响应时间戳无效")
                return False
            
            # 计算期望的响应哈希
            response_data_str = f"{challenge}:{timestamp}"
            expected_hash = hmac.new(
                self.secret_key,
                response_data_str.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            # 验证响应哈希
            is_valid = hmac.compare_digest(response_hash, expected_hash)
            
            if not is_valid:
                self.logger.warning("握手响应哈希验证失败")
            
            return is_valid
            
        except Exception as e:
            self.logger.error(f"握手响应验证失败: {e}")
            return False
    
    def get_security_info(self) -> Dict[str, Any]:
        """获取安全配置信息"""
        return {
            "mode": "server" if self.server_mode else "client",
            "key_configured": self.secret_key is not None,
            "signature_algorithm": self.signature_algorithm,
            "max_timestamp_diff": self.max_timestamp_diff
        }
