#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全配置管理
Security Configuration Management
"""

import json
import os
import secrets
from pathlib import Path
from typing import Dict, Any, Optional
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from shared.enhanced_logger import get_server_logger

class SecurityConfig:
    """安全配置管理器"""
    
    def __init__(self, config_dir: Optional[Path] = None):
        """
        初始化安全配置管理器
        
        Args:
            config_dir: 配置文件目录，默认为项目根目录下的config文件夹
        """
        self.logger = get_server_logger("SecurityConfig")
        
        # 设置配置目录
        if config_dir is None:
            self.config_dir = project_root / "config"
        else:
            self.config_dir = config_dir
        
        # 确保配置目录存在
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置文件路径
        self.config_file = self.config_dir / "security.json"
        self.server_key_file = self.config_dir / "server_key.dat"
        
        # 默认配置
        self.default_config = {
            "server": {
                "host": "localhost",
                "port": 8888,
                "max_connections": 100,
                "heartbeat_interval": 30,
                "heartbeat_timeout": 60,
                "session_timeout": 300
            },
            "security": {
                "signature_algorithm": "sha256",
                "max_timestamp_diff": 300,
                "key_rotation_interval": 86400,  # 24小时
                "enable_encryption": False,  # 暂时不启用加密
                "min_password_length": 6
            },
            "logging": {
                "level": "INFO",
                "max_file_size": 10485760,  # 10MB
                "backup_count": 5
            },
            "game": {
                "max_rooms": 50,
                "max_players_per_room": 4,
                "room_idle_timeout": 1800,  # 30分钟
                "turn_timeout": 300  # 5分钟
            }
        }
        
        # 当前配置
        self.config = {}
        
        # 加载配置
        self.load_config()
        
        self.logger.info("安全配置管理器初始化完成")
    
    def load_config(self):
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                
                # 合并默认配置和加载的配置
                self.config = self._merge_config(self.default_config, loaded_config)
                self.logger.info("配置文件加载成功")
            else:
                # 使用默认配置
                self.config = self.default_config.copy()
                self.save_config()
                self.logger.info("使用默认配置并保存到文件")
                
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            self.config = self.default_config.copy()
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            self.logger.info("配置文件保存成功")
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            raise
    
    def _merge_config(self, default: Dict[str, Any], loaded: Dict[str, Any]) -> Dict[str, Any]:
        """合并配置字典"""
        result = default.copy()
        
        for key, value in loaded.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def get(self, key_path: str, default=None):
        """
        获取配置值
        
        Args:
            key_path: 配置键路径，如 "server.host" 或 "security.signature_algorithm"
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any):
        """
        设置配置值
        
        Args:
            key_path: 配置键路径
            value: 配置值
        """
        keys = key_path.split('.')
        config = self.config
        
        # 导航到最后一级
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # 设置值
        config[keys[-1]] = value
        
        # 保存配置
        self.save_config()
        self.logger.info(f"配置已更新: {key_path} = {value}")
    
    def get_server_config(self) -> Dict[str, Any]:
        """获取服务器配置"""
        return self.config.get("server", {})
    
    def get_security_config(self) -> Dict[str, Any]:
        """获取安全配置"""
        return self.config.get("security", {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.config.get("logging", {})
    
    def get_game_config(self) -> Dict[str, Any]:
        """获取游戏配置"""
        return self.config.get("game", {})
    
    def load_or_generate_server_key(self) -> bytes:
        """加载或生成服务器密钥"""
        try:
            if self.server_key_file.exists():
                with open(self.server_key_file, 'rb') as f:
                    key = f.read()
                self.logger.info("服务器密钥加载成功")
                return key
            else:
                # 生成新密钥
                key = secrets.token_bytes(32)  # 256位密钥
                with open(self.server_key_file, 'wb') as f:
                    f.write(key)
                
                # 设置文件权限（仅所有者可读写）
                os.chmod(self.server_key_file, 0o600)
                
                self.logger.info("新服务器密钥生成并保存")
                return key
                
        except Exception as e:
            self.logger.error(f"密钥操作失败: {e}")
            raise
    
    def rotate_server_key(self) -> bytes:
        """轮换服务器密钥"""
        try:
            # 备份旧密钥
            if self.server_key_file.exists():
                backup_file = self.server_key_file.with_suffix('.bak')
                self.server_key_file.rename(backup_file)
                self.logger.info("旧密钥已备份")
            
            # 生成新密钥
            new_key = self.load_or_generate_server_key()
            self.logger.info("服务器密钥轮换完成")
            return new_key
            
        except Exception as e:
            self.logger.error(f"密钥轮换失败: {e}")
            raise
    
    def validate_config(self) -> bool:
        """验证配置的有效性"""
        try:
            # 验证服务器配置
            server_config = self.get_server_config()
            if not (1 <= server_config.get("port", 0) <= 65535):
                self.logger.error("无效的服务器端口")
                return False
            
            if server_config.get("max_connections", 0) <= 0:
                self.logger.error("无效的最大连接数")
                return False
            
            # 验证安全配置
            security_config = self.get_security_config()
            if security_config.get("max_timestamp_diff", 0) <= 0:
                self.logger.error("无效的时间戳容差")
                return False
            
            # 验证游戏配置
            game_config = self.get_game_config()
            if game_config.get("max_rooms", 0) <= 0:
                self.logger.error("无效的最大房间数")
                return False
            
            if not (2 <= game_config.get("max_players_per_room", 0) <= 8):
                self.logger.error("无效的房间最大玩家数")
                return False
            
            self.logger.info("配置验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
    
    def reset_to_default(self):
        """重置为默认配置"""
        self.config = self.default_config.copy()
        self.save_config()
        self.logger.info("配置已重置为默认值")
    
    def export_config(self, export_path: Path):
        """导出配置到指定路径"""
        try:
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            self.logger.info(f"配置已导出到: {export_path}")
        except Exception as e:
            self.logger.error(f"导出配置失败: {e}")
            raise
    
    def import_config(self, import_path: Path):
        """从指定路径导入配置"""
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            self.config = self._merge_config(self.default_config, imported_config)
            self.save_config()
            self.logger.info(f"配置已从 {import_path} 导入")
        except Exception as e:
            self.logger.error(f"导入配置失败: {e}")
            raise
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            "server_host": self.get("server.host"),
            "server_port": self.get("server.port"),
            "max_connections": self.get("server.max_connections"),
            "security_enabled": True,
            "signature_algorithm": self.get("security.signature_algorithm"),
            "max_rooms": self.get("game.max_rooms"),
            "max_players_per_room": self.get("game.max_players_per_room"),
            "config_file": str(self.config_file),
            "key_file_exists": self.server_key_file.exists()
        }
