#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的语言管理器
Simplified Language Manager
"""

import json
import os
from pathlib import Path
from typing import Dict, Optional

class SimpleLanguageManager:
    """简化的语言管理器"""
    
    def __init__(self, app_type: str = "server"):
        self.app_type = app_type
        self.current_language = "chinese"
        self.bilingual_mode = False
        self.languages = {}
        
        # 设置路径
        if app_type == "server":
            self.base_dir = Path(__file__).parent.parent
            self.languages_dir = self.base_dir / "languages"
        else:
            self.base_dir = Path(__file__).parent.parent
            self.languages_dir = self.base_dir / "client" / "languages"
        
        self._load_languages()
    
    def _load_languages(self):
        """加载语言文件"""
        try:
            # 加载中文
            chinese_file = self.languages_dir / "chinese.json"
            if chinese_file.exists():
                with open(chinese_file, 'r', encoding='utf-8') as f:
                    self.languages['chinese'] = json.load(f)
            
            # 加载英文
            english_file = self.languages_dir / "english.json"
            if english_file.exists():
                with open(english_file, 'r', encoding='utf-8') as f:
                    self.languages['english'] = json.load(f)
                    
        except Exception as e:
            print(f"Error loading languages: {e}")
            self.languages = {'chinese': {}, 'english': {}}
    
    def get_text(self, key: str, default: str = None, **kwargs) -> str:
        """获取文本"""
        if self.bilingual_mode:
            # 双语模式
            chinese_text = self._get_text_from_language('chinese', key, default)
            english_text = self._get_text_from_language('english', key, default)
            
            if chinese_text and english_text and chinese_text != key and english_text != key:
                return f"{chinese_text} / {english_text}"
            elif chinese_text and chinese_text != key:
                return chinese_text
            elif english_text and english_text != key:
                return english_text
            else:
                return default or key
        else:
            # 单语模式
            text = self._get_text_from_language(self.current_language, key, default)
            return text if text and text != key else (default or key)
    
    def _get_text_from_language(self, language: str, key: str, default: str = None) -> str:
        """从指定语言获取文本"""
        if language not in self.languages:
            return default or key
        
        # 支持点号分隔的键路径
        keys = key.split('.')
        current_dict = self.languages[language]
        
        try:
            for k in keys:
                if isinstance(current_dict, dict) and k in current_dict:
                    current_dict = current_dict[k]
                else:
                    return default or key
            
            return str(current_dict) if current_dict is not None else (default or key)
            
        except Exception:
            return default or key
    
    def set_language(self, language: str):
        """设置当前语言"""
        if language in ['chinese', 'english', 'bilingual']:
            if language == 'bilingual':
                self.bilingual_mode = True
                self.current_language = 'chinese'
            else:
                self.bilingual_mode = False
                self.current_language = language
    
    def is_first_run(self) -> bool:
        """检查是否首次运行"""
        return False  # 简化实现
    
    def show_language_selection(self):
        """显示语言选择"""
        print("Language Selection / 语言选择:")
        print("1. 中文")
        print("2. English")
        print("3. Bilingual / 双语")
        
        try:
            choice = input("Please select / 请选择 (1-3): ").strip()
            if choice == "1":
                self.set_language("chinese")
            elif choice == "2":
                self.set_language("english")
            elif choice == "3":
                self.set_language("bilingual")
            else:
                self.set_language("chinese")  # 默认中文
        except:
            self.set_language("chinese")  # 默认中文

# 全局实例
_language_manager = None

def create_language_manager(app_type: str = "server") -> SimpleLanguageManager:
    """创建语言管理器"""
    global _language_manager
    if _language_manager is None:
        _language_manager = SimpleLanguageManager(app_type)
    return _language_manager

def get_language_manager(app_type: str = "server") -> SimpleLanguageManager:
    """获取语言管理器"""
    return create_language_manager(app_type)

def get_text(key: str, default: str = None, **kwargs) -> str:
    """快捷函数: 获取文本"""
    return get_language_manager().get_text(key, default, **kwargs)
