{"language_name": "English", "server": {"starting": "Starting server...", "started": "Server started", "stopping": "Stopping server...", "stopped": "Server stopped", "listening": "Server listening", "address": "Address", "port": "Port", "max_connections": "Max connections", "current_connections": "Current connections", "uptime": "Uptime", "status": "Status", "online": "Online", "offline": "Offline", "error": "Error", "warning": "Warning", "info": "Info", "debug": "Debug", "language_selection": "Language Selection", "select_language": "Please select server language", "language_changed": "Language changed to English", "welcome": "Welcome to World War Strategy Game Server", "app_name": "World War Game Server", "cannot_acquire_lock": "Cannot acquire process lock, exiting", "initializing": "Initializing server", "init_complete": "Server initialization complete, starting...", "keyboard_interrupt": "Received keyboard interrupt signal", "startup_failed": "Server startup failed", "error_details": "Error details", "press_any_key": "Press any key to continue...", "shutdown_signal": "Shutdown signal received", "graceful_shutdown": "Gracefully shutting down server...", "force_shutdown": "Force shutdown server", "debug_enabled": "🔧 Debug mode enabled / 调试模式已启用", "debug_modules": "   Debug modules / 调试模块: {modules}", "debug_log_level": "   Log level / 日志级别: {level}", "server_started": "🎮 Server started", "server_address": "📡 Address: {host}:{port}", "waiting_connections": "⏳ Waiting for client connections...", "stop_instruction": "🛑 Press Ctrl+C to stop server", "new_client": "📱 New client connected: {address}", "socket_error": "❌ Socket error", "keyboard_interrupt_received": "🛑 Keyboard interrupt received", "startup_error": "❌ Server startup failed: {error}", "client_message": "📨 Received message from {address}: {message}", "client_error": "❌ Error handling client {address}: {error}", "client_disconnected": "📱 Client {address} disconnected", "room_created_success": "✅ Room created successfully: {room_name} (ID: {room_id})", "room_list_sent": "📋 Sent room list, {count} rooms total", "shutting_down": "🛑 Shutting down server...", "server_stopped": "✅ Server stopped", "shutdown_signal_received": "🛑 Received stop signal ({signal}), safely shutting down server...", "shutdown_warning": "⚠️ Warning during server shutdown: {error}", "shutdown_complete": "👋 Server safely exited", "multi_instance_warning": "⚠️ Multi-instance check failed, continuing startup: {error}", "interrupt_signal": "🛑 Interrupt signal received", "runtime_error": "❌ Server runtime error: {error}"}, "game": {"room_created": "Room created", "room_deleted": "Room deleted", "player_joined": "Player joined", "player_left": "Player left", "game_started": "Game started", "game_ended": "Game ended", "turn_changed": "Turn changed", "data_mode": "Data mode", "real_world_data": "Real world data", "ai_generated_data": "AI generated data", "select_data_mode": "Please select data mode", "loading_data": "Loading data...", "data_loaded": "Data loaded", "data_error": "Data loading error", "_创建玩家会话": "Created player session: {session_id}", "message_游戏循环模块不可用": "Game loop module unavailable", "_初始化游戏数据": "Initializing game data...", "房间不存在": "Room does not exist", "您不在任何房间中": "You are not in any room", "只有房主可以开始游戏": "Only host can start the game", "请先加入游戏": "Please join game first", "房间名称不能为空": "Room name cannot be empty", "_玩家加入游戏": "Player joined: {player_name}", "message_f玩家未全部准备_ready_count": "{current_players})", "玩家未全部准备_ready_count": "{current_players})"}, "network": {"client_connected": "Client connected", "client_disconnected": "Client disconnected", "message_received": "Message received", "message_sent": "Message sent", "invalid_message": "Invalid message", "authentication_failed": "Authentication failed", "authentication_success": "Authentication success", "handshake_completed": "Handshake completed", "connection_timeout": "Connection timeout", "connection_error": "Connection error", "_世界大战游戏服务器": "World War Game Server", "世界大战游戏服务器": "World War Game Server", "正在启动服务器": "Starting server...", "1_直接启动服务器": "Start Server Directly", "description_基础服务器数据世界数据将在房间创建时": "Base server data, world data will be initialized when rooms are created", "正在初始化基础服务器数据": "Initializing base server data...", "_客户端重置连接": "Client reset connection: {player_session.session_id}", "server_name_安全世界大战游戏服务器": "Secure World War Game Server", "使用兼容性游戏服务器": "Using compatibility game server", "正在关闭服务器": "Shutting down server...", "_新客户端连接": "New client connected: {client_address}", "正在初始化服务器数据": "Initializing server data...", "_请检查网络配置": "Please check network configuration", "n_收到中断信号正在关闭服务器": "Received interrupt signal, shutting down server...", "_按_ctrlc_停止服务器": "Press Ctrl+C to stop server", "_等待客户端连接": "Waiting for client connections...", "_服务器地址": "Server Address: {self.host}:{self.port}", "_正在启动游戏服务器": "Starting game server...", "warningsappendf服务器运行时间较长_uptim": "3600:.1f}小时", "连接数必须在11000之间": "Connections must be between 1-1000", "输入新的最大连接数": "Enter new max connections (1-1000):", "n当前最大连接数": "Current max connections: {current}", "输入新的服务器端口": "Enter new server port (1024-65535):", "n当前服务器端口": "Current server port: {current}", "输入新的服务器地址": "Enter new server host:", "n当前服务器地址": "Current server host: {current}", "8_最大连接数": "Max Connections: {self.get_setting('max_connections')}", "7_服务器端口": "Server Port: {self.get_setting('server_port')}", "6_服务器地址": "Server Host: {self.get_setting('server_host')}", "服务器设置": "Server Settings", "total_sessions_lenselfsessions": "active_sessions\n        else:\n            avg_connection_time = 0\n        \n        # 统计消息数量\n        total_messages_sent = sum(session.messages_sent for session in self.sessions.values())\n        total_messages_received = sum(session.messages_received for session in self.sessions.values())\n        \n        return {", "_服务器状态报告": "Server Status Report ===\n运行时间: {server_stats['uptime']:.0f}秒\n总连接数: {server_stats['total_connections']}\n活跃连接: {server_stats['active_connections']}\n峰值连接: {server_stats['peak_connections']}\n活跃房间: {server_stats['active_rooms']}\n消息总数: {server_stats['message_count']}\n错误总数: {server_stats['error_count']}\n\n=== 性能统计 / Performance Statistics ===\n消息处理速度: {performance_stats['messages_per_second']:.2f} 消息/秒\n错误率: {performance_stats['errors_per_hour']:.2f} 错误/小时\n平均连接数: {performance_stats['average_connections']:.2f}\n连接效率: {performance_stats['connection_efficiency']:.1f}%\n\n=== 房间统计 / Room Statistics ===\n总房间数: {room_stats.get('total_rooms', 0)}\n总玩家数: {room_stats.get('total_players', 0)}\n平均每房间玩家数: {room_stats.get('average_players_per_room', 0):.1f}\n房间状态分布: {room_stats.get('room_status_distribution', {})}\n============================================", "服务器运行时间较长_uptime": "3600:.1f}小时"}, "errors": {"port_in_use": "Port already in use", "invalid_port": "Invalid port", "bind_failed": "Bind failed", "socket_error": "Socket error", "config_error": "Configuration error", "file_not_found": "File not found", "permission_denied": "Permission denied", "unknown_error": "Unknown error"}, "config": {"loading_config": "Loading configuration...", "config_loaded": "Configuration loaded", "saving_config": "Saving configuration...", "config_saved": "Configuration saved", "default_config": "Using default configuration", "invalid_config": "Invalid configuration"}, "messages": {"enter_port": "Enter port number (default 8888): ", "enter_address": "Enter server address (default localhost): ", "confirm_shutdown": "Are you sure you want to shutdown the server?", "server_ready": "Server ready, waiting for client connections...", "press_ctrl_c": "Press Ctrl+C to stop server"}, "launcher": {"title": "🎮 WorldWar World War Strategy Game Launcher", "subtitle": "🎯 World War Strategy Game Launcher", "select_mode": "Please select launch mode:", "start_server": "1. 🖥️  Start Server", "start_client": "2. 🎮 Start Client", "run_tests": "3. 🧪 Run Tests", "show_help": "4. 📚 Show Help", "quick_start": "5. 🚀 Quick Start", "exit": "0. ❌ Exit", "starting_server": "🖥️ Starting server...", "starting_client": "🎮 Starting client...", "server_script_not_found": "❌ Server script not found: {script_path}", "client_script_not_found": "❌ Client script not found: {script_path}", "client_exited": "🛑 Client exited", "client_start_failed": "❌ Failed to start client: {error}", "server_start_failed": "❌ Failed to start server: {error}", "waiting_server": "⏳ Waiting for server to start...", "running_tests": "🧪 Running functional tests...", "test_server_script": "✅ Testing server startup script...", "test_client_script": "✅ Testing client startup script...", "server_script_missing": "❌ Server startup script does not exist!", "client_script_missing": "❌ Client startup script does not exist!", "all_scripts_ok": "✅ All startup scripts check passed!", "server_tip": "💡 Tip: Use 'python worldwar-server/server_main.py' to start server", "client_tip": "💡 Tip: Use 'python worldwar-client/client_main.py' to start client", "help_title": "📚 WorldWar Game Help", "game_intro": "🎯 Game Introduction:", "game_description": "WorldWar is a multiplayer online strategy game with bilingual Chinese-English interface support.", "game_interaction": "Players can connect to the server and interact with other players in real-time.", "quick_start_title": "🚀 Quick Start:", "quick_start_step1": "1. Select '1' to start server", "quick_start_step2": "2. In another terminal, select '2' to start client", "quick_start_step3": "3. In client, select '1. Connect to Server'", "quick_start_step4": "4. Start playing!", "advanced_usage": "🔧 Advanced Usage:", "server_custom": "• Server supports custom address and port", "client_custom": "• Client supports specifying default server", "multi_client": "• Supports multiple clients connecting simultaneously", "language_support": "🌐 Language Support:", "chinese_interface": "• Chinese interface", "english_interface": "• English interface", "bilingual_mode": "• Bilingual mode (Chinese / English)", "tech_support": "📞 Technical Support:", "check_summary": "• Check FINAL_PROJECT_SUMMARY.md for detailed information", "run_tests_check": "• Run tests to check functionality status", "quick_starting": "🚀 Quick starting...", "closing_server": "🛑 Closing server..."}, "client": {"initialized": "🎮 World War Game Client initialized", "server_info": "📡 Server: {host}:{port}", "username_info": "👤 Username: {username}", "starting": "🚀 Starting client...", "connection_failed": "❌ Failed to connect to server, exiting", "connected_success": "✅ Successfully connected to server", "getting_rooms": "📋 Getting room list...", "connection_attempt": "🔄 Connection attempt {attempt}/{max_attempts}...", "retry_wait": "⏳ Waiting 3 seconds before retry...", "title": "🎮 World War Game Client", "help_command": "📝 Type 'help' to see available commands", "quit_command": "🛑 Type 'quit' to exit game", "unknown_command": "❓ Unknown command: {command}", "help_tip": "💡 Type 'help' to see available commands", "interrupt_signal": "🛑 Received interrupt signal", "input_ended": "🛑 Input ended", "input_error": "❌ Error processing input: {error}", "exiting": "👋 Exiting game...", "leaving_room": "📤 Leaving room...", "safely_exited": "✅ Safely exited", "disconnecting": "Disconnecting...", "help_info": "📖 Help Information", "general_commands": "🔧 General Commands:", "help_desc": "help, h          - Show this help information", "clear_desc": "clear, cls       - Clear screen", "quit_desc": "quit, exit, q    - Exit game", "status_desc": "status           - Show connection status", "rooms_desc": "rooms            - Refresh room list", "room_list_commands": "🏠 Room List Commands:", "join_desc": "join <number>    - Join specified room", "create_desc": "create <name>   - Create new room", "refresh_desc": "refresh          - Refresh room list", "waiting_room_commands": "⏳ Waiting Room Commands:", "ready_desc": "ready            - Toggle ready status", "start_desc": "start            - Start game (host only)", "leave_desc": "leave            - Leave room", "program_interrupted": "🛑 Program interrupted by user", "program_error": "❌ Program error occurred: {error}", "program_exited": "👋 Program exited", "username_generated": "No username provided, generated random username: {username}", "connection_failed_error": "Connection failed: {error}", "send_message_failed": "Failed to send message: {error}", "json_parse_error": "JSON parse error: {error}", "ui_update_callback_error": "UI update callback error: {error}", "ui_update_thread_error": "UI update worker thread error: {error}"}, "common": {"2_检查_dist": "WorldWarServer/ 目录", "dist": "WorldWarServer/config", "readmemd_dist": "WorldWarServer/", "config": "", "languages": "chinese.json", "_日志_logs": "server/ 目录", "world_war_strategy_game_server": "世界大战策略游戏服务器", "按回车键继续": "Press Enter to continue...", "退出程序": "Exiting...", "3_退出": "Exit", "2_进入设置": "<PERSON><PERSON> Set<PERSON>s", "selfworld_bank_api": "country/all/indicator/SI.POV.DDAY", "正在获取城市数据": "Fetching cities data...", "正在获取灾害数据": "Fetching disaster data...", "正在获取资源数据": "Fetching resource data...", "正在生成气候数据": "Generating climate data...", "正在生成地形数据": "Generating terrain data...", "正在获取世界银行贫困数据": "Fetching World Bank poverty data...", "data": "offline_data", "https": "/api.worldbank.org/v2", "useragent_worldwargame": "1.0 (Educational Purpose)", "正在创建备用数据": "Creating fallback data...", "description_ai生成数据": "AI generated data", "正在初始化政策系统": "Initializing policy system...", "正在初始化经济系统": "Initializing economic system...", "正在生成城市数据": "Generating cities data...", "正在生成灾害数据": "Generating disaster data...", "正在生成资源数据": "Generating resource data...", "正在生成贫困城市数据": "Generating poverty city data...", "description_混合数据真实贫困城市_ai生成环境": "Hybrid data: real poverty cities + AI environment", "正在获取真实贫困城市数据": "Fetching real poverty city data...", "_客户端心跳超时": "Client heartbeat timeout: {player_session.session_id}", "_无法绑定到地址_selfhost": "Cannot bind to address {self.host}", "_请尝试其他端口或关闭占用该端口的程序": "Please try another port or close the program using this port", "广播消息到_success_count": "{len(target_sessions)} 个会话", "id冲突重新生成_尝试_attempt_1": "{max_attempts}): {room_id}", "不是你的回合": "Not your turn", "取消重置": "Reset cancelled", "n确定要重置所有设置吗": "Are you sure to reset all settings? (y/N):", "启用": "Enabled", "禁用": "Disabled", "无效数字": "Invalid number", "无效端口号": "Invalid port number", "端口必须在102465535之间": "Port must be between 1024-65535", "n日志级别": "Log Level:", "值必须在10010000之间": "Value must be between 100-10000", "输入新的最大日志行数": "Enter new max log lines (100-10000):", "n当前最大日志行数": "Current max log lines: {current}", "3_双语模式": "Bilingual Mode", "0_返回": "Back", "10_重置所有设置": "Reset All Settings", "9_调试模式": "Debug Mode: {self.get_setting('debug_mode')}", "5_自动归档日志": "Auto Archive Logs: {self.get_setting('auto_archive_logs')}", "4_日志级别": "Log Level: {self.get_setting('log_level')}", "3_清空终端": "Clear Terminal: {self.get_setting('clear_terminal')}", "2_最大日志行数": "Max Log Lines: {self.get_setting('max_log_lines')}", "1_语言设置": "Language: {self.get_setting('language')}", "当前设置": "Current Settings:", "client": "", "ninvalid_input": "无效输入", "invalid_choice": "无效选择", "nbilingual_mode_enabled": "双语模式已启用", "nlanguage_set_to": "语言设置为: {lang_name}", "please_select": "请选择 (1-{}):", "lenavailable_languages_1_bilin": "双语模式 (中文 + English)", "available_languages": "可用语言:", "no_languages_available": "没有可用的语言", "_language_selection": "语言选择", "chinese_text": "{english_text}", "语言管理器": "Language Manager", "消息类型_messagegettype_n": "A')}", "消息时间戳_messagegettimestamp_n": "A')}", "unix": "Linux下获取进程锁", "tasklist_": "FI", "_执行清理操作_i1": "{len(self.cleanup_callbacks)}...", "示例用法": "Example Usage:\n  %(prog)s                          # 使用默认配置启动\n  %(prog)s --host 0.0.0.0 --port 8888  # 指定地址和端口\n  %(prog)s --debug                  # 启用调试模式", "selfsession_requestssession_se": "1.0 (Educational Purpose)'\n        })\n        \n        # API端点\n        self.world_bank_api =", "_min10_itemvalue": "100.0),  # 转换为0-1范围", "_windows特定的keepalive设置_sockioc": "Unix keepalive设置\n                try:\n                    sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPIDLE, 30)  # 30秒后开始keepalive\n                    sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPINTVL, 5)  # 5秒间隔\n                    sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPCNT, 3)    # 3次重试\n                except AttributeError:\n                    # 某些系统可能不支持这些选项\n                    pass\n\n            # 设置发送和接收缓冲区大小\n            sock.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 65536)  # 64KB发送缓冲区\n            sock.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 65536)  # 64KB接收缓冲区\n\n            self.logger.debug(", "_selfmessage_count": "uptime,", "_selferror_count": "(uptime / 3600) if uptime > 0 else 0,", "_selftotal_connections": "(uptime / 60) if uptime > 60 else 0,", "_selfactive_connections": "self.peak_connections * 100) if self.peak_connections > 0 else 0\n        }\n\n    def get_room_stats(self) -> Dict[str, Any]:", "_total_players": "len(room_list) if room_list else 0,", "通用语言管理器": "Universal Language Manager\n支持双语输出和语言切换功能", "selflock_name_lock_name_selflo": "f", "try_with_openselflock_file_r_e": "FI', f'PID eq {pid}'],\n                                              capture_output=True, text=True)\n                        return str(pid) not in result.stdout\n                    except:\n                        # 如果无法检查，假设进程不存在\n                        return True\n\n            return True  # 无法解析PID，认为是僵尸锁\n\n        except Exception:\n            return True  # 读取失败，认为是僵尸锁\n\n    def _acquire_unix(self, timeout: int = 0) -> bool:", "_windows下直接关闭文件_oscloseselfloc": "Linux下先解锁再关闭\n                    if HAS_FCNTL:\n                        fcntl.flock(self.lock_fd, fcntl.LOCK_UN)\n                    os.close(self.lock_fd)\n\n                # 删除锁文件\n                if self.lock_file.exists():\n                    self.lock_file.unlink()\n\n                self.is_locked = False\n                self.lock_fd = None\n\n            except Exception as e:\n                print(f"}, "success": {"_世界大战游戏服务器已关闭": "World War Game Server Stopped", "_服务器已在运行程序退出": "Server already running, exiting", "气候数据生成完成": "Climate data generation complete", "地形数据生成完成": "Terrain data generation complete", "_基础服务器数据初始化完成": "Base server data initialization complete", "战斗系统初始化完成": "Combat system initialized", "经济系统初始化完成": "Economic system initialized", "政策管理器初始化完成": "Policy manager initialized", "胜利条件系统初始化完成": "Victory system initialized", "_安全握手完成": "Secure handshake completed: {session_id}", "连接管理器初始化完成": "Connection manager initialized", "游戏管理器初始化完成": "Game manager initialized", "服务器已关闭": "Server shutdown complete", "房间管理线程已启动": "Room management thread started", "_端口_selfport_已被占用": "Port {self.port} is already in use", "_成功绑定到_selfhostselfport": "Successfully bound to {self.host}:{self.port}", "_世界大战游戏服务器已启动": "World War Game Server Started", "_数据初始化完成": "Data initialization complete", "安全游戏服务器初始化完成": "Secure game server initialized", "消息处理器初始化完成": "Message processor initialized", "连接意外断开已接收_lendata": "{length} 字节", "线程安全会话管理器初始化完成": "Thread-safe session manager initialized", "房间id生成器初始化完成": "Room ID generator initialized", "房间管理器初始化完成": "Room manager initialized", "message_f成功加入房间_lenselfplayers": "{self.max_players})", "message_f房间已满_lenselfplayers": "{self.max_players})", "服务器监控器初始化完成": "Server monitor initialized", "所有设置已重置为默认值": "All settings reset to default", "最大连接数已设置为": "Max connections set to: {connections}", "服务器端口已设置为": "Server port set to: {port}", "服务器地址已设置为": "Server host set to: {new_value}", "日志级别已设置为": "Log level set to: {level}", "最大日志行数已设置为": "Max log lines set to: {lines}", "语言已设置为双语模式": "Language set to bilingual mode", "房间已满_lenselfplayers": "{self.max_players})", "成功加入房间_lenselfplayers": "{self.max_players})"}, "ui": {"无效选择": "Invalid choice", "输入选择": "Enter choice (1-3):", "请选择": "Enter choice (0-10):", "服务器启动选项": "Server Startup Options", "n语言选择": "Language Selection:", "输入新的最大日志行数": "Enter new max log lines (100-10000):", "输入新的服务器地址": "Enter new server host:", "输入新的服务器端口": "Enter new server port (1024-65535):", "输入新的最大连接数": "Enter new max connections (1-1000):"}, "error": {"获取城市数据失败_e": "Failed to fetch cities data: {e}", "获取灾害数据失败_e": "Failed to fetch disaster data: {e}", "获取资源数据失败_e": "Failed to fetch resource data: {e}", "生成气候数据失败_e": "Failed to generate climate data: {e}", "生成地形数据失败_e": "Failed to generate terrain data: {e}", "获取贫困数据失败_e": "Failed to fetch poverty data: {e}", "api数据获取失败使用预设数据": "API fetch failed, using preset data...", "加载数据失败": "Failed to load data from {file_path}: {e}", "保存数据失败": "Failed to save data to {file_path}: {e}", "ai数据生成失败": "AI data generation failed: {e}", "混合数据初始化失败": "Failed to initialize hybrid data: {e}", "_客户端处理错误": "Client handling error: {e}", "_客户端连接错误": "Client connection error: {e}", "message_游戏循环启动失败": "Failed to start game loop", "_未知错误": "Unknown error: {e}", "_接受连接错误": "Connection accept error: {e}", "_绑定失败_e": "Bind failed: {e}", "_服务器启动失败": "Server startup failed: {e}", "创建房间失败": "Failed to create room"}}