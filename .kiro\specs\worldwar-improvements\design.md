# 世界大战游戏改进设计文档

## 概述

本设计文档详细描述了世界大战游戏改进项目的技术架构和实现方案。项目将修复现有的通信问题，实现国际化支持，并添加两种先进的地形生成系统：基于柏林噪声的程序化生成和基于真实世界数据的地形生成。

## 架构

### 整体架构改进

```
世界大战游戏 v2.0
├── worldwar-server/
│   ├── server/                 # 服务器核心（重构）
│   │   ├── game_server.py     # 主服务器类
│   │   ├── message_handler.py # 消息处理器（新增）
│   │   ├── room_manager.py    # 房间管理器（增强）
│   │   └── session_manager.py # 会话管理器（增强）
│   ├── world/                 # 世界生成系统（新增）
│   │   ├── terrain/           # 地形生成
│   │   │   ├── perlin_generator.py    # 柏林噪声生成器
│   │   │   ├── realworld_generator.py # 真实世界生成器
│   │   │   └── terrain_types.py       # 地形类型定义
│   │   ├── data/              # 数据获取和处理
│   │   │   ├── api_client.py          # API客户端
│   │   │   ├── data_cache.py          # 数据缓存
│   │   │   └── geo_data.py            # 地理数据处理
│   │   └── resources/         # 资源分配系统
│   │       ├── resource_generator.py  # 资源生成器
│   │       └── economic_data.py       # 经济数据处理
│   ├── shared/                # 共享组件（增强）
│   │   ├── language_manager.py # 语言管理器（重构）
│   │   ├── protocol.py        # 通信协议（标准化）
│   │   └── message_types.py   # 消息类型定义（新增）
│   └── languages/             # 语言文件（完善）
│       ├── chinese.json       # 中文语言包
│       ├── english.json       # 英文语言包
│       └── template.json      # 语言模板
├── worldwar-client/
│   ├── client/                # 客户端核心（重构）
│   │   ├── game_client.py     # 主客户端类
│   │   ├── ui_manager.py      # 界面管理器（增强）
│   │   ├── network_client.py  # 网络客户端（重构）
│   │   └── room_ui.py         # 房间界面（新增）
│   ├── shared/                # 共享组件（同步）
│   └── languages/             # 客户端语言文件
└── tools/                     # 开发工具（新增）
    ├── terrain_preview.py     # 地形预览工具
    ├── language_checker.py    # 语言文件检查工具
    └── performance_monitor.py # 性能监控工具
```

### 通信协议重构

#### 标准化消息格式
```json
{
  "version": "2.0",
  "type": "message_type",
  "id": "unique_message_id",
  "timestamp": "2025-01-15T10:30:00Z",
  "data": {
    "key": "value"
  },
  "metadata": {
    "sender": "client_id",
    "session": "session_id"
  }
}
```

#### 消息类型定义
- **认证消息**: `auth_request`, `auth_response`, `auth_failed`
- **房间消息**: `room_create`, `room_join`, `room_leave`, `room_update`, `room_list`
- **游戏消息**: `game_start`, `game_state`, `game_action`, `game_end`
- **系统消息**: `heartbeat`, `error`, `disconnect`, `reconnect`

## 组件和接口

### 1. 通信系统重构

#### MessageHandler 类
```python
class MessageHandler:
    """统一消息处理器"""
    
    def __init__(self, protocol_version="2.0"):
        self.protocol_version = protocol_version
        self.handlers = {}
        self.middleware = []
    
    def register_handler(self, message_type: str, handler: Callable):
        """注册消息处理器"""
        
    def add_middleware(self, middleware: Callable):
        """添加中间件（日志、验证等）"""
        
    async def process_message(self, session: Session, message: dict):
        """处理消息"""
        
    def validate_message(self, message: dict) -> bool:
        """验证消息格式"""
```

#### 增强的会话管理
```python
class EnhancedSession:
    """增强的会话类"""
    
    def __init__(self, session_id: str, socket, address):
        self.session_id = session_id
        self.socket = socket
        self.address = address
        self.authenticated = False
        self.player_name = None
        self.current_room = None
        self.last_heartbeat = time.time()
        self.message_queue = asyncio.Queue()
        self.language = "chinese"
    
    async def send_message(self, message: dict):
        """发送消息"""
        
    def is_alive(self) -> bool:
        """检查会话是否活跃"""
        
    def update_heartbeat(self):
        """更新心跳时间"""
```

### 2. 地形生成系统

#### 柏林噪声地形生成器
```python
class PerlinTerrainGenerator:
    """基于柏林噪声的地形生成器"""
    
    def __init__(self, seed: int = None, size: tuple = (1024, 1024)):
        self.seed = seed or random.randint(0, 2**32)
        self.size = size
        self.noise = PerlinNoise(octaves=4, seed=self.seed)
    
    def generate_heightmap(self) -> np.ndarray:
        """生成高度图"""
        
    def generate_biomes(self, heightmap: np.ndarray) -> np.ndarray:
        """根据高度图生成生物群系"""
        
    def generate_resources(self, heightmap: np.ndarray, biomes: np.ndarray) -> dict:
        """生成资源分布"""
        
    def create_region(self, region_name: str, center: tuple, radius: int) -> dict:
        """创建指定区域的地形数据"""
```

#### 真实世界数据生成器
```python
class RealWorldGenerator:
    """基于真实世界数据的地形生成器"""
    
    def __init__(self, api_client: APIClient, cache: DataCache):
        self.api_client = api_client
        self.cache = cache
        self.supported_regions = {
            "california": {"country": "US", "state": "CA"},
            "guangzhou": {"country": "CN", "city": "广州"},
            "tokyo": {"country": "JP", "city": "東京"},
            # 更多支持的地区
        }
    
    async def generate_region_data(self, region_name: str) -> dict:
        """生成指定地区的数据"""
        
    async def fetch_geographic_data(self, region: dict) -> dict:
        """获取地理数据"""
        
    async def fetch_economic_data(self, region: dict) -> dict:
        """获取经济数据"""
        
    async def fetch_demographic_data(self, region: dict) -> dict:
        """获取人口数据"""
```

### 3. 数据获取和缓存系统

#### API客户端
```python
class APIClient:
    """统一API客户端"""
    
    def __init__(self):
        self.session = aiohttp.ClientSession()
        self.endpoints = {
            "world_bank": "https://api.worldbank.org/v2",
            "openstreetmap": "https://api.openstreetmap.org",
            "geonames": "http://api.geonames.org",
            "natural_earth": "https://www.naturalearthdata.com/http//www.naturalearthdata.com/download"
        }
        self.rate_limits = {}
    
    async def fetch_world_bank_data(self, indicator: str, country: str) -> dict:
        """获取世界银行数据"""
        
    async def fetch_geographic_features(self, bbox: tuple) -> dict:
        """获取地理特征数据"""
        
    async def fetch_city_data(self, city_name: str, country: str) -> dict:
        """获取城市数据"""
```

#### 数据缓存系统
```python
class DataCache:
    """数据缓存系统"""
    
    def __init__(self, cache_dir: Path, ttl: int = 86400):
        self.cache_dir = cache_dir
        self.ttl = ttl  # 缓存有效期（秒）
        self.memory_cache = {}
    
    async def get(self, key: str) -> Optional[dict]:
        """获取缓存数据"""
        
    async def set(self, key: str, data: dict, ttl: int = None):
        """设置缓存数据"""
        
    def is_expired(self, timestamp: float) -> bool:
        """检查缓存是否过期"""
        
    async def cleanup_expired(self):
        """清理过期缓存"""
```

### 4. 语言管理系统重构

#### 增强的语言管理器
```python
class EnhancedLanguageManager:
    """增强的语言管理器"""
    
    def __init__(self, app_type: str = "server"):
        self.app_type = app_type
        self.current_language = "chinese"
        self.fallback_language = "english"
        self.languages = {}
        self.missing_keys = set()
        self.observers = []  # 语言变更观察者
    
    def get_text(self, key: str, **kwargs) -> str:
        """获取本地化文本"""
        
    def set_language(self, language: str):
        """设置当前语言"""
        
    def add_observer(self, observer: Callable):
        """添加语言变更观察者"""
        
    def validate_language_files(self) -> dict:
        """验证语言文件完整性"""
        
    def get_missing_keys(self) -> set:
        """获取缺失的翻译键"""
```

## 数据模型

### 地形数据模型
```python
@dataclass
class TerrainCell:
    """地形单元格"""
    x: int
    y: int
    elevation: float  # 海拔高度
    biome: str       # 生物群系
    temperature: float  # 温度
    precipitation: float  # 降水量
    resources: Dict[str, float]  # 资源分布
    
@dataclass
class Region:
    """地区数据模型"""
    name: str
    country: str
    center: Tuple[float, float]  # 经纬度
    terrain: List[List[TerrainCell]]
    cities: List['City']
    economic_data: Dict[str, Any]
    demographic_data: Dict[str, Any]
    
@dataclass
class City:
    """城市数据模型"""
    name: str
    population: int
    gdp_per_capita: float
    poverty_rate: float
    coordinates: Tuple[float, float]
    resources: Dict[str, float]
    infrastructure_level: float
```

### 游戏状态数据模型
```python
@dataclass
class GameRoom:
    """游戏房间数据模型"""
    room_id: str
    name: str
    host_id: str
    players: List['Player']
    max_players: int
    game_mode: str
    terrain_type: str  # "perlin" 或 "realworld"
    region_name: Optional[str]  # 真实世界模式的地区名
    status: str  # "waiting", "starting", "playing", "ended"
    created_at: datetime
    game_data: Optional[Dict[str, Any]]
    
@dataclass
class Player:
    """玩家数据模型"""
    player_id: str
    name: str
    session_id: str
    ready: bool
    country: Optional[str]  # 游戏中控制的国家
    resources: Dict[str, float]
    technologies: List[str]
    military_units: List['MilitaryUnit']
```

## 错误处理

### 错误类型定义
```python
class GameError(Exception):
    """游戏基础错误类"""
    def __init__(self, code: str, message: str, details: dict = None):
        self.code = code
        self.message = message
        self.details = details or {}
        super().__init__(message)

class NetworkError(GameError):
    """网络相关错误"""
    pass

class TerrainGenerationError(GameError):
    """地形生成错误"""
    pass

class DataFetchError(GameError):
    """数据获取错误"""
    pass
```

### 错误处理策略
1. **网络错误**: 自动重试机制，最多3次重试
2. **数据获取错误**: 使用缓存数据或默认数据
3. **地形生成错误**: 回退到简单地形生成
4. **会话错误**: 优雅断开连接并清理资源

## 测试策略

### 单元测试
- **地形生成器测试**: 验证柏林噪声和真实数据生成的正确性
- **API客户端测试**: 模拟API响应，测试数据解析
- **语言管理器测试**: 验证多语言支持和回退机制
- **消息处理器测试**: 验证消息格式和处理逻辑

### 集成测试
- **客户端-服务端通信测试**: 验证完整的消息流程
- **多玩家房间测试**: 模拟多个客户端同时操作
- **地形生成集成测试**: 验证完整的地形生成流程
- **数据缓存测试**: 验证缓存机制的正确性

### 性能测试
- **并发连接测试**: 测试服务器处理多个并发连接的能力
- **地形生成性能测试**: 测试大规模地形生成的性能
- **内存使用测试**: 监控长时间运行的内存使用情况
- **网络延迟测试**: 测试不同网络条件下的表现

### 用户验收测试
- **房间创建和加入流程测试**: 验证用户体验
- **语言切换测试**: 验证多语言支持的用户体验
- **地形预览测试**: 验证地形生成结果的可视化
- **错误处理测试**: 验证错误信息的用户友好性