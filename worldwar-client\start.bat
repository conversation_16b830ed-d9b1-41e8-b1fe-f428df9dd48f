@echo off
chcp 65001 >nul 2>&1
title World War Game Client

echo.
echo ============================================================
echo World War Game Client / 世界大战游戏客户端
echo ============================================================
echo Version: v3.0 / 版本: v3.0
echo Default Server: localhost:8888 / 默认服务器: localhost:8888
echo Log Directory: logs/client/ / 日志目录: logs/client/
echo Press Ctrl+C to exit / 按 Ctrl+C 退出程序
echo ============================================================
echo.

REM Check if Python is installed / 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found, please install Python 3.8+
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

REM Check dependencies / 检查依赖是否安装
if not exist "requirements.txt" (
    echo Warning: requirements.txt not found / 警告: 未找到requirements.txt文件
) else (
    echo Checking dependencies... / 检查依赖包...
    pip show requests >nul 2>&1
    if errorlevel 1 (
        echo Installing dependencies... / 正在安装依赖包...
        pip install -r requirements.txt
        if errorlevel 1 (
            echo Error: Failed to install dependencies / 依赖安装失败
            echo Please run manually: pip install -r requirements.txt
            echo 请手动运行: pip install -r requirements.txt
            pause
            exit /b 1
        )
    )
)

REM Create log directories / 创建日志目录
if not exist "logs" mkdir logs
if not exist "logs\client" mkdir logs\client

echo Starting client... / 启动客户端...
echo.

REM Start client / 启动客户端
python client_main.py %*

REM If client exits abnormally / 如果客户端异常退出，显示错误信息
if errorlevel 1 (
    echo.
    echo Error: Client exited abnormally, error code: %errorlevel%
    echo 错误: 客户端异常退出，错误代码: %errorlevel%
    echo Please check log files: logs\client\
    echo 请查看日志文件: logs\client\
    echo.
    pause
)
