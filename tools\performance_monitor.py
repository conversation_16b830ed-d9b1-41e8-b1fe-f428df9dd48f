#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能监控工具
Performance Monitoring Tool

提供实时性能监控、指标收集和分析功能
Provides real-time performance monitoring, metrics collection and analysis
"""

import json
import psutil
import threading
import time
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from collections import deque
import argparse

@dataclass
class PerformanceMetric:
    """性能指标数据结构"""
    timestamp: float
    metric_name: str
    value: float
    unit: str
    tags: Dict[str, str]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

@dataclass
class SystemSnapshot:
    """系统快照"""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_usage_percent: float
    disk_free_gb: float
    network_bytes_sent: int
    network_bytes_recv: int
    active_connections: int
    process_count: int
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, collection_interval: float = 5.0, max_history: int = 1000):
        """
        初始化性能监控器
        
        Args:
            collection_interval: 数据收集间隔（秒）
            max_history: 最大历史记录数量
        """
        self.collection_interval = collection_interval
        self.max_history = max_history
        
        # 数据存储
        self.metrics_history = deque(maxlen=max_history)
        self.system_snapshots = deque(maxlen=max_history)
        self.custom_metrics = {}
        
        # 控制变量
        self.is_running = False
        self.monitor_thread = None
        self.lock = threading.Lock()
        
        # 回调函数
        self.alert_callbacks = []
        self.metric_callbacks = []
        
        # 告警阈值
        self.alert_thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'disk_usage_percent': 90.0,
            'response_time_ms': 1000.0,
            'error_rate_percent': 5.0
        }
        
        # 网络基线（用于计算网络速度）
        self.network_baseline = None
        self.last_network_check = None
    
    def start_monitoring(self):
        """开始监控"""
        if self.is_running:
            print("性能监控已在运行中")
            return
        
        self.is_running = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        print(f"性能监控已启动，收集间隔: {self.collection_interval}秒")
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.is_running:
            return
        
        self.is_running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        print("性能监控已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.is_running:
            try:
                # 收集系统快照
                snapshot = self._collect_system_snapshot()
                
                with self.lock:
                    self.system_snapshots.append(snapshot)
                
                # 检查告警
                self._check_alerts(snapshot)
                
                # 调用回调函数
                for callback in self.metric_callbacks:
                    try:
                        callback(snapshot)
                    except Exception as e:
                        print(f"指标回调函数执行失败: {e}")
                
                time.sleep(self.collection_interval)
                
            except Exception as e:
                print(f"性能监控循环出错: {e}")
                time.sleep(self.collection_interval)
    
    def _collect_system_snapshot(self) -> SystemSnapshot:
        """收集系统快照"""
        current_time = time.time()
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 内存信息
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_used_mb = memory.used / (1024 * 1024)
        memory_available_mb = memory.available / (1024 * 1024)
        
        # 磁盘信息
        disk = psutil.disk_usage('/')
        disk_usage_percent = (disk.used / disk.total) * 100
        disk_free_gb = disk.free / (1024 * 1024 * 1024)
        
        # 网络信息
        network = psutil.net_io_counters()
        network_bytes_sent = network.bytes_sent
        network_bytes_recv = network.bytes_recv
        
        # 连接数
        try:
            connections = psutil.net_connections()
            active_connections = len([c for c in connections if c.status == 'ESTABLISHED'])
        except (psutil.AccessDenied, psutil.NoSuchProcess):
            active_connections = 0
        
        # 进程数
        process_count = len(psutil.pids())
        
        return SystemSnapshot(
            timestamp=current_time,
            cpu_percent=cpu_percent,
            memory_percent=memory_percent,
            memory_used_mb=memory_used_mb,
            memory_available_mb=memory_available_mb,
            disk_usage_percent=disk_usage_percent,
            disk_free_gb=disk_free_gb,
            network_bytes_sent=network_bytes_sent,
            network_bytes_recv=network_bytes_recv,
            active_connections=active_connections,
            process_count=process_count
        )
    
    def add_custom_metric(self, metric_name: str, value: float, unit: str = "",
                         tags: Dict[str, str] = None):
        """
        添加自定义指标
        
        Args:
            metric_name: 指标名称
            value: 指标值
            unit: 单位
            tags: 标签
        """
        metric = PerformanceMetric(
            timestamp=time.time(),
            metric_name=metric_name,
            value=value,
            unit=unit,
            tags=tags or {}
        )
        
        with self.lock:
            if metric_name not in self.custom_metrics:
                self.custom_metrics[metric_name] = deque(maxlen=self.max_history)
            
            self.custom_metrics[metric_name].append(metric)
            self.metrics_history.append(metric)
    
    def get_current_stats(self) -> Dict[str, Any]:
        """获取当前统计信息"""
        with self.lock:
            if not self.system_snapshots:
                return {}
            
            latest = self.system_snapshots[-1]
            
            # 计算网络速度（如果有历史数据）
            network_speed_mbps = 0.0
            if len(self.system_snapshots) >= 2:
                prev = self.system_snapshots[-2]
                time_diff = latest.timestamp - prev.timestamp
                if time_diff > 0:
                    bytes_diff = (latest.network_bytes_sent + latest.network_bytes_recv) - \
                                (prev.network_bytes_sent + prev.network_bytes_recv)
                    network_speed_mbps = (bytes_diff / time_diff) / (1024 * 1024)  # MB/s
            
            return {
                'timestamp': datetime.fromtimestamp(latest.timestamp).isoformat(),
                'cpu_percent': latest.cpu_percent,
                'memory_percent': latest.memory_percent,
                'memory_used_mb': latest.memory_used_mb,
                'memory_available_mb': latest.memory_available_mb,
                'disk_usage_percent': latest.disk_usage_percent,
                'disk_free_gb': latest.disk_free_gb,
                'network_speed_mbps': network_speed_mbps,
                'active_connections': latest.active_connections,
                'process_count': latest.process_count,
                'monitoring_duration_minutes': (time.time() - self.system_snapshots[0].timestamp) / 60
                    if len(self.system_snapshots) > 1 else 0
            }
    
    def get_historical_stats(self, minutes: int = 60) -> Dict[str, Any]:
        """
        获取历史统计信息
        
        Args:
            minutes: 历史时间范围（分钟）
            
        Returns:
            历史统计信息
        """
        cutoff_time = time.time() - (minutes * 60)
        
        with self.lock:
            # 过滤历史数据
            recent_snapshots = [s for s in self.system_snapshots if s.timestamp >= cutoff_time]
            
            if not recent_snapshots:
                return {}
            
            # 计算统计信息
            cpu_values = [s.cpu_percent for s in recent_snapshots]
            memory_values = [s.memory_percent for s in recent_snapshots]
            disk_values = [s.disk_usage_percent for s in recent_snapshots]
            connection_values = [s.active_connections for s in recent_snapshots]
            
            return {
                'time_range_minutes': minutes,
                'sample_count': len(recent_snapshots),
                'cpu_stats': {
                    'avg': sum(cpu_values) / len(cpu_values),
                    'min': min(cpu_values),
                    'max': max(cpu_values),
                    'current': cpu_values[-1]
                },
                'memory_stats': {
                    'avg': sum(memory_values) / len(memory_values),
                    'min': min(memory_values),
                    'max': max(memory_values),
                    'current': memory_values[-1]
                },
                'disk_stats': {
                    'avg': sum(disk_values) / len(disk_values),
                    'min': min(disk_values),
                    'max': max(disk_values),
                    'current': disk_values[-1]
                },
                'connection_stats': {
                    'avg': sum(connection_values) / len(connection_values),
                    'min': min(connection_values),
                    'max': max(connection_values),
                    'current': connection_values[-1]
                }
            }
    
    def get_custom_metric_stats(self, metric_name: str, minutes: int = 60) -> Dict[str, Any]:
        """
        获取自定义指标统计
        
        Args:
            metric_name: 指标名称
            minutes: 时间范围（分钟）
            
        Returns:
            指标统计信息
        """
        with self.lock:
            if metric_name not in self.custom_metrics:
                return {}
            
            cutoff_time = time.time() - (minutes * 60)
            recent_metrics = [m for m in self.custom_metrics[metric_name] 
                            if m.timestamp >= cutoff_time]
            
            if not recent_metrics:
                return {}
            
            values = [m.value for m in recent_metrics]
            
            return {
                'metric_name': metric_name,
                'time_range_minutes': minutes,
                'sample_count': len(recent_metrics),
                'unit': recent_metrics[0].unit,
                'stats': {
                    'avg': sum(values) / len(values),
                    'min': min(values),
                    'max': max(values),
                    'current': values[-1]
                },
                'recent_values': values[-10:]  # 最近10个值
            }
    
    def set_alert_threshold(self, metric_name: str, threshold: float):
        """
        设置告警阈值
        
        Args:
            metric_name: 指标名称
            threshold: 阈值
        """
        self.alert_thresholds[metric_name] = threshold
        print(f"设置告警阈值: {metric_name} = {threshold}")
    
    def add_alert_callback(self, callback: Callable[[str, float, float], None]):
        """
        添加告警回调函数
        
        Args:
            callback: 回调函数，参数为 (metric_name, current_value, threshold)
        """
        self.alert_callbacks.append(callback)
    
    def add_metric_callback(self, callback: Callable[[SystemSnapshot], None]):
        """
        添加指标回调函数
        
        Args:
            callback: 回调函数，参数为 SystemSnapshot
        """
        self.metric_callbacks.append(callback)
    
    def _check_alerts(self, snapshot: SystemSnapshot):
        """检查告警条件"""
        alerts = []
        
        # 检查CPU使用率
        if snapshot.cpu_percent > self.alert_thresholds.get('cpu_percent', 80):
            alerts.append(('cpu_percent', snapshot.cpu_percent, self.alert_thresholds['cpu_percent']))
        
        # 检查内存使用率
        if snapshot.memory_percent > self.alert_thresholds.get('memory_percent', 85):
            alerts.append(('memory_percent', snapshot.memory_percent, self.alert_thresholds['memory_percent']))
        
        # 检查磁盘使用率
        if snapshot.disk_usage_percent > self.alert_thresholds.get('disk_usage_percent', 90):
            alerts.append(('disk_usage_percent', snapshot.disk_usage_percent, self.alert_thresholds['disk_usage_percent']))
        
        # 触发告警回调
        for metric_name, current_value, threshold in alerts:
            for callback in self.alert_callbacks:
                try:
                    callback(metric_name, current_value, threshold)
                except Exception as e:
                    print(f"告警回调函数执行失败: {e}")
    
    def export_data(self, output_file: Path, format: str = "json"):
        """
        导出监控数据
        
        Args:
            output_file: 输出文件路径
            format: 导出格式 ("json", "csv")
        """
        with self.lock:
            data = {
                'export_time': datetime.now().isoformat(),
                'collection_interval': self.collection_interval,
                'system_snapshots': [s.to_dict() for s in self.system_snapshots],
                'custom_metrics': {
                    name: [m.to_dict() for m in metrics]
                    for name, metrics in self.custom_metrics.items()
                },
                'alert_thresholds': self.alert_thresholds
            }
        
        if format.lower() == "json":
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        elif format.lower() == "csv":
            self._export_csv(output_file, data)
        else:
            raise ValueError(f"不支持的导出格式: {format}")
        
        print(f"监控数据已导出到: {output_file}")
    
    def _export_csv(self, output_file: Path, data: Dict[str, Any]):
        """导出CSV格式数据"""
        import csv
        
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # 写入系统快照数据
            writer.writerow(['# System Snapshots'])
            if data['system_snapshots']:
                headers = list(data['system_snapshots'][0].keys())
                writer.writerow(headers)
                
                for snapshot in data['system_snapshots']:
                    row = [snapshot.get(h, '') for h in headers]
                    writer.writerow(row)
            
            # 写入自定义指标数据
            writer.writerow([])
            writer.writerow(['# Custom Metrics'])
            
            for metric_name, metrics in data['custom_metrics'].items():
                writer.writerow([f'## {metric_name}'])
                if metrics:
                    headers = list(metrics[0].keys())
                    writer.writerow(headers)
                    
                    for metric in metrics:
                        row = [metric.get(h, '') for h in headers]
                        writer.writerow(row)
                writer.writerow([])
    
    def generate_report(self, minutes: int = 60) -> str:
        """
        生成性能报告
        
        Args:
            minutes: 报告时间范围（分钟）
            
        Returns:
            报告文本
        """
        current_stats = self.get_current_stats()
        historical_stats = self.get_historical_stats(minutes)
        
        if not current_stats:
            return "暂无监控数据"
        
        report_lines = [
            "=" * 60,
            "性能监控报告",
            "=" * 60,
            "",
            f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"监控时间范围: 最近 {minutes} 分钟",
            f"数据收集间隔: {self.collection_interval} 秒",
            "",
            "当前系统状态:",
            "-" * 20,
            f"CPU使用率: {current_stats['cpu_percent']:.1f}%",
            f"内存使用率: {current_stats['memory_percent']:.1f}% ({current_stats['memory_used_mb']:.0f}MB)",
            f"可用内存: {current_stats['memory_available_mb']:.0f}MB",
            f"磁盘使用率: {current_stats['disk_usage_percent']:.1f}%",
            f"磁盘剩余空间: {current_stats['disk_free_gb']:.1f}GB",
            f"网络速度: {current_stats.get('network_speed_mbps', 0):.2f}MB/s",
            f"活跃连接数: {current_stats['active_connections']}",
            f"进程数: {current_stats['process_count']}",
        ]
        
        if historical_stats:
            report_lines.extend([
                "",
                f"历史统计 (最近{minutes}分钟):",
                "-" * 20,
                f"数据样本数: {historical_stats['sample_count']}",
                "",
                "CPU使用率:",
                f"  平均: {historical_stats['cpu_stats']['avg']:.1f}%",
                f"  最小: {historical_stats['cpu_stats']['min']:.1f}%",
                f"  最大: {historical_stats['cpu_stats']['max']:.1f}%",
                "",
                "内存使用率:",
                f"  平均: {historical_stats['memory_stats']['avg']:.1f}%",
                f"  最小: {historical_stats['memory_stats']['min']:.1f}%",
                f"  最大: {historical_stats['memory_stats']['max']:.1f}%",
                "",
                "磁盘使用率:",
                f"  平均: {historical_stats['disk_stats']['avg']:.1f}%",
                f"  最小: {historical_stats['disk_stats']['min']:.1f}%",
                f"  最大: {historical_stats['disk_stats']['max']:.1f}%",
                "",
                "活跃连接数:",
                f"  平均: {historical_stats['connection_stats']['avg']:.0f}",
                f"  最小: {historical_stats['connection_stats']['min']}",
                f"  最大: {historical_stats['connection_stats']['max']}",
            ])
        
        # 添加自定义指标统计
        if self.custom_metrics:
            report_lines.extend([
                "",
                "自定义指标:",
                "-" * 20
            ])
            
            for metric_name in self.custom_metrics.keys():
                metric_stats = self.get_custom_metric_stats(metric_name, minutes)
                if metric_stats:
                    stats = metric_stats['stats']
                    unit = metric_stats['unit']
                    report_lines.extend([
                        f"{metric_name} ({unit}):",
                        f"  平均: {stats['avg']:.2f}",
                        f"  最小: {stats['min']:.2f}",
                        f"  最大: {stats['max']:.2f}",
                        f"  当前: {stats['current']:.2f}",
                        ""
                    ])
        
        # 添加告警阈值信息
        report_lines.extend([
            "",
            "告警阈值:",
            "-" * 20
        ])
        
        for metric_name, threshold in self.alert_thresholds.items():
            report_lines.append(f"{metric_name}: {threshold}")
        
        # 添加健康状态评估
        report_lines.extend([
            "",
            "系统健康状态:",
            "-" * 20
        ])
        
        health_issues = []
        
        if current_stats['cpu_percent'] > self.alert_thresholds.get('cpu_percent', 80):
            health_issues.append(f"CPU使用率过高 ({current_stats['cpu_percent']:.1f}%)")
        
        if current_stats['memory_percent'] > self.alert_thresholds.get('memory_percent', 85):
            health_issues.append(f"内存使用率过高 ({current_stats['memory_percent']:.1f}%)")
        
        if current_stats['disk_usage_percent'] > self.alert_thresholds.get('disk_usage_percent', 90):
            health_issues.append(f"磁盘使用率过高 ({current_stats['disk_usage_percent']:.1f}%)")
        
        if health_issues:
            report_lines.append("⚠️  发现以下问题:")
            for issue in health_issues:
                report_lines.append(f"  - {issue}")
        else:
            report_lines.append("✅ 系统运行状态良好")
        
        report_lines.extend([
            "",
            "=" * 60,
            "报告结束",
            "=" * 60
        ])
        
        return "\n".join(report_lines)

def default_alert_callback(metric_name: str, current_value: float, threshold: float):
    """默认告警回调函数"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"🚨 [{timestamp}] 告警: {metric_name} = {current_value:.2f} (阈值: {threshold:.2f})")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='性能监控工具')
    parser.add_argument('--interval', type=float, default=5.0, help='数据收集间隔（秒）')
    parser.add_argument('--duration', type=int, default=0, help='监控持续时间（秒），0表示持续监控')
    parser.add_argument('--output', type=Path, help='导出数据文件路径')
    parser.add_argument('--format', choices=['json', 'csv'], default='json', help='导出格式')
    parser.add_argument('--report-interval', type=int, default=60, help='报告生成间隔（秒）')
    parser.add_argument('--cpu-threshold', type=float, default=80.0, help='CPU告警阈值')
    parser.add_argument('--memory-threshold', type=float, default=85.0, help='内存告警阈值')
    parser.add_argument('--disk-threshold', type=float, default=90.0, help='磁盘告警阈值')
    
    args = parser.parse_args()
    
    # 创建性能监控器
    monitor = PerformanceMonitor(collection_interval=args.interval)
    
    # 设置告警阈值
    monitor.set_alert_threshold('cpu_percent', args.cpu_threshold)
    monitor.set_alert_threshold('memory_percent', args.memory_threshold)
    monitor.set_alert_threshold('disk_usage_percent', args.disk_threshold)
    
    # 添加默认告警回调
    monitor.add_alert_callback(default_alert_callback)
    
    # 启动监控
    monitor.start_monitoring()
    
    try:
        if args.duration > 0:
            # 固定时间监控
            print(f"开始监控 {args.duration} 秒...")
            time.sleep(args.duration)
        else:
            # 持续监控
            print("开始持续监控，按 Ctrl+C 停止...")
            last_report_time = time.time()
            
            while True:
                time.sleep(1)
                
                # 定期生成报告
                if time.time() - last_report_time >= args.report_interval:
                    print("\n" + "="*60)
                    print("定期性能报告")
                    print("="*60)
                    
                    current_stats = monitor.get_current_stats()
                    if current_stats:
                        print(f"CPU: {current_stats['cpu_percent']:.1f}% | "
                              f"内存: {current_stats['memory_percent']:.1f}% | "
                              f"磁盘: {current_stats['disk_usage_percent']:.1f}% | "
                              f"连接: {current_stats['active_connections']}")
                    
                    last_report_time = time.time()
    
    except KeyboardInterrupt:
        print("\n收到停止信号...")
    
    finally:
        # 停止监控
        monitor.stop_monitoring()
        
        # 生成最终报告
        print("\n生成最终报告...")
        final_report = monitor.generate_report(minutes=60)
        print(final_report)
        
        # 导出数据
        if args.output:
            monitor.export_data(args.output, args.format)
        
        print("性能监控已结束")

if __name__ == '__main__':
    main()