# 🔧 故障排除指南 (Troubleshooting Guide)

本文档提供了世界大战游戏常见问题的解决方案和故障排除步骤。

---

## 📋 目录

- [快速诊断](#快速诊断)
- [服务器问题](#服务器问题)
- [客户端问题](#客户端问题)
- [网络连接问题](#网络连接问题)
- [地形生成问题](#地形生成问题)
- [性能问题](#性能问题)
- [语言和本地化问题](#语言和本地化问题)
- [日志分析](#日志分析)
- [高级故障排除](#高级故障排除)

---

## 🚀 快速诊断

### 自动诊断工具
```bash
# 运行系统诊断
python tools/system_monitor.py --diagnose

# 检查系统健康状态
python tools/system_monitor.py --health-check

# 运行连接测试
python tools/network_test.py --full-check
```

### 快速检查清单
- [ ] Python 版本是否为 3.8+
- [ ] 所有依赖是否已安装
- [ ] 配置文件是否正确
- [ ] 端口 8888 是否可用
- [ ] 网络连接是否正常
- [ ] 日志文件是否有错误信息

---

## 🖥️ 服务器问题

### 问题：服务器无法启动

#### 症状
```
Error: Address already in use
或
Error: Permission denied
或
服务器启动后立即退出
```

#### 解决方案

**1. 检查端口占用**
```bash
# Windows
netstat -ano | findstr :8888

# Linux/macOS
netstat -tulpn | grep 8888
lsof -i :8888
```

**2. 终止占用进程**
```bash
# Windows
taskkill /PID <PID> /F

# Linux/macOS
kill -9 <PID>
```

**3. 更改端口**
```json
// 编辑 worldwar-server/config/config.json
{
  "server": {
    "port": 8889  // 更改为其他端口
  }
}
```

**4. 检查权限**
```bash
# 确保有写入日志目录的权限
chmod 755 worldwar-server/logs/
chmod 644 worldwar-server/config/config.json
```

### 问题：服务器频繁崩溃

#### 症状
```
服务器运行一段时间后自动退出
内存使用持续增长
连接数达到上限
```

#### 解决方案

**1. 检查内存使用**
```bash
python tools/performance_monitor.py --memory-check
```

**2. 调整配置**
```json
{
  "server": {
    "max_connections": 50,  // 降低最大连接数
    "timeout": 30,          // 设置超时时间
    "cleanup_interval": 60  // 设置清理间隔
  }
}
```

**3. 启用详细日志**
```json
{
  "logging": {
    "level": "DEBUG",
    "enable_memory_tracking": true
  }
}
```

### 问题：房间管理异常

#### 症状
```
房间创建失败
玩家无法加入房间
房间状态不同步
```

#### 解决方案

**1. 重置房间数据**
```bash
python tools/game_admin.py --reset-rooms
```

**2. 检查房间配置**
```python
# 在服务器控制台运行
from server.enhanced_room_manager import EnhancedRoomManager
manager = EnhancedRoomManager()
print(manager.get_room_statistics())
```

**3. 清理僵尸房间**
```bash
python tools/game_admin.py --cleanup-zombie-rooms
```

---

## 💻 客户端问题

### 问题：客户端无法连接服务器

#### 症状
```
Connection refused
Connection timeout
Unable to connect to server
```

#### 解决方案

**1. 检查服务器状态**
```bash
# 测试服务器连接
telnet localhost 8888
# 或
nc -zv localhost 8888
```

**2. 检查防火墙设置**
```bash
# Windows
netsh advfirewall firewall add rule name="WorldWar" dir=in action=allow protocol=TCP localport=8888

# Linux (ufw)
sudo ufw allow 8888

# macOS
sudo pfctl -f /etc/pf.conf
```

**3. 检查网络配置**
```bash
# 检查本地网络
ping localhost
ping 127.0.0.1

# 检查DNS解析
nslookup your-server-domain.com
```

### 问题：客户端界面异常

#### 症状
```
界面显示错乱
文字重叠或缺失
颜色显示异常
```

#### 解决方案

**1. 检查终端兼容性**
```bash
# 检查终端类型
echo $TERM

# 设置兼容的终端类型
export TERM=xterm-256color
```

**2. 更新客户端样式**
```bash
# 重新生成样式文件
python worldwar-client/generate_styles.py
```

**3. 重置客户端配置**
```bash
# 删除客户端配置文件
rm worldwar-client/config/client_config.json
# 重新启动客户端
```

### 问题：客户端频繁断线

#### 症状
```
连接经常中断
需要频繁重新连接
网络错误提示
```

#### 解决方案

**1. 启用自动重连**
```json
// 编辑客户端配置
{
  "network": {
    "auto_reconnect": true,
    "reconnect_interval": 5,
    "max_reconnect_attempts": 10
  }
}
```

**2. 调整心跳设置**
```json
{
  "network": {
    "heartbeat_interval": 30,
    "heartbeat_timeout": 10
  }
}
```

---

## 🌐 网络连接问题

### 问题：网络延迟过高

#### 症状
```
操作响应缓慢
游戏卡顿
连接超时
```

#### 解决方案

**1. 网络诊断**
```bash
# 测试网络延迟
python tools/network_test.py --latency-test

# 检查网络质量
python tools/network_test.py --quality-check
```

**2. 优化网络设置**
```json
{
  "network": {
    "buffer_size": 8192,
    "tcp_nodelay": true,
    "keep_alive": true
  }
}
```

### 问题：防火墙阻止连接

#### 症状
```
连接被拒绝
特定端口无法访问
间歇性连接问题
```

#### 解决方案

**1. Windows 防火墙**
```cmd
# 添加防火墙规则
netsh advfirewall firewall add rule name="WorldWar Server" dir=in action=allow protocol=TCP localport=8888
netsh advfirewall firewall add rule name="WorldWar Client" dir=out action=allow protocol=TCP localport=8888
```

**2. Linux 防火墙 (iptables)**
```bash
# 允许端口
sudo iptables -A INPUT -p tcp --dport 8888 -j ACCEPT
sudo iptables -A OUTPUT -p tcp --sport 8888 -j ACCEPT

# 保存规则
sudo iptables-save > /etc/iptables/rules.v4
```

**3. 企业网络**
```bash
# 检查代理设置
echo $HTTP_PROXY
echo $HTTPS_PROXY

# 配置代理（如果需要）
export HTTP_PROXY=http://proxy.company.com:8080
export HTTPS_PROXY=http://proxy.company.com:8080
```

---

## 🗺️ 地形生成问题

### 问题：AI地形生成失败

#### 症状
```
地形生成超时
生成的地形为空
柏林噪声计算错误
```

#### 解决方案

**1. 检查地形生成器**
```bash
# 测试地形生成
python tools/terrain_preview.py --test-generation

# 检查噪声算法
python -c "from worldwar_server.world.terrain.perlin_generator import PerlinTerrainGenerator; gen = PerlinTerrainGenerator(); print('OK')"
```

**2. 调整生成参数**
```json
{
  "terrain": {
    "generation_timeout": 60,
    "max_size": [1024, 1024],
    "noise_scale": 0.1,
    "octaves": 4
  }
}
```

**3. 清理地形缓存**
```bash
python tools/terrain_preview.py --clear-cache
```

### 问题：真实世界数据获取失败

#### 症状
```
API调用超时
数据格式错误
地理数据缺失
```

#### 解决方案

**1. 检查API连接**
```bash
# 测试API连接
python tools/api_test.py --test-all

# 检查API密钥
python tools/api_test.py --check-keys
```

**2. 配置API设置**
```json
{
  "api": {
    "world_bank_key": "your-api-key",
    "timeout": 30,
    "retry_count": 3,
    "use_cache": true
  }
}
```

**3. 使用备用数据源**
```json
{
  "api": {
    "fallback_enabled": true,
    "fallback_data_path": "data/fallback/"
  }
}
```

### 问题：地形预览无法显示

#### 症状
```
预览图片生成失败
图片显示为空白
颜色映射错误
```

#### 解决方案

**1. 检查图像库**
```bash
# 安装/更新图像处理库
pip install --upgrade Pillow matplotlib numpy
```

**2. 测试图像生成**
```bash
# 生成测试预览
python tools/terrain_preview.py --test-image

# 检查字体支持
python tools/terrain_preview.py --check-fonts
```

**3. 修复中文字体问题**
```bash
# 运行字体修复脚本
python fix_chinese_font.py
```

---

## ⚡ 性能问题

### 问题：内存使用过高

#### 症状
```
内存使用持续增长
系统变慢
内存不足错误
```

#### 解决方案

**1. 内存分析**
```bash
# 分析内存使用
python tools/performance_monitor.py --memory-profile

# 检查内存泄漏
python tools/performance_monitor.py --leak-check
```

**2. 优化内存设置**
```json
{
  "performance": {
    "max_memory_usage": "512MB",
    "gc_threshold": 100,
    "cache_size": "128MB"
  }
}
```

**3. 启用内存监控**
```bash
# 持续监控内存
python tools/performance_monitor.py --monitor-memory --interval 60
```

### 问题：CPU使用率过高

#### 症状
```
CPU使用率持续100%
系统响应缓慢
风扇噪音大
```

#### 解决方案

**1. CPU分析**
```bash
# 分析CPU使用
python tools/performance_monitor.py --cpu-profile

# 查看进程状态
python tools/system_monitor.py --process-info
```

**2. 优化并发设置**
```json
{
  "server": {
    "worker_threads": 4,
    "max_concurrent_requests": 50,
    "request_timeout": 30
  }
}
```

### 问题：磁盘I/O过高

#### 症状
```
磁盘使用率100%
文件操作缓慢
日志写入延迟
```

#### 解决方案

**1. 磁盘分析**
```bash
# 检查磁盘使用
df -h
du -sh worldwar-server/logs/

# 分析I/O
python tools/system_monitor.py --disk-io
```

**2. 优化日志设置**
```json
{
  "logging": {
    "buffer_size": 8192,
    "flush_interval": 5,
    "compress_old_logs": true,
    "max_log_files": 5
  }
}
```

---

## 🌐 语言和本地化问题

### 问题：中文显示乱码

#### 症状
```
中文字符显示为方块
文字重叠
编码错误
```

#### 解决方案

**1. 检查系统编码**
```bash
# 检查系统编码
python -c "import sys; print(sys.getdefaultencoding())"
python -c "import locale; print(locale.getpreferredencoding())"

# 设置UTF-8编码
export LANG=zh_CN.UTF-8
export LC_ALL=zh_CN.UTF-8
```

**2. 修复字体问题**
```bash
# 运行字体修复
python fix_chinese_font.py

# 测试中文显示
python test_chinese_font.py
```

### 问题：语言切换失败

#### 症状
```
语言切换无效果
部分文本未翻译
语言文件加载失败
```

#### 解决方案

**1. 检查语言文件**
```bash
# 验证语言文件
python tools/language_checker.py --check-all

# 修复缺失翻译
python tools/language_checker.py --fix-missing
```

**2. 重新加载语言**
```python
# 在游戏中执行
from worldwar_server.shared.enhanced_language_manager import EnhancedLanguageManager
manager = EnhancedLanguageManager()
manager.reload_all_languages()
```

---

## 📊 日志分析

### 日志文件位置
```
worldwar-server/logs/
├── server.log          # 服务器主日志
├── network.log         # 网络通信日志
├── game.log           # 游戏逻辑日志
├── terrain.log        # 地形生成日志
├── performance.log    # 性能监控日志
└── error.log          # 错误日志
```

### 常用日志分析命令

**1. 查看最新错误**
```bash
tail -f worldwar-server/logs/error.log
```

**2. 搜索特定错误**
```bash
grep -i "error" worldwar-server/logs/server.log
grep -i "timeout" worldwar-server/logs/network.log
grep -i "memory" worldwar-server/logs/performance.log
```

**3. 分析日志统计**
```bash
python tools/log_analyzer.py --analyze worldwar-server/logs/server.log
```

### 常见错误模式

**1. 连接错误**
```
[ERROR] Connection refused by client
[ERROR] Socket timeout after 30 seconds
[ERROR] Maximum connections reached
```

**2. 内存错误**
```
[ERROR] Out of memory
[ERROR] Memory allocation failed
[WARNING] High memory usage: 85%
```

**3. API错误**
```
[ERROR] API request failed: timeout
[ERROR] Invalid API response format
[WARNING] API rate limit exceeded
```

---

## 🔬 高级故障排除

### 调试模式启动

**1. 服务器调试模式**
```bash
python worldwar-server/server_main.py --debug --verbose
```

**2. 客户端调试模式**
```bash
python worldwar-client/client_main.py --debug --log-level DEBUG
```

### 性能分析

**1. 代码性能分析**
```bash
# 使用cProfile分析
python -m cProfile -o profile.stats worldwar-server/server_main.py

# 分析结果
python -c "import pstats; p = pstats.Stats('profile.stats'); p.sort_stats('cumulative').print_stats(20)"
```

**2. 内存分析**
```bash
# 使用memory_profiler
pip install memory_profiler
python -m memory_profiler worldwar-server/server_main.py
```

### 网络分析

**1. 抓包分析**
```bash
# 使用tcpdump (Linux/macOS)
sudo tcpdump -i lo -p tcp port 8888

# 使用Wireshark (图形界面)
# 过滤器: tcp.port == 8888
```

**2. 连接状态分析**
```bash
# 查看连接状态
netstat -an | grep 8888
ss -tuln | grep 8888
```

### 数据库分析

**1. SQLite数据库检查**
```bash
# 检查数据库完整性
sqlite3 worldwar-server/data/game.db "PRAGMA integrity_check;"

# 分析数据库大小
sqlite3 worldwar-server/data/game.db ".dbinfo"
```

### 系统资源监控

**1. 实时监控**
```bash
# 系统资源监控
python tools/system_monitor.py --real-time

# 进程监控
python tools/system_monitor.py --monitor-process server_main.py
```

**2. 资源限制**
```bash
# 设置内存限制 (Linux)
ulimit -v 1048576  # 1GB

# 设置文件描述符限制
ulimit -n 1024
```

---

## 📞 获取帮助

如果以上解决方案都无法解决问题，请：

1. **收集诊断信息**
   ```bash
   python tools/system_monitor.py --diagnose > diagnosis.txt
   ```

2. **生成错误报告**
   ```bash
   python tools/error_reporter.py --generate-report
   ```

3. **查看项目文档**
   - [部署指南](DEPLOYMENT_GUIDE.md)
   - [技术文档](TECHNICAL.md)
   - [游戏规则](GAME_RULES.md)

4. **联系支持**
   - 提交详细的错误报告
   - 包含系统信息和日志文件
   - 描述重现步骤

---

**最后更新**: 2024年1月
**版本**: v5.0