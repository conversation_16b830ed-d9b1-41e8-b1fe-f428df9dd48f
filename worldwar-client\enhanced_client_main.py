#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强世界大战游戏客户端主程序
Enhanced World War Game Client Main Program
"""

import argparse
import sys
import time
import threading
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
client_dir = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(client_dir))

# 添加服务器路径以访问共享模块
server_dir = project_root / "worldwar-server"
sys.path.insert(0, str(server_dir))

from client.enhanced_network_client import EnhancedNetworkClient, ConnectionState
from client.room_ui import RoomUI, UIState
from shared.enhanced_language_manager import get_enhanced_language_manager

lang_manager = get_enhanced_language_manager("client")
class EnhancedGameClient:
    """增强游戏客户端"""
    
    def __init__(self, host="localhost", port=8888, username=None):
        """初始化增强游戏客户端"""
        self.host = host
        self.port = port
        self.username = username or self._generate_username()
        
        # 创建网络客户端
        self.network_client = EnhancedNetworkClient(host, port)
        self.room_ui = self.network_client.get_room_ui()
        
        # UI状态
        self.running = True
        self.last_ui_render = ""
        
        # 添加UI更新回调
        self.network_client.add_ui_callback(self._on_ui_update)
        
        print(lang_manager.get_text("client.initialized"))
        print(lang_manager.get_text("client.server_info", host=host, port=port))
        print(lang_manager.get_text("client.username_info", username=self.username))
    
    def _generate_username(self):
        """生成随机用户名"""
        import random
        adjectives = ["勇敢的", "智慧的", "强大的", "神秘的", "传奇的"]
        nouns = ["战士", "指挥官", "将军", "英雄", "领袖"]
        return f"{random.choice(adjectives)}{random.choice(nouns)}{random.randint(100, 999)}"
    
    def start(self):
        """启动客户端"""
        print(f"\n{lang_manager.get_text('client.starting')}")
        
        # 连接到服务器
        if not self._connect_to_server():
            print(lang_manager.get_text("client.connection_failed"))
            return
        
        print(lang_manager.get_text("client.connected_success"))
        print(lang_manager.get_text("client.getting_rooms"))
        
        # 请求房间列表
        self.network_client.request_room_list()
        
        # 启动主循环
        self._main_loop()
    
    def _connect_to_server(self):
        """连接到服务器"""
        max_attempts = 3
        for attempt in range(max_attempts):
            print(lang_manager.get_text("client.connection_attempt", attempt=attempt + 1, max_attempts=max_attempts))
            
            if self.network_client.connect(self.username):
                return True
            
            if attempt < max_attempts - 1:
                print(lang_manager.get_text("client.retry_wait"))
                time.sleep(3)
        
        return False
    
    def _main_loop(self):
        """主循环"""
        print("\n" + "="*60)
        print(lang_manager.get_text("client.title"))
        print(lang_manager.get_text("client.help_command"))
        print(lang_manager.get_text("client.quit_command"))
        print("="*60)
        
        # 初始UI渲染
        self._render_ui()
        
        try:
            while self.running and self.network_client.is_connected():
                try:
                    # 获取用户输入
                    user_input = input("\n>>> ").strip()
                    
                    if not user_input:
                        continue
                    
                    # 处理退出命令
                    if user_input.lower() in ['quit', 'exit', 'q']:
                        self._handle_quit()
                        break
                    
                    # 处理帮助命令
                    if user_input.lower() in ['help', 'h']:
                        self._show_help()
                        continue
                    
                    # 处理清屏命令
                    if user_input.lower() in ['clear', 'cls']:
                        self._clear_screen()
                        self._render_ui()
                        continue
                    
                    # 让网络客户端处理命令
                    if not self.network_client.handle_user_input(user_input):
                        print(lang_manager.get_text("client.unknown_command", command=user_input))
                        print(lang_manager.get_text("client.help_tip"))
                
                except KeyboardInterrupt:
                    print(f"\n{lang_manager.get_text('client.interrupt_signal')}")
                    self._handle_quit()
                    break
                except EOFError:
                    print(f"\n{lang_manager.get_text('client.input_ended')}")
                    self._handle_quit()
                    break
                except Exception as e:
                    print(lang_manager.get_text("client.input_error", error=e))
        
        finally:
            self._cleanup()
    
    def _handle_quit(self):
        """处理退出"""
        print(lang_manager.get_text("client.exiting"))
        self.running = False
        
        # 如果在房间中，先离开房间
        if self.room_ui.is_in_room():
            print(lang_manager.get_text("client.leaving_room"))
            self.network_client.leave_room()
            time.sleep(0.5)  # 等待离开房间完成
        
        # 断开连接
        self.network_client.disconnect()
        print(lang_manager.get_text("client.safely_exited"))
    
    def _cleanup(self):
        """清理资源"""
        try:
            if self.network_client.is_connected():
                self.network_client.disconnect()
        except:
            pass
    
    def _show_help(self):
        """显示帮助信息"""
        current_state = self.room_ui.get_current_state()
        
        print("\n" + "="*50)
        print(lang_manager.get_text("client.help_info"))
        print("="*50)
        
        # 通用命令
        print(lang_manager.get_text("client.general_commands"))
        print(f"  {lang_manager.get_text('client.help_desc')}")
        print(f"  {lang_manager.get_text('client.clear_desc')}")
        print(f"  {lang_manager.get_text('client.quit_desc')}")
        print(f"  {lang_manager.get_text('client.status_desc')}")
        print(f"  {lang_manager.get_text('client.rooms_desc')}")
        
        # 根据当前状态显示相关命令
        if current_state == UIState.ROOM_LIST:
            print(f"\n{lang_manager.get_text('client.room_list_commands')}")
            print(f"  {lang_manager.get_text('client.join_desc')}")
            print(f"  {lang_manager.get_text('client.create_desc')}")
            print(f"  {lang_manager.get_text('client.refresh_desc')}")
        
        elif current_state == UIState.WAITING_ROOM:
            print(f"\n{lang_manager.get_text('client.waiting_room_commands')}")
            print(f"  {lang_manager.get_text('client.ready_desc')}")
            print(f"  {lang_manager.get_text('client.start_desc')}")
            print(f"  {lang_manager.get_text('client.leave_desc')}")
        
        print("="*50)
    
    def _clear_screen(self):
        """清屏"""
        import os
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def _render_ui(self):
        """渲染UI"""
        current_render = self.room_ui.render_current_ui()
        
        # 只有当UI内容发生变化时才重新渲染
        if current_render != self.last_ui_render:
            if current_render:
                print("\n" + current_render)
            self.last_ui_render = current_render
    
    def _on_ui_update(self):
        """UI更新回调"""
        # 在主线程中渲染UI
        threading.Thread(target=self._render_ui, daemon=True).start()
    
    def get_connection_info(self):
        """获取连接信息"""
        state = self.network_client.get_connection_state()
        return {
            "state": state.value,
            "connected": self.network_client.is_connected(),
            "username": self.username,
            "server": f"{self.host}:{self.port}"
        }


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="世界大战游戏增强客户端",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s                                    # 使用默认设置
  %(prog)s --host ************* --port 8888  # 连接到指定服务器
  %(prog)s --username "我的用户名"             # 使用指定用户名
        """
    )
    
    parser.add_argument(
        "--host",
        default="localhost",
        help="服务器地址 (默认: localhost)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=8888,
        help="服务器端口 (默认: 8888)"
    )
    
    parser.add_argument(
        "--username",
        help="玩家用户名 (默认: 自动生成)"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="启用调试模式"
    )
    
    parser.add_argument(
        "--version",
        action="version",
        version="世界大战游戏增强客户端 v1.0"
    )
    
    return parser.parse_args()


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 创建并启动客户端
    try:
        client = EnhancedGameClient(
            host=args.host,
            port=args.port,
            username=args.username
        )
        
        client.start()
        
    except KeyboardInterrupt:
        print(f"\n{lang_manager.get_text('client.interrupted')}")
    except Exception as e:
        print(lang_manager.get_text("client.program_error", error=e))
        if args.debug:
            import traceback
            traceback.print_exc()
    finally:
        print(lang_manager.get_text("client.program_exited"))


if __name__ == "__main__":
    main()