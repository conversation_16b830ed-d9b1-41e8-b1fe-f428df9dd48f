"""
地形数据模型

本模块定义了地形生成系统使用的核心数据结构，包括地形单元格、区域和城市等模型。
支持数据序列化、反序列化和完整性验证。
"""

from dataclasses import dataclass, field, asdict
from typing import Dict, List, Tuple, Optional, Any, Union
from datetime import datetime
import json
import logging
from enum import Enum
import math

logger = logging.getLogger(__name__)


class BiomeType(Enum):
    """生物群系类型枚举"""
    OCEAN = "ocean"
    PLAINS = "plains"
    FOREST = "forest"
    DESERT = "desert"
    MOUNTAIN = "mountain"
    TUNDRA = "tundra"
    SWAMP = "swamp"
    GRASSLAND = "grassland"
    HILLS = "hills"
    COASTAL = "coastal"


class ResourceType(Enum):
    """资源类型枚举"""
    OIL = "oil"
    COAL = "coal"
    IRON = "iron"
    GOLD = "gold"
    URANIUM = "uranium"
    FOOD = "food"
    WATER = "water"
    WOOD = "wood"
    STONE = "stone"


@dataclass
class TerrainCell:
    """地形单元格数据模型"""
    x: int
    y: int
    elevation: float  # 海拔高度 (米)
    biome: str       # 生物群系
    temperature: float  # 温度 (摄氏度)
    precipitation: float  # 降水量 (毫米/年)
    resources: Dict[str, float] = field(default_factory=dict)  # 资源分布 (0-1)
    
    def __post_init__(self):
        """数据验证和初始化"""
        self.validate()
    
    def validate(self) -> bool:
        """验证地形单元格数据的有效性"""
        errors = []
        
        # 验证坐标
        if not isinstance(self.x, int) or not isinstance(self.y, int):
            errors.append("坐标必须为整数")
        
        if self.x < 0 or self.y < 0:
            errors.append("坐标不能为负数")
        
        # 验证海拔高度 (-11000米到8848米)
        if not isinstance(self.elevation, (int, float)):
            errors.append("海拔高度必须为数值")
        elif self.elevation < -11000 or self.elevation > 9000:
            errors.append("海拔高度超出合理范围 (-11000m 到 9000m)")
        
        # 验证生物群系
        if not isinstance(self.biome, str):
            errors.append("生物群系必须为字符串")
        elif self.biome not in [biome.value for biome in BiomeType]:
            logger.warning(f"未知的生物群系类型: {self.biome}")
        
        # 验证温度 (-90°C到60°C)
        if not isinstance(self.temperature, (int, float)):
            errors.append("温度必须为数值")
        elif self.temperature < -90 or self.temperature > 60:
            errors.append("温度超出合理范围 (-90°C 到 60°C)")
        
        # 验证降水量 (0到10000毫米/年)
        if not isinstance(self.precipitation, (int, float)):
            errors.append("降水量必须为数值")
        elif self.precipitation < 0 or self.precipitation > 10000:
            errors.append("降水量超出合理范围 (0 到 10000mm/年)")
        
        # 验证资源分布
        if not isinstance(self.resources, dict):
            errors.append("资源分布必须为字典")
        else:
            for resource, amount in self.resources.items():
                if not isinstance(resource, str):
                    errors.append(f"资源类型必须为字符串: {resource}")
                if not isinstance(amount, (int, float)):
                    errors.append(f"资源数量必须为数值: {resource}")
                elif amount < 0 or amount > 1:
                    errors.append(f"资源数量必须在0-1范围内: {resource}={amount}")
        
        if errors:
            raise ValueError(f"地形单元格验证失败: {'; '.join(errors)}")
        
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TerrainCell':
        """从字典创建地形单元格"""
        return cls(**data)
    
    def get_resource_richness(self) -> float:
        """计算资源丰富度"""
        if not self.resources:
            return 0.0
        return sum(self.resources.values()) / len(self.resources)
    
    def is_habitable(self) -> bool:
        """判断是否适合居住"""
        # 基于温度、降水量和海拔判断
        temp_ok = -20 <= self.temperature <= 40
        precip_ok = self.precipitation >= 200  # 至少200mm降水
        elev_ok = self.elevation >= 0 and self.elevation <= 3000  # 海平面到3000米
        return temp_ok and precip_ok and elev_ok


@dataclass
class City:
    """城市数据模型"""
    name: str
    population: int
    gdp_per_capita: float  # 人均GDP (美元)
    poverty_rate: float    # 贫困率 (0-1)
    coordinates: Tuple[float, float]  # 经纬度
    resources: Dict[str, float] = field(default_factory=dict)  # 城市资源
    infrastructure_level: float = 0.5  # 基础设施水平 (0-1)
    
    def __post_init__(self):
        """数据验证和初始化"""
        self.validate()
    
    def validate(self) -> bool:
        """验证城市数据的有效性"""
        errors = []
        
        # 验证城市名称
        if not isinstance(self.name, str) or not self.name.strip():
            errors.append("城市名称不能为空")
        
        # 验证人口
        if not isinstance(self.population, int) or self.population < 0:
            errors.append("人口必须为非负整数")
        
        # 验证人均GDP
        if not isinstance(self.gdp_per_capita, (int, float)) or self.gdp_per_capita < 0:
            errors.append("人均GDP必须为非负数值")
        
        # 验证贫困率
        if not isinstance(self.poverty_rate, (int, float)):
            errors.append("贫困率必须为数值")
        elif self.poverty_rate < 0 or self.poverty_rate > 1:
            errors.append("贫困率必须在0-1范围内")
        
        # 验证坐标
        if not isinstance(self.coordinates, (tuple, list)) or len(self.coordinates) != 2:
            errors.append("坐标必须为包含两个元素的元组或列表")
        else:
            lat, lon = self.coordinates
            if not isinstance(lat, (int, float)) or not isinstance(lon, (int, float)):
                errors.append("经纬度必须为数值")
            elif lat < -90 or lat > 90:
                errors.append("纬度必须在-90到90度之间")
            elif lon < -180 or lon > 180:
                errors.append("经度必须在-180到180度之间")
        
        # 验证基础设施水平
        if not isinstance(self.infrastructure_level, (int, float)):
            errors.append("基础设施水平必须为数值")
        elif self.infrastructure_level < 0 or self.infrastructure_level > 1:
            errors.append("基础设施水平必须在0-1范围内")
        
        # 验证资源
        if not isinstance(self.resources, dict):
            errors.append("资源必须为字典")
        else:
            for resource, amount in self.resources.items():
                if not isinstance(resource, str):
                    errors.append(f"资源类型必须为字符串: {resource}")
                if not isinstance(amount, (int, float)) or amount < 0:
                    errors.append(f"资源数量必须为非负数值: {resource}")
        
        if errors:
            raise ValueError(f"城市数据验证失败: {'; '.join(errors)}")
        
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'City':
        """从字典创建城市"""
        # 处理coordinates坐标（确保是tuple）
        if 'coordinates' in data and isinstance(data['coordinates'], list):
            data['coordinates'] = tuple(data['coordinates'])
        return cls(**data)
    
    def get_economic_level(self) -> str:
        """获取经济发展水平"""
        if self.gdp_per_capita >= 50000:
            return "发达"
        elif self.gdp_per_capita >= 20000:
            return "中等发达"
        elif self.gdp_per_capita >= 5000:
            return "发展中"
        else:
            return "欠发达"
    
    def calculate_city_score(self) -> float:
        """计算城市综合评分"""
        # 基于人口、GDP、基础设施和贫困率的综合评分
        pop_score = min(self.population / 1000000, 1.0)  # 人口评分，100万为满分
        gdp_score = min(self.gdp_per_capita / 50000, 1.0)  # GDP评分，5万美元为满分
        infra_score = self.infrastructure_level
        poverty_penalty = self.poverty_rate  # 贫困率越高扣分越多
        
        return (pop_score * 0.3 + gdp_score * 0.4 + infra_score * 0.3) * (1 - poverty_penalty)


@dataclass
class Region:
    """地区数据模型"""
    name: str
    country: str
    center: Tuple[float, float]  # 中心点经纬度
    terrain: List[List[TerrainCell]] = field(default_factory=list)
    cities: List[City] = field(default_factory=list)
    economic_data: Dict[str, Any] = field(default_factory=dict)
    demographic_data: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    
    def __post_init__(self):
        """数据验证和初始化"""
        self.validate()
    
    def validate(self) -> bool:
        """验证地区数据的有效性"""
        errors = []
        
        # 验证地区名称
        if not isinstance(self.name, str) or not self.name.strip():
            errors.append("地区名称不能为空")
        
        # 验证国家名称
        if not isinstance(self.country, str) or not self.country.strip():
            errors.append("国家名称不能为空")
        
        # 验证中心坐标
        if not isinstance(self.center, (tuple, list)) or len(self.center) != 2:
            errors.append("中心坐标必须为包含两个元素的元组或列表")
        else:
            lat, lon = self.center
            if not isinstance(lat, (int, float)) or not isinstance(lon, (int, float)):
                errors.append("中心坐标必须为数值")
            elif lat < -90 or lat > 90:
                errors.append("中心纬度必须在-90到90度之间")
            elif lon < -180 or lon > 180:
                errors.append("中心经度必须在-180到180度之间")
        
        # 验证地形数据
        if not isinstance(self.terrain, list):
            errors.append("地形数据必须为列表")
        else:
            for i, row in enumerate(self.terrain):
                if not isinstance(row, list):
                    errors.append(f"地形数据第{i}行必须为列表")
                else:
                    for j, cell in enumerate(row):
                        if not isinstance(cell, TerrainCell):
                            errors.append(f"地形单元格[{i}][{j}]必须为TerrainCell类型")
        
        # 验证城市数据
        if not isinstance(self.cities, list):
            errors.append("城市数据必须为列表")
        else:
            for i, city in enumerate(self.cities):
                if not isinstance(city, City):
                    errors.append(f"城市[{i}]必须为City类型")
        
        # 验证经济和人口数据
        if not isinstance(self.economic_data, dict):
            errors.append("经济数据必须为字典")
        
        if not isinstance(self.demographic_data, dict):
            errors.append("人口数据必须为字典")
        
        if errors:
            raise ValueError(f"地区数据验证失败: {'; '.join(errors)}")
        
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        # 处理datetime序列化
        data['created_at'] = self.created_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Region':
        """从字典创建地区"""
        # 处理datetime反序列化
        if 'created_at' in data and isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        
        # 处理center坐标（确保是tuple）
        if 'center' in data and isinstance(data['center'], list):
            data['center'] = tuple(data['center'])
        
        # 重建地形数据
        if 'terrain' in data:
            terrain = []
            for row in data['terrain']:
                terrain_row = []
                for cell_data in row:
                    terrain_row.append(TerrainCell.from_dict(cell_data))
                terrain.append(terrain_row)
            data['terrain'] = terrain
        
        # 重建城市数据
        if 'cities' in data:
            cities = []
            for city_data in data['cities']:
                cities.append(City.from_dict(city_data))
            data['cities'] = cities
        
        return cls(**data)
    
    def get_terrain_size(self) -> Tuple[int, int]:
        """获取地形尺寸"""
        if not self.terrain:
            return (0, 0)
        return (len(self.terrain), len(self.terrain[0]) if self.terrain[0] else 0)
    
    def get_terrain_cell(self, x: int, y: int) -> Optional[TerrainCell]:
        """获取指定位置的地形单元格"""
        if 0 <= y < len(self.terrain) and 0 <= x < len(self.terrain[y]):
            return self.terrain[y][x]
        return None
    
    def get_total_population(self) -> int:
        """获取总人口"""
        return sum(city.population for city in self.cities)
    
    def get_average_gdp_per_capita(self) -> float:
        """获取平均人均GDP"""
        if not self.cities:
            return 0.0
        
        total_gdp = sum(city.gdp_per_capita * city.population for city in self.cities)
        total_population = self.get_total_population()
        
        return total_gdp / total_population if total_population > 0 else 0.0
    
    def get_resource_summary(self) -> Dict[str, float]:
        """获取地区资源汇总"""
        resource_totals = {}
        cell_count = 0
        
        for row in self.terrain:
            for cell in row:
                cell_count += 1
                for resource, amount in cell.resources.items():
                    resource_totals[resource] = resource_totals.get(resource, 0) + amount
        
        # 计算平均资源密度
        if cell_count > 0:
            for resource in resource_totals:
                resource_totals[resource] /= cell_count
        
        return resource_totals
    
    def get_biome_distribution(self) -> Dict[str, int]:
        """获取生物群系分布统计"""
        biome_counts = {}
        
        for row in self.terrain:
            for cell in row:
                biome_counts[cell.biome] = biome_counts.get(cell.biome, 0) + 1
        
        return biome_counts


class TerrainDataValidator:
    """地形数据验证器"""
    
    @staticmethod
    def validate_terrain_consistency(region: Region) -> List[str]:
        """验证地形数据的一致性"""
        warnings = []
        
        if not region.terrain:
            return ["地形数据为空"]
        
        # 检查地形网格的一致性
        expected_width = len(region.terrain[0]) if region.terrain else 0
        for i, row in enumerate(region.terrain):
            if len(row) != expected_width:
                warnings.append(f"地形第{i}行宽度不一致: 期望{expected_width}, 实际{len(row)}")
        
        # 检查坐标一致性
        for y, row in enumerate(region.terrain):
            for x, cell in enumerate(row):
                if cell.x != x or cell.y != y:
                    warnings.append(f"地形单元格坐标不匹配: 位置({x},{y}), 单元格坐标({cell.x},{cell.y})")
        
        # 检查生物群系和海拔的合理性
        for row in region.terrain:
            for cell in row:
                if cell.biome == BiomeType.OCEAN.value and cell.elevation > 0:
                    warnings.append(f"海洋生物群系的海拔应为负值: ({cell.x},{cell.y})")
                elif cell.biome == BiomeType.MOUNTAIN.value and cell.elevation < 1000:
                    warnings.append(f"山地生物群系的海拔过低: ({cell.x},{cell.y})")
        
        return warnings
    
    @staticmethod
    def validate_city_positions(region: Region) -> List[str]:
        """验证城市位置的合理性"""
        warnings = []
        
        for city in region.cities:
            lat, lon = city.coordinates
            center_lat, center_lon = region.center
            
            # 检查城市是否在地区范围内（简单的距离检查）
            distance = math.sqrt((lat - center_lat)**2 + (lon - center_lon)**2)
            if distance > 10:  # 假设地区半径不超过10度
                warnings.append(f"城市{city.name}距离地区中心过远: {distance:.2f}度")
            
            # 检查城市是否在适宜居住的地形上
            # 这里需要将经纬度转换为地形网格坐标，简化处理
            if region.terrain:
                # 简单映射到地形网格中心附近
                height, width = region.get_terrain_size()
                grid_x = int((lon - center_lon + 5) * width / 10)  # 假设10度范围
                grid_y = int((lat - center_lat + 5) * height / 10)
                
                cell = region.get_terrain_cell(grid_x, grid_y)
                if cell and not cell.is_habitable():
                    warnings.append(f"城市{city.name}位于不适宜居住的地形上")
        
        return warnings


def serialize_region_to_json(region: Region, filepath: str) -> None:
    """将地区数据序列化为JSON文件"""
    try:
        data = region.to_dict()
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info(f"地区数据已保存到: {filepath}")
    except Exception as e:
        logger.error(f"序列化地区数据失败: {e}")
        raise


def deserialize_region_from_json(filepath: str) -> Region:
    """从JSON文件反序列化地区数据"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        region = Region.from_dict(data)
        logger.info(f"地区数据已从文件加载: {filepath}")
        return region
    except Exception as e:
        logger.error(f"反序列化地区数据失败: {e}")
        raise