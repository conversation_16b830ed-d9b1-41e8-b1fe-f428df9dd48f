#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
世界生成器
World Generator

根据游戏设计理念生成非对称的世界地图和起始条件
Generates asymmetric world maps and starting conditions based on game design philosophy
"""

import random
import math
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from enum import Enum

class TerrainType(Enum):
    """地形类型"""
    PLAINS = "plains"          # 平原
    MOUNTAINS = "mountains"    # 山地
    FOREST = "forest"         # 森林
    DESERT = "desert"         # 沙漠
    SWAMP = "swamp"           # 沼泽
    COAST = "coast"           # 海岸
    OCEAN = "ocean"           # 海洋
    RIVER = "river"           # 河流

class ResourceType(Enum):
    """资源类型"""
    # 基础资源
    FOOD = "food"             # 食物
    WOOD = "wood"             # 木材
    STONE = "stone"           # 石材
    COAL = "coal"             # 煤炭
    IRON = "iron"             # 铁矿
    
    # 战略资源
    OIL = "oil"               # 石油
    URANIUM = "uranium"       # 铀
    RUBBER = "rubber"         # 橡胶
    RARE_METALS = "rare_metals"  # 稀有金属

class RegionType(Enum):
    """地区类型"""
    DEVELOPED = "developed"       # 发达地区
    DEVELOPING = "developing"     # 发展中地区
    POOR = "poor"                # 贫困地区
    RESOURCE_RICH = "resource_rich"  # 资源丰富地区

@dataclass
class WorldTile:
    """世界地图格子"""
    x: int
    y: int
    terrain: TerrainType
    resources: Dict[ResourceType, int]
    population: int
    infrastructure: int  # 基础设施水平 0-100
    owner: Optional[str] = None  # 拥有者ID

@dataclass
class StartingPosition:
    """起始位置"""
    player_id: str
    region_type: RegionType
    capital_position: Tuple[int, int]
    controlled_tiles: List[Tuple[int, int]]
    starting_resources: Dict[ResourceType, int]
    starting_population: int
    starting_technology: Dict[str, int]
    special_traits: List[str]

class WorldGenerator:
    """世界生成器"""
    
    def __init__(self, width: int = 50, height: int = 30, num_players: int = 4):
        """
        初始化世界生成器
        
        Args:
            width: 地图宽度
            height: 地图高度
            num_players: 玩家数量
        """
        self.width = width
        self.height = height
        self.num_players = num_players
        self.world_map: List[List[WorldTile]] = []
        self.starting_positions: List[StartingPosition] = []
        
        # 导入日志
        from shared.enhanced_logger import get_server_logger
        self.logger = get_server_logger("WorldGenerator")
    
    def generate_world(self, seed: Optional[int] = None) -> Dict[str, Any]:
        """
        生成完整的世界
        
        Args:
            seed: 随机种子
            
        Returns:
            世界数据字典
        """
        if seed:
            random.seed(seed)
        
        self.logger.info(f"开始生成世界地图 ({self.width}x{self.height}, {self.num_players}玩家)")
        
        # 1. 生成基础地形
        self._generate_terrain()
        
        # 2. 分布资源
        self._distribute_resources()
        
        # 3. 生成人口分布
        self._generate_population()
        
        # 4. 设置基础设施
        self._setup_infrastructure()
        
        # 5. 确定玩家起始位置
        self._determine_starting_positions()
        
        # 6. 应用非对称设计
        self._apply_asymmetric_design()
        
        world_data = {
            "map": self._serialize_map(),
            "starting_positions": self._serialize_starting_positions(),
            "world_info": {
                "width": self.width,
                "height": self.height,
                "num_players": self.num_players,
                "generation_seed": seed
            }
        }
        
        self.logger.info("世界生成完成")
        return world_data
    
    def _generate_terrain(self):
        """生成基础地形"""
        self.world_map = []
        
        for y in range(self.height):
            row = []
            for x in range(self.width):
                # 使用柏林噪声的简化版本生成地形
                terrain = self._determine_terrain(x, y)
                tile = WorldTile(
                    x=x, y=y, terrain=terrain,
                    resources={}, population=0, infrastructure=0
                )
                row.append(tile)
            self.world_map.append(row)
        
        # 添加河流和海岸线
        self._add_rivers()
        self._add_coastlines()
    
    def _determine_terrain(self, x: int, y: int) -> TerrainType:
        """确定指定位置的地形类型"""
        # 简化的地形生成算法
        noise_value = self._noise(x * 0.1, y * 0.1)
        
        if noise_value < -0.3:
            return TerrainType.OCEAN
        elif noise_value < -0.1:
            return TerrainType.COAST
        elif noise_value < 0.1:
            return TerrainType.PLAINS
        elif noise_value < 0.3:
            return TerrainType.FOREST
        elif noise_value < 0.5:
            return TerrainType.MOUNTAINS
        else:
            return TerrainType.DESERT
    
    def _noise(self, x: float, y: float) -> float:
        """简化的噪声函数"""
        return math.sin(x * 2.5) * math.cos(y * 1.8) + \
               math.sin(x * 1.2) * math.cos(y * 2.3) * 0.5 + \
               random.uniform(-0.2, 0.2)
    
    def _add_rivers(self):
        """添加河流"""
        num_rivers = random.randint(3, 6)
        
        for _ in range(num_rivers):
            # 从山地开始，流向海洋或低地
            start_x = random.randint(0, self.width - 1)
            start_y = random.randint(0, self.height - 1)
            
            # 简化的河流生成
            current_x, current_y = start_x, start_y
            river_length = random.randint(5, 15)
            
            for _ in range(river_length):
                if (0 <= current_x < self.width and 
                    0 <= current_y < self.height):
                    
                    tile = self.world_map[current_y][current_x]
                    if tile.terrain not in [TerrainType.OCEAN, TerrainType.COAST]:
                        tile.terrain = TerrainType.RIVER
                
                # 随机移动方向
                current_x += random.randint(-1, 1)
                current_y += random.randint(-1, 1)
    
    def _add_coastlines(self):
        """添加海岸线"""
        for y in range(self.height):
            for x in range(self.width):
                tile = self.world_map[y][x]
                if tile.terrain == TerrainType.OCEAN:
                    # 检查相邻格子
                    for dx in [-1, 0, 1]:
                        for dy in [-1, 0, 1]:
                            nx, ny = x + dx, y + dy
                            if (0 <= nx < self.width and 
                                0 <= ny < self.height):
                                neighbor = self.world_map[ny][nx]
                                if neighbor.terrain not in [TerrainType.OCEAN, TerrainType.COAST]:
                                    neighbor.terrain = TerrainType.COAST
    
    def _distribute_resources(self):
        """分布资源"""
        # 基础资源分布
        for y in range(self.height):
            for x in range(self.width):
                tile = self.world_map[y][x]
                tile.resources = self._generate_tile_resources(tile.terrain)
        
        # 战略资源的稀有分布
        self._place_strategic_resources()
    
    def _generate_tile_resources(self, terrain: TerrainType) -> Dict[ResourceType, int]:
        """根据地形生成资源"""
        resources = {}
        
        if terrain == TerrainType.PLAINS:
            resources[ResourceType.FOOD] = random.randint(2, 5)
            if random.random() < 0.3:
                resources[ResourceType.COAL] = random.randint(1, 3)
        
        elif terrain == TerrainType.FOREST:
            resources[ResourceType.WOOD] = random.randint(3, 6)
            resources[ResourceType.FOOD] = random.randint(1, 3)
        
        elif terrain == TerrainType.MOUNTAINS:
            resources[ResourceType.STONE] = random.randint(2, 4)
            resources[ResourceType.IRON] = random.randint(1, 4)
            if random.random() < 0.2:
                resources[ResourceType.COAL] = random.randint(2, 5)
        
        elif terrain == TerrainType.DESERT:
            if random.random() < 0.1:
                resources[ResourceType.OIL] = random.randint(1, 3)
        
        elif terrain == TerrainType.COAST:
            resources[ResourceType.FOOD] = random.randint(1, 4)
        
        elif terrain == TerrainType.RIVER:
            resources[ResourceType.FOOD] = random.randint(2, 4)
        
        return resources
    
    def _place_strategic_resources(self):
        """放置战略资源"""
        strategic_resources = [
            (ResourceType.OIL, 8, 12),
            (ResourceType.URANIUM, 3, 5),
            (ResourceType.RUBBER, 5, 8),
            (ResourceType.RARE_METALS, 4, 7)
        ]
        
        for resource_type, min_deposits, max_deposits in strategic_resources:
            num_deposits = random.randint(min_deposits, max_deposits)
            
            for _ in range(num_deposits):
                x = random.randint(0, self.width - 1)
                y = random.randint(0, self.height - 1)
                tile = self.world_map[y][x]
                
                if tile.terrain not in [TerrainType.OCEAN]:
                    amount = random.randint(1, 5)
                    tile.resources[resource_type] = amount

    def _generate_population(self):
        """生成人口分布"""
        for y in range(self.height):
            for x in range(self.width):
                tile = self.world_map[y][x]
                tile.population = self._calculate_base_population(tile.terrain)

                # 河流和海岸附近人口更多
                if self._near_water(x, y):
                    tile.population = int(tile.population * 1.5)

    def _calculate_base_population(self, terrain: TerrainType) -> int:
        """计算基础人口"""
        population_map = {
            TerrainType.PLAINS: random.randint(100, 500),
            TerrainType.FOREST: random.randint(50, 200),
            TerrainType.MOUNTAINS: random.randint(20, 100),
            TerrainType.DESERT: random.randint(10, 50),
            TerrainType.COAST: random.randint(200, 800),
            TerrainType.RIVER: random.randint(150, 600),
            TerrainType.SWAMP: random.randint(20, 80),
            TerrainType.OCEAN: 0
        }
        return population_map.get(terrain, 0)

    def _near_water(self, x: int, y: int) -> bool:
        """检查是否靠近水源"""
        for dx in [-1, 0, 1]:
            for dy in [-1, 0, 1]:
                nx, ny = x + dx, y + dy
                if (0 <= nx < self.width and 0 <= ny < self.height):
                    terrain = self.world_map[ny][nx].terrain
                    if terrain in [TerrainType.RIVER, TerrainType.COAST, TerrainType.OCEAN]:
                        return True
        return False

    def _setup_infrastructure(self):
        """设置基础设施"""
        for y in range(self.height):
            for x in range(self.width):
                tile = self.world_map[y][x]

                # 基础设施与人口和地形相关
                base_infrastructure = 0

                if tile.terrain == TerrainType.PLAINS:
                    base_infrastructure = random.randint(20, 40)
                elif tile.terrain == TerrainType.COAST:
                    base_infrastructure = random.randint(30, 50)
                elif tile.terrain == TerrainType.RIVER:
                    base_infrastructure = random.randint(25, 45)
                elif tile.terrain == TerrainType.FOREST:
                    base_infrastructure = random.randint(10, 25)
                elif tile.terrain == TerrainType.MOUNTAINS:
                    base_infrastructure = random.randint(5, 20)
                elif tile.terrain == TerrainType.DESERT:
                    base_infrastructure = random.randint(5, 15)

                # 人口密度影响基础设施
                if tile.population > 300:
                    base_infrastructure += 10
                elif tile.population > 100:
                    base_infrastructure += 5

                tile.infrastructure = min(base_infrastructure, 100)

    def _determine_starting_positions(self):
        """确定玩家起始位置"""
        self.starting_positions = []

        # 根据设计理念，创建非对称起始位置
        region_types = self._assign_region_types()

        for i in range(self.num_players):
            player_id = f"player_{i+1}"
            region_type = region_types[i]

            # 寻找合适的起始位置
            capital_pos = self._find_starting_location(region_type, i)
            controlled_tiles = self._get_starting_territory(capital_pos, region_type)

            starting_pos = StartingPosition(
                player_id=player_id,
                region_type=region_type,
                capital_position=capital_pos,
                controlled_tiles=controlled_tiles,
                starting_resources=self._calculate_starting_resources(region_type, controlled_tiles),
                starting_population=self._calculate_starting_population(controlled_tiles),
                starting_technology=self._get_starting_technology(region_type),
                special_traits=self._get_special_traits(region_type)
            )

            self.starting_positions.append(starting_pos)

            # 标记领土所有权
            for tile_pos in controlled_tiles:
                x, y = tile_pos
                self.world_map[y][x].owner = player_id

    def _assign_region_types(self) -> List[RegionType]:
        """分配地区类型，确保非对称性"""
        # 根据玩家数量分配不同类型的起始地区
        if self.num_players <= 2:
            return [RegionType.POOR, RegionType.DEVELOPED]
        elif self.num_players <= 4:
            return [RegionType.POOR, RegionType.DEVELOPING,
                   RegionType.RESOURCE_RICH, RegionType.DEVELOPED]
        else:
            # 更多玩家时的分配策略
            types = [RegionType.POOR] * 2 + [RegionType.DEVELOPING] * 2 + \
                   [RegionType.RESOURCE_RICH] * 1 + [RegionType.DEVELOPED] * 1

            # 随机打乱并取前N个
            random.shuffle(types)
            return types[:self.num_players]

    def _find_starting_location(self, region_type: RegionType, player_index: int) -> Tuple[int, int]:
        """寻找合适的起始位置"""
        attempts = 0
        max_attempts = 100

        while attempts < max_attempts:
            x = random.randint(5, self.width - 6)
            y = random.randint(5, self.height - 6)

            tile = self.world_map[y][x]

            # 检查是否符合地区类型要求
            if self._is_suitable_location(tile, region_type):
                # 检查与其他玩家的距离
                if self._check_distance_from_others(x, y, player_index):
                    return (x, y)

            attempts += 1

        # 如果找不到理想位置，返回一个可用位置
        return (random.randint(5, self.width - 6), random.randint(5, self.height - 6))

    def _is_suitable_location(self, tile: WorldTile, region_type: RegionType) -> bool:
        """检查位置是否适合指定的地区类型"""
        if tile.terrain == TerrainType.OCEAN:
            return False

        if region_type == RegionType.POOR:
            # 贫困地区：基础设施差，资源少
            return tile.infrastructure < 30 and sum(tile.resources.values()) < 5

        elif region_type == RegionType.DEVELOPING:
            # 发展中地区：中等条件
            return 20 <= tile.infrastructure <= 50 and 3 <= sum(tile.resources.values()) <= 8

        elif region_type == RegionType.RESOURCE_RICH:
            # 资源丰富地区：资源多但可能基础设施一般
            return sum(tile.resources.values()) >= 6

        elif region_type == RegionType.DEVELOPED:
            # 发达地区：基础设施好
            return tile.infrastructure >= 40

        return True

    def _check_distance_from_others(self, x: int, y: int, current_player: int) -> bool:
        """检查与其他玩家起始位置的距离"""
        min_distance = 8  # 最小距离

        for i in range(current_player):
            if i < len(self.starting_positions):
                other_x, other_y = self.starting_positions[i].capital_position
                distance = math.sqrt((x - other_x) ** 2 + (y - other_y) ** 2)
                if distance < min_distance:
                    return False

        return True

    def _get_starting_territory(self, capital_pos: Tuple[int, int], region_type: RegionType) -> List[Tuple[int, int]]:
        """获取起始领土"""
        x, y = capital_pos
        territory = [(x, y)]  # 首都

        # 根据地区类型确定起始领土大小
        territory_size = {
            RegionType.POOR: 3,
            RegionType.DEVELOPING: 4,
            RegionType.RESOURCE_RICH: 5,
            RegionType.DEVELOPED: 6
        }

        target_size = territory_size.get(region_type, 4)

        # 扩展领土
        for radius in range(1, 3):
            if len(territory) >= target_size:
                break

            for dx in range(-radius, radius + 1):
                for dy in range(-radius, radius + 1):
                    if len(territory) >= target_size:
                        break

                    nx, ny = x + dx, y + dy
                    if (0 <= nx < self.width and 0 <= ny < self.height and
                        (nx, ny) not in territory):

                        tile = self.world_map[ny][nx]
                        if tile.terrain != TerrainType.OCEAN:
                            territory.append((nx, ny))

        return territory

    def _calculate_starting_resources(self, region_type: RegionType,
                                    controlled_tiles: List[Tuple[int, int]]) -> Dict[ResourceType, int]:
        """计算起始资源"""
        total_resources = {}

        # 统计领土内的资源
        for x, y in controlled_tiles:
            tile = self.world_map[y][x]
            for resource_type, amount in tile.resources.items():
                total_resources[resource_type] = total_resources.get(resource_type, 0) + amount

        # 根据地区类型调整起始资源
        multipliers = {
            RegionType.POOR: 0.5,
            RegionType.DEVELOPING: 0.8,
            RegionType.RESOURCE_RICH: 1.2,
            RegionType.DEVELOPED: 1.0
        }

        multiplier = multipliers.get(region_type, 1.0)

        for resource_type in total_resources:
            total_resources[resource_type] = int(total_resources[resource_type] * multiplier)

        # 确保基础资源
        basic_resources = {
            ResourceType.FOOD: 10,
            ResourceType.WOOD: 5,
            ResourceType.STONE: 3
        }

        for resource_type, min_amount in basic_resources.items():
            if total_resources.get(resource_type, 0) < min_amount:
                total_resources[resource_type] = min_amount

        return total_resources

    def _calculate_starting_population(self, controlled_tiles: List[Tuple[int, int]]) -> int:
        """计算起始人口"""
        total_population = 0

        for x, y in controlled_tiles:
            tile = self.world_map[y][x]
            total_population += tile.population

        return total_population

    def _get_starting_technology(self, region_type: RegionType) -> Dict[str, int]:
        """获取起始科技水平"""
        base_tech = {
            "agriculture": 1,
            "mining": 1,
            "construction": 1,
            "military": 1,
            "industry": 0,
            "science": 0
        }

        if region_type == RegionType.DEVELOPED:
            base_tech.update({
                "agriculture": 3,
                "mining": 2,
                "construction": 3,
                "military": 2,
                "industry": 2,
                "science": 1
            })
        elif region_type == RegionType.DEVELOPING:
            base_tech.update({
                "agriculture": 2,
                "mining": 2,
                "construction": 2,
                "military": 1,
                "industry": 1,
                "science": 0
            })
        elif region_type == RegionType.RESOURCE_RICH:
            base_tech.update({
                "agriculture": 1,
                "mining": 3,
                "construction": 1,
                "military": 1,
                "industry": 1,
                "science": 0
            })
        # POOR 地区使用默认的低科技水平

        return base_tech

    def _get_special_traits(self, region_type: RegionType) -> List[str]:
        """获取特殊特质"""
        traits = []

        if region_type == RegionType.POOR:
            traits.extend([
                "resilient_population",  # 坚韧的人口
                "guerrilla_warfare",     # 游击战专长
                "resource_scarcity"      # 资源稀缺（逆境增益）
            ])
        elif region_type == RegionType.DEVELOPING:
            traits.extend([
                "rapid_growth",          # 快速发展
                "adaptable_economy"      # 适应性经济
            ])
        elif region_type == RegionType.RESOURCE_RICH:
            traits.extend([
                "abundant_resources",    # 资源丰富
                "export_economy",        # 出口经济
                "resource_dependency"    # 资源依赖
            ])
        elif region_type == RegionType.DEVELOPED:
            traits.extend([
                "advanced_infrastructure",  # 先进基础设施
                "technological_advantage",  # 技术优势
                "high_living_standards"     # 高生活水平
            ])

        return traits

    def _apply_asymmetric_design(self):
        """应用非对称设计原则"""
        # 为贫困地区添加潜在优势
        for starting_pos in self.starting_positions:
            if starting_pos.region_type == RegionType.POOR:
                self._add_hidden_potential(starting_pos)

    def _add_hidden_potential(self, starting_pos: StartingPosition):
        """为贫困地区添加隐藏潜力"""
        # 在领土附近添加未发现的资源
        capital_x, capital_y = starting_pos.capital_position

        # 在周围区域随机放置一些高价值资源
        for _ in range(2):  # 2个隐藏资源点
            for attempt in range(20):
                dx = random.randint(-5, 5)
                dy = random.randint(-5, 5)
                x, y = capital_x + dx, capital_y + dy

                if (0 <= x < self.width and 0 <= y < self.height):
                    tile = self.world_map[y][x]
                    if tile.owner is None and tile.terrain != TerrainType.OCEAN:
                        # 添加隐藏的战略资源
                        hidden_resource = random.choice([
                            ResourceType.OIL,
                            ResourceType.RARE_METALS,
                            ResourceType.URANIUM
                        ])
                        tile.resources[hidden_resource] = random.randint(3, 8)
                        break

    def _serialize_map(self) -> List[List[Dict[str, Any]]]:
        """序列化地图数据"""
        serialized_map = []

        for row in self.world_map:
            serialized_row = []
            for tile in row:
                tile_data = {
                    "x": tile.x,
                    "y": tile.y,
                    "terrain": tile.terrain.value,
                    "resources": {rt.value: amount for rt, amount in tile.resources.items()},
                    "population": tile.population,
                    "infrastructure": tile.infrastructure,
                    "owner": tile.owner
                }
                serialized_row.append(tile_data)
            serialized_map.append(serialized_row)

        return serialized_map

    def _serialize_starting_positions(self) -> List[Dict[str, Any]]:
        """序列化起始位置数据"""
        serialized_positions = []

        for pos in self.starting_positions:
            pos_data = {
                "player_id": pos.player_id,
                "region_type": pos.region_type.value,
                "capital_position": pos.capital_position,
                "controlled_tiles": pos.controlled_tiles,
                "starting_resources": {rt.value: amount for rt, amount in pos.starting_resources.items()},
                "starting_population": pos.starting_population,
                "starting_technology": pos.starting_technology,
                "special_traits": pos.special_traits
            }
            serialized_positions.append(pos_data)

        return serialized_positions
