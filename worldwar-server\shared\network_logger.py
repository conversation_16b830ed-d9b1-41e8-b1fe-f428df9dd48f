#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络通信日志记录器
Network Communication Logger

专门用于记录网络通信的详细日志
Specialized logger for detailed network communication logging
"""

import json
import time
import threading
from typing import Dict, Any, Optional, Union
from datetime import datetime
import uuid

from shared.structured_logger import get_structured_logger, LogCategory, LogLevel

class NetworkLogger:
    """网络通信日志记录器"""
    
    def __init__(self, component: str = "network"):
        """
        初始化网络日志记录器
        
        Args:
            component: 组件名称
        """
        self.logger = get_structured_logger(f"network_{component}")
        self.active_connections = {}
        self.message_stats = {
            'sent': 0,
            'received': 0,
            'errors': 0,
            'bytes_sent': 0,
            'bytes_received': 0
        }
        self.lock = threading.Lock()
    
    def log_connection_attempt(self, peer_address: str, connection_type: str = "tcp",
                              trace_id: str = None) -> str:
        """
        记录连接尝试
        
        Args:
            peer_address: 对端地址
            connection_type: 连接类型
            trace_id: 跟踪ID
            
        Returns:
            连接跟踪ID
        """
        if not trace_id:
            trace_id = str(uuid.uuid4())
        
        self.logger.log_network_event(
            event_type="connection_attempt",
            direction="outbound",
            peer_address=peer_address,
            message_data={
                'connection_type': connection_type,
                'trace_id': trace_id
            }
        )
        
        return trace_id
    
    def log_connection_established(self, peer_address: str, session_id: str = None,
                                  connection_info: Dict[str, Any] = None,
                                  trace_id: str = None):
        """
        记录连接建立
        
        Args:
            peer_address: 对端地址
            session_id: 会话ID
            connection_info: 连接信息
            trace_id: 跟踪ID
        """
        with self.lock:
            self.active_connections[session_id or peer_address] = {
                'peer_address': peer_address,
                'established_at': time.time(),
                'bytes_sent': 0,
                'bytes_received': 0,
                'messages_sent': 0,
                'messages_received': 0,
                'last_activity': time.time()
            }
        
        data = {'connection_info': connection_info or {}}
        if trace_id:
            data['trace_id'] = trace_id
        
        self.logger.log_network_event(
            event_type="connect",
            direction="inbound",
            peer_address=peer_address,
            message_data=data,
            success=True
        )
        
        if session_id:
            self.logger.set_session_context(session_id)
    
    def log_connection_failed(self, peer_address: str, error: str,
                             trace_id: str = None, duration_ms: float = None):
        """
        记录连接失败
        
        Args:
            peer_address: 对端地址
            error: 错误信息
            trace_id: 跟踪ID
            duration_ms: 持续时间
        """
        data = {'error_details': error}
        if trace_id:
            data['trace_id'] = trace_id
        
        self.logger.log_network_event(
            event_type="connect",
            direction="outbound",
            peer_address=peer_address,
            message_data=data,
            success=False,
            error=error,
            duration_ms=duration_ms
        )
        
        with self.lock:
            self.message_stats['errors'] += 1
    
    def log_connection_closed(self, peer_address: str, session_id: str = None,
                             reason: str = None, graceful: bool = True):
        """
        记录连接关闭
        
        Args:
            peer_address: 对端地址
            session_id: 会话ID
            reason: 关闭原因
            graceful: 是否优雅关闭
        """
        connection_key = session_id or peer_address
        connection_info = {}
        
        with self.lock:
            if connection_key in self.active_connections:
                conn_data = self.active_connections.pop(connection_key)
                duration = time.time() - conn_data['established_at']
                connection_info = {
                    'duration_seconds': duration,
                    'bytes_sent': conn_data['bytes_sent'],
                    'bytes_received': conn_data['bytes_received'],
                    'messages_sent': conn_data['messages_sent'],
                    'messages_received': conn_data['messages_received']
                }
        
        data = {
            'reason': reason or ('graceful' if graceful else 'abrupt'),
            'graceful': graceful,
            'connection_stats': connection_info
        }
        
        self.logger.log_network_event(
            event_type="disconnect",
            direction="bidirectional",
            peer_address=peer_address,
            message_data=data,
            success=graceful
        )
        
        if session_id:
            self.logger.clear_session_context()
    
    def log_message_sent(self, peer_address: str, message_type: str,
                        message_data: Dict[str, Any] = None,
                        message_size: int = None, session_id: str = None,
                        success: bool = True, error: str = None,
                        duration_ms: float = None):
        """
        记录消息发送
        
        Args:
            peer_address: 对端地址
            message_type: 消息类型
            message_data: 消息数据（敏感信息会被过滤）
            message_size: 消息大小（字节）
            session_id: 会话ID
            success: 是否成功
            error: 错误信息
            duration_ms: 发送耗时
        """
        # 过滤敏感信息
        filtered_data = self._filter_sensitive_data(message_data) if message_data else None
        
        data = {
            'message_size_bytes': message_size,
            'message_content': filtered_data
        }
        
        if error:
            data['error_details'] = error
        
        self.logger.log_network_event(
            event_type="send",
            direction="outbound",
            message_type=message_type,
            message_data=data,
            peer_address=peer_address,
            success=success,
            error=error,
            duration_ms=duration_ms
        )
        
        # 更新统计
        with self.lock:
            if success:
                self.message_stats['sent'] += 1
                if message_size:
                    self.message_stats['bytes_sent'] += message_size
                
                # 更新连接统计
                connection_key = session_id or peer_address
                if connection_key in self.active_connections:
                    self.active_connections[connection_key]['messages_sent'] += 1
                    if message_size:
                        self.active_connections[connection_key]['bytes_sent'] += message_size
                    self.active_connections[connection_key]['last_activity'] = time.time()
            else:
                self.message_stats['errors'] += 1
    
    def log_message_received(self, peer_address: str, message_type: str,
                           message_data: Dict[str, Any] = None,
                           message_size: int = None, session_id: str = None,
                           processing_time_ms: float = None,
                           validation_success: bool = True,
                           validation_error: str = None):
        """
        记录消息接收
        
        Args:
            peer_address: 对端地址
            message_type: 消息类型
            message_data: 消息数据（敏感信息会被过滤）
            message_size: 消息大小（字节）
            session_id: 会话ID
            processing_time_ms: 处理耗时
            validation_success: 验证是否成功
            validation_error: 验证错误信息
        """
        # 过滤敏感信息
        filtered_data = self._filter_sensitive_data(message_data) if message_data else None
        
        data = {
            'message_size_bytes': message_size,
            'message_content': filtered_data,
            'validation_success': validation_success
        }
        
        if validation_error:
            data['validation_error'] = validation_error
        
        self.logger.log_network_event(
            event_type="receive",
            direction="inbound",
            message_type=message_type,
            message_data=data,
            peer_address=peer_address,
            success=validation_success,
            error=validation_error,
            duration_ms=processing_time_ms
        )
        
        # 更新统计
        with self.lock:
            if validation_success:
                self.message_stats['received'] += 1
                if message_size:
                    self.message_stats['bytes_received'] += message_size
                
                # 更新连接统计
                connection_key = session_id or peer_address
                if connection_key in self.active_connections:
                    self.active_connections[connection_key]['messages_received'] += 1
                    if message_size:
                        self.active_connections[connection_key]['bytes_received'] += message_size
                    self.active_connections[connection_key]['last_activity'] = time.time()
            else:
                self.message_stats['errors'] += 1
    
    def log_heartbeat(self, peer_address: str, session_id: str = None,
                     response_time_ms: float = None, success: bool = True):
        """
        记录心跳
        
        Args:
            peer_address: 对端地址
            session_id: 会话ID
            response_time_ms: 响应时间
            success: 是否成功
        """
        data = {
            'heartbeat_type': 'ping_pong',
            'response_time_ms': response_time_ms
        }
        
        self.logger.log_network_event(
            event_type="heartbeat",
            direction="bidirectional",
            message_type="heartbeat",
            message_data=data,
            peer_address=peer_address,
            success=success,
            duration_ms=response_time_ms
        )
        
        # 更新连接活动时间
        with self.lock:
            connection_key = session_id or peer_address
            if connection_key in self.active_connections:
                self.active_connections[connection_key]['last_activity'] = time.time()
    
    def log_protocol_error(self, peer_address: str, error_type: str,
                          error_details: str, message_data: Dict[str, Any] = None,
                          session_id: str = None):
        """
        记录协议错误
        
        Args:
            peer_address: 对端地址
            error_type: 错误类型
            error_details: 错误详情
            message_data: 相关消息数据
            session_id: 会话ID
        """
        data = {
            'error_type': error_type,
            'error_details': error_details,
            'related_message': self._filter_sensitive_data(message_data) if message_data else None
        }
        
        self.logger.log_network_event(
            event_type="protocol_error",
            direction="bidirectional",
            message_data=data,
            peer_address=peer_address,
            success=False,
            error=f"{error_type}: {error_details}"
        )
        
        with self.lock:
            self.message_stats['errors'] += 1
    
    def _filter_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        过滤敏感数据
        
        Args:
            data: 原始数据
            
        Returns:
            过滤后的数据
        """
        if not isinstance(data, dict):
            return data
        
        sensitive_keys = {
            'password', 'token', 'secret', 'key', 'auth',
            'credential', 'private', 'session_key'
        }
        
        filtered = {}
        for key, value in data.items():
            key_lower = key.lower()
            
            # 检查是否为敏感键
            if any(sensitive in key_lower for sensitive in sensitive_keys):
                filtered[key] = "[FILTERED]"
            elif isinstance(value, dict):
                filtered[key] = self._filter_sensitive_data(value)
            elif isinstance(value, list) and value and isinstance(value[0], dict):
                filtered[key] = [self._filter_sensitive_data(item) for item in value]
            else:
                # 限制字符串长度以避免日志过大
                if isinstance(value, str) and len(value) > 200:
                    filtered[key] = value[:200] + "...[TRUNCATED]"
                else:
                    filtered[key] = value
        
        return filtered
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        with self.lock:
            active_count = len(self.active_connections)
            
            # 计算连接持续时间统计
            current_time = time.time()
            durations = []
            idle_times = []
            
            for conn_data in self.active_connections.values():
                duration = current_time - conn_data['established_at']
                idle_time = current_time - conn_data['last_activity']
                durations.append(duration)
                idle_times.append(idle_time)
            
            stats = {
                'active_connections': active_count,
                'total_messages_sent': self.message_stats['sent'],
                'total_messages_received': self.message_stats['received'],
                'total_errors': self.message_stats['errors'],
                'total_bytes_sent': self.message_stats['bytes_sent'],
                'total_bytes_received': self.message_stats['bytes_received']
            }
            
            if durations:
                stats.update({
                    'avg_connection_duration': sum(durations) / len(durations),
                    'max_connection_duration': max(durations),
                    'avg_idle_time': sum(idle_times) / len(idle_times),
                    'max_idle_time': max(idle_times)
                })
            
            return stats
    
    def log_connection_stats(self):
        """记录连接统计信息"""
        stats = self.get_connection_stats()
        
        self.logger.log_performance_metric(
            "active_connections",
            stats['active_connections'],
            unit="count"
        )
        
        self.logger.log_performance_metric(
            "total_messages_sent",
            stats['total_messages_sent'],
            unit="count"
        )
        
        self.logger.log_performance_metric(
            "total_messages_received",
            stats['total_messages_received'],
            unit="count"
        )
        
        if stats.get('avg_connection_duration'):
            self.logger.log_performance_metric(
                "avg_connection_duration",
                stats['avg_connection_duration'],
                unit="seconds"
            )
    
    def cleanup_stale_connections(self, timeout_seconds: int = 300):
        """
        清理过期连接
        
        Args:
            timeout_seconds: 超时时间（秒）
        """
        current_time = time.time()
        stale_connections = []
        
        with self.lock:
            for conn_key, conn_data in self.active_connections.items():
                if current_time - conn_data['last_activity'] > timeout_seconds:
                    stale_connections.append(conn_key)
            
            for conn_key in stale_connections:
                conn_data = self.active_connections.pop(conn_key)
                self.logger.warning(
                    LogCategory.NETWORK,
                    f"清理过期连接: {conn_key}",
                    data={
                        'peer_address': conn_data['peer_address'],
                        'idle_time_seconds': current_time - conn_data['last_activity'],
                        'connection_duration': current_time - conn_data['established_at']
                    }
                )
        
        if stale_connections:
            self.logger.info(
                LogCategory.NETWORK,
                f"清理了 {len(stale_connections)} 个过期连接"
            )

# 全局网络日志记录器实例
_network_loggers: Dict[str, NetworkLogger] = {}
_logger_lock = threading.Lock()

def get_network_logger(component: str = "default") -> NetworkLogger:
    """获取网络日志记录器实例"""
    with _logger_lock:
        if component not in _network_loggers:
            _network_loggers[component] = NetworkLogger(component)
        return _network_loggers[component]