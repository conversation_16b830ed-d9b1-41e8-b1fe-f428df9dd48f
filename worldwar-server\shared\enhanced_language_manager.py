#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的语言管理器
Enhanced Language Manager

支持观察者模式、语言文件验证、缺失键检测和动态语言切换
"""

import json
import os
import time
from pathlib import Path
from typing import Dict, Optional, List, Callable, Set, Any
from dataclasses import dataclass
from abc import ABC, abstractmethod


@dataclass
class ValidationResult:
    """语言文件验证结果"""
    is_valid: bool
    missing_keys: Set[str]
    extra_keys: Set[str]
    errors: List[str]
    warnings: List[str]


class LanguageObserver(ABC):
    """语言变更观察者接口"""
    
    @abstractmethod
    def on_language_changed(self, old_language: str, new_language: str):
        """语言变更时的回调"""
        pass


class EnhancedLanguageManager:
    """增强的语言管理器"""
    
    def __init__(self, app_type: str = "server"):
        self.app_type = app_type
        self.current_language = "chinese"
        self.fallback_language = "english"
        self.bilingual_mode = False
        self.languages: Dict[str, Dict] = {}
        self.missing_keys: Set[str] = set()
        self.observers: List[LanguageObserver] = []
        self.validation_cache: Dict[str, ValidationResult] = {}
        self.last_validation_time: float = 0
        
        # 初始化路径属性
        self.base_dir = None
        self.languages_dir = None
        
        # 设置路径
        self._setup_paths()
        
        # 加载语言文件
        self._load_languages()
        
        # 初始验证
        self._validate_all_languages()
    
    def _setup_paths(self):
        """设置语言文件路径"""
        if self.app_type == "server":
            self.base_dir = Path(__file__).parent.parent
            self.languages_dir = self.base_dir / "languages"
        else:
            # 客户端路径需要特殊处理
            server_dir = Path(__file__).parent.parent
            client_dir = server_dir.parent / "worldwar-client"
            self.base_dir = client_dir
            self.languages_dir = client_dir / "client" / "languages"

        
        # 确保语言目录存在
        self.languages_dir.mkdir(parents=True, exist_ok=True)
    
    def _load_languages(self):
        """加载所有语言文件"""
        self.languages.clear()
        
        # 支持的语言列表
        supported_languages = ["chinese", "english"]
        
        for language in supported_languages:
            language_file = self.languages_dir / f"{language}.json"
            if language_file.exists():
                try:
                    with open(language_file, 'r', encoding='utf-8') as f:
                        self.languages[language] = json.load(f)
                except Exception as e:
                    print(f"Error loading {language} language file: {e}")
                    self.languages[language] = {}
            else:
                print(f"Warning: {language} language file not found")
                self.languages[language] = {}
    
    def get_text(self, key: str, default: str = None, **kwargs) -> str:
        """
        获取本地化文本
        
        Args:
            key: 文本键，支持点号分隔的路径
            default: 默认文本
            **kwargs: 格式化参数
            
        Returns:
            本地化文本
        """
        # 记录缺失的键
        if not self._key_exists(key):
            self.missing_keys.add(key)
        
        if self.bilingual_mode:
            return self._get_bilingual_text(key, default, **kwargs)
        else:
            return self._get_single_language_text(key, default, **kwargs)
    
    def _get_single_language_text(self, key: str, default: str = None, **kwargs) -> str:
        """获取单语言文本"""
        # 首先尝试当前语言
        text = self._get_text_from_language(self.current_language, key)
        
        # 如果当前语言没有，尝试回退语言
        if text is None and self.current_language != self.fallback_language:
            text = self._get_text_from_language(self.fallback_language, key)
        
        # 如果都没有，使用默认值或键名
        if text is None:
            text = default or key
        
        # 应用格式化参数
        try:
            if kwargs and isinstance(text, str):
                text = text.format(**kwargs)
        except (KeyError, ValueError) as e:
            print(f"Warning: Failed to format text '{key}': {e}")
        
        return text
    
    def _get_bilingual_text(self, key: str, default: str = None, **kwargs) -> str:
        """获取双语文本"""
        chinese_text = self._get_text_from_language('chinese', key)
        english_text = self._get_text_from_language('english', key)
        
        # 应用格式化参数
        try:
            if kwargs:
                if chinese_text and isinstance(chinese_text, str):
                    chinese_text = chinese_text.format(**kwargs)
                if english_text and isinstance(english_text, str):
                    english_text = english_text.format(**kwargs)
        except (KeyError, ValueError) as e:
            print(f"Warning: Failed to format bilingual text '{key}': {e}")
        
        # 组合双语文本
        if chinese_text and english_text:
            return f"{chinese_text} / {english_text}"
        elif chinese_text:
            return chinese_text
        elif english_text:
            return english_text
        else:
            return default or key
    
    def _get_text_from_language(self, language: str, key: str) -> Optional[str]:
        """从指定语言获取文本"""
        if language not in self.languages:
            return None
        
        # 支持点号分隔的键路径
        keys = key.split('.')
        current_dict = self.languages[language]
        
        try:
            for k in keys:
                if isinstance(current_dict, dict) and k in current_dict:
                    current_dict = current_dict[k]
                else:
                    return None
            
            return str(current_dict) if current_dict is not None else None
            
        except Exception:
            return None
    
    def _key_exists(self, key: str) -> bool:
        """检查键是否存在于任何语言中"""
        for language in self.languages:
            if self._get_text_from_language(language, key) is not None:
                return True
        return False
    
    def set_language(self, language: str):
        """
        设置当前语言
        
        Args:
            language: 语言代码 ('chinese', 'english', 'bilingual')
        """
        old_language = self.current_language if not self.bilingual_mode else "bilingual"
        
        if language in ['chinese', 'english', 'bilingual']:
            if language == 'bilingual':
                self.bilingual_mode = True
                self.current_language = 'chinese'  # 双语模式下的主语言
            else:
                self.bilingual_mode = False
                self.current_language = language
            
            # 通知观察者
            new_language = language
            self._notify_observers(old_language, new_language)
    
    def add_observer(self, observer: LanguageObserver):
        """添加语言变更观察者"""
        if observer not in self.observers:
            self.observers.append(observer)
    
    def remove_observer(self, observer: LanguageObserver):
        """移除语言变更观察者"""
        if observer in self.observers:
            self.observers.remove(observer)
    
    def _notify_observers(self, old_language: str, new_language: str):
        """通知所有观察者语言变更"""
        for observer in self.observers:
            try:
                observer.on_language_changed(old_language, new_language)
            except Exception as e:
                print(f"Error notifying observer: {e}")
    
    def validate_language_files(self, force_refresh: bool = False) -> Dict[str, ValidationResult]:
        """
        验证语言文件完整性
        
        Args:
            force_refresh: 是否强制刷新验证缓存
            
        Returns:
            验证结果字典
        """
        current_time = time.time()
        
        # 如果缓存有效且不强制刷新，返回缓存结果
        if not force_refresh and (current_time - self.last_validation_time) < 300:  # 5分钟缓存
            return self.validation_cache
        
        self.validation_cache.clear()
        
        # 获取所有键的集合
        all_keys = self._get_all_keys()
        
        # 验证每种语言
        for language in self.languages:
            result = self._validate_single_language(language, all_keys)
            self.validation_cache[language] = result
        
        self.last_validation_time = current_time
        return self.validation_cache
    
    def _validate_single_language(self, language: str, all_keys: Set[str]) -> ValidationResult:
        """验证单个语言文件"""
        language_keys = self._get_language_keys(language)
        
        missing_keys = all_keys - language_keys
        extra_keys = language_keys - all_keys
        
        errors = []
        warnings = []
        
        # 检查缺失的关键键
        critical_keys = {
            "server.welcome", "server.error", "server.starting", "server.started",
            "game.room_created", "game.player_joined"
        }
        
        missing_critical = missing_keys & critical_keys
        if missing_critical:
            errors.extend([f"Missing critical key: {key}" for key in missing_critical])
        
        # 检查额外的键（可能是过时的）
        if extra_keys:
            warnings.extend([f"Extra key (possibly obsolete): {key}" for key in extra_keys])
        
        # 检查空值
        empty_keys = self._find_empty_keys(language)
        if empty_keys:
            warnings.extend([f"Empty value for key: {key}" for key in empty_keys])
        
        is_valid = len(errors) == 0
        
        return ValidationResult(
            is_valid=is_valid,
            missing_keys=missing_keys,
            extra_keys=extra_keys,
            errors=errors,
            warnings=warnings
        )
    
    def _get_all_keys(self) -> Set[str]:
        """获取所有语言文件中的键集合"""
        all_keys = set()
        for language in self.languages:
            all_keys.update(self._get_language_keys(language))
        return all_keys
    
    def _get_language_keys(self, language: str) -> Set[str]:
        """获取指定语言的所有键"""
        keys = set()
        if language in self.languages:
            self._collect_keys(self.languages[language], "", keys)
        return keys
    
    def _collect_keys(self, data: Any, prefix: str, keys: Set[str]):
        """递归收集键"""
        if isinstance(data, dict):
            for key, value in data.items():
                full_key = f"{prefix}.{key}" if prefix else key
                if isinstance(value, dict):
                    self._collect_keys(value, full_key, keys)
                else:
                    keys.add(full_key)
    
    def _find_empty_keys(self, language: str) -> Set[str]:
        """查找空值的键"""
        empty_keys = set()
        if language in self.languages:
            self._collect_empty_keys(self.languages[language], "", empty_keys)
        return empty_keys
    
    def _collect_empty_keys(self, data: Any, prefix: str, empty_keys: Set[str]):
        """递归收集空值键"""
        if isinstance(data, dict):
            for key, value in data.items():
                full_key = f"{prefix}.{key}" if prefix else key
                if isinstance(value, dict):
                    self._collect_empty_keys(value, full_key, empty_keys)
                elif not value or (isinstance(value, str) and not value.strip()):
                    empty_keys.add(full_key)
    
    def get_missing_keys(self) -> Set[str]:
        """获取缺失的翻译键"""
        return self.missing_keys.copy()
    
    def clear_missing_keys(self):
        """清空缺失键记录"""
        self.missing_keys.clear()
    
    def reload_languages(self):
        """重新加载语言文件"""
        old_language = self.current_language if not self.bilingual_mode else "bilingual"
        self._load_languages()
        self._validate_all_languages()
        
        # 通知观察者重新加载
        self._notify_observers(old_language, old_language)
    
    def _validate_all_languages(self):
        """验证所有语言文件"""
        self.validate_language_files(force_refresh=True)
    
    def get_supported_languages(self) -> List[str]:
        """获取支持的语言列表"""
        return list(self.languages.keys())
    
    def get_current_language(self) -> str:
        """获取当前语言"""
        if self.bilingual_mode:
            return "bilingual"
        return self.current_language
    
    def is_bilingual_mode(self) -> bool:
        """是否为双语模式"""
        return self.bilingual_mode
    
    def get_language_info(self, language: str) -> Dict[str, Any]:
        """获取语言信息"""
        if language not in self.languages:
            return {}
        
        return {
            "name": self.languages[language].get("language_name", language),
            "key_count": len(self._get_language_keys(language)),
            "validation": self.validation_cache.get(language)
        }
    
    def export_missing_keys_template(self, target_language: str) -> Dict[str, str]:
        """导出缺失键的模板"""
        if target_language not in self.languages:
            return {}
        
        validation_result = self.validation_cache.get(target_language)
        if not validation_result:
            return {}
        
        template = {}
        for key in validation_result.missing_keys:
            # 尝试从其他语言获取参考文本
            reference_text = None
            for lang in self.languages:
                if lang != target_language:
                    text = self._get_text_from_language(lang, key)
                    if text:
                        reference_text = text
                        break
            
            template[key] = reference_text or f"[TRANSLATE: {key}]"
        
        return template


# 全局实例管理
_language_manager_instances: Dict[str, EnhancedLanguageManager] = {}


def create_enhanced_language_manager(app_type: str = "server") -> EnhancedLanguageManager:
    """创建增强语言管理器"""
    if app_type not in _language_manager_instances:
        _language_manager_instances[app_type] = EnhancedLanguageManager(app_type)
    return _language_manager_instances[app_type]


def get_enhanced_language_manager(app_type: str = "server") -> EnhancedLanguageManager:
    """获取增强语言管理器"""
    return create_enhanced_language_manager(app_type)


def get_text(key: str, default: str = None, app_type: str = "server", **kwargs) -> str:
    """快捷函数: 获取文本"""
    return get_enhanced_language_manager(app_type).get_text(key, default, **kwargs)