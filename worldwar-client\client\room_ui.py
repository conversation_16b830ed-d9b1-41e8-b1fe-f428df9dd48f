#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
房间界面管理器 - 处理房间等待界面和玩家列表显示
Room UI Manager - Handle room waiting interface and player list display
"""

import time
import threading
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum


class UIState(Enum):
    """UI状态"""
    HIDDEN = "hidden"
    ROOM_LIST = "room_list"
    WAITING_ROOM = "waiting_room"
    GAME_STARTING = "game_starting"
    IN_GAME = "in_game"


@dataclass
class PlayerInfo:
    """玩家信息"""
    session_id: str
    player_name: str
    is_ready: bool
    is_host: bool
    connection_status: str
    join_time: float


@dataclass
class RoomInfo:
    """房间信息"""
    room_id: str
    room_name: str
    status: str
    max_players: int
    current_players: int
    game_mode: str
    difficulty: str
    is_private: bool


class RoomUI:
    """房间界面管理器"""
    
    def __init__(self, network_client=None):
        """初始化房间UI管理器"""
        self.network_client = network_client
        self.current_state = UIState.HIDDEN
        self.current_room_info: Optional[Dict[str, Any]] = None
        self.room_list: List[RoomInfo] = []
        
        # UI更新回调
        self.ui_update_callbacks: List[Callable] = []
        
        # 界面刷新控制
        self.auto_refresh = True
        self.refresh_interval = 1.0  # 1秒刷新间隔
        self.last_refresh = 0
        
        # 启动UI更新线程
        self.ui_thread = threading.Thread(target=self._ui_update_worker, daemon=True)
        self.ui_thread.start()
    
    def add_update_callback(self, callback: Callable):
        """添加UI更新回调"""
        self.ui_update_callbacks.append(callback)
    
    def show_room_list(self, room_list: List[Dict[str, Any]]):
        """显示房间列表"""
        self.current_state = UIState.ROOM_LIST
        self.room_list = [
            RoomInfo(
                room_id=room["room_id"],
                room_name=room["room_name"],
                status=room["status"],
                max_players=room["max_players"],
                current_players=room["current_players"],
                game_mode=room.get("game_mode", "unknown"),
                difficulty=room.get("difficulty", "normal"),
                is_private=room.get("is_private", False)
            )
            for room in room_list
        ]
        self._trigger_ui_update()
    
    def show_waiting_room(self, room_data: Dict[str, Any]):
        """显示房间等待界面"""
        self.current_state = UIState.WAITING_ROOM
        self.current_room_info = room_data
        self._trigger_ui_update()
    
    def update_room_info(self, room_data: Dict[str, Any]):
        """更新房间信息"""
        if self.current_state == UIState.WAITING_ROOM:
            self.current_room_info = room_data
            self._trigger_ui_update()
    
    def show_game_starting(self, countdown_data: Dict[str, Any]):
        """显示游戏开始界面"""
        self.current_state = UIState.GAME_STARTING
        if self.current_room_info:
            self.current_room_info["countdown"] = countdown_data
        self._trigger_ui_update()
    
    def hide_ui(self):
        """隐藏UI"""
        self.current_state = UIState.HIDDEN
        self.current_room_info = None
        self._trigger_ui_update()
    
    def render_current_ui(self) -> str:
        """渲染当前UI状态"""
        if self.current_state == UIState.HIDDEN:
            return ""
        elif self.current_state == UIState.ROOM_LIST:
            return self._render_room_list()
        elif self.current_state == UIState.WAITING_ROOM:
            return self._render_waiting_room()
        elif self.current_state == UIState.GAME_STARTING:
            return self._render_game_starting()
        else:
            return "未知UI状态"
    
    def _render_room_list(self) -> str:
        """渲染房间列表"""
        if not self.room_list:
            return """
╔══════════════════════════════════════════════════════════════╗
║                          房间列表                            ║
║                        Room List                             ║
╠══════════════════════════════════════════════════════════════╣
║  当前没有可用的房间                                          ║
║  No rooms available                                          ║
║                                                              ║
║  输入 'create <房间名>' 创建新房间                           ║
║  Type 'create <room_name>' to create a new room             ║
╚══════════════════════════════════════════════════════════════╝
"""
        
        lines = [
            "╔══════════════════════════════════════════════════════════════╗",
            "║                          房间列表                            ║",
            "║                        Room List                             ║",
            "╠══════════════════════════════════════════════════════════════╣"
        ]
        
        for i, room in enumerate(self.room_list, 1):
            status_icon = "🟢" if room.status == "waiting" else "🔴"
            private_icon = "🔒" if room.is_private else "🔓"
            
            room_line = f"║ {i:2d}. {status_icon}{private_icon} {room.room_name:<20} "
            room_line += f"({room.current_players}/{room.max_players}) "
            room_line += f"{room.game_mode:<10} {room.difficulty:<8} ║"
            
            # 确保行长度正确
            if len(room_line) > 66:
                room_line = room_line[:63] + "... ║"
            elif len(room_line) < 66:
                room_line = room_line[:-1] + " " * (66 - len(room_line)) + "║"
            
            lines.append(room_line)
        
        lines.extend([
            "╠══════════════════════════════════════════════════════════════╣",
            "║  输入 'join <序号>' 加入房间                                 ║",
            "║  输入 'create <房间名>' 创建新房间                           ║",
            "║  输入 'refresh' 刷新房间列表                                 ║",
            "╚══════════════════════════════════════════════════════════════╝"
        ])
        
        return "\n".join(lines)
    
    def _render_waiting_room(self) -> str:
        """渲染房间等待界面"""
        if not self.current_room_info:
            return "房间信息加载中..."
        
        room_data = self.current_room_info
        settings = room_data.get("settings", {})
        players = room_data.get("players", [])
        game_status = room_data.get("game_status", {})
        
        lines = [
            "╔══════════════════════════════════════════════════════════════╗",
            f"║                    房间: {settings.get('room_name', 'Unknown'):<30} ║",
            f"║                    Room: {settings.get('room_name', 'Unknown'):<30} ║",
            "╠══════════════════════════════════════════════════════════════╣",
            f"║  游戏模式: {settings.get('game_mode', 'unknown'):<20} 难度: {settings.get('difficulty', 'normal'):<10} ║",
            f"║  玩家数量: {len(players)}/{settings.get('max_players', 0):<20} 状态: {room_data.get('status', 'unknown'):<10} ║",
            "╠══════════════════════════════════════════════════════════════╣",
            "║                        玩家列表                              ║",
            "║                      Player List                             ║",
            "╠══════════════════════════════════════════════════════════════╣"
        ]
        
        # 渲染玩家列表
        for player in players:
            ready_icon = "✅" if player.get("is_ready", False) else "⏳"
            host_icon = "👑" if player.get("is_host", False) else "  "
            connection_icon = self._get_connection_icon(player.get("connection_status", "unknown"))
            
            player_line = f"║ {host_icon}{ready_icon}{connection_icon} {player.get('player_name', 'Unknown'):<25} "
            
            # 添加连接时间信息
            join_time = player.get("join_time", time.time())
            duration = int(time.time() - join_time)
            duration_str = f"{duration//60:02d}:{duration%60:02d}"
            player_line += f"{duration_str:>8} ║"
            
            # 确保行长度正确
            if len(player_line) > 66:
                player_line = player_line[:63] + "... ║"
            elif len(player_line) < 66:
                player_line = player_line[:-1] + " " * (66 - len(player_line)) + "║"
            
            lines.append(player_line)
        
        # 添加空行填充
        while len(lines) < 15:
            lines.append("║" + " " * 64 + "║")
        
        # 游戏状态信息
        lines.extend([
            "╠══════════════════════════════════════════════════════════════╣",
            f"║  游戏状态: {game_status.get('start_reason', '等待玩家准备'):<40} ║"
        ])
        
        # 操作提示
        if game_status.get("can_start", False):
            lines.append("║  所有玩家已准备，房主可以开始游戏！                          ║")
        else:
            lines.append("║  等待所有玩家准备...                                        ║")
        
        lines.extend([
            "║                                                              ║",
            "║  输入 'ready' 切换准备状态                                   ║",
            "║  输入 'start' 开始游戏 (仅房主)                              ║",
            "║  输入 'leave' 离开房间                                       ║",
            "╚══════════════════════════════════════════════════════════════╝"
        ])
        
        return "\n".join(lines)
    
    def _render_game_starting(self) -> str:
        """渲染游戏开始界面"""
        countdown_data = self.current_room_info.get("countdown", {}) if self.current_room_info else {}
        countdown = countdown_data.get("countdown", 0)
        message = countdown_data.get("message", "游戏即将开始...")
        
        lines = [
            "╔══════════════════════════════════════════════════════════════╗",
            "║                        游戏开始                              ║",
            "║                      Game Starting                           ║",
            "╠══════════════════════════════════════════════════════════════╣",
            "║                                                              ║",
            "║                                                              ║",
            f"║                    倒计时: {countdown:2d} 秒                        ║",
            f"║                   Countdown: {countdown:2d} seconds                ║",
            "║                                                              ║",
            f"║  {message:<58} ║",
            "║                                                              ║",
            "║                                                              ║",
            "║                    请稍候...                                 ║",
            "║                   Please wait...                             ║",
            "║                                                              ║",
            "╚══════════════════════════════════════════════════════════════╝"
        ]
        
        return "\n".join(lines)
    
    def _get_connection_icon(self, status: str) -> str:
        """获取连接状态图标"""
        status_icons = {
            "connected": "🟢",
            "active": "🟢",
            "idle": "🟡",
            "disconnected": "🔴",
            "unknown": "⚪"
        }
        return status_icons.get(status, "⚪")
    
    def _trigger_ui_update(self):
        """触发UI更新"""
        for callback in self.ui_update_callbacks:
            try:
                callback()
            except Exception as e:
                print(f"UI更新回调错误: {e}")
    
    def _ui_update_worker(self):
        """UI更新工作线程"""
        while True:
            try:
                current_time = time.time()
                
                if (self.auto_refresh and 
                    current_time - self.last_refresh >= self.refresh_interval and
                    self.current_state in [UIState.WAITING_ROOM, UIState.GAME_STARTING]):
                    
                    self.last_refresh = current_time
                    self._trigger_ui_update()
                
                time.sleep(0.1)  # 100ms检查间隔
                
            except Exception as e:
                print(f"UI更新工作线程错误: {e}")
                time.sleep(1)
    
    def handle_user_input(self, command: str) -> bool:
        """
        处理用户输入
        
        Returns:
            是否处理了该命令
        """
        if self.current_state == UIState.ROOM_LIST:
            return self._handle_room_list_input(command)
        elif self.current_state == UIState.WAITING_ROOM:
            return self._handle_waiting_room_input(command)
        else:
            return False
    
    def _handle_room_list_input(self, command: str) -> bool:
        """处理房间列表界面的用户输入"""
        parts = command.strip().split()
        if not parts:
            return False
        
        cmd = parts[0].lower()
        
        if cmd == "join" and len(parts) > 1:
            try:
                room_index = int(parts[1]) - 1
                if 0 <= room_index < len(self.room_list):
                    room = self.room_list[room_index]
                    if self.network_client:
                        self.network_client.join_room(room.room_id)
                    return True
            except ValueError:
                pass
        
        elif cmd == "create" and len(parts) > 1:
            room_name = " ".join(parts[1:])
            if self.network_client:
                self.network_client.create_room(room_name)
            return True
        
        elif cmd == "refresh":
            if self.network_client:
                self.network_client.request_room_list()
            return True
        
        return False
    
    def _handle_waiting_room_input(self, command: str) -> bool:
        """处理等待房间界面的用户输入"""
        cmd = command.strip().lower()
        
        if cmd == "ready":
            if self.network_client:
                self.network_client.toggle_ready()
            return True
        
        elif cmd == "start":
            if self.network_client:
                self.network_client.start_game()
            return True
        
        elif cmd == "leave":
            if self.network_client:
                self.network_client.leave_room()
            return True
        
        return False
    
    def get_current_state(self) -> UIState:
        """获取当前UI状态"""
        return self.current_state
    
    def is_in_room(self) -> bool:
        """检查是否在房间中"""
        return self.current_state in [UIState.WAITING_ROOM, UIState.GAME_STARTING]
    
    def get_room_id(self) -> Optional[str]:
        """获取当前房间ID"""
        if self.current_room_info:
            return self.current_room_info.get("room_id")
        return None