# 🎮 世界大战游戏服务器
## World War Game Server

**版本**: v3.0  
**类型**: 独立服务器应用  
**语言**: Python 3.8+

---

## 📋 项目简介

世界大战游戏的独立服务器端，提供安全的多人游戏服务，支持房间管理、玩家认证、游戏逻辑处理等功能。

### 主要功能
- 🔐 安全的客户端认证和通信
- 🏠 房间创建和管理
- 👥 多玩家会话管理
- 🎯 游戏逻辑处理
- 📊 实时状态监控
- 📝 完整的日志记录

---

## 🚀 快速开始

### 环境要求
- Python 3.8 或更高版本
- 操作系统：Windows/Linux/macOS

### 安装依赖
```bash
pip install -r requirements.txt
```

### 启动服务器
```bash
# 使用默认配置启动
python server_main.py

# 指定地址和端口
python server_main.py --host localhost --port 8888

# 启用调试模式
python server_main.py --debug
```

### 配置文件
- `config/server_config.ini` - 服务器主配置
- `config/security.json` - 安全配置
- `config/game_config.ini` - 游戏规则配置

---

## 📁 项目结构

```
worldwar-server/
├── server_main.py          # 服务器启动入口
├── requirements.txt        # 依赖包列表
├── config/                 # 配置文件
│   ├── server_config.ini
│   ├── security.json
│   └── game_config.ini
├── server/                 # 服务器核心
│   ├── game_server.py      # 主服务器类
│   ├── network_handler.py  # 网络处理
│   ├── player_session.py   # 玩家会话管理
│   ├── room_manager.py     # 房间管理
│   └── room_id_generator.py
├── game_logic/             # 游戏逻辑
│   ├── game_loop.py        # 游戏循环
│   ├── combat_system.py    # 战斗系统
│   ├── economic_system.py  # 经济系统
│   └── victory_system.py   # 胜利条件
├── shared/                 # 共享组件
│   ├── message_types.py    # 消息类型定义
│   ├── protocol.py         # 通信协议
│   ├── secure_protocol.py  # 安全协议
│   ├── security_manager.py # 安全管理
│   └── message_manager.py  # 消息管理
├── utils/                  # 工具模块
│   ├── logger.py           # 日志系统
│   └── encoding_utils.py   # 编码工具
├── data/                   # 数据管理
│   ├── local_storage.py    # 本地存储
│   └── data_fetcher.py     # 数据获取
├── world/                  # 世界数据
│   └── ai_generator.py     # AI数据生成
└── logs/                   # 日志文件
    ├── server/             # 服务器日志
    └── latest_server.log   # 最新服务器日志
```

---

## 🔧 配置说明

### 服务器配置 (server_config.ini)
```ini
[server]
host = localhost
port = 8888
max_connections = 100
heartbeat_interval = 30

[logging]
level = INFO
log_to_file = true
log_to_console = false
```

### 安全配置 (security.json)
```json
{
  "encryption_enabled": true,
  "key_rotation_interval": 3600,
  "max_failed_attempts": 5
}
```

---

## 📊 监控和管理

### 日志文件
- `logs/server/server_YYYYMMDD_HHMMSS.log` - 详细服务器日志
- `logs/latest_server.log` - 最新日志的快捷链接

### 状态监控
服务器提供实时状态信息：
- 活跃连接数
- 房间数量
- 玩家统计
- 系统资源使用

---

## 🔒 安全特性

- **消息签名验证**: 所有消息都经过HMAC签名验证
- **安全握手**: 客户端连接时进行安全握手
- **会话管理**: 安全的玩家会话管理
- **防重放攻击**: 时间戳验证防止重放攻击

---

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   netstat -an | findstr :8888
   ```

2. **权限问题**
   ```bash
   # 确保有写入日志目录的权限
   chmod 755 logs/
   ```

3. **配置文件错误**
   - 检查配置文件格式
   - 验证路径是否正确

### 日志分析
```bash
# 查看最新日志
tail -f logs/latest_server.log

# 搜索错误信息
grep "ERROR" logs/server/*.log
```

---

## 📞 技术支持

如有问题，请查看：
1. 日志文件中的错误信息
2. 配置文件是否正确
3. 网络连接是否正常

---

## 📄 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。
