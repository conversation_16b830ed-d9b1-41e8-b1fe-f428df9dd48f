#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务器监控模块
Server Monitor Module

负责服务器状态监控、统计、性能监控等功能
Handles server status monitoring, statistics, performance monitoring, etc.
"""

import time
import threading
import sys
from pathlib import Path
from typing import Dict, Any, Optional, Callable

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from server.room_manager import RoomManager
from shared.enhanced_logger import get_server_logger


class ServerMonitor:
    """服务器监控器 - 负责服务器状态监控和统计"""

    def __init__(self, room_manager: RoomManager):
        """初始化服务器监控器"""
        self.room_manager = room_manager
        self.logger = get_server_logger("ServerMonitor")
        
        # 监控状态
        self.monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        
        # 服务器统计
        self.start_time: Optional[float] = None
        self.total_connections = 0
        self.active_connections = 0
        self.peak_connections = 0
        
        # 性能统计
        self.message_count = 0
        self.error_count = 0
        self.last_stats_time = time.time()
        
        # 回调函数
        self.connection_stats_callback: Optional[Callable] = None
        self.game_stats_callback: Optional[Callable] = None
        
        self.logger.info("服务器监控器初始化完成 / Server monitor initialized")

    def set_connection_stats_callback(self, callback: Callable):
        """设置连接统计回调"""
        self.connection_stats_callback = callback

    def set_game_stats_callback(self, callback: Callable):
        """设置游戏统计回调"""
        self.game_stats_callback = callback

    def start_monitoring(self, interval: int = 30):
        """开始监控"""
        if self.monitoring:
            self.logger.warning("监控已在运行")
            return

        self.monitoring = True
        self.start_time = time.time()
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True,
            name="ServerMonitor"
        )
        self.monitor_thread.start()
        
        self.logger.info(f"服务器监控已启动，监控间隔: {interval}秒")

    def stop_monitoring(self):
        """停止监控"""
        if not self.monitoring:
            return

        self.monitoring = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5.0)
        
        self.logger.info("服务器监控已停止")

    def _monitor_loop(self, interval: int):
        """监控循环"""
        while self.monitoring:
            try:
                # 更新统计信息
                self._update_statistics()
                
                # 输出服务器状态
                self._log_server_status()
                
                # 清理过期数据
                self._cleanup_expired_data()
                
                # 等待下一次监控
                time.sleep(interval)
                
            except Exception as e:
                self.logger.error(f"监控循环错误: {e}")
                time.sleep(5)  # 出错时短暂等待

    def _update_statistics(self):
        """更新统计信息"""
        try:
            # 获取连接统计
            if self.connection_stats_callback:
                conn_stats = self.connection_stats_callback()
                self.total_connections = conn_stats.get("total_connections", 0)
                self.active_connections = conn_stats.get("active_connections", 0)
                
                # 更新峰值连接数
                if self.active_connections > self.peak_connections:
                    self.peak_connections = self.active_connections

            # 获取游戏统计
            if self.game_stats_callback:
                game_stats = self.game_stats_callback()
                # 这里可以处理游戏相关的统计信息

        except Exception as e:
            self.logger.error(f"更新统计信息时发生错误: {e}")

    def _log_server_status(self):
        """记录服务器状态"""
        try:
            uptime = time.time() - self.start_time if self.start_time else 0
            active_rooms = self.room_manager.get_active_room_count()
            
            status_msg = (
                f"服务器状态 - 运行时间: {uptime:.0f}秒, "
                f"活跃连接: {self.active_connections}, "
                f"总连接数: {self.total_connections}, "
                f"峰值连接: {self.peak_connections}, "
                f"活跃房间: {active_rooms}, "
                f"消息数: {self.message_count}, "
                f"错误数: {self.error_count}"
            )
            
            self.logger.info(status_msg)
            
            # 如果有严重问题，输出警告
            if self.error_count > 100:
                self.logger.warning(f"错误数量过多: {self.error_count}")
            
            if self.active_connections > 50:
                self.logger.warning(f"连接数较高: {self.active_connections}")

        except Exception as e:
            self.logger.error(f"记录服务器状态时发生错误: {e}")

    def _cleanup_expired_data(self):
        """清理过期数据"""
        try:
            # 清理房间管理器中的过期数据
            self.room_manager.cleanup_expired_rooms()
            
            # 重置计数器（每小时重置一次）
            current_time = time.time()
            if current_time - self.last_stats_time > 3600:  # 1小时
                self.message_count = 0
                self.error_count = 0
                self.last_stats_time = current_time
                self.logger.info("统计计数器已重置")

        except Exception as e:
            self.logger.error(f"清理过期数据时发生错误: {e}")

    def record_message(self):
        """记录消息"""
        self.message_count += 1

    def record_error(self):
        """记录错误"""
        self.error_count += 1

    def get_server_stats(self) -> Dict[str, Any]:
        """获取服务器统计信息"""
        uptime = time.time() - self.start_time if self.start_time else 0
        
        return {
            "uptime": uptime,
            "total_connections": self.total_connections,
            "active_connections": self.active_connections,
            "peak_connections": self.peak_connections,
            "active_rooms": self.room_manager.get_active_room_count(),
            "message_count": self.message_count,
            "error_count": self.error_count,
            "monitoring": self.monitoring
        }

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        uptime = time.time() - self.start_time if self.start_time else 1
        
        return {
            "messages_per_second": self.message_count / uptime,
            "errors_per_hour": self.error_count / (uptime / 3600) if uptime > 0 else 0,
            "average_connections": self.total_connections / (uptime / 60) if uptime > 60 else 0,
            "connection_efficiency": (self.active_connections / self.peak_connections * 100) if self.peak_connections > 0 else 0
        }

    def get_room_stats(self) -> Dict[str, Any]:
        """获取房间统计信息"""
        try:
            room_list = self.room_manager.get_room_list()
            
            # 统计房间状态
            status_counts = {}
            total_players = 0
            
            for room_info in room_list:
                status = room_info.get("status", "unknown")
                status_counts[status] = status_counts.get(status, 0) + 1
                total_players += room_info.get("current_players", 0)
            
            return {
                "total_rooms": len(room_list),
                "total_players": total_players,
                "average_players_per_room": total_players / len(room_list) if room_list else 0,
                "room_status_distribution": status_counts
            }
            
        except Exception as e:
            self.logger.error(f"获取房间统计时发生错误: {e}")
            return {}

    def generate_status_report(self) -> str:
        """生成状态报告"""
        try:
            server_stats = self.get_server_stats()
            performance_stats = self.get_performance_stats()
            room_stats = self.get_room_stats()
            
            report = f"""
=== 服务器状态报告 / Server Status Report ===
运行时间: {server_stats['uptime']:.0f}秒
总连接数: {server_stats['total_connections']}
活跃连接: {server_stats['active_connections']}
峰值连接: {server_stats['peak_connections']}
活跃房间: {server_stats['active_rooms']}
消息总数: {server_stats['message_count']}
错误总数: {server_stats['error_count']}

=== 性能统计 / Performance Statistics ===
消息处理速度: {performance_stats['messages_per_second']:.2f} 消息/秒
错误率: {performance_stats['errors_per_hour']:.2f} 错误/小时
平均连接数: {performance_stats['average_connections']:.2f}
连接效率: {performance_stats['connection_efficiency']:.1f}%

=== 房间统计 / Room Statistics ===
总房间数: {room_stats.get('total_rooms', 0)}
总玩家数: {room_stats.get('total_players', 0)}
平均每房间玩家数: {room_stats.get('average_players_per_room', 0):.1f}
房间状态分布: {room_stats.get('room_status_distribution', {})}
============================================
"""
            return report
            
        except Exception as e:
            self.logger.error(f"生成状态报告时发生错误: {e}")
            return f"状态报告生成失败: {e}"

    def check_server_health(self) -> Dict[str, Any]:
        """检查服务器健康状态"""
        try:
            health_status = {
                "healthy": True,
                "warnings": [],
                "errors": [],
                "score": 100
            }
            
            # 检查错误率
            if self.error_count > 50:
                health_status["warnings"].append(f"错误数量较高: {self.error_count}")
                health_status["score"] -= 10
            
            if self.error_count > 100:
                health_status["errors"].append(f"错误数量过多: {self.error_count}")
                health_status["healthy"] = False
                health_status["score"] -= 20
            
            # 检查连接数
            if self.active_connections > 80:
                health_status["warnings"].append(f"连接数较高: {self.active_connections}")
                health_status["score"] -= 5
            
            # 检查运行时间
            uptime = time.time() - self.start_time if self.start_time else 0
            if uptime > 86400:  # 24小时
                health_status["warnings"].append(f"服务器运行时间较长: {uptime/3600:.1f}小时")
            
            return health_status
            
        except Exception as e:
            self.logger.error(f"检查服务器健康状态时发生错误: {e}")
            return {
                "healthy": False,
                "errors": [f"健康检查失败: {e}"],
                "score": 0
            }
