#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志配置管理器
Log Configuration Manager

提供日志系统的配置和初始化功能
Provides log system configuration and initialization functionality
"""

import os
import logging
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any

class LogConfig:
    """日志配置管理器"""
    
    # 默认配置
    DEFAULT_LEVEL = "INFO"
    DEFAULT_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    DEFAULT_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"
    MAX_LOG_SIZE = 10 * 1024 * 1024  # 10MB
    
    @classmethod
    def get_log_directory(cls, log_type: str = "server") -> Path:
        """
        获取日志目录
        
        Args:
            log_type: 日志类型 ("server", "client")
            
        Returns:
            日志目录路径
        """
        base_dir = Path("logs")
        
        if log_type == "server":
            return base_dir / "server"
        elif log_type == "client":
            return base_dir / "client"
        else:
            return base_dir
    
    @classmethod
    def ensure_log_directory(cls, log_type: str = "server") -> Path:
        """
        确保日志目录存在
        
        Args:
            log_type: 日志类型
            
        Returns:
            日志目录路径
        """
        log_dir = cls.get_log_directory(log_type)
        log_dir.mkdir(parents=True, exist_ok=True)
        return log_dir
    
    @classmethod
    def get_log_formatter(cls, include_thread: bool = False) -> logging.Formatter:
        """
        获取日志格式化器
        
        Args:
            include_thread: 是否包含线程信息
            
        Returns:
            日志格式化器
        """
        if include_thread:
            format_str = "%(asctime)s - %(name)s - %(levelname)s - [%(thread)d] - %(message)s"
        else:
            format_str = cls.DEFAULT_FORMAT
        
        return logging.Formatter(
            format_str,
            datefmt=cls.DEFAULT_DATE_FORMAT
        )
    
    @classmethod
    def get_console_handler(cls, level: str = None) -> logging.StreamHandler:
        """
        获取控制台处理器
        
        Args:
            level: 日志级别
            
        Returns:
            控制台处理器
        """
        handler = logging.StreamHandler()
        handler.setLevel(getattr(logging, (level or cls.DEFAULT_LEVEL).upper()))
        handler.setFormatter(cls.get_log_formatter())
        return handler
    
    @classmethod
    def get_file_handler(cls, log_file: Path, level: str = None) -> logging.FileHandler:
        """
        获取文件处理器
        
        Args:
            log_file: 日志文件路径
            level: 日志级别
            
        Returns:
            文件处理器
        """
        handler = logging.FileHandler(log_file, encoding='utf-8')
        handler.setLevel(getattr(logging, (level or cls.DEFAULT_LEVEL).upper()))
        handler.setFormatter(cls.get_log_formatter(include_thread=True))
        return handler
    
    @classmethod
    def create_logger(cls, name: str, log_type: str = "server", 
                     level: str = None, log_to_file: bool = True,
                     log_to_console: bool = True) -> logging.Logger:
        """
        创建日志记录器
        
        Args:
            name: 日志记录器名称
            log_type: 日志类型
            level: 日志级别
            log_to_file: 是否记录到文件
            log_to_console: 是否输出到控制台
            
        Returns:
            日志记录器
        """
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, (level or cls.DEFAULT_LEVEL).upper()))
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 添加控制台处理器
        if log_to_console:
            console_handler = cls.get_console_handler(level)
            logger.addHandler(console_handler)
        
        # 添加文件处理器
        if log_to_file:
            log_dir = cls.ensure_log_directory(log_type)
            log_file = log_dir / "latest.log"
            
            # 如果文件太大，先归档
            if log_file.exists() and log_file.stat().st_size > cls.MAX_LOG_SIZE:
                cls._archive_log_file(log_file, log_dir)
            
            file_handler = cls.get_file_handler(log_file, level)
            logger.addHandler(file_handler)
        
        # 防止重复日志
        logger.propagate = False
        
        return logger
    
    @classmethod
    def _archive_log_file(cls, log_file: Path, log_dir: Path):
        """
        归档日志文件
        
        Args:
            log_file: 要归档的日志文件
            log_dir: 日志目录
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            archive_name = f"archived_{timestamp}.log"
            archive_path = log_dir / archive_name
            
            import shutil
            shutil.move(str(log_file), str(archive_path))
            
        except Exception as e:
            print(f"归档日志文件失败: {e}")
    
    @classmethod
    def setup_root_logger(cls, level: str = None):
        """
        设置根日志记录器
        
        Args:
            level: 日志级别
        """
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, (level or cls.DEFAULT_LEVEL).upper()))
        
        # 清除现有处理器
        root_logger.handlers.clear()
        
        # 添加控制台处理器
        console_handler = cls.get_console_handler(level)
        root_logger.addHandler(console_handler)
    
    @classmethod
    def get_log_levels(cls) -> Dict[str, int]:
        """获取所有可用的日志级别"""
        return {
            "DEBUG": logging.DEBUG,
            "INFO": logging.INFO,
            "WARNING": logging.WARNING,
            "ERROR": logging.ERROR,
            "CRITICAL": logging.CRITICAL
        }
