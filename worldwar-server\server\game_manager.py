#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏管理器模块
Game Manager Module

负责游戏循环、游戏状态管理等功能
Handles game loops, game state management, etc.
"""

import sys
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from server.room_manager import GameRoom, RoomManager
from shared.enhanced_logger import get_server_logger


class GameManager:
    """游戏管理器 - 负责游戏循环和状态管理"""

    def __init__(self, room_manager: RoomManager):
        """初始化游戏管理器"""
        self.room_manager = room_manager
        self.logger = get_server_logger("GameManager")
        
        # 活跃游戏管理
        self.active_games: Dict[str, Any] = {}  # room_id -> GameLoop
        
        self.logger.info("游戏管理器初始化完成 / Game manager initialized")

    def start_game(self, room_id: str, room: <PERSON>Room) -> Dict[str, Any]:
        """开始游戏"""
        try:
            # 使用房间管理器的开始游戏功能
            start_result = self.room_manager.start_game(room_id)
            if not start_result["success"]:
                return start_result

            # 创建游戏循环
            try:
                from game_logic.game_loop import GameLoop
                
                # 获取房间的地形数据
                terrain_data = self._get_room_terrain_data(room)
                
                game_loop = GameLoop(room_id, list(room.players.keys()), self.room_manager, terrain_data)

                if game_loop.start_game():
                    self.active_games[room_id] = game_loop

                    # 通知房间内所有玩家游戏开始
                    start_message = {
                        "type": "game_started",
                        "data": {
                            "room_id": room_id,
                            "players": list(room.players.keys()),
                            "game_mode": room.game_mode,
                            "message": start_result["message"]
                        }
                    }

                    self.room_manager.broadcast_to_room(room_id, start_message)
                    self.logger.info(f"房间 {room_id} 的游戏已开始")
                    
                    return {
                        "success": True,
                        "message": "游戏已成功开始",
                        "game_loop": game_loop
                    }
                else:
                    return {
                        "success": False,
                        "message": "游戏循环启动失败 / Failed to start game loop"
                    }
            except ImportError as e:
                self.logger.error(f"导入游戏循环模块失败: {e}")
                return {
                    "success": False,
                    "message": "游戏循环模块不可用 / Game loop module unavailable"
                }

        except Exception as e:
            self.logger.error(f"开始游戏时发生错误: {e}")
            return {
                "success": False,
                "message": f"开始游戏失败: {str(e)}"
            }

    def get_game_status(self, room_id: str) -> Optional[Dict[str, Any]]:
        """获取游戏状态"""
        try:
            if room_id in self.active_games:
                game_loop = self.active_games[room_id]
                game_status = game_loop.get_game_status()

                # 获取胜利条件进度
                try:
                    victory_progress = game_loop.victory_system.get_victory_progress_summary(game_loop.players)
                except AttributeError:
                    victory_progress = {}

                return {
                    "game_status": game_status,
                    "victory_progress": victory_progress
                }
            else:
                return None

        except Exception as e:
            self.logger.error(f"获取游戏状态时发生错误: {e}")
            return None

    def end_game(self, room_id: str, reason: str = "游戏结束") -> Dict[str, Any]:
        """结束游戏"""
        try:
            if room_id in self.active_games:
                game_loop = self.active_games[room_id]
                
                # 停止游戏循环
                if hasattr(game_loop, 'stop_game'):
                    game_loop.stop_game()

                # 从活跃游戏中移除
                del self.active_games[room_id]

                # 通知房间内所有玩家游戏结束
                end_message = {
                    "type": "game_ended",
                    "data": {
                        "room_id": room_id,
                        "reason": reason,
                        "message": f"游戏已结束: {reason}"
                    }
                }

                self.room_manager.broadcast_to_room(room_id, end_message)
                self.logger.info(f"房间 {room_id} 的游戏已结束: {reason}")

                return {
                    "success": True,
                    "message": f"游戏已结束: {reason}"
                }
            else:
                return {
                    "success": False,
                    "message": "游戏不存在或未开始"
                }

        except Exception as e:
            self.logger.error(f"结束游戏时发生错误: {e}")
            return {
                "success": False,
                "message": f"结束游戏失败: {str(e)}"
            }

    def pause_game(self, room_id: str) -> Dict[str, Any]:
        """暂停游戏"""
        try:
            if room_id in self.active_games:
                game_loop = self.active_games[room_id]
                
                # 暂停游戏循环
                if hasattr(game_loop, 'pause_game'):
                    game_loop.pause_game()

                # 通知房间内所有玩家游戏暂停
                pause_message = {
                    "type": "game_paused",
                    "data": {
                        "room_id": room_id,
                        "message": "游戏已暂停"
                    }
                }

                self.room_manager.broadcast_to_room(room_id, pause_message)
                self.logger.info(f"房间 {room_id} 的游戏已暂停")

                return {
                    "success": True,
                    "message": "游戏已暂停"
                }
            else:
                return {
                    "success": False,
                    "message": "游戏不存在或未开始"
                }

        except Exception as e:
            self.logger.error(f"暂停游戏时发生错误: {e}")
            return {
                "success": False,
                "message": f"暂停游戏失败: {str(e)}"
            }

    def resume_game(self, room_id: str) -> Dict[str, Any]:
        """恢复游戏"""
        try:
            if room_id in self.active_games:
                game_loop = self.active_games[room_id]
                
                # 恢复游戏循环
                if hasattr(game_loop, 'resume_game'):
                    game_loop.resume_game()

                # 通知房间内所有玩家游戏恢复
                resume_message = {
                    "type": "game_resumed",
                    "data": {
                        "room_id": room_id,
                        "message": "游戏已恢复"
                    }
                }

                self.room_manager.broadcast_to_room(room_id, resume_message)
                self.logger.info(f"房间 {room_id} 的游戏已恢复")

                return {
                    "success": True,
                    "message": "游戏已恢复"
                }
            else:
                return {
                    "success": False,
                    "message": "游戏不存在或未开始"
                }

        except Exception as e:
            self.logger.error(f"恢复游戏时发生错误: {e}")
            return {
                "success": False,
                "message": f"恢复游戏失败: {str(e)}"
            }

    def get_active_games_count(self) -> int:
        """获取活跃游戏数量"""
        return len(self.active_games)

    def get_active_games_info(self) -> Dict[str, Dict[str, Any]]:
        """获取活跃游戏信息"""
        games_info = {}
        for room_id, game_loop in self.active_games.items():
            try:
                games_info[room_id] = {
                    "room_id": room_id,
                    "status": getattr(game_loop, 'status', 'unknown'),
                    "players": getattr(game_loop, 'players', []),
                    "current_turn": getattr(game_loop, 'current_turn', 0)
                }
            except Exception as e:
                self.logger.warning(f"获取游戏 {room_id} 信息时发生错误: {e}")
                games_info[room_id] = {
                    "room_id": room_id,
                    "status": "error",
                    "error": str(e)
                }
        
        return games_info

    def cleanup_finished_games(self) -> None:
        """清理已结束的游戏"""
        finished_games = []
        
        for room_id, game_loop in self.active_games.items():
            try:
                # 检查游戏是否已结束
                if hasattr(game_loop, 'is_finished') and game_loop.is_finished():
                    finished_games.append(room_id)
            except Exception as e:
                self.logger.warning(f"检查游戏 {room_id} 状态时发生错误: {e}")
                finished_games.append(room_id)  # 出错的游戏也清理掉
        
        # 清理已结束的游戏
        for room_id in finished_games:
            try:
                self.end_game(room_id, "游戏自然结束")
            except Exception as e:
                self.logger.error(f"清理游戏 {room_id} 时发生错误: {e}")
                # 强制移除
                if room_id in self.active_games:
                    del self.active_games[room_id]

    def _get_room_terrain_data(self, room: GameRoom) -> Optional[Dict[str, Any]]:
        """获取房间的地形数据"""
        try:
            # 检查房间是否有地形配置
            if not hasattr(room, 'terrain_config') or not room.terrain_config:
                self.logger.info(f"房间 {room.room_id} 没有地形配置，使用默认初始化")
                return None
            
            terrain_config = room.terrain_config
            
            # 检查地形生成状态
            if terrain_config.get("status") != "completed":
                self.logger.warning(f"房间 {room.room_id} 地形生成未完成，状态: {terrain_config.get('status')}")
                
                # 如果地形生成失败或未完成，尝试获取结果
                if hasattr(room, 'terrain_task_id'):
                    try:
                        from world.terrain_generation_service import get_terrain_service
                        terrain_service = get_terrain_service()
                        terrain_result = terrain_service.get_task_result(room.terrain_task_id)
                        
                        if terrain_result:
                            self.logger.info(f"成功获取房间 {room.room_id} 的地形数据")
                            return terrain_result
                        else:
                            self.logger.warning(f"无法获取房间 {room.room_id} 的地形数据")
                    except Exception as e:
                        self.logger.error(f"获取地形数据时发生错误: {e}")
                
                return None
            
            # 如果有完成的地形数据，尝试获取
            if hasattr(room, 'terrain_task_id'):
                try:
                    from world.terrain_generation_service import get_terrain_service
                    terrain_service = get_terrain_service()
                    terrain_result = terrain_service.get_task_result(room.terrain_task_id)
                    
                    if terrain_result:
                        self.logger.info(f"成功获取房间 {room.room_id} 的完整地形数据")
                        return terrain_result
                    else:
                        self.logger.warning(f"地形任务已完成但无法获取结果: {room.terrain_task_id}")
                except Exception as e:
                    self.logger.error(f"获取完成的地形数据时发生错误: {e}")
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取房间地形数据时发生异常: {e}")
            return None