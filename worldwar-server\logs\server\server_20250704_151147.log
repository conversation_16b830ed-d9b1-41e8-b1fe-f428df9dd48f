2025-07-04 15:11:47 - server_ServerMain - INFO - [enhanced_logger.py:174] - ============================================================
2025-07-04 15:11:47 - server_ServerMain - INFO - [enhanced_logger.py:174] - 🚀 世界大战游戏服务器启动 / World War Game Server Starting
2025-07-04 15:11:47 - server_ServerMain - INFO - [enhanced_logger.py:174] - ============================================================
2025-07-04 15:11:47 - server_ServerPrint - INFO - [enhanced_logger.py:265] - ============================================================
2025-07-04 15:11:47 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 🎮 世界大战游戏服务器 / World War Game Server
2025-07-04 15:11:47 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 📡 版本: v3.0
2025-07-04 15:11:47 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 🌐 地址: localhost:8888
2025-07-04 15:11:47 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 📝 日志: logs/server/ 目录
2025-07-04 15:11:47 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 🛑 按 Ctrl+C 停止服务器
2025-07-04 15:11:47 - server_ServerPrint - INFO - [enhanced_logger.py:265] - ============================================================
2025-07-04 15:11:47 - server_ServerMain - INFO - [enhanced_logger.py:174] - 初始化服务器 - 地址: localhost:8888
2025-07-04 15:11:47 - server_ConfigManager - INFO - [enhanced_logger.py:174] - 配置管理器初始化完成
2025-07-04 15:11:47 - server_ConnectionManager - INFO - [enhanced_logger.py:174] - 连接管理器初始化完成 / Connection manager initialized
2025-07-04 15:11:47 - server_GameManager - INFO - [enhanced_logger.py:174] - 游戏管理器初始化完成 / Game manager initialized
2025-07-04 15:11:47 - server_MessageProcessor - INFO - [enhanced_logger.py:174] - 消息处理器初始化完成 / Message processor initialized
2025-07-04 15:11:48 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器监控器初始化完成 / Server monitor initialized
2025-07-04 15:11:48 - server_SecureGameServer - INFO - [enhanced_logger.py:174] - 安全游戏服务器初始化完成 / Secure game server initialized
2025-07-04 15:11:48 - server_ServerMain - INFO - [enhanced_logger.py:174] - 服务器初始化完成，开始启动...
2025-07-04 15:11:48 - server_SecureGameServer - INFO - [enhanced_logger.py:174] - 正在启动游戏服务器...
2025-07-04 15:11:48 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 🚀 正在启动游戏服务器... / Starting game server...
2025-07-04 15:11:48 - server_ServerPrint - INFO - [enhanced_logger.py:265] - ✅ 成功绑定到 localhost:8888 / Successfully bound to localhost:8888
2025-07-04 15:11:48 - server_ServerPrint - INFO - [enhanced_logger.py:265] - ============================================================
2025-07-04 15:11:48 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 🎮 世界大战游戏服务器已启动 / World War Game Server Started
2025-07-04 15:11:48 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 📡 服务器地址 / Server Address: localhost:8888
2025-07-04 15:11:48 - server_ServerPrint - INFO - [enhanced_logger.py:265] - ⏳ 等待客户端连接... / Waiting for client connections...
2025-07-04 15:11:48 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 🛑 按 Ctrl+C 停止服务器 / Press Ctrl+C to stop server
2025-07-04 15:11:48 - server_ServerPrint - INFO - [enhanced_logger.py:265] - ============================================================
2025-07-04 15:11:48 - server_SecureGameServer - INFO - [enhanced_logger.py:174] - 服务器启动在 localhost:8888
2025-07-04 15:11:48 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 0秒, 活跃连接: 0, 总连接数: 0, 峰值连接: 0, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 15:11:48 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器监控已启动，监控间隔: 30秒
2025-07-04 15:11:48 - server_SecureGameServer - INFO - [enhanced_logger.py:174] - 房间管理线程已启动 / Room management thread started
2025-07-04 15:12:18 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 30秒, 活跃连接: 0, 总连接数: 0, 峰值连接: 0, 活跃房间: 0, 消息数: 0, 错误数: 0
2025-07-04 15:12:40 - server_SecureGameServer - INFO - [enhanced_logger.py:174] - 新客户端连接: ('127.0.0.1', 14197)
2025-07-04 15:12:40 - server_ServerPrint - INFO - [enhanced_logger.py:265] - ✅ 新客户端连接 / New client connected: ('127.0.0.1', 14197)
2025-07-04 15:12:40 - server_ConnectionManager - INFO - [enhanced_logger.py:174] - 创建玩家会话: session_0_1751613160
2025-07-04 15:12:40 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 📝 创建玩家会话 / Created player session: session_0_1751613160
2025-07-04 15:12:40 - server_ConnectionManager - INFO - [enhanced_logger.py:174] - 开始安全握手: session_0_1751613160
2025-07-04 15:12:40 - server_ConnectionManager - INFO - [enhanced_logger.py:174] - 安全握手完成: session_0_1751613160
2025-07-04 15:12:40 - server_ServerPrint - INFO - [enhanced_logger.py:265] - 🔐 安全握手完成 / Secure handshake completed: session_0_1751613160
2025-07-04 15:12:40 - server_MessageProcessor - INFO - [enhanced_logger.py:174] - 玩家 sweatent 成功加入游戏
2025-07-04 15:12:40 - server_ServerPrint - INFO - [enhanced_logger.py:265] - ✅ 玩家加入游戏 / Player joined: sweatent
2025-07-04 15:12:47 - server_MessageProcessor - INFO - [enhanced_logger.py:174] - 玩家 sweatent 创建房间 我的房间 (ID: ROOM316705V, 模式: ai_generated)
2025-07-04 15:12:48 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 60秒, 活跃连接: 1, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:13:18 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 90秒, 活跃连接: 1, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:13:48 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 120秒, 活跃连接: 1, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:14:18 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 150秒, 活跃连接: 1, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:14:25 - server_ConnectionManager - WARNING - [enhanced_logger.py:194] - 客户端心跳超时: session_0_1751613160
2025-07-04 15:14:25 - server_ServerPrint - INFO - [enhanced_logger.py:265] - ⚠️ 客户端心跳超时 / Client heartbeat timeout: session_0_1751613160
2025-07-04 15:14:25 - server_ConnectionManager - INFO - [enhanced_logger.py:174] - 客户端 ('127.0.0.1', 14197) 断开连接
2025-07-04 15:14:25 - server_SecureGameServer - INFO - [enhanced_logger.py:174] - 客户端会话已清理: session_0_1751613160
2025-07-04 15:14:48 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 180秒, 活跃连接: 0, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:15:18 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 210秒, 活跃连接: 0, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:15:47 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 240秒, 活跃连接: 0, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:16:17 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 270秒, 活跃连接: 0, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:16:47 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 300秒, 活跃连接: 0, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:17:17 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 330秒, 活跃连接: 0, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:17:47 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 360秒, 活跃连接: 0, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:18:17 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 390秒, 活跃连接: 0, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:18:47 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 420秒, 活跃连接: 0, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:19:17 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 450秒, 活跃连接: 0, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:19:47 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 480秒, 活跃连接: 0, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:20:17 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 510秒, 活跃连接: 0, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:20:47 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 540秒, 活跃连接: 0, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:21:17 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 570秒, 活跃连接: 0, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:21:47 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 600秒, 活跃连接: 0, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:22:17 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 630秒, 活跃连接: 0, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:22:47 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 660秒, 活跃连接: 0, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:23:17 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 690秒, 活跃连接: 0, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:23:47 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 720秒, 活跃连接: 0, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:24:17 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 750秒, 活跃连接: 0, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:24:47 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 780秒, 活跃连接: 0, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:25:17 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 810秒, 活跃连接: 0, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:25:47 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 840秒, 活跃连接: 0, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
2025-07-04 15:26:17 - server_ServerMonitor - INFO - [enhanced_logger.py:174] - 服务器状态 - 运行时间: 870秒, 活跃连接: 0, 总连接数: 1, 峰值连接: 1, 活跃房间: 1, 消息数: 0, 错误数: 0
