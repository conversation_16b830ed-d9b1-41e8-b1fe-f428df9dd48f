"""
柏林噪声地形生成器
Perlin Noise Terrain Generator

基于柏林噪声算法生成程序化地形，支持多层噪声、地形平滑和可配置参数。
"""

import numpy as np
import random
from typing import Tuple, Dict, Optional, List
from perlin_noise import PerlinNoise
from dataclasses import dataclass
import json
import logging

logger = logging.getLogger(__name__)


@dataclass
class NoiseConfig:
    """噪声配置参数"""
    octaves: int = 4          # 噪声层数
    persistence: float = 0.5   # 持续性（每层振幅衰减）
    lacunarity: float = 2.0    # 间隙性（每层频率增长）
    scale: float = 100.0       # 噪声缩放
    base_frequency: float = 0.01  # 基础频率


@dataclass
class TerrainConfig:
    """地形配置参数"""
    size: Tuple[int, int] = (512, 512)  # 地形尺寸
    height_scale: float = 100.0         # 高度缩放
    sea_level: float = 0.3              # 海平面高度（0-1）
    mountain_threshold: float = 0.7     # 山地阈值
    smoothing_iterations: int = 2       # 平滑迭代次数
    smoothing_factor: float = 0.1       # 平滑因子


class PerlinTerrainGenerator:
    """基于柏林噪声的地形生成器"""
    
    def __init__(self, seed: Optional[int] = None, 
                 noise_config: Optional[NoiseConfig] = None,
                 terrain_config: Optional[TerrainConfig] = None):
        """
        初始化地形生成器
        
        Args:
            seed: 随机种子，None则使用随机种子
            noise_config: 噪声配置参数
            terrain_config: 地形配置参数
        """
        self.seed = seed if seed is not None else random.randint(0, 2**32 - 1)
        self.noise_config = noise_config or NoiseConfig()
        self.terrain_config = terrain_config or TerrainConfig()
        
        # 设置随机种子
        random.seed(self.seed)
        np.random.seed(self.seed)
        
        logger.info(f"PerlinTerrainGenerator initialized with seed: {self.seed}")
    
    def generate_heightmap(self) -> np.ndarray:
        """
        生成高度图
        
        Returns:
            np.ndarray: 高度图数组，值范围0-1
        """
        width, height = self.terrain_config.size
        heightmap = np.zeros((height, width))
        
        logger.info(f"Generating heightmap of size {width}x{height}")
        
        # 创建柏林噪声生成器
        noise = PerlinNoise(octaves=self.noise_config.octaves, seed=self.seed)
        
        # 生成多层柏林噪声
        for y in range(height):
            for x in range(width):
                # 计算噪声值
                noise_value = noise([
                    x * self.noise_config.base_frequency,
                    y * self.noise_config.base_frequency
                ])
                
                # 将噪声值从[-1, 1]映射到[0, 1]
                heightmap[y, x] = (noise_value + 1) / 2
        
        # 应用地形平滑
        heightmap = self._smooth_terrain(heightmap)
        
        # 应用高度缩放
        heightmap = self._apply_height_scaling(heightmap)
        
        logger.info("Heightmap generation completed")
        return heightmap
    
    def _smooth_terrain(self, heightmap: np.ndarray) -> np.ndarray:
        """
        地形平滑算法
        
        Args:
            heightmap: 原始高度图
            
        Returns:
            np.ndarray: 平滑后的高度图
        """
        smoothed = heightmap.copy()
        
        for iteration in range(self.terrain_config.smoothing_iterations):
            new_smoothed = smoothed.copy()
            height, width = smoothed.shape
            
            for y in range(1, height - 1):
                for x in range(1, width - 1):
                    # 计算周围8个点的平均值
                    neighbors = [
                        smoothed[y-1, x-1], smoothed[y-1, x], smoothed[y-1, x+1],
                        smoothed[y, x-1],                     smoothed[y, x+1],
                        smoothed[y+1, x-1], smoothed[y+1, x], smoothed[y+1, x+1]
                    ]
                    
                    neighbor_avg = np.mean(neighbors)
                    current_value = smoothed[y, x]
                    
                    # 应用平滑因子
                    new_smoothed[y, x] = (
                        current_value * (1 - self.terrain_config.smoothing_factor) +
                        neighbor_avg * self.terrain_config.smoothing_factor
                    )
            
            smoothed = new_smoothed
        
        return smoothed
    
    def _apply_height_scaling(self, heightmap: np.ndarray) -> np.ndarray:
        """
        应用高度缩放和调整
        
        Args:
            heightmap: 原始高度图
            
        Returns:
            np.ndarray: 缩放后的高度图
        """
        # 增强对比度
        heightmap = np.power(heightmap, 1.2)
        
        # 确保值在[0, 1]范围内
        heightmap = np.clip(heightmap, 0, 1)
        
        return heightmap
    
    def generate_temperature_map(self, heightmap: np.ndarray) -> np.ndarray:
        """
        基于高度图生成温度图
        
        Args:
            heightmap: 高度图
            
        Returns:
            np.ndarray: 温度图，值范围0-1（0=寒冷，1=炎热）
        """
        height, width = heightmap.shape
        temperature_map = np.zeros((height, width))
        
        # 创建温度噪声生成器
        temp_noise_gen = PerlinNoise(octaves=3, seed=self.seed + 1000)
        
        # 基础温度噪声
        for y in range(height):
            for x in range(width):
                # 纬度效应（假设地图从北到南）
                latitude_factor = 1.0 - (y / height)  # 北部较冷
                
                # 高度效应（海拔越高越冷）
                altitude_factor = 1.0 - heightmap[y, x] * 0.6
                
                # 温度噪声
                temp_noise = temp_noise_gen([x * 0.005, y * 0.005])
                temp_noise = (temp_noise + 1) / 2
                
                # 综合计算温度
                temperature = (
                    latitude_factor * 0.4 +
                    altitude_factor * 0.3 +
                    temp_noise * 0.3
                )
                
                temperature_map[y, x] = np.clip(temperature, 0, 1)
        
        return temperature_map
    
    def generate_precipitation_map(self, heightmap: np.ndarray) -> np.ndarray:
        """
        基于高度图生成降水图
        
        Args:
            heightmap: 高度图
            
        Returns:
            np.ndarray: 降水图，值范围0-1（0=干旱，1=湿润）
        """
        height, width = heightmap.shape
        precipitation_map = np.zeros((height, width))
        
        # 创建降水噪声生成器
        precip_noise_gen = PerlinNoise(octaves=4, seed=self.seed + 2000)
        
        for y in range(height):
            for x in range(width):
                # 降水噪声
                precip_noise = precip_noise_gen([x * 0.008, y * 0.008])
                precip_noise = (precip_noise + 1) / 2
                
                # 高度影响（山地迎风坡降水多）
                height_factor = heightmap[y, x] * 0.3 + 0.7
                
                # 综合计算降水
                precipitation = precip_noise * height_factor
                precipitation_map[y, x] = np.clip(precipitation, 0, 1)
        
        return precipitation_map
    
    def get_terrain_stats(self, heightmap: np.ndarray) -> Dict[str, float]:
        """
        获取地形统计信息
        
        Args:
            heightmap: 高度图
            
        Returns:
            Dict[str, float]: 地形统计信息
        """
        sea_level = self.terrain_config.sea_level
        mountain_threshold = self.terrain_config.mountain_threshold
        
        # 计算各种地形类型的比例
        water_ratio = np.sum(heightmap < sea_level) / heightmap.size
        land_ratio = np.sum(heightmap >= sea_level) / heightmap.size
        mountain_ratio = np.sum(heightmap > mountain_threshold) / heightmap.size
        
        stats = {
            'min_height': float(np.min(heightmap)),
            'max_height': float(np.max(heightmap)),
            'mean_height': float(np.mean(heightmap)),
            'std_height': float(np.std(heightmap)),
            'water_ratio': float(water_ratio),
            'land_ratio': float(land_ratio),
            'mountain_ratio': float(mountain_ratio),
            'sea_level': float(sea_level),
            'mountain_threshold': float(mountain_threshold)
        }
        
        return stats
    
    def create_region(self, region_name: str, center: Tuple[int, int], 
                     radius: int) -> Dict:
        """
        创建指定区域的地形数据
        
        Args:
            region_name: 区域名称
            center: 区域中心坐标 (x, y)
            radius: 区域半径
            
        Returns:
            Dict: 包含地形数据的字典
        """
        logger.info(f"Creating region '{region_name}' at center {center} with radius {radius}")
        
        # 生成完整地形
        heightmap = self.generate_heightmap()
        temperature_map = self.generate_temperature_map(heightmap)
        precipitation_map = self.generate_precipitation_map(heightmap)
        
        # 提取指定区域
        cx, cy = center
        x_start = max(0, cx - radius)
        x_end = min(heightmap.shape[1], cx + radius)
        y_start = max(0, cy - radius)
        y_end = min(heightmap.shape[0], cy + radius)
        
        region_heightmap = heightmap[y_start:y_end, x_start:x_end]
        region_temperature = temperature_map[y_start:y_end, x_start:x_end]
        region_precipitation = precipitation_map[y_start:y_end, x_start:x_end]
        
        # 创建区域数据
        region_data = {
            'name': region_name,
            'center': center,
            'radius': radius,
            'bounds': {
                'x_start': x_start,
                'x_end': x_end,
                'y_start': y_start,
                'y_end': y_end
            },
            'size': region_heightmap.shape,
            'heightmap': region_heightmap.tolist(),
            'temperature_map': region_temperature.tolist(),
            'precipitation_map': region_precipitation.tolist(),
            'stats': self.get_terrain_stats(region_heightmap),
            'generation_params': {
                'seed': self.seed,
                'noise_config': {
                    'octaves': self.noise_config.octaves,
                    'persistence': self.noise_config.persistence,
                    'lacunarity': self.noise_config.lacunarity,
                    'scale': self.noise_config.scale,
                    'base_frequency': self.noise_config.base_frequency
                },
                'terrain_config': {
                    'size': self.terrain_config.size,
                    'height_scale': self.terrain_config.height_scale,
                    'sea_level': self.terrain_config.sea_level,
                    'mountain_threshold': self.terrain_config.mountain_threshold,
                    'smoothing_iterations': self.terrain_config.smoothing_iterations,
                    'smoothing_factor': self.terrain_config.smoothing_factor
                }
            }
        }
        
        logger.info(f"Region '{region_name}' created successfully")
        return region_data
    
    def save_heightmap_as_image(self, heightmap: np.ndarray, filename: str):
        """
        将高度图保存为图像文件（用于调试和预览）
        
        Args:
            heightmap: 高度图
            filename: 保存的文件名
        """
        try:
            from PIL import Image
            
            # 将高度图转换为0-255的灰度值
            image_data = (heightmap * 255).astype(np.uint8)
            
            # 创建PIL图像
            image = Image.fromarray(image_data, mode='L')
            image.save(filename)
            
            logger.info(f"Heightmap saved as image: {filename}")
        except ImportError:
            logger.warning("PIL not available, cannot save heightmap as image")
        except Exception as e:
            logger.error(f"Error saving heightmap as image: {e}")


def create_default_generator(seed: Optional[int] = None) -> PerlinTerrainGenerator:
    """
    创建默认配置的地形生成器
    
    Args:
        seed: 随机种子
        
    Returns:
        PerlinTerrainGenerator: 地形生成器实例
    """
    return PerlinTerrainGenerator(seed=seed)


def create_large_world_generator(seed: Optional[int] = None) -> PerlinTerrainGenerator:
    """
    创建大世界地形生成器
    
    Args:
        seed: 随机种子
        
    Returns:
        PerlinTerrainGenerator: 大世界地形生成器实例
    """
    noise_config = NoiseConfig(
        octaves=6,
        persistence=0.6,
        lacunarity=2.0,
        scale=200.0,
        base_frequency=0.005
    )
    
    terrain_config = TerrainConfig(
        size=(1024, 1024),
        height_scale=150.0,
        sea_level=0.25,
        mountain_threshold=0.75,
        smoothing_iterations=3,
        smoothing_factor=0.15
    )
    
    return PerlinTerrainGenerator(seed, noise_config, terrain_config)


def create_island_generator(seed: Optional[int] = None) -> PerlinTerrainGenerator:
    """
    创建岛屿地形生成器
    
    Args:
        seed: 随机种子
        
    Returns:
        PerlinTerrainGenerator: 岛屿地形生成器实例
    """
    noise_config = NoiseConfig(
        octaves=5,
        persistence=0.4,
        lacunarity=2.5,
        scale=80.0,
        base_frequency=0.015
    )
    
    terrain_config = TerrainConfig(
        size=(512, 512),
        height_scale=80.0,
        sea_level=0.4,
        mountain_threshold=0.8,
        smoothing_iterations=4,
        smoothing_factor=0.2
    )
    
    return PerlinTerrainGenerator(seed, noise_config, terrain_config)